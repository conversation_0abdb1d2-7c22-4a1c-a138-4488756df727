from __future__ import annotations

import csv
import json
import logging
import math
import multiprocessing as mp
import os
import re
from datetime import datetime, timedelta
from multiprocessing import Pool
from typing import Any

from dateutil import parser as date_parser

logger = logging.getLogger(__name__)


class LogParser:
    """Parses Resource-System CSV and Minknow log files,
    maintains in-memory data (including quarantine for errors),
    and provides methods to query the data for REST API use cases.
    """

    def __init__(self):
        # In-memory datasets
        self.resource_data: list[dict] = []  # Each dict is a Resource-System log record.
        self.minknow_data: list[dict] = []  # Each dict is a Minknow log entry.
        self.quarantine: list[dict] = []  # Malformed or error lines.

    def parse_all(self, base_dir: str) -> None:
        """Discover and parse log files under base_dir.
        Combines parsed data from Resource-System CSV and Minknow logs.
        """
        logger.debug("Starting parse_all with base_dir: %s", base_dir)

        resource_dir = os.path.join(base_dir, "ont-resource-logger")
        minknow_dir = os.path.join(base_dir, "minknow")

        print(f"[DEBUG] Resource directory: {resource_dir}")
        print(f"[DEBUG] Minknow directory: {minknow_dir}")

        if not os.path.exists(resource_dir):
            print(f"[WARNING] Resource directory does not exist: {resource_dir}")
        if not os.path.exists(minknow_dir):
            print(f"[WARNING] Minknow directory does not exist: {minknow_dir}")

        self._discover_and_parse_resource_logs(resource_dir)
        self._discover_and_parse_minknow_logs(minknow_dir)

        # Sort data by timestamp.
        self.resource_data.sort(key=lambda x: x["timestamp"])
        self.minknow_data.sort(key=lambda x: x["timestamp"])

        print(f"[DEBUG] Parsed {len(self.resource_data)} Resource-System rows")
        print(f"[DEBUG] Parsed {len(self.minknow_data)} Minknow entries")

        # Print all unique folderNames found in the data
        folder_names = set(entry.get("folderName", "") for entry in self.minknow_data)
        print(f"[DEBUG] All unique folderNames found in data: {sorted(folder_names)}")

        # Print sample entries for each unique folderName
        print("[DEBUG] Sample entries for each folderName:")
        for folder in sorted(folder_names):
            sample = next((entry for entry in self.minknow_data if entry.get("folderName") == folder), None)
            if sample:
                print(f"[DEBUG] {folder}: {sample.get('fileName', 'No filename')}")

        # Print common timestamp range.
        common_range = self.get_full_timestamp_range()
        if common_range[0] and common_range[1]:
            print(f"[DEBUG] Common timestamp range: {common_range[0]} to {common_range[1]}")
        else:
            print("[WARNING] No overlapping timestamp range available across both datasets")

    def store_parsed_data(self, output_dir: str) -> None:
        """Serialize in-memory datasets (resource_data, minknow_data, and quarantine)
        into JSON files for debugging/validation. Timestamps are pre-formatted to
        conform to the required format: "yyyy-MM-ddTHH:mm:ss" optionally followed
        by ".SSS" if milliseconds are present.
        """
        os.makedirs(output_dir, exist_ok=True)

        def format_timestamp(dt: datetime) -> str:
            # If there are microseconds, round them to milliseconds.
            if dt.microsecond:
                ms = int(round(dt.microsecond / 1000.0))
                return dt.strftime("%Y-%m-%dT%H:%M:%S") + f".{ms:03d}"
            return dt.strftime("%Y-%m-%dT%H:%M:%S")

        def format_record(record: dict) -> dict:
            new_record = record.copy()
            if "timestamp" in new_record and isinstance(new_record["timestamp"], datetime):
                new_record["timestamp"] = format_timestamp(new_record["timestamp"])
            return new_record

        # Pre-format the timestamps in resource and Minknow data.
        formatted_resource = [format_record(r) for r in self.resource_data]
        formatted_minknow = [format_record(r) for r in self.minknow_data]

        with open(os.path.join(output_dir, "resource_data.json"), "w", encoding="utf-8") as f:
            json.dump(formatted_resource, f, indent=2)

        with open(os.path.join(output_dir, "minknow_data.json"), "w", encoding="utf-8") as f:
            json.dump(formatted_minknow, f, indent=2)

        # Quarantine data is written as-is (usually they don't contain datetime objects).
        with open(os.path.join(output_dir, "quarantine_data.json"), "w", encoding="utf-8") as f:
            json.dump(self.quarantine, f, indent=2)

        logger.info("Data stored to: %s", output_dir)

    # ---------------------- Query Methods ----------------------
    # The following query methods now return results with the timestamp formatted
    # as a string to meet the frontend requirements.

    def _format_timestamp(self, dt: datetime) -> str:
        """Format a datetime object to the required string format."""
        if dt.microsecond:
            ms = int(round(dt.microsecond / 1000.0))
            return dt.strftime("%Y-%m-%dT%H:%M:%S") + f".{ms:03d}"
        return dt.strftime("%Y-%m-%dT%H:%M:%S")

    def _format_record(self, record: dict) -> dict:
        """Return a copy of the record with a formatted timestamp."""
        rec = record.copy()
        if "timestamp" in rec and isinstance(rec["timestamp"], datetime):
            rec["timestamp"] = self._format_timestamp(rec["timestamp"])
        return rec

    def query_resource_time_range(self, start: datetime, end: datetime, column_name: str | None = None) -> list[dict[str, Any]]:
        """Returns data points as timestamp and value pairs for the given time range.
        If a column name is provided, returns only that column's value paired with the timestamp.
        Otherwise, returns all columns for each record with the timestamp formatted.
        """
        if column_name:
            return [
                {
                    "timestamp": self._format_timestamp(record["timestamp"]),
                    "value": record.get(column_name),
                }
                for record in self.resource_data
                if start <= record["timestamp"] <= end and column_name in record
            ]
        return [self._format_record(record) for record in self.resource_data if start <= record["timestamp"] <= end]

    def get_resource_column(self, column_name: str) -> list[dict[str, Any]]:
        """Retrieves data points of timestamp and value pairs for a specified column
        from the Resource-System logs.
        """
        return [
            {"timestamp": self._format_timestamp(record["timestamp"]), "value": record[column_name]}
            for record in self.resource_data
            if column_name in record
        ]

    def query_minknow(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        log_event: str | None = None,
        process_name: str | None = None,
        log_level: str | None = None,
        library: str | None = None,
        position: str | None = None,
    ) -> list[dict]:
        """Returns Minknow log entries matching the provided filters with the timestamp
        formatted as a string.
        The 'position' parameter filters by the Minknow log's folderName.
        """
        print(f"[DEBUG] query_minknow called with position={position}, start={start}, end={end}")
        print(f"[DEBUG] Total entries in minknow_data before filtering: {len(self.minknow_data)}")

        # If a specific position is requested, show its timestamp range
        if position:
            position_entries = [r for r in self.minknow_data if r.get("folderName") == position]
            if position_entries:
                pos_timestamps = [r["timestamp"] for r in position_entries]
                print(f"[DEBUG] Position {position} timestamp range: {min(pos_timestamps)} to {max(pos_timestamps)}")
                print(f"[DEBUG] Position {position} has {len(position_entries)} total entries")

        results = self.minknow_data

        if start is not None:
            results = [r for r in results if r["timestamp"] >= start]
            print(f"[DEBUG] After start filter: {len(results)} entries")

        if end is not None:
            results = [r for r in results if r["timestamp"] <= end]
            print(f"[DEBUG] After end filter: {len(results)} entries")

        if log_event is not None:
            results = [r for r in results if r["log_event"] == log_event]
            print(f"[DEBUG] After log_event filter: {len(results)} entries")

        if process_name is not None:
            results = [r for r in results if r["processName"] == process_name]
            print(f"[DEBUG] After process_name filter: {len(results)} entries")

        if log_level is not None:
            results = [r for r in results if r["log_level"] == log_level]
            print(f"[DEBUG] After log_level filter: {len(results)} entries")

        if library is not None:
            results = [r for r in results if r["library"] == library]
            print(f"[DEBUG] After library filter: {len(results)} entries")

        if position is not None:
            print(f"[DEBUG] Filtering for position {position}")
            results = [r for r in results if r.get("folderName") == position]
            print(f"[DEBUG] After position filter: {len(results)} entries")

        formatted_results = [self._format_record(r) for r in results]
        print(f"[DEBUG] Final formatted results count: {len(formatted_results)}")
        return formatted_results

    def get_full_timestamp_range(self) -> tuple[datetime | None, datetime | None]:
        """Computes the timestamp range based on Resource-System logs.
        Returns a tuple (start, end) rounded respectively down/up to the nearest minute.
        If the dataset is empty, returns (None, None).
        """
        # Get resource log range
        resource_timestamps = [r["timestamp"] for r in self.resource_data if "timestamp" in r]
        if not resource_timestamps:
            return None, None

        resource_min, resource_max = min(resource_timestamps), max(resource_timestamps)
        print(f"[DEBUG] Resource timestamp range: {resource_min} to {resource_max}")

        # Round down start time and up end time to nearest minute
        return self._round_down_to_minute(resource_min), self._round_up_to_minute(resource_max)

    def get_minknow_positions(self) -> list[str]:
        """Returns a list of distinct positions (folderName) from the Minknow logs."""
        positions = {record.get("folderName", "") for record in self.minknow_data}
        return list(positions)

    def _round_down_to_minute(self, dt: datetime) -> datetime:
        """Round a datetime down to the nearest minute."""
        return dt.replace(second=0, microsecond=0)

    def _round_up_to_minute(self, dt: datetime) -> datetime:
        """Round a datetime up to the nearest minute."""
        if dt.second == 0 and dt.microsecond == 0:
            return dt
        return dt.replace(second=0, microsecond=0) + timedelta(minutes=1)

    # ---------------------- Internal Parsing Methods ----------------------

    def _discover_and_parse_resource_logs(self, resource_dir: str) -> None:
        """Discover and parse resource log files in parallel."""
        if not os.path.isdir(resource_dir):
            return

        # Collect all resource log files
        resource_files = []
        for root, _, files in os.walk(resource_dir):
            for fname in files:
                if fname.startswith("resource-system") and fname.endswith(".log"):
                    resource_files.append(os.path.join(root, fname))

        if not resource_files:
            return

        # Use multiprocessing to parse files in parallel
        num_processes = min(mp.cpu_count(), len(resource_files))
        print(f"[DEBUG] Processing {len(resource_files)} resource files using {num_processes} processes")

        with Pool(processes=num_processes) as pool:
            results = pool.map(self._parallel_parse_resource_file, resource_files)

        # Combine results while maintaining chronological order
        all_data = []
        all_quarantine = []
        for data, quarantine in results:
            all_data.extend(data)
            all_quarantine.extend(quarantine)

        # Sort by timestamp before storing
        all_data.sort(key=lambda x: x["timestamp"])
        self.resource_data.extend(all_data)
        self.quarantine.extend(all_quarantine)

    def _discover_and_parse_minknow_logs(self, minknow_dir: str) -> None:
        """Discover and parse Minknow log files in parallel."""
        print(f"[DEBUG] Starting to discover and parse Minknow logs in: {minknow_dir}")

        if not os.path.isdir(minknow_dir):
            print(f"[WARNING] Minknow directory does not exist: {minknow_dir}")
            return

        # Collect all Minknow log files with their metadata
        minknow_files = []
        for root, _, files in os.walk(minknow_dir):
            subfolder = os.path.relpath(root, minknow_dir)
            if subfolder == ".":
                subfolder = ""

            for fname in files:
                if fname.startswith("bream-") and fname.endswith(".txt"):
                    continue

                match = re.match(r"(.*)_log-(\d+)\.txt", fname)
                if match:
                    process_name = match.group(1)
                    full_path = os.path.join(root, fname)
                    minknow_files.append((full_path, process_name, subfolder))

        if not minknow_files:
            return

        # Use multiprocessing to parse files in parallel
        num_processes = min(mp.cpu_count(), len(minknow_files))
        print(f"[DEBUG] Processing {len(minknow_files)} Minknow files using {num_processes} processes")

        with Pool(processes=num_processes) as pool:
            results = pool.map(self._parallel_parse_minknow_file, minknow_files)

        # Combine results while maintaining chronological order
        all_data = []
        all_quarantine = []
        for data, quarantine in results:
            all_data.extend(data)
            all_quarantine.extend(quarantine)

        # Sort by timestamp before storing
        all_data.sort(key=lambda x: x["timestamp"])
        self.minknow_data.extend(all_data)
        self.quarantine.extend(all_quarantine)

        # Print summary of what was found
        folder_counts = {}
        for entry in all_data:
            folder = entry.get("folderName", "")
            folder_counts[folder] = folder_counts.get(folder, 0) + 1

        print("[DEBUG] Entry counts by folderName:")
        for folder, count in sorted(folder_counts.items()):
            print(f"[DEBUG]   {folder}: {count} entries")

    def _parse_resource_system_csv(self, file_path: str) -> None:
        try:
            with open(file_path, encoding="utf-8") as f:
                reader = csv.reader(f)
                line_number = 0

                try:
                    header = next(reader)
                    line_number += 1
                except StopIteration:
                    return

                for row in reader:
                    line_number += 1
                    if len(row) != len(header):
                        self._quarantine_line(
                            original_line=",".join(row),
                            file=file_path,
                            line_number=line_number,
                            error="Row has incorrect number of columns",
                        )
                        continue

                    row_dict = {}
                    for col_name, col_value in zip(header, row, strict=False):
                        if col_name.lower() == "timestamp":
                            try:
                                # Parse timestamp and ensure it's timezone-naive
                                timestamp = date_parser.parse(col_value)
                                if timestamp.tzinfo:
                                    timestamp = timestamp.replace(tzinfo=None)
                                row_dict["timestamp"] = timestamp
                            except Exception:
                                self._quarantine_line(
                                    original_line=",".join(row),
                                    file=file_path,
                                    line_number=line_number,
                                    error=f"Could not parse timestamp '{col_value}'",
                                )
                                row_dict = None
                                break
                        else:
                            bracket_match = re.match(r"([A-Za-z0-9_]+)\[(\d+)\]\.(.+)", col_name)
                            if bracket_match:
                                prefix = bracket_match.group(1)
                                idx = int(bracket_match.group(2))
                                subkey = bracket_match.group(3)

                                if prefix not in row_dict:
                                    row_dict[prefix] = []
                                while len(row_dict[prefix]) <= idx:
                                    row_dict[prefix].append({})
                                row_dict[prefix][idx][subkey] = self._convert_numeric_if_possible(col_value)
                            else:
                                row_dict[col_name] = self._convert_numeric_if_possible(col_value)

                    if row_dict is not None and "timestamp" in row_dict:
                        self.resource_data.append(row_dict)
        except FileNotFoundError:
            return
        except Exception as e:
            self._quarantine_line(
                original_line="(File Read Error)",
                file=file_path,
                line_number=-1,
                error=f"Error reading file: {e!s}",
            )

    def _parse_minknow_log_file(self, file_path: str, process_name: str, folder_name: str) -> None:
        try:
            with open(file_path, encoding="utf-8") as f:
                current_entry = None
                line_number = 0

                for line in f:
                    line_number += 1
                    match = re.match(r"^(\S+\s+\S+)\s+(\S+):\s+(\S+)\s+\(([^)]+)\)\s*$", line.strip())
                    if match:
                        # Ensure required fields are present.
                        timestamp_str = match.group(1)
                        log_level = match.group(2)
                        log_event = match.group(3)
                        if not (timestamp_str and log_level and log_event):
                            self._quarantine_line(
                                original_line=line.strip(),
                                file=file_path,
                                folder=folder_name,
                                line_number=line_number,
                                error="Minknow log entry missing required field(s): timestamp, log level, or event",
                            )
                            continue

                        if current_entry is not None:
                            self.minknow_data.append(current_entry)

                        try:
                            # Parse timestamp and ensure it's timezone-naive
                            parsed_timestamp = date_parser.parse(timestamp_str)
                            if parsed_timestamp.tzinfo:
                                parsed_timestamp = parsed_timestamp.replace(tzinfo=None)
                        except Exception:
                            self._quarantine_line(
                                original_line=line.strip(),
                                file=file_path,
                                folder=folder_name,
                                line_number=line_number,
                                error=f"Could not parse timestamp '{timestamp_str}'",
                            )
                            current_entry = None
                            continue

                        current_entry = {
                            "timestamp": parsed_timestamp,
                            "log_level": log_level,
                            "log_event": log_event,
                            "library": match.group(4),
                            "fileName": os.path.basename(file_path),
                            "folderName": folder_name,
                            "processName": process_name,
                            "keys": {},
                        }
                    else:
                        if current_entry is None:
                            self._quarantine_line(
                                original_line=line.strip(),
                                file=file_path,
                                folder=folder_name,
                                line_number=line_number,
                                error="Line not associated with any log entry",
                            )
                            continue
                        stripped = line.strip()
                        kv_match = re.match(r"^([^:]+):\s+(.*)$", stripped)
                        if kv_match:
                            key = kv_match.group(1).strip()
                            value = kv_match.group(2).strip()
                            current_entry["keys"][key] = value
                        else:
                            if "meta" not in current_entry["keys"]:
                                current_entry["keys"]["meta"] = []
                            current_entry["keys"]["meta"].append(stripped)

                if current_entry is not None:
                    self.minknow_data.append(current_entry)

        except FileNotFoundError:
            return
        except Exception as e:
            self._quarantine_line(
                original_line="(File Read Error)",
                file=file_path,
                folder=folder_name,
                line_number=-1,
                error=f"Error reading Minknow log file: {e!s}",
            )

    def _quarantine_line(self, original_line: str, file: str, line_number: int, error: str, folder: str = "") -> None:
        self.quarantine.append(
            {
                "line": original_line,
                "file": file,
                "folder": folder,
                "line_number": line_number,
                "error": error,
            }
        )

    def _convert_numeric_if_possible(self, value: str) -> Any:
        if value is None or value == "":
            return value
        try:
            return int(value)
        except ValueError:
            pass
        try:
            return float(value)
        except ValueError:
            pass
        return value

    def aggregate_logs_by_time(
        self,
        start_time: datetime,
        end_time: datetime,
        bucket_size_seconds: int = 3600,
        positions: list[str] | None = None,
    ) -> dict:
        """Aggregate log data by time buckets, log levels, and positions.
        Creates a complete grid of buckets for the time range, including empty buckets.

        Args:
            start_time: Start of the time range to aggregate
            end_time: End of the time range to aggregate
            bucket_size_seconds: Size of each time bucket in seconds
            positions: Optional list of positions to filter by
        """
        # Convert times to UTC timestamps for consistent comparison
        start_ts = int(start_time.astimezone().timestamp())
        end_ts = int(end_time.astimezone().timestamp())

        # Filter logs within time range and by positions if specified
        filtered_logs = [
            log
            for log in self.minknow_data
            if start_ts <= log["timestamp"].astimezone().timestamp() <= end_ts
            and (positions is None or log.get("folderName", "") in positions)
        ]

        # Initialize buckets with position-specific counters
        buckets = []
        log_levels = ["INFO", "WARNING", "ERROR"]

        # Get unique positions from filtered logs
        actual_positions = positions if positions else list(set(log.get("folderName", "") for log in filtered_logs))

        # Create all possible buckets for each position
        current_ts = start_ts
        while current_ts < end_ts:
            bucket_end = min(current_ts + bucket_size_seconds, end_ts)

            # Create a bucket for each position and log level at this timestamp
            for position in actual_positions:
                for level in log_levels:
                    bucket = {
                        "timestamp": current_ts * 1000,  # Convert to milliseconds for frontend
                        "logLevel": level,
                        "position": position,
                        "value": 0,
                    }
                    buckets.append(bucket)

            current_ts += bucket_size_seconds

        # Fill in the actual counts
        for log in filtered_logs:
            ts = int(log["timestamp"].astimezone().timestamp())
            position = log.get("folderName", "")
            # Find which bucket this log belongs to
            bucket_start = start_ts + ((ts - start_ts) // bucket_size_seconds) * bucket_size_seconds

            # Find the matching bucket for this position and log level
            for bucket in buckets:
                if bucket["timestamp"] == bucket_start * 1000 and bucket["logLevel"] == log["log_level"] and bucket["position"] == position:
                    bucket["value"] += 1
                    break

        # Sort by timestamp, position, and then by log level
        buckets.sort(key=lambda x: (x["timestamp"], x["position"], x["logLevel"]))

        # Calculate max counts for each level
        max_counts = {
            "info": max([d["value"] for d in buckets if d["logLevel"] == "INFO"], default=0),
            "warning": max([d["value"] for d in buckets if d["logLevel"] == "WARNING"], default=0),
            "error": max([d["value"] for d in buckets if d["logLevel"] == "ERROR"], default=0),
        }

        # Reorganize data by log level and position
        data_by_level = {"info": [], "warning": [], "error": []}

        # Group buckets by log level, maintaining position separation
        current_ts = None
        current_position = None
        for bucket in buckets:
            level_key = bucket["logLevel"].lower()
            data_by_level[level_key].append(
                {
                    "timestamp": bucket["timestamp"],
                    "position": bucket["position"],
                    "value": bucket["value"],
                }
            )

        return {"data": data_by_level, "maxCounts": max_counts}

    def calculate_bucket_size(self, start_time: datetime, end_time: datetime) -> int:
        """Calculate bucket size dynamically based on time range to achieve a target number of buckets.

        Args:
            start_time: Start of the time range
            end_time: End of the time range

        Returns:
            Bucket size in seconds
        """
        # Target number of buckets for visualization
        TARGET_BUCKETS = 75  # Aim for 75 buckets
        MIN_BUCKET_SIZE = 10  # Minimum bucket size of 10 seconds

        # Calculate total time range in seconds
        total_seconds = (end_time - start_time).total_seconds()

        # Calculate ideal bucket size to achieve target number of buckets
        bucket_size = max(MIN_BUCKET_SIZE, int(total_seconds / TARGET_BUCKETS))

        # Round bucket size to nearest sensible interval
        if bucket_size < 60:  # Less than 1 minute
            # Round to nearest 10 seconds
            bucket_size = ((bucket_size + 5) // 10) * 10
        elif bucket_size < 300:  # Less than 5 minutes
            # Round to nearest minute
            bucket_size = ((bucket_size + 30) // 60) * 60
        elif bucket_size < 3600:  # Less than 1 hour
            # Round to nearest 5 minutes
            bucket_size = ((bucket_size + 150) // 300) * 300
        elif bucket_size < 86400:  # Less than 1 day
            # Round to nearest hour
            bucket_size = ((bucket_size + 1800) // 3600) * 3600
        else:
            # Round to nearest day
            bucket_size = ((bucket_size + 43200) // 86400) * 86400

        return bucket_size

    def precompute_full_heatmap_data(self):
        """Precomputes heatmap data for the entire dataset at startup.
        Uses fixed bucket size based on total time range.
        """
        start_time, end_time = self.get_full_timestamp_range()
        if not start_time or not end_time:
            return None

        # Calculate total duration and determine bucket size
        total_duration = (end_time - start_time).total_seconds()
        TARGET_BUCKETS = 500  # Aim for ~200 buckets total
        bucket_size = max(60, int(total_duration / TARGET_BUCKETS))  # Minimum 1 minute buckets

        positions = sorted(self.get_minknow_positions())
        if not positions:
            return None

        print(f"[DEBUG] Precomputing heatmap with {len(positions)} positions and bucket size {bucket_size} seconds")

        # Initialize data structure
        heatmap_data = []
        max_count = 0

        # Create buckets for each position
        current_time = start_time
        while current_time < end_time:
            bucket_end = min(current_time + timedelta(seconds=bucket_size), end_time)

            for position in positions:
                # Filter logs for this bucket and position
                bucket_logs = [
                    log
                    for log in self.minknow_data
                    if (current_time <= log["timestamp"] < bucket_end and log.get("folderName") == position)
                ]

                count = len(bucket_logs)
                if count > 0:  # Only add non-empty buckets
                    has_error = any(log["log_level"] == "ERROR" for log in bucket_logs)
                    max_count = max(max_count, count)

                    # Calculate log-scaled value (add 1 to avoid log(0))
                    # We'll use natural log (base e) as it tends to give good visualization results
                    log_value = math.log(count + 1)

                    heatmap_data.append(
                        {
                            "timestamp": int(current_time.timestamp() * 1000),  # milliseconds
                            "position": position,
                            "count": count,
                            "log_value": log_value,
                            "has_error": has_error,
                        }
                    )

            current_time = bucket_end

        # Calculate max log value for scaling
        max_log_value = math.log(max_count + 1)

        print(f"[DEBUG] Generated {len(heatmap_data)} heatmap data points")
        print(f"[DEBUG] Max count: {max_count}, Max log value: {max_log_value:.2f}")

        return {
            "data": heatmap_data,
            "start_time": int(start_time.timestamp() * 1000),
            "end_time": int(end_time.timestamp() * 1000),
            "bucket_size": bucket_size,
            "positions": positions,
            "max_count": max_count,
            "max_log_value": max_log_value,
        }

    def _parallel_parse_resource_file(self, file_path: str) -> tuple[list[dict], list[dict]]:
        """Parse a single resource log file and return its data and quarantine entries.
        This is designed to be called in parallel for multiple files.
        """
        resource_data = []
        quarantine = []

        try:
            with open(file_path, encoding="utf-8") as f:
                reader = csv.reader(f)
                line_number = 0

                try:
                    header = next(reader)
                    line_number += 1
                except StopIteration:
                    return [], []

                for row in reader:
                    line_number += 1
                    if len(row) != len(header):
                        quarantine.append(
                            {
                                "line": ",".join(row),
                                "file": file_path,
                                "line_number": line_number,
                                "error": "Row has incorrect number of columns",
                            }
                        )
                        continue

                    row_dict = {}
                    for col_name, col_value in zip(header, row, strict=False):
                        if col_name.lower() == "timestamp":
                            try:
                                timestamp = date_parser.parse(col_value)
                                if timestamp.tzinfo:
                                    timestamp = timestamp.replace(tzinfo=None)
                                row_dict["timestamp"] = timestamp
                            except Exception:
                                quarantine.append(
                                    {
                                        "line": ",".join(row),
                                        "file": file_path,
                                        "line_number": line_number,
                                        "error": f"Could not parse timestamp '{col_value}'",
                                    }
                                )
                                row_dict = None
                                break
                        else:
                            bracket_match = re.match(r"([A-Za-z0-9_]+)\[(\d+)\]\.(.+)", col_name)
                            if bracket_match:
                                prefix = bracket_match.group(1)
                                idx = int(bracket_match.group(2))
                                subkey = bracket_match.group(3)

                                if prefix not in row_dict:
                                    row_dict[prefix] = []
                                while len(row_dict[prefix]) <= idx:
                                    row_dict[prefix].append({})
                                row_dict[prefix][idx][subkey] = self._convert_numeric_if_possible(col_value)
                            else:
                                row_dict[col_name] = self._convert_numeric_if_possible(col_value)

                    if row_dict is not None and "timestamp" in row_dict:
                        resource_data.append(row_dict)

        except Exception as e:
            quarantine.append(
                {
                    "line": "(File Read Error)",
                    "file": file_path,
                    "line_number": -1,
                    "error": f"Error reading file: {e!s}",
                }
            )

        return resource_data, quarantine

    def _parallel_parse_minknow_file(self, args: tuple[str, str, str]) -> tuple[list[dict], list[dict]]:
        """Parse a single Minknow log file and return its data and quarantine entries.
        This is designed to be called in parallel for multiple files.
        """
        file_path, process_name, folder_name = args
        minknow_data = []
        quarantine = []

        try:
            with open(file_path, encoding="utf-8") as f:
                current_entry = None
                line_number = 0

                for line in f:
                    line_number += 1
                    match = re.match(r"^(\S+\s+\S+)\s+(\S+):\s+(\S+)\s+\(([^)]+)\)\s*$", line.strip())
                    if match:
                        timestamp_str = match.group(1)
                        log_level = match.group(2)
                        log_event = match.group(3)

                        if not (timestamp_str and log_level and log_event):
                            quarantine.append(
                                {
                                    "line": line.strip(),
                                    "file": file_path,
                                    "folder": folder_name,
                                    "line_number": line_number,
                                    "error": "Minknow log entry missing required field(s)",
                                }
                            )
                            continue

                        if current_entry is not None:
                            minknow_data.append(current_entry)

                        try:
                            parsed_timestamp = date_parser.parse(timestamp_str)
                            if parsed_timestamp.tzinfo:
                                parsed_timestamp = parsed_timestamp.replace(tzinfo=None)
                        except Exception:
                            quarantine.append(
                                {
                                    "line": line.strip(),
                                    "file": file_path,
                                    "folder": folder_name,
                                    "line_number": line_number,
                                    "error": f"Could not parse timestamp '{timestamp_str}'",
                                }
                            )
                            current_entry = None
                            continue

                        current_entry = {
                            "timestamp": parsed_timestamp,
                            "log_level": log_level,
                            "log_event": log_event,
                            "library": match.group(4),
                            "fileName": os.path.basename(file_path),
                            "folderName": folder_name,
                            "processName": process_name,
                            "keys": {},
                        }
                    else:
                        if current_entry is None:
                            quarantine.append(
                                {
                                    "line": line.strip(),
                                    "file": file_path,
                                    "folder": folder_name,
                                    "line_number": line_number,
                                    "error": "Line not associated with any log entry",
                                }
                            )
                            continue
                        stripped = line.strip()
                        kv_match = re.match(r"^([^:]+):\s+(.*)$", stripped)
                        if kv_match:
                            key = kv_match.group(1).strip()
                            value = kv_match.group(2).strip()
                            current_entry["keys"][key] = value
                        else:
                            if "meta" not in current_entry["keys"]:
                                current_entry["keys"]["meta"] = []
                            current_entry["keys"]["meta"].append(stripped)

                if current_entry is not None:
                    minknow_data.append(current_entry)

        except Exception as e:
            quarantine.append(
                {
                    "line": "(File Read Error)",
                    "file": file_path,
                    "folder": folder_name,
                    "line_number": -1,
                    "error": f"Error reading Minknow log file: {e!s}",
                }
            )

        return minknow_data, quarantine


def main():
    """Entry point for running the parser as a script.
    Usage: python log_parser.py <base_dir_of_logs> <output_dir_for_parsed_data>
    """
    import sys

    if len(sys.argv) < 3:
        print("Usage: python log_parser.py <base_dir_of_logs> <output_dir_for_parsed_data>")
        sys.exit(1)

    base_dir = sys.argv[1]
    output_dir = sys.argv[2]

    parser = LogParser()
    parser.parse_all(base_dir)
    parser.store_parsed_data(output_dir)

    print(f"JSON output stored in: {output_dir}")


if __name__ == "__main__":
    main()
