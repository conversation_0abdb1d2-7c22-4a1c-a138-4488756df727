from __future__ import annotations

import re
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd

from ..backend.app.models.log_entry import ResourceLogEntry, StructuredLogEntry
from ..backend.app.utils.time_utils import (
    round_down_to_minute,
    round_up_to_minute,
)


class LogAnalyzer:
    """Analyzes log data by providing querying and aggregation capabilities.
    This layer sits on top of the parsed data and provides all analysis functionality.
    Uses Pandas DataFrames for efficient data manipulation and aggregation.
    """

    def __init__(self, resource_data: list[ResourceLogEntry], minknow_data: list[StructuredLogEntry]):
        """Initialize with resource and minknow data, converting to DataFrames."""
        # Convert resource data to DataFrame, restructuring nested columns
        resource_records = []

        # First pass: discover all possible block device names and their indices
        block_device_mapping = {}  # Maps device names to their original indices
        print("\n[DEBUG] First pass: discovering block devices")
        for entry in resource_data:
            for key in entry.data.keys():
                if key.startswith("Block[") and ".Name" in key:
                    idx = int(key[key.index("[") + 1 : key.index("]")])
                    name = entry.data[key]
                    if name and name not in block_device_mapping:
                        block_device_mapping[name] = idx
                        print(f"[DEBUG] Found block device: {name} at index {idx}")

        try:
            for entry in resource_data:
                # Create a dictionary to store the flattened data
                record = {"timestamp": entry.timestamp}

                # Process block devices
                block_data = {}
                for key, value in entry.data.items():
                    if key.startswith("Block["):
                        match = re.match(r"Block\[(\d+)\]\.(.+)", key)
                        if match:
                            idx, field = match.groups()
                            idx = int(idx)
                            if idx not in block_data:
                                block_data[idx] = {}
                            block_data[idx][field] = value
                    else:
                        record[key] = value

                # Add Block data if present
                if block_data:
                    flattened_block = {}
                    for idx, block in block_data.items():
                        if "Name" in block:
                            name = block["Name"]
                            flattened_block[f"{name}_read"] = block.get("ReadBytesPerSecond")
                            flattened_block[f"{name}_write"] = block.get("WriteBytesPerSecond")
                            flattened_block[f"{name}_saturation"] = block.get("Saturation")
                    record["Block"] = flattened_block

                resource_records.append(record)

            self.resource_df = pd.DataFrame(resource_records)
            if not self.resource_df.empty:
                self.resource_df.set_index("timestamp", inplace=True)
                self.resource_df.sort_index(inplace=True)

                print(f"\n[DEBUG] Successfully created resource DataFrame with {len(self.resource_df)} records")
                print("[DEBUG] Block device columns:")
                block_columns = [col for col in self.resource_df.columns if col == "Block"]
                for col in sorted(block_columns):
                    print(f"[DEBUG]   {col}")

            # Convert minknow data to DataFrame
            minknow_records = []
            for entry in minknow_data:
                record = {
                    "timestamp": entry.timestamp,
                    "log_level": entry.log_level,
                    "log_event": entry.log_event,
                    "library": entry.library,
                    "file_name": entry.file_name,
                    "folder_name": entry.folder_name,
                    "process_name": entry.process_name,
                    "keys": entry.keys,
                }
                minknow_records.append(record)
            self.minknow_df = pd.DataFrame(minknow_records)
            if not self.minknow_df.empty:
                self.minknow_df.set_index("timestamp", inplace=True)
                self.minknow_df.sort_index(inplace=True)

            # Initialize column statistics
            self.column_stats = {}
            if not self.resource_df.empty:
                total_entries = len(self.resource_df)
                print(f"\n[DEBUG] Total entries in resource_df: {total_entries}")

                # Calculate statistics for all columns
                for col in self.resource_df.columns:
                    non_null_count = self.resource_df[col].count()
                    first_seen = self.resource_df[col].first_valid_index()
                    last_seen = self.resource_df[col].last_valid_index()

                    self.column_stats[col] = {
                        "completeness": ((non_null_count / total_entries * 100) if total_entries > 0 else 0),
                        "first_seen": first_seen.isoformat() if first_seen else None,
                        "last_seen": last_seen.isoformat() if last_seen else None,
                        "non_null_count": non_null_count,
                        "total_entries": total_entries,
                    }

                    # Print stats for block device columns
                    if col == "Block":
                        print(f"[DEBUG] Stats for {col}: {self.column_stats[col]}")

        except Exception as e:
            print(f"[ERROR] Error during LogAnalyzer initialization: {e!s}")
            print(f"[ERROR] Exception type: {type(e)}")
            import traceback

            print(f"[ERROR] Traceback: {traceback.format_exc()}")
            raise  # Re-raise the exception after logging it

    def get_full_timestamp_range(self) -> tuple[datetime | None, datetime | None]:
        """Get the full timestamp range of the data."""
        if self.resource_df.empty:
            return None, None

        resource_min = self.resource_df.index.min()
        resource_max = self.resource_df.index.max()

        return round_down_to_minute(resource_min), round_up_to_minute(resource_max)

    def get_minknow_positions(self) -> list[str]:
        """Get list of unique Minknow positions."""
        if self.minknow_df.empty:
            return []
        return sorted(self.minknow_df["folder_name"].unique())

    def get_position_timestamp_range(self, position: str) -> tuple[datetime | None, datetime | None]:
        """Get the timestamp range for a specific position and its intersection with resource data.

        Args:
            position: The position name to get timestamp range for

        Returns:
            Tuple of (start, end) timestamps representing the intersection of the position's
            logs with the resource data. If no intersection exists, returns the position's
            log timestamps. If position has no logs, returns (None, None).
        """
        if self.minknow_df.empty:
            return None, None

        # Filter minknow logs for the specified position
        position_df = self.minknow_df[self.minknow_df["folder_name"] == position]

        if position_df.empty:
            print(f"[DEBUG] No logs found for position: {position}")
            return None, None

        # Get timestamp range for the position
        position_min = position_df.index.min()
        position_max = position_df.index.max()

        print(f"[DEBUG] Position {position} timestamp range: {position_min} to {position_max}")

        # If resource data is available, find intersection
        if not self.resource_df.empty:
            resource_min = self.resource_df.index.min()
            resource_max = self.resource_df.index.max()

            # Calculate intersection
            intersection_min = max(position_min, resource_min) if position_min and resource_min else position_min
            intersection_max = min(position_max, resource_max) if position_max and resource_max else position_max

            # Check if there's a valid intersection
            if intersection_min and intersection_max and intersection_min <= intersection_max:
                print(f"[DEBUG] Position {position} intersection with resource data: {intersection_min} to {intersection_max}")
                return round_down_to_minute(intersection_min), round_up_to_minute(intersection_max)

        # If no intersection or no resource data, return position range
        return round_down_to_minute(position_min), round_up_to_minute(position_max)

    def get_column_availability(self, start: datetime | None, end: datetime | None) -> dict[str, Any]:
        """Calculate column availability statistics for a given time range.

        Args:
            start: Start of the time range
            end: End of the time range

        Returns:
            Dictionary containing:
                - total_columns: Total number of columns in resource data
                - available_columns: Number of columns with data in the specified range
                - availability_percent: Percentage of columns available
                - column_details: Dict with column names as keys and availability percentage as values
        """
        if self.resource_df.empty or start is None or end is None:
            return {
                "total_columns": 0,
                "available_columns": 0,
                "availability_percent": 0,
                "column_details": {},
            }

        try:
            # Create a mask for the time range
            filtered_df = self.resource_df.loc[(self.resource_df.index.to_series() >= start) & (self.resource_df.index.to_series() <= end)]

            if filtered_df.empty:
                return {
                    "total_columns": len(self.resource_df.columns),
                    "available_columns": 0,
                    "availability_percent": 0,
                    "column_details": {},
                }

            # Calculate availability for each column
            total_rows = len(filtered_df)
            column_details = {}
            available_columns = 0

            for col in self.resource_df.columns:
                # Count non-null values in the filtered range
                non_null_count = filtered_df[col].count()
                availability = (non_null_count / total_rows * 100) if total_rows > 0 else 0

                # Consider a column "available" if it has at least one non-null value
                if non_null_count > 0:
                    available_columns += 1

                column_details[col] = {
                    "availability_percent": availability,
                    "non_null_count": int(non_null_count),
                    "total_rows": total_rows,
                }

            total_columns = len(self.resource_df.columns)
            availability_percent = (available_columns / total_columns * 100) if total_columns > 0 else 0

            return {
                "total_columns": total_columns,
                "available_columns": available_columns,
                "availability_percent": availability_percent,
                "column_details": column_details,
            }

        except Exception as e:
            print(f"[ERROR] Error calculating column availability: {e!s}")
            return {
                "total_columns": 0,
                "available_columns": 0,
                "availability_percent": 0,
                "column_details": {},
                "error": str(e),
            }

    def get_resource_columns(self) -> list[str]:
        """Get list of all available columns in the resource data."""
        if self.resource_df.empty:
            return []

        # Get all column names from the DataFrame
        columns = list(self.resource_df.columns)

        print("\n[DEBUG] Available columns in resource_df:")
        for col in sorted(columns):
            if col in self.column_stats:
                stats = self.column_stats[col]
                print(f"[DEBUG]   {col}: {stats['completeness']:.1f}% complete")
            else:
                print(f"[DEBUG]   {col}: no stats available")

        return sorted(columns)

    def query_resource_time_range(self, start: datetime, end: datetime, columns: list[str] | None = None) -> list[ResourceLogEntry]:
        """Query resource data within a time range, optionally filtering columns."""
        if self.resource_df.empty:
            return []

        try:
            # Create a mask for the time range using loc to avoid ambiguity
            filtered_df = self.resource_df.loc[(self.resource_df.index.to_series() >= start) & (self.resource_df.index.to_series() <= end)]

            # Debug: Show first few records of filtered data
            print("\n[DEBUG] First 3 records of filtered data:")
            for idx, row in filtered_df.head(3).iterrows():
                print(f"\n[DEBUG] Record at {idx}:")
                block_columns = [col for col in row.index if col.startswith("Block[")]
                if block_columns:
                    print("[DEBUG] Block data:")
                    for col in block_columns:
                        if isinstance(row[col], (list, np.ndarray)):
                            print(f"[DEBUG]   {col}: {row[col].tolist() if isinstance(row[col], np.ndarray) else row[col]}")
                        elif not pd.isna(row[col]):
                            print(f"[DEBUG]   {col}: {row[col]}")

            # Process requested columns or all columns if none specified
            process_columns = columns if columns else self.get_resource_columns()

            # Debug: Show requested columns
            print(f"\n[DEBUG] Processing columns: {process_columns}")

            # Convert to ResourceLogEntry objects
            result = []
            for timestamp, row in filtered_df.iterrows():
                # Convert row data to a dictionary, handling nested structures
                row_dict = {}
                block_data = {}

                for col, val in row.items():
                    # Handle arrays and lists
                    if isinstance(val, (list, np.ndarray)):
                        if any(not pd.isna(x) for x in val):
                            if col.startswith("Block["):
                                idx, field = re.match(r"Block\[(\d+)\]\.(.+)", col).groups()
                                idx = int(idx)
                                if idx not in block_data:
                                    block_data[idx] = {}
                                block_data[idx][field] = val.tolist() if isinstance(val, np.ndarray) else val
                            else:
                                row_dict[col] = val.tolist() if isinstance(val, np.ndarray) else val
                    # Handle scalar values
                    elif not pd.isna(val):
                        if col.startswith("Block["):
                            idx, field = re.match(r"Block\[(\d+)\]\.(.+)", col).groups()
                            idx = int(idx)
                            if idx not in block_data:
                                block_data[idx] = {}
                            block_data[idx][field] = val
                        else:
                            row_dict[col] = self._convert_value(val)

                # Add Block data if present
                if block_data:
                    flattened_block = {}
                    for idx, block in block_data.items():
                        if "Name" in block:
                            name = block["Name"]
                            flattened_block[f"{name}_read"] = block.get("ReadBytesPerSecond")
                            flattened_block[f"{name}_write"] = block.get("WriteBytesPerSecond")
                            flattened_block[f"{name}_saturation"] = block.get("Saturation")
                    row_dict["Block"] = flattened_block

                # Create ResourceLogEntry
                result.append(ResourceLogEntry(timestamp=timestamp, data=row_dict))

            # Debug: Show first few records of final result
            print("\n[DEBUG] First 3 records of final result:")
            for i, entry in enumerate(result[:3]):
                print(f"\n[DEBUG] Result entry {i + 1}:")
                if "Block" in entry.data:
                    print(f"[DEBUG] Block data: {entry.data['Block']}")

            return result

        except Exception as e:
            print(f"[ERROR] Error in query_resource_time_range: {e!s}")
            print(f"[ERROR] Exception type: {type(e)}")
            import traceback

            print(f"[ERROR] Traceback: {traceback.format_exc()}")
            return []

    def _convert_value(self, val: Any) -> Any:
        """Helper method to convert a single value to the appropriate Python type."""
        if isinstance(val, (list, np.ndarray)):
            # Convert array/list values
            converted = [self._convert_value(x) for x in val]
            # Filter out None values
            non_null = [x for x in converted if x is not None]
            return non_null if non_null else None
        if pd.isna(val):
            return None
        if isinstance(val, (np.integer, np.floating)):
            return val.item()  # Convert numpy number to native Python number
        if isinstance(val, dict):
            # Check if this is a Block entry
            if all(key.startswith("Block[") for key in val.keys()):
                # Group block data by index
                block_data = {}
                for key, value in val.items():
                    match = re.match(r"Block\[(\d+)\]\.(.+)", key)
                    if match:
                        idx, field = match.groups()
                        idx = int(idx)
                        if idx not in block_data:
                            block_data[idx] = {}
                        block_data[idx][field] = value

                # Convert to flattened format
                result = {}
                for idx, block in block_data.items():
                    if "Name" in block:
                        name = block["Name"]
                        result[f"{name}_read"] = block.get("ReadBytesPerSecond")
                        result[f"{name}_write"] = block.get("WriteBytesPerSecond")
                        result[f"{name}_saturation"] = block.get("Saturation")
                return result
            return {k: self._convert_value(v) for k, v in val.items()}  # Convert dict values
        return val

    def get_resource_column(
        self,
        name: str,
        start: datetime | None = None,
        end: datetime | None = None,
        aggregate: str | None = None,
    ) -> list[dict]:
        """Get data for a specific column with optional time range and aggregation.

        Args:
            name: Column name (can be nested like CPU[0].usage)
            start: Optional start timestamp for filtering
            end: Optional end timestamp for filtering
            aggregate: Optional aggregation function (mean, sum, min, max)
        """
        if self.resource_df.empty:
            return []

        # Apply time range filter if provided
        df = self.resource_df
        if start is not None:
            df = df[df.index >= start]
        if end is not None:
            df = df[df.index <= end]

        # Handle nested columns
        match = re.match(r"([A-Za-z0-9_]+)\[(\d+)\]\.(.+)", name)
        if match:
            prefix, idx, subkey = match.groups()
            idx = int(idx)
            if prefix in df.columns:
                # Extract the nested value for each row
                values = df[prefix].apply(lambda x: x[idx][subkey] if x and len(x) > idx and subkey in x[idx] else None)
            else:
                return []
        else:
            if name not in df.columns:
                return []
            values = df[name]

        # Apply aggregation if requested
        if aggregate:
            # Convert to numeric, replacing non-numeric values with NaN
            numeric_values = pd.to_numeric(values, errors="coerce")

            if aggregate == "mean":
                agg_value = float(numeric_values.mean())
            elif aggregate == "sum":
                agg_value = float(numeric_values.sum())
            elif aggregate == "min":
                agg_value = float(numeric_values.min())
            elif aggregate == "max":
                agg_value = float(numeric_values.max())
            else:
                raise ValueError(f"Unsupported aggregation function: {aggregate}")

            return [{"timestamp": df.index[0], "value": agg_value}]

        # Return timestamp-value pairs with converted numpy types
        result = []
        for timestamp, value in zip(df.index, values, strict=False):
            if pd.isna(value):
                converted_value = None
            elif isinstance(value, (np.integer, np.floating)):
                converted_value = value.item()  # Convert numpy number to native Python number
            elif isinstance(value, np.ndarray):
                converted_value = value.tolist()  # Convert numpy array to list
            else:
                converted_value = value

            result.append({"timestamp": timestamp, "value": converted_value})

        return result

    def query_minknow(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        log_event: str | None = None,
        process_name: str | None = None,
        log_level: str | None = None,
        library: str | None = None,
        position: str | None = None,
    ) -> list[StructuredLogEntry]:
        """Query Minknow data with filters using DataFrame operations."""
        if self.minknow_df.empty:
            return []

        df = self.minknow_df.copy()

        # Apply filters
        if start is not None:
            df = df[df.index >= start]
        if end is not None:
            df = df[df.index <= end]
        if log_event is not None:
            df = df[df["log_event"] == log_event]
        if process_name is not None:
            df = df[df["process_name"] == process_name]
        if log_level is not None:
            df = df[df["log_level"] == log_level]
        if library is not None:
            df = df[df["library"] == library]
        if position is not None:
            df = df[df["folder_name"] == position]

        # Convert back to MinknowLogEntry objects
        return [
            StructuredLogEntry(
                timestamp=timestamp,
                log_level=row["log_level"],
                log_event=row["log_event"],
                library=row["library"],
                file_name=row["file_name"],
                folder_name=row["folder_name"],
                process_name=row["process_name"],
                keys=row["keys"],
            )
            for timestamp, row in df.iterrows()
        ]

    def aggregate_logs_by_time(
        self,
        start_time: datetime,
        end_time: datetime,
        bucket_size_seconds: int = 3600,
        positions: list[str] | None = None,
    ) -> dict:
        """Aggregate log data by time buckets using DataFrame operations.
        Returns counts of log entries by level for each time bucket and position.
        """
        if self.minknow_df.empty:
            return {
                "data": {"info": [], "warning": [], "error": []},
                "maxCounts": {"info": 0, "warning": 0, "error": 0},
            }

        # Filter by time range and positions
        df = self.minknow_df.copy()
        df = df[(df.index >= start_time) & (df.index <= end_time)]
        if positions:
            df = df[df["folder_name"].isin(positions)]

        if df.empty:
            return {
                "data": {"info": [], "warning": [], "error": []},
                "maxCounts": {"info": 0, "warning": 0, "error": 0},
            }

        # Create time buckets
        df["bucket"] = pd.to_datetime((df.index.astype(int) // (bucket_size_seconds * 1e9)) * bucket_size_seconds * 1e9)

        # Group by bucket, position, and log level
        grouped = df.groupby(["bucket", "folder_name", "log_level"]).size().reset_index(name="value")

        # Prepare data by log level
        data_by_level = {"info": [], "warning": [], "error": []}
        max_counts = {"info": 0, "warning": 0, "error": 0}

        for _, row in grouped.iterrows():
            level_key = str(row["log_level"]).lower()
            if level_key in data_by_level:
                entry = {
                    "timestamp": int(row["bucket"].timestamp() * 1000),
                    "position": str(row["folder_name"]),
                    "value": int(row["value"]),
                }
                data_by_level[level_key].append(entry)
                max_counts[level_key] = max(max_counts[level_key], entry["value"])

        return {"data": data_by_level, "maxCounts": max_counts}

    def get_log_summary(self) -> dict:
        """Get a summary of the log data including counts by position and level using DataFrame operations."""
        if self.minknow_df.empty:
            return {
                "total_entries": 0,
                "total_resource_entries": (len(self.resource_df) if not self.resource_df.empty else 0),
                "by_position": {},
                "by_level": {"INFO": 0, "WARNING": 0, "ERROR": 0},
            }

        # Convert numpy types to native Python types
        folder_counts = {str(k): int(v) for k, v in self.minknow_df["folder_name"].value_counts().items()}
        level_counts = {str(k): int(v) for k, v in self.minknow_df["log_level"].value_counts().items()}

        return {
            "total_entries": len(self.minknow_df),
            "total_resource_entries": len(self.resource_df) if not self.resource_df.empty else 0,
            "by_position": folder_counts,
            "by_level": level_counts,
        }

    def get_error_distribution(self, start_time: datetime | None = None, end_time: datetime | None = None) -> dict:
        """Analyze the distribution of errors across positions and time using DataFrame operations."""
        if self.minknow_df.empty:
            return {"total_errors": 0, "by_position": {}, "by_type": {}}

        # Filter errors
        df = self.minknow_df[self.minknow_df["log_level"] == "ERROR"]

        # Apply time filters if provided
        if start_time is not None:
            df = df[df.index >= start_time]
        if end_time is not None:
            df = df[df.index <= end_time]

        if df.empty:
            return {"total_errors": 0, "by_position": {}, "by_type": {}}

        # Analyze by position and convert numpy types to native Python types
        error_by_position = {}
        for folder_name, group in df.groupby("folder_name"):
            error_by_position[str(folder_name)] = {
                "count": len(group),
                "events": [str(event) for event in group["log_event"].unique().tolist()],
            }

        # Analyze by error type and convert numpy types to native Python types
        error_types = {}
        for log_event, group in df.groupby("log_event"):
            error_types[str(log_event)] = {
                "count": len(group),
                "positions": [str(pos) for pos in group["folder_name"].unique().tolist()],
            }

        return {"total_errors": len(df), "by_position": error_by_position, "by_type": error_types}

    def get_column_statistics(self) -> dict[str, dict[str, Any]]:
        """Get statistics about each column in the resource data."""
        if self.resource_df.empty:
            return {}

        stats = {}
        total_rows = len(self.resource_df)

        # Calculate statistics for each column
        for column in self.resource_df.columns:
            if column == "timestamp":
                continue

            non_null_count = self.resource_df[column].count()
            completeness = (non_null_count / total_rows) * 100

            # Get first and last timestamps where this column has data
            non_null_data = self.resource_df[self.resource_df[column].notnull()]
            first_seen = non_null_data.index.min().isoformat() if not non_null_data.empty else None
            last_seen = non_null_data.index.max().isoformat() if not non_null_data.empty else None

            stats[column] = {
                "completeness": completeness,
                "first_seen": first_seen,
                "last_seen": last_seen,
                "total_entries": total_rows,
            }

            print(f"[DEBUG] Column stats for {column}:")
            print(f"[DEBUG]   Completeness: {completeness:.1f}%")
            print(f"[DEBUG]   First seen: {first_seen}")
            print(f"[DEBUG]   Last seen: {last_seen}")
            print(f"[DEBUG]   Total entries: {total_rows}")

        return stats
