from __future__ import annotations

import logging
import multiprocessing as mp
import os
import re
from multiprocessing import Pool

from ..backend.app.models.log_entry import (
    QuarantineEntry,
    ResourceLogEntry,
    StructuredLogEntry,
)
from ..backend.app.parsers.resource_parser import parse_resource_file
from ..backend.app.parsers.structured_log_parser import parse_minknow_file

logger = logging.getLogger(__name__)


class LogParser:
    """Main parser class that coordinates parsing of Resource-System and Structured logs.
    Uses parallel processing for improved performance while maintaining chronological order.
    """

    def __init__(self):
        self.resource_data: list[ResourceLogEntry] = []
        self.minknow_data: list[StructuredLogEntry] = []
        self.quarantine: list[QuarantineEntry] = []

    def parse_all(self, base_dir: str) -> None:
        """Parse all log files under base_dir in parallel."""
        print(f"[DEBUG] Starting parse_all with base_dir: {base_dir}")

        resource_dir = os.path.join(base_dir, "ont-resource-logger")
        minknow_dir = os.path.join(base_dir, "minknow")

        if not os.path.exists(resource_dir):
            print(f"[WARNING] Resource directory does not exist: {resource_dir}")
        if not os.path.exists(minknow_dir):
            print(f"[WARNING] Minknow directory does not exist: {minknow_dir}")

        self._parse_resource_logs(resource_dir)
        self._parse_minknow_logs(minknow_dir)

        print(f"[DEBUG] Parsed {len(self.resource_data)} Resource-System rows")
        print(f"[DEBUG] Parsed {len(self.minknow_data)} Minknow entries")

    def _parse_resource_logs(self, resource_dir: str) -> None:
        """Parse Resource-System logs in parallel."""
        if not os.path.isdir(resource_dir):
            return

        # Collect all resource log files
        resource_files = []
        for root, _, files in os.walk(resource_dir):
            for fname in files:
                if fname.startswith("resource-system") and fname.endswith(".log"):
                    resource_files.append(os.path.join(root, fname))

        if not resource_files:
            return

        # Use multiprocessing to parse files in parallel
        num_processes = min(mp.cpu_count(), len(resource_files))
        print(f"[DEBUG] Processing {len(resource_files)} resource files using {num_processes} processes")

        with Pool(processes=num_processes) as pool:
            results = pool.map(parse_resource_file, resource_files)

        # First pass: collect all possible columns across all files
        all_columns = set()
        for data, _ in results:
            for entry in data:
                all_columns.update(entry.data.keys())

        print(f"\n[DEBUG] Found {len(all_columns)} unique columns across all resource files:")
        for col in sorted(all_columns):
            print(f"[DEBUG]   - {col}")

        # Second pass: ensure all entries have all columns (with None for missing values)
        # and track column statistics
        all_data = []
        all_quarantine = []
        column_stats = {col: {"non_null_count": 0, "first_seen": None, "last_seen": None} for col in all_columns}

        for data, quarantine in results:
            # Normalize each entry to have all columns
            for entry in data:
                normalized_data = {col: entry.data.get(col, None) for col in all_columns}
                entry.data = normalized_data

                # Update column statistics
                for col, value in normalized_data.items():
                    if value is not None:
                        stats = column_stats[col]
                        stats["non_null_count"] += 1
                        if stats["first_seen"] is None or entry.timestamp < stats["first_seen"]:
                            stats["first_seen"] = entry.timestamp
                        if stats["last_seen"] is None or entry.timestamp > stats["last_seen"]:
                            stats["last_seen"] = entry.timestamp

                all_data.append(entry)
            all_quarantine.extend(quarantine)

        # Sort by timestamp before storing
        all_data.sort(key=lambda x: x.timestamp)
        self.resource_data.extend(all_data)
        self.quarantine.extend(all_quarantine)

        # Calculate and print statistics about data completeness
        print("\n[DEBUG] Column statistics:")
        total_entries = len(all_data)
        if total_entries > 0:
            for col in sorted(all_columns):
                stats = column_stats[col]
                non_null_count = stats["non_null_count"]
                percentage = (non_null_count / total_entries) * 100
                first_seen = stats["first_seen"].strftime("%Y-%m-%d %H:%M:%S") if stats["first_seen"] else "never"
                last_seen = stats["last_seen"].strftime("%Y-%m-%d %H:%M:%S") if stats["last_seen"] else "never"
                print(f"[DEBUG]   - {col}:")
                print(f"[DEBUG]     * {percentage:.1f}% complete ({non_null_count}/{total_entries} entries)")
                print(f"[DEBUG]     * First seen: {first_seen}")
                print(f"[DEBUG]     * Last seen: {last_seen}")

        # Store column statistics for later use
        self.column_stats = {
            col: {
                "completeness": ((column_stats[col]["non_null_count"] / total_entries * 100) if total_entries > 0 else 0),
                "first_seen": (column_stats[col]["first_seen"].isoformat() if column_stats[col]["first_seen"] else None),
                "last_seen": (column_stats[col]["last_seen"].isoformat() if column_stats[col]["last_seen"] else None),
                "non_null_count": column_stats[col]["non_null_count"],
                "total_entries": total_entries,
            }
            for col in all_columns
        }

    def _parse_minknow_logs(self, minknow_dir: str) -> None:
        """Parse Application logs in parallel."""
        if not os.path.isdir(minknow_dir):
            return

        # Collect all Minknow log files with their metadata
        minknow_files = []
        for root, _, files in os.walk(minknow_dir):
            subfolder = os.path.relpath(root, minknow_dir)
            if subfolder == ".":
                subfolder = ""

            for fname in files:
                if fname.startswith("bream-") and fname.endswith(".txt"):
                    continue

                match = re.match(r"(.*)_log-(\d+)\.txt", fname)
                if match:
                    process_name = match.group(1)
                    full_path = os.path.join(root, fname)
                    minknow_files.append((full_path, process_name, subfolder))

        if not minknow_files:
            return

        # Use multiprocessing to parse files in parallel
        num_processes = min(mp.cpu_count(), len(minknow_files))
        print(f"[DEBUG] Processing {len(minknow_files)} Application files using {num_processes} processes")

        with Pool(processes=num_processes) as pool:
            results = pool.starmap(parse_minknow_file, minknow_files)

        # Combine results while maintaining chronological order
        all_data = []
        all_quarantine = []
        for data, quarantine in results:
            all_data.extend(data)
            all_quarantine.extend(quarantine)

        # Sort by timestamp before storing
        all_data.sort(key=lambda x: x.timestamp)
        self.minknow_data.extend(all_data)
        self.quarantine.extend(all_quarantine)

    def get_resource_data(self) -> list[ResourceLogEntry]:
        """Get the parsed resource data."""
        return self.resource_data

    def get_minknow_data(self) -> list[StructuredLogEntry]:
        """Get the parsed Minknow data."""
        return self.minknow_data

    def get_quarantine_data(self) -> list[QuarantineEntry]:
        """Get the quarantined entries that couldn't be parsed."""
        return self.quarantine
