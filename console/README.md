# 🖥️ Log Visualizer Console CLI

A **Typer‑based command‑line interface** for power users who prefer the shell. Use it to query the FastAPI backend, visualise metrics in‑terminal, or run one‑off anomaly scans — all powered by the shared `log_parser` engine.

> ℹ️ **Heads‑up:** The CLI is optional. End‑users of Tern can ignore this and just use the GUI.

---

## 🚀 Installation

```bash
python -m venv .venv && source .venv/bin/activate
pip install -e .[cli] -e ../log_parser   # within repo root
```

*Python ≥ 3.10 required.*

---

## ✨ Key Features

### 🔍 Resource Monitoring

* Real‑time CPU, memory, disk, GPU, network & swap plots (Plotext)
* Configurable live update interval & window length
* Position‑based filtering for multi‑device deployments

### ⚡ Anomaly Detection

* **Log‑level** anomaly detection (ERROR / WARNING rates)
* **Time‑series** algorithms: *z‑score*, *IQR*, *control‑chart*, *persist*, *level‑shift*
* Per‑column thresholds, rolling window sizing, full timestamped reports

### 📊 Data Analysis

* Quick statistical summaries for any metric column
* Flexible time‑range & position filters
* CSV export forthcoming (TBD)

---

## ▶️ Usage Cookbook

### 1 — Resource Plots

```bash
# CPU load for the last hour (default)
python -m console.app.main plot

# CPU + Memory for the past 30 minutes
python -m console.app.main plot --minutes 30 --plot-types cpu memory

# GPU metrics for a specific position
python -m console.app.main plot --position "P01" --plot-types gpu
```

*Plot types:* `cpu`, `memory`, `disk`, `gpu`, `network`, `swap`

### 2 — Anomaly Detection

#### Log‑Level

```bash
# Default thresholds (ERROR > 1%, WARNING > 10%)
python -m console.app.main anomalies

# Custom thresholds & time range
python -m console.app.main anomalies \
  --start "2025-06-14T00:00:00" --end "2025-06-14T12:00:00" \
  --error-threshold 0.05 --warning-threshold 0.40
```

#### Time‑Series

```bash
# Z‑score on cpu_load column (|z| > 3)
python -m console.app.main anomalies --method zscore \
  --column cpu_load --threshold 3.0

# IQR on memory_usage column (multiplier 2.5)
python -m console.app.main anomalies --method iqr \
  --column memory_usage --threshold 2.5
```

**Available anomaly detection methods**

* `log_level` — Detects spikes in ERROR / WARNING rates (default)

  * Flags: `--error-threshold`, `--warning-threshold`
  * *No* `--column` needed
* `zscore` — Standard‑deviation based outlier detection

  * Requires `--column`
  * Flag: `--threshold` (e.g., 3.0 ⇒ |z| > 3)
* `iqr` — Inter‑quartile range outlier detection

  * Requires `--column`
  * Flag: `--threshold` multiplier (e.g., 2.5 × IQR)
* `control_chart` — Statistical process‑control limits (X‑bar chart)

  * Requires `--column`
  * Flag: `--threshold` ⇒ number of σ
* `persist` — Detects values that persist above/below a baseline

  * Requires `--column`
  * Uses `--window-size` for baseline window
* `level_shift` — Detects sudden mean shifts in the series

  * Requires `--column`
  * Uses `--window-size` & `--threshold` to tune sensitivity

### 3 — Data Summaries

```bash
# One‑liner summary for last 24 h
python -m console.app.main data --summary

# Column subset & position filter
python -m console.app.main data \
  --columns cpu_load,memory_usage --position P01 \
  --start "2025-06-13T12:00:00" --end "2025-06-14T12:00:00"
```

```bash
# One‑liner summary for last 24 h
python -m console.app.main data --summary

# Column subset & position filter
python -m console.app.main data \
  --columns cpu_load,memory_usage --position P01 \
  --start "2025-06-13T12:00:00" --end "2025-06-14T12:00:00"
```

---

## 🔧 Common Flags & Options

| Flag                                       | Applies to      | Description                             |
| ------------------------------------------ | --------------- | --------------------------------------- |
| `--start`, `--end`                         | all cmds        | ISO 8601 range (overrides `--minutes`)  |
| `--minutes`                                | plot, anomalies | Convenience trailing window             |
| `--position`                               | all             | Device position name                    |
| `--plot-types`                             | plot            | Metrics to draw (space/comma separated) |
| `--method`                                 | anomalies       | Algorithm to use (see list above)       |
| `--column`                                 | anomalies       | Target column for time‑series methods   |
| `--threshold`                              | anomalies       | Threshold multiplier (method‑specific)  |
| `--error-threshold`, `--warning-threshold` | anomalies       | %-based log‑level limits                |
| `--window-size`                            | anomalies       | Rolling window (e.g. `5min`)            |
| `--host`, `--port`                         | all             | FastAPI backend address                 |

---

## 🛠 Development & Testing

```bash
# Run console tests (placeholder target)
hatch run test-tern

# Lint only the console package
hatch run lint -- console/
```

---

## 🐞 Troubleshooting

| Symptom                        | Fix                                                                      |
| ------------------------------ | ------------------------------------------------------------------------ |
| "Cannot connect to API"        | Ensure backend is running; check `--host` & `--port`.                    |
| Unicode box‑drawing issues     | Use a font with Unicode support or pass `--no-unicode` (if implemented). |
| Import errors for `log_parser` | Confirm editable install: `pip install -e ../log_parser`.                |

---

## Requirements

* Python 3.10+
* Running FastAPI backend (defaults to `http://127.0.0.1:8000`)
* Packages via `pip install -e .[cli]`

---

## 📜 License

MIT – see root `LICENSE`.
