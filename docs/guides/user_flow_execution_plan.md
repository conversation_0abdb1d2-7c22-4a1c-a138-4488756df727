# Tern User Flow Redesign - Execution Plan

## Overview

This document outlines the detailed execution plan for implementing the user flow redesign with **two parallel developers**:
- **Backend Developer** (Agent A): Core parsing, progress tracking, container modifications
- **Frontend Developer** (Agent B): UI components, callbacks, layout changes

## Developer Assignment Strategy

### Backend Developer (Agent A) - Core & Data Layer
- Container and parsing engine modifications
- Progress tracking system implementation
- File dialog service integration
- Store data structure definitions
- Error handling and state management backend

### Frontend Developer (Agent B) - UI & Interaction Layer
- Component creation and styling
- Callback implementation (using interfaces)
- Layout restructuring and state transitions
- Error dialogs and user feedback components
- Progress display and user interaction elements

## Interface Contracts

### 1. Progress Tracking Interface

**File**: `tern/core/interfaces.py` (to be created)

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class ProgressUpdate:
    """Standard progress update data structure"""
    stage: str
    progress: int  # 0-100
    current_file: str = ""
    files_processed: int = 0
    total_files: int = 0
    elapsed_time: float = 0

@dataclass
class ParsingStatus:
    """Complete parsing status data structure"""
    status: str  # 'idle' | 'parsing' | 'complete' | 'error'
    stage: str = ""
    progress: int = 0
    current_file: str = ""
    files_processed: int = 0
    total_files: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[Dict[str, Any]] = None

class IProgressTracker(ABC):
    """Interface for progress tracking functionality"""

    @abstractmethod
    def update_progress(self, update: ProgressUpdate) -> None:
        """Update parsing progress with thread-safe operations"""
        pass

    @abstractmethod
    def get_status(self) -> ParsingStatus:
        """Get current parsing status"""
        pass

    @abstractmethod
    def reset_status(self) -> None:
        """Reset to idle state"""
        pass

    @abstractmethod
    def set_error(self, error_msg: str, stage: str = "") -> None:
        """Set error state with details"""
        pass
```

### 2. File Dialog Service Interface

```python
from typing import Optional, Union, List

class IFileDialogService(ABC):
    """Interface for file dialog operations"""

    @abstractmethod
    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        """Open directory selection dialog"""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Check if file dialog is available in current environment"""
        pass

    @abstractmethod
    def validate_path(self, path: str) -> Dict[str, Any]:
        """Validate selected path and return validation result"""
        pass
```

### 3. Parsing Controller Interface

```python
class IParsingController(ABC):
    """Interface for parsing operations"""

    @abstractmethod
    def start_parsing(self, log_folder: str) -> bool:
        """Start parsing process, returns success status"""
        pass

    @abstractmethod
    def cancel_parsing(self) -> bool:
        """Cancel current parsing operation"""
        pass

    @abstractmethod
    def is_parsing(self) -> bool:
        """Check if parsing is currently in progress"""
        pass
```

### 4. Store Schema Contracts

**File**: `tern/types/store_types.py` (to be created)

```python
from typing import TypedDict, Optional, Literal

class ParsingState(TypedDict):
    status: Literal['idle', 'parsing', 'complete', 'error']
    stage: str
    progress: int  # 0-100
    current_file: str
    files_processed: int
    total_files: int
    start_time: Optional[str]
    end_time: Optional[str]
    error: Optional[dict]

class SetupState(TypedDict):
    log_path: str
    is_valid: bool
    environment_path: str
    dialog_available: bool

class UIState(TypedDict):
    navbar_open: bool
    filters_visible: bool
    setup_minimized: bool

class AppStore(TypedDict):
    parsing: ParsingState
    setup: SetupState
    ui: UIState
```

## Phase 1: Core UX Restructure

### Backend Tasks (Agent A)

#### Task A1.1: Interface Setup & Container Modifications
**Duration**: 1-2 days
**Dependencies**: None

**Deliverables:**
- Create `tern/core/interfaces.py` with all interface definitions
- Create `tern/types/store_types.py` with TypedDict schemas
- Modify `AnalyzerContainer` to implement `IProgressTracker`
- Add mock `IFileDialogService` implementation for testing

**Implementation Notes:**
```python
# In tern/core/container.py
class AnalyzerContainer(IProgressTracker):
    def __init__(self, config=None):
        # ... existing code ...
        self._progress_tracker = ProgressTracker()

    def update_progress(self, update: ProgressUpdate) -> None:
        # Implementation using existing _parsing_lock
        pass

    def start_parsing_async(self, log_folder: str) -> bool:
        # Modified version of existing parse_logs with progress calls
        pass
```

**Testing Strategy:**
- Unit tests for interface compliance
- Mock progress updates to verify thread safety
- Status state transitions testing

#### Task A1.2: Remove Background Parsing Thread
**Duration**: 0.5 days
**Dependencies**: A1.1

**Deliverables:**
- Remove automatic parsing from `app.py:run()` method
- Modify container initialization to not trigger parsing
- Add manual parsing trigger method

**Integration Point**: Backend provides `start_parsing_async()` method for frontend to call

#### Task A1.3: File Dialog Service Implementation
**Duration**: 1 day
**Dependencies**: A1.1

**Deliverables:**
- Create `FileDialogService` class implementing `IFileDialogService`
- PyWebView integration for native dialogs
- Web fallback for `--no-webview` mode
- Path validation utilities

```python
# In tern/services/file_dialog.py
class FileDialogService(IFileDialogService):
    def __init__(self, webview_window=None):
        self.webview_window = webview_window

    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        # Implementation with webview.FOLDER_DIALOG
        pass
```

### Frontend Tasks (Agent B)

#### Task B1.1: Layout Structure & Navbar Changes
**Duration**: 1 day
**Dependencies**: None (can use mock data)

**Deliverables:**
- Modify `main_layout.py` to open navbar by default
- Create setup section components structure
- Add placeholder progress area (hidden initially)
- Update global filters to be conditionally visible

**Mock Data Usage:**
```python
# Use mock store data during development
MOCK_PARSING_STATE = {
    "status": "idle",
    "stage": "",
    "progress": 0,
    "current_file": "",
    "files_processed": 0,
    "total_files": 0
}
```

#### Task B1.2: Setup Section Components
**Duration**: 1-2 days
**Dependencies**: B1.1

**Deliverables:**
- Path input component with validation styling
- Browse button component (will call backend service)
- Load Logs button with loading states
- Basic form validation for path input

**Component Structure:**
```python
def create_setup_section():
    return dmc.Stack([
        dmc.TextInput(
            id="log-path-input",
            label="Log Folder Path",
            placeholder="Select or enter log folder path",
            style={"width": "100%"}
        ),
        dmc.Group([
            dmc.Button(
                "Browse",
                id="browse-button",
                variant="outline",
                leftIcon=DashIconify(icon="mdi:folder-open")
            ),
            dmc.Button(
                "Load Logs",
                id="load-logs-button",
                leftIcon=DashIconify(icon="mdi:rocket-launch")
            )
        ])
    ])
```

#### Task B1.3: Basic Callbacks (with Stubs)
**Duration**: 1 day
**Dependencies**: B1.2, interfaces from A1.1

**Deliverables:**
- Browse button callback (calls file dialog service)
- Load logs button callback (calls parsing controller)
- Path validation callback
- Basic error display callback

**Stub Implementation:**
```python
@app.callback(
    Output("log-path-input", "value"),
    Input("browse-button", "n_clicks"),
    prevent_initial_call=True
)
def handle_browse_click(n_clicks):
    if n_clicks:
        # Will call backend file dialog service
        # For now, return mock path
        return "/mock/log/path"
    return no_update
```

### Integration Checkpoint 1
**Timeline**: End of Phase 1 (3-4 days)

**Integration Tasks:**
1. **Backend Integration**: Connect real file dialog service to frontend callbacks
2. **Container Integration**: Connect load button to actual parsing trigger
3. **Testing**: Manual parsing trigger works, navbar opens, browse button functional

**Success Criteria:**
- User can browse for log folder using native dialog
- Load button triggers parsing (even if progress is not yet implemented)
- Navbar opens by default and shows setup section
- No automatic parsing on startup

## Phase 2: Progress & Feedback System

### Backend Tasks (Agent A)

#### Task A2.1: Progress Calculation Engine
**Duration**: 2 days
**Dependencies**: A1.1, A1.2

**Deliverables:**
- Stage-weighted progress calculation
- File discovery pre-scan functionality
- Progress update integration in parsing flow
- Real-time progress tracking

**Implementation:**
```python
class ProgressCalculator:
    STAGE_WEIGHTS = {
        "🎣 Casting nets for logs": 5,
        "🐟 Teaching analyzers to fish": 10,
        "🐠 Diving for MinKnow logs": 40,
        "🌊 Cross-referencing events": 35,
        "📊 Counting the catch": 10
    }

    def calculate_progress(self, stage: str, stage_progress: float = 100) -> int:
        # Implementation of weighted progress calculation
        pass
```

#### Task A2.2: Enhanced Container Status Updates
**Duration**: 1 day
**Dependencies**: A2.1

**Deliverables:**
- Modified `parse_logs()` with progress calls
- File tracking during parsing operations
- Error state management
- Parsing cancellation support

#### Task A2.3: Error Handling Backend
**Duration**: 1 day
**Dependencies**: A2.1

**Deliverables:**
- Error categorization (critical vs. validation)
- Error message formatting
- Recovery action suggestions
- Error state integration with progress system

### Frontend Tasks (Agent B)

#### Task B2.1: Progress Display Components
**Duration**: 2 days
**Dependencies**: B1.3, interfaces from A2.1

**Deliverables:**
- Progress bar component with fish-themed messages
- Elapsed time display
- Current file indicator
- File count progress display

**Component Structure:**
```python
def create_progress_component(stage: str, progress: int, current_file: str,
                            files_processed: int, total_files: int, elapsed_time: float):
    return dmc.Stack([
        dmc.Text(stage, size="sm", weight=500),
        dmc.Progress(value=progress, size="lg", striped=True, animate=True),
        dmc.Group([
            dmc.Text(f"⏱️ Elapsed: {format_time(elapsed_time)}", size="xs", c="dimmed"),
            dmc.Text(f"📊 Files: {files_processed}/{total_files}", size="xs", c="dimmed")
        ], justify="space-between"),
        dmc.Text(f"📁 {current_file}", size="xs", c="dimmed", truncate=True) if current_file else None
    ])
```

#### Task B2.2: Error Dialog Components
**Duration**: 1-2 days
**Dependencies**: B2.1

**Deliverables:**
- Modal error dialogs for critical errors
- Inline error messages for validation
- Progress bar error states
- Recovery action buttons

```python
def create_error_modal(error_data: Dict[str, Any]):
    return dmc.Modal(
        title=dmc.Group([
            DashIconify(icon="mdi:alert-circle", color="red"),
            dmc.Text("Parsing Error", weight=500)
        ]),
        children=[
            dmc.Text(error_data.get("message", "An error occurred")),
            dmc.Space(h="md"),
            dmc.Group([
                dmc.Button("Retry", id="error-retry-btn", variant="outline"),
                dmc.Button("Change Path", id="error-change-path-btn", variant="outline"),
                dmc.Button("Cancel", id="error-cancel-btn")
            ], justify="end")
        ],
        opened=True,
        id="error-modal"
    )
```

#### Task B2.3: Progress Callbacks & State Management
**Duration**: 1 day
**Dependencies**: B2.1, B2.2

**Deliverables:**
- Progress display callback using interval polling
- Error state callbacks
- Progress visibility toggling
- State synchronization with backend

### Integration Checkpoint 2
**Timeline**: End of Phase 2 (6-8 days total)

**Integration Tasks:**
1. **Progress System Integration**: Connect backend progress tracking to frontend display
2. **Error Handling Integration**: Connect backend error states to frontend dialogs
3. **Polling Integration**: Ensure interval callbacks get real progress data

**Success Criteria:**
- Progress bar shows real-time parsing progress with fish-themed messages
- File count and current file display accurately
- Error dialogs appear for parsing failures
- Progress accuracy meets ~70-90% target

## Phase 3: State Management & UI Polish ⏸️ **PARKED**

### Implementation Status: Deferred

**Decision Date**: January 2025
**Rationale**: Other development priorities take precedence over workflow optimizations

### Original Phase 3 Scope (Deferred)

#### Backend Tasks (Agent A) - **PARKED**
- **Task A3.1**: State persistence & optimization
- **Task A3.2**: Advanced error recovery

#### Frontend Tasks (Agent B) - **PARKED**
- **Task B3.1**: Filter visibility & state transitions
- **Task B3.2**: UI polish & responsiveness
- **Task B3.3**: Final integration & testing

### Impact Assessment

**What's Missing Without Phase 3:**
- State persistence across app restarts (minimal impact for PyWebView desktop app)
- Memory optimization for large log directories (will need to address in future phases)
- Advanced error recovery mechanisms (basic retry logic may be sufficient)
- UI state transitions and polish (cosmetic improvements)

**Dependencies for Future Phases:**
- **Phase 4**: Can proceed but may need to include basic memory optimization
- **Phase 5**: Will require implementing minimal state management for workflow presets

### Future Resumption Strategy

When workflow features become a development priority, consider implementing:

1. **Essential Memory Optimization**: Batch processing for large log directories
2. **Minimal State Management**: Save/load analysis configurations only
3. **Skip Complex Features**: Avoid over-engineering until proven necessary

**Estimated Effort When Resumed**: 0.5-1 day (reduced scope focusing on essentials)

---

### Integration Checkpoint 2+ (Revised)
**Timeline**: End of Phase 2 (6-8 days total) - **CURRENT COMPLETION TARGET**

**Success Criteria:**
- Progress bar shows real-time parsing progress with fish-themed messages
- File count and current file display accurately
- Error dialogs appear for parsing failures
- Progress accuracy meets ~70-90% target
- **System ready for production use without advanced workflow features**

## Phases 4-5: Advanced Features

### Phase 4: Advanced Parsing Features (2-3 days)
**Parallel Development:**
- **Backend (Agent A)**: Re-parsing, path validation, cancellation
- **Frontend (Agent B)**: Re-parsing UI, validation feedback, cancellation controls

### Phase 5: Workflow Foundation (2-3 days)
**Parallel Development:**
- **Backend (Agent A)**: Timestamp centering logic, workflow data structures
- **Frontend (Agent B)**: Workflow UI components, timestamp controls

## Git Branching Strategy

### Recommended Branch Structure

```
main
├── feature/user-flow-redesign (base feature branch)
    ├── feature/backend-core-restructure (Agent A)
    ├── feature/frontend-ui-components (Agent B)
    └── feature/integration-testing (joint work)
```

### Branch Workflow

**Initial Setup:**
1. Create base feature branch: `feature/user-flow-redesign` from `main`
2. Agent A creates: `feature/backend-core-restructure` from base
3. Agent B creates: `feature/frontend-ui-components` from base
4. Both agents work on their respective branches

**Daily Integration Process:**
1. **Morning (9 AM)**: Both agents pull latest changes from base branch
2. **Midday (1 PM)**: Quick sync - merge interface changes to base if needed
3. **Evening (5 PM)**: Merge completed tasks to base branch
4. **Before next day**: Both agents rebase from updated base branch

**Phase Completion Process:**
1. Both agents merge their phase work to base branch
2. Create integration branch for joint testing
3. Resolve conflicts and test integration points
4. Merge base branch to `main` after phase completion

### Why Feature Branches?

**Benefits:**
- **Parallel Development**: No blocking each other's commits
- **Clear Ownership**: Each agent owns their branch and files
- **Safe Integration**: Controlled merge points with testing
- **Rollback Safety**: Can revert individual agent changes
- **Conflict Isolation**: Merge conflicts handled at integration points

**Risks Mitigated:**
- **File Conflicts**: Different agents work on different files mostly
- **Integration Issues**: Regular sync points catch problems early
- **Code Quality**: Proper review process before merging
- **Deployment Safety**: Base branch always in deployable state

### File Ownership During Development

**Backend Agent (A) Primary Files:**
```
tern/core/
├── interfaces.py (new)
├── container.py (modifications)
└── analyzer.py (modifications)

tern/services/
└── file_dialog.py (new)

tern/types/
└── store_types.py (new)

tern/mocks/
└── backend_mocks.py (new)
```

**Frontend Agent (B) Primary Files:**
```
tern/layout/
└── main_layout.py (modifications)

tern/components/
├── setup_section.py (new)
├── progress_display.py (new)
└── error_dialogs.py (new)

tern/callbacks/
├── setup_callbacks.py (new)
├── progress_callbacks.py (new)
└── navbar_callbacks.py (modifications)

tern/mocks/
└── frontend_mocks.py (new)
```

**Shared/Interface Files (Require Joint Review):**
```
tern/core/interfaces.py
tern/types/store_types.py
tern/callbacks/__init__.py
```

### Integration Points & Merge Strategy

**Daily Mini-Integrations:**
```bash
# Agent A (evening)
git checkout feature/user-flow-redesign
git merge feature/backend-core-restructure
git push origin feature/user-flow-redesign

# Agent B (evening)
git checkout feature/user-flow-redesign
git pull origin feature/user-flow-redesign
git merge feature/frontend-ui-components
git push origin feature/user-flow-redesign

# Both agents (next morning)
git checkout feature/backend-core-restructure  # or frontend branch
git pull origin feature/user-flow-redesign
git rebase feature/user-flow-redesign
```

**Phase Integration Process:**
1. Both agents complete their phase tasks
2. Create `feature/phase-X-integration` branch
3. Joint testing and conflict resolution
4. Merge to base feature branch
5. Tag phase completion

### Alternative: Same Branch Approach

**If you prefer same branch development:**

**Pros:**
- Simpler git workflow
- Immediate integration visibility
- No merge conflicts at integration

**Cons:**
- **High conflict risk** when both agents modify same files
- **Blocking potential** if one agent breaks functionality
- **Harder rollback** if one agent's changes cause issues
- **Complex coordination** required for file modifications

**Same Branch Guidelines (if chosen):**
1. **Frequent small commits** (every 30 minutes)
2. **Always pull before push**
3. **Immediate conflict resolution**
4. **Clear commit message conventions**:
   - `[Backend]` prefix for Agent A commits
   - `[Frontend]` prefix for Agent B commits
   - `[Integration]` for joint work

### Recommendation: Feature Branches

**I strongly recommend the feature branch approach** because:

1. **Lower Risk**: Parallel development without blocking
2. **Better Testing**: Integration points force proper testing
3. **Cleaner History**: Clear development timeline
4. **Easier Debugging**: Can isolate issues to specific branches
5. **Professional Workflow**: Mirrors real-world development practices

The overhead of branch management is minimal compared to the benefits of safe parallel development.

## Development Guidelines

### Code Review & Integration Protocol

**Daily Integration Points:**
1. **Morning Sync**: Review interface changes and dependencies
2. **Mid-day Check**: Validate integration points and resolve conflicts
3. **End-of-day Integration**: Merge compatible changes and test

**Code Review Process:**
1. All interface changes require review from both developers
2. Backend changes should include integration tests
3. Frontend changes should include component tests
4. Integration points require joint testing

### Testing Strategy

**Backend Testing (Agent A):**
- Unit tests for all interface implementations
- Integration tests for parsing flow
- Performance tests with real data
- Mock frontend callbacks for isolated testing

**Frontend Testing (Agent B):**
- Component tests with mock data
- Callback tests with stubbed services
- Visual regression tests
- User interaction flow tests

**Joint Testing:**
- End-to-end user flow testing
- Integration point validation
- Error scenario testing
- Performance validation

### Conflict Resolution

**Interface Conflicts:**
1. Discuss and agree on interface changes before implementation
2. Use version control branching for major interface changes
3. Joint review sessions for breaking changes

**Code Conflicts:**
1. Daily merging to minimize conflicts
2. Clear file ownership during development
3. Pair programming for complex integration points

### Mock Implementation Strategy

**Backend Mocks for Frontend:**
```python
# tern/mocks/backend_mocks.py
class MockProgressTracker(IProgressTracker):
    def update_progress(self, update: ProgressUpdate) -> None:
        # Simulate progress updates for frontend testing
        pass

class MockFileDialogService(IFileDialogService):
    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        return "/mock/selected/path"
```

**Frontend Mocks for Backend:**
```python
# Test callbacks without real UI components
def mock_progress_callback(progress_data):
    print(f"Progress: {progress_data}")
    return True
```

## Risk Mitigation

**Technical Risks:**
1. **Interface Misalignment**: Daily syncs and clear documentation
2. **Integration Complexity**: Phased integration with testing
3. **Performance Issues**: Early testing with real datasets

**Process Risks:**
1. **Development Conflicts**: Clear file ownership and communication
2. **Timeline Delays**: Buffer time in estimates and parallel fallbacks
3. **Scope Creep**: Strict adherence to phase deliverables

**Mitigation Strategies:**
1. **Daily standups** with both developers
2. **Interface-first development** to minimize coupling
3. **Continuous integration** testing
4. **Rollback procedures** for failed integrations

## Success Metrics

**Phase 1 Success:**
- Manual parsing trigger functional
- File dialog works across platforms
- Navbar restructuring complete

**Phase 2 Success:**
- Progress tracking 70%+ accuracy
- Error handling works for all scenarios
- User feedback is clear and helpful

~~**Phase 3 Success:** (PARKED)~~
~~- Complete user flow works end-to-end~~
~~- UI states transition smoothly~~
~~- Performance meets acceptance criteria~~

**Current Implementation Target (Phase 1-2):**
- Zero automatic parsing on startup
- Rich user feedback during operations
- Native file dialog integration
- Improved development workflow support
- **System ready for production use without workflow optimizations**

**Future Phase 3+ Success (When Resumed):**
- Memory optimization for large log directories
- Workflow preset functionality
- Advanced error recovery mechanisms

---

*This execution plan enables parallel development while minimizing conflicts through clear interfaces and integration checkpoints.*
