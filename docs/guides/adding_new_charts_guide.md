# Adding New Charts: Developer Guide

This guide explains how to add new visualization charts to the Tern log visualization system based on the refactored pipeline architecture.

## Overview

The system uses a **pipeline-based architecture** where:
- Each chart type has its own dedicated pipeline class
- Data transformation logic is self-contained in static methods
- Caching and data sources are injected as dependencies
- All charts follow consistent patterns for data fetching and transformation

## Architecture Components

### Core Components
- **Pipeline Classes**: Handle data transformation (e.g., `MemoryDataPipeline`)
- **DataContext**: Provides cache and data sources to pipelines
- **VisualizationDataCache**: Handles intelligent caching of transformed data
- **DataSources**: Abstracts access to analyzers (cross, resource, action)

### Integrated Caching System
The `CachedTransformationPipeline` base class **automatically integrates** with `VisualizationDataCache`:
- Cache checks happen automatically before pipeline execution
- Cache storage happens automatically after successful execution
- All cache parameters (including custom kwargs) are handled transparently
- No manual cache management needed in static methods

### File Structure
```
tern/core/
├── cache.py                    # VisualizationDataCache
├── data_context.py            # DataContext for dependencies
├── performance_utils.py       # Performance tracking decorator
├── {chart_type}_pipeline.py   # Individual pipeline classes
└── pipeline/
    ├── base_pipeline.py       # Base pipeline infrastructure
    ├── data_sources.py        # Data source abstraction
    └── transformations.py     # Reusable transformations
```

## Step-by-Step Guide

### 1. Create Your Pipeline Class

Create a new file: `tern/core/{your_chart}_pipeline.py`

```python
"""
{Your chart} data pipeline for LogVisualisation.

This module contains the specialized pipeline for {your chart} data transformation.
"""

import logging
from datetime import datetime
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class YourChartDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for {your chart} data transformation."""

    def build_your_chart_pipeline(self, start_time: datetime, end_time: datetime):
        """Build the {your chart} data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Self for method chaining
        """
        # Define the columns you need from the data sources
        required_columns = ['Column1', 'Column2', 'Column3']

        return self \
            .add_step("fetch_data", self._fetch_your_data, required_columns, start_time, end_time) \
            .add_step("transform_data", self._transform_your_data) \
            .add_step("format_output", self._format_output)

    async def _fetch_your_data(self, data, columns, start_time, end_time):
        """Fetch your chart's data from data sources.

        Args:
            data: Input data (ignored for data source step)
            columns: List of column names to fetch
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with raw data from analyzers
        """
        logger.info(f"Fetching {self.__class__.__name__} data for columns: {columns}")

        # Use data sources to fetch data
        return await self.data_sources.resource_metrics(
            columns=columns,
            start_time=start_time,
            end_time=end_time
        )

    async def _transform_your_data(self, data):
        """Transform raw data into chart-ready format.

        Args:
            data: Raw data from fetch step

        Returns:
            Transformed data dictionary
        """
        # Implement your specific transformation logic here
        records = data.get("records", [])

        if not records:
            return {"timestamps": [], "your_data_fields": []}

        # Example transformation
        timestamps = []
        values = []

        for record in records:
            timestamps.append(record["timestamp"])
            # Process your specific data fields
            values.append(record.get("your_field", 0))

        return {
            "timestamps": timestamps,
            "your_data_fields": values
        }

    async def _format_output(self, data):
        """Final formatting step for chart consumption.

        Args:
            data: Transformed data

        Returns:
            Final formatted data ready for charts
        """
        # Any final formatting, unit conversions, etc.
        return data

    @staticmethod
    @_track_data_performance("get_your_chart_data")
    async def get_your_chart_data(
        cache: VisualizationDataCache,
        sources: DataSources,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        **kwargs  # Any additional parameters specific to your chart
    ) -> dict[str, Any]:
        """Get {your chart} data for visualization.

        Args:
            cache: Visualization data cache instance
            sources: Data sources instance
            start_time: Start timestamp for data range
            end_time: End timestamp for data range
            **kwargs: Additional chart-specific parameters

        Returns:
            Dictionary containing chart data:
            {
                "timestamps": List[str] - ISO format timestamps
                "your_data_fields": List[Any] - Your chart's data
            }
        """
        try:
            logger.info("GET_YOUR_CHART_DATA_CALLED: start=%s, end=%s", start_time, end_time)

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = YourChartDataPipeline(
                cache=cache,
                method_name="get_your_chart_data",
                start_time=start_time,
                end_time=end_time,
                **kwargs
            )

            pipeline.inject_sources(sources)
            result = await pipeline.build_your_chart_pipeline(start_time, end_time).execute()

            logger.info("YOUR_CHART_TRANSFORM_COMPLETE: Pipeline execution successful")
            return result

        except Exception:
            logger.exception("Error getting your chart data")
            # Return empty data structure on error
            return {"timestamps": [], "your_data_fields": []}
```

### 2. Add Chart Configuration

Add your chart configuration to `tern/components/charts/configs.py`:

```python
def get_chart_config(chart_type: str, cached_data: Optional[dict] = None) -> dict:
    """Get chart configuration for different chart types."""

    # Add your chart type
    if chart_type == "your_chart":
        return {
            "title": "Your Chart Title",
            "x_axis_title": "Time",
            "y_axis_title": "Your Units",
            "chart_mode": "lines",  # or "markers", "lines+markers"
            "color_palette": ["#FF6B6B", "#4ECDC4", "#45B7D1"],
            "height": 400
        }

    # ... existing chart configurations
```

### 3. Create Chart Factory Function

Add a factory function in `tern/components/charts/factories.py`:

```python
def create_your_chart(data: dict, config: dict) -> go.Figure:
    """Create your chart visualization.

    Args:
        data: Chart data from pipeline
        config: Chart configuration

    Returns:
        Plotly figure object
    """
    fig = go.Figure()

    # Extract data
    timestamps = data.get("timestamps", [])
    values = data.get("your_data_fields", [])

    if not timestamps or not values:
        return _create_empty_figure(config)

    # Add trace(s) to figure
    fig.add_trace(go.Scatter(
        x=timestamps,
        y=values,
        mode=config.get("chart_mode", "lines"),
        name="Your Data",
        line=dict(color=config["color_palette"][0])
    ))

    # Apply styling
    fig.update_layout(
        title=config["title"],
        xaxis_title=config["x_axis_title"],
        yaxis_title=config["y_axis_title"],
        height=config["height"]
    )

    return fig
```

### 4. Update Chart Callbacks

Add your chart to the callback system in `tern/components/charts/callbacks.py`:

```python
# Add import at top
from ...core.your_chart_pipeline import YourChartDataPipeline

# Add to validation function
def _validate_chart_data(cached_data: dict, data_key: str) -> tuple[dict, bool]:
    """Common validation for chart data extraction."""
    # Add your chart data key
    valid_keys = ["memory", "cpu", "disk", "io", "gpu", "your_chart"]
    # ... rest of validation logic

# Add factory mapping
CHART_FACTORIES = {
    "memory": create_memory_chart,
    "cpu": create_cpu_chart,
    "disk": create_disk_chart,
    "io": create_io_chart,
    "gpu": create_gpu_chart,
    "your_chart": create_your_chart,  # Add your factory
}
```

### 5. Add Data Fetching to Callbacks

Update `tern/callbacks/data.py` to fetch your chart data:

```python
# Add import at top
from ..core.your_chart_pipeline import YourChartDataPipeline

# Add to fetch_all_resource_data function
async def fetch_all_data():
    # ... existing tasks

    # Your chart data
    tasks.append(
        (
            "your_chart",
            YourChartDataPipeline.get_your_chart_data(
                cache=log_visualizer_app.data_context.transformation_cache,
                sources=log_visualizer_app.data_context.pipeline_sources,
                start_time=start_time,
                end_time=end_time
            ),
        )
    )
```

### 6. Add Chart to UI Layout

Add your chart component to the appropriate layout file:

```python
# In your layout file
from tern.components.charts.base import create_chart_component

your_chart_component = create_chart_component(
    chart_id="your-chart",
    chart_type="your_chart",
    title="Your Chart Title"
)
```

## Best Practices

### Performance
- **Use caching**: Always implement the cache check pattern in your static method
- **Add performance tracking**: Use the `@_track_data_performance` decorator
- **Batch data fetching**: Fetch all required data in a single operation when possible

### Error Handling
- **Return empty structures**: Always return valid but empty data structures on errors
- **Log errors**: Use logger.exception() for debugging
- **Graceful degradation**: Ensure UI doesn't break on data errors

### Data Transformation
- **Use existing transformations**: Leverage `BaseTransformations` for common operations
- **Keep pipelines focused**: Each pipeline should handle one chart type
- **Validate inputs**: Check for required data fields and handle missing data

### Testing
- **Create unit tests**: Test your pipeline class independently
- **Mock data sources**: Use pytest-mock to mock the DataSources
- **Test edge cases**: Empty data, malformed data, time range edge cases

## Example: Complete Memory Chart Implementation

The `MemoryDataPipeline` is a good reference implementation:

```python
# View the existing implementation at:
# tern/core/memory_pipeline.py

# Key patterns to follow:
# 1. Cache check first
# 2. Pipeline execution with error handling
# 3. Result caching
# 4. Performance tracking
# 5. Consistent return format
```

## Data Source Types

Your pipeline can access different types of data:

### Resource Metrics
```python
# For system metrics (CPU, memory, disk, etc.)
await self.data_sources.resource_metrics(
    columns=['MemTotalBytes', 'MemUsedBytes'],
    start_time=start_time,
    end_time=end_time
)
```

### Cross Analysis Data
```python
# For cross-analyzer queries
await self.data_sources.cross_analysis(
    query_params=your_params
)
```

### Action Timeline Data
```python
# For action/event timeline data
await self.data_sources.action_timeline(
    start_time=start_time,
    end_time=end_time
)
```

## Common Patterns

### Time Series Data
Most charts follow this pattern:
1. Fetch timestamped records
2. Extract timestamps and values
3. Sort by timestamp
4. Format for chart consumption

### Aggregation/Grouping
For charts that group data:
1. Fetch raw data
2. Group by criteria (device, category, etc.)
3. Apply aggregation (sum, average, max)
4. Format grouped results

### Threshold-based Filtering
For charts with "Other" categories:
1. Calculate metrics per category
2. Identify categories below threshold
3. Group small categories into "Other"
4. Return separate series

## Troubleshooting

### Common Issues

**Empty charts**: Check data source query parameters and time ranges
**Performance issues**: Ensure caching is working and queries are optimized
**Cache misses**: Verify cache key generation includes all relevant parameters
**Import errors**: Check relative imports and module structure

### Debugging

Enable debug logging:
```python
import logging
logging.getLogger("app").setLevel(logging.DEBUG)
```

Check performance logs:
```python
logging.getLogger("app.performance").setLevel(logging.DEBUG)
```

## Conclusion

The pipeline architecture provides a clean, scalable way to add new charts:

1. **Self-contained**: Each chart owns its data transformation logic
2. **Cacheable**: Automatic caching with intelligent cache management
3. **Testable**: Pipeline classes can be tested independently
4. **Performant**: Built-in performance tracking and optimization
5. **Consistent**: All charts follow the same patterns

Follow this guide and use existing pipeline implementations as references to add new charts efficiently and maintainably.
