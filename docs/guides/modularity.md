# Chart Component Modularity Refactoring Plan

## Overview

This document outlines the refactoring plan to transform the monolithic chart components in `tern/tern/layout/main_layout.py` into modular, reusable components using dependency injection patterns. The goal is to eliminate code duplication and create truly reusable chart components.

## Current Problems

- **800+ lines of duplicated code** across chart functions
- **Hardcoded input dependencies** make charts non-reusable
- **Inconsistent styling and configuration** across similar charts
- **Difficult to maintain** and extend with new chart types
- **No separation of concerns** between layout and data binding

## Solution: Dependency Injection Pattern

The core insight is to treat **input IDs as parameters** to chart factory functions, allowing the same chart component to listen to different input sources depending on context.

## Implementation Plan

### Phase 1: Create Chart Component Infrastructure

**Timeline**: 2-3 hours
**Priority**: High

#### 1.1 Create Base Directory Structure
```
tern/tern/components/
├── __init__.py
├── charts/
│   ├── __init__.py
│   ├── base.py          # Base chart factory function
│   ├── configs.py       # Centralized chart configurations
│   ├── factories.py     # Specialized chart factories
│   └── callbacks.py     # Chart callback management
```

#### 1.2 Base Chart Factory Function
```python
# tern/tern/components/charts/base.py
from typing import Union, Dict, List, Optional
import dash_mantine_components as dmc
from dash import dcc, Input, Output, callback

def chart_card(
    card_id: str,
    input_ids: Union[List[str], str],
    *,
    title: str = "",
    chart_type: str = "line",
    height: int = 400,
    config_overrides: Optional[Dict] = None,
    description: str = ""
) -> dmc.Card:
    """
    Factory function that creates a chart card with injected input dependencies.

    Args:
        card_id: Unique identifier for this chart instance
        input_ids: List of input component IDs that this chart should listen to
        title: Chart title to display
        chart_type: Type of chart to render (line, area, scatter, etc.)
        height: Chart height in pixels
        config_overrides: Custom Plotly configuration overrides
        description: Optional description text

    Returns:
        DMC Card component with embedded graph and loading overlay
    """
    graph_id = f"{card_id}-graph"
    loading_id = f"{card_id}-loading"

    # Create the DMC card structure
    card = create_chart_card_layout(graph_id, loading_id, title, height, description)

    # Register callback with injected input IDs
    register_chart_callback(graph_id, input_ids, chart_type, config_overrides)

    return card
```

#### 1.3 Chart Configuration Management
```python
# tern/tern/components/charts/configs.py
DEFAULT_PLOTLY_CONFIG = {
    "displaylogo": False,
    "modeBarButtonsToRemove": [
        "pan2d", "lasso2d", "select2d", "zoom2d",
        "zoomIn2d", "zoomOut2d", "autoScale2d", "resetScale2d",
        "hoverClosestCartesian", "hoverCompareCartesian", "toggleSpikelines"
    ],
    "scrollZoom": False,
    "doubleClick": False,
    "editable": False,
    "staticPlot": False
}

DEFAULT_LAYOUT = {
    "title": None,
    "showlegend": True,
    "legend": {
        "orientation": "h",
        "yanchor": "bottom",
        "y": 1.02,
        "xanchor": "right",
        "x": 1
    },
    "hovermode": "x unified",
    "margin": {"t": 10, "r": 10, "b": 40, "l": 60},
    "plot_bgcolor": "rgba(0,0,0,0)",
    "paper_bgcolor": "rgba(0,0,0,0)"
}

CHART_TYPE_CONFIGS = {
    "cpu_load": {
        "yaxis": {"title": "Load Average"},
        "traces": ["Load (1 min)", "Load (5 min)", "Load (15 min)"]
    },
    "disk_capacity": {
        "yaxis": {"title": "Usage (%)", "range": [0, 100]},
        "chart_mode": "stacked_area"
    },
    "gpu_metrics": {
        "yaxis": {"title": "Percent (%)", "range": [0, 100]},
        "yaxis2": {
            "title": "Temp °C / Power W",
            "overlaying": "y",
            "side": "right",
            "showgrid": False
        }
    }
}
```

### Phase 2: Extract Base Chart Configurations and Styles

**Timeline**: 1-2 hours
**Priority**: High

#### 2.1 Common Layout Components
```python
# tern/tern/components/charts/base.py
def create_chart_card_layout(
    graph_id: str,
    loading_id: str,
    title: str,
    height: int,
    description: str = ""
) -> dmc.Card:
    """Create standardized chart card layout."""
    return dmc.Stack(
        gap="md",
        children=[
            dmc.Group(
                justify="space-between",
                align="center",
                children=[
                    dmc.Text(title, fw=500, size="lg"),
                    dmc.Text(description, size="sm", c="dimmed") if description else None
                ]
            ),
            dmc.Paper(
                className="graph-container",
                style={"minHeight": f"{height}px"},
                children=[
                    dmc.LoadingOverlay(
                        id=loading_id,
                        visible=True,
                        loaderProps={"variant": "dots", "size": "lg"},
                        overlayProps={"opacity": 0.6, "blur": 2},
                        className="loading-overlay"
                    ),
                    dcc.Graph(
                        id=graph_id,
                        config=DEFAULT_PLOTLY_CONFIG,
                        style={"width": "100%", "height": f"{height}px"},
                        figure={"data": [], "layout": DEFAULT_LAYOUT}
                    )
                ]
            )
        ]
    )
```

### Phase 3: Create Reusable Chart Factory Functions with ID Injection

**Timeline**: 3-4 hours
**Priority**: High

#### 3.1 Specialized Chart Factories
```python
# tern/tern/components/charts/factories.py
def cpu_load_chart(
    card_id: str,
    time_input_ids: Union[List[str], str],
    position_input_id: Optional[str] = None
) -> dmc.Card:
    """Factory for CPU load charts with configurable input sources."""
    input_ids = _normalize_input_list(time_input_ids)
    if position_input_id:
        input_ids.append(position_input_id)

    return chart_card(
        card_id=card_id,
        input_ids=input_ids,
        title="CPU Load Average",
        description="Shows 1, 5, and 15-minute load averages",
        chart_type="cpu_load",
        height=400
    )

def disk_capacity_chart(
    card_id: str,
    time_input_ids: Union[List[str], str],
    position_input_id: Optional[str] = None
) -> dmc.Card:
    """Factory for disk capacity charts."""
    input_ids = _normalize_input_list(time_input_ids)
    if position_input_id:
        input_ids.append(position_input_id)

    return chart_card(
        card_id=card_id,
        input_ids=input_ids,
        title="Disk Capacity Usage",
        description="Shows disk usage percentage for all mount points",
        chart_type="disk_capacity",
        height=400
    )

def log_scatter_chart(
    card_id: str,
    time_input_ids: Union[List[str], str],
    position_input_id: Optional[str] = None
) -> dmc.Card:
    """Factory for log scatter plots."""
    input_ids = _normalize_input_list(time_input_ids)
    if position_input_id:
        input_ids.append(position_input_id)

    return chart_card(
        card_id=card_id,
        input_ids=input_ids,
        title="Log Events by Position",
        description="Shows warnings and errors across positions over time",
        chart_type="log_scatter",
        height=250
    )
```

#### 3.2 Callback Registration System
```python
# tern/tern/components/charts/callbacks.py
def register_chart_callback(
    graph_id: str,
    input_ids: List[str],
    chart_type: str,
    config_overrides: Optional[Dict] = None
):
    """Register callback for chart with dynamic input sources."""
    from dash import callback, Input, Output
    from ..core.log_visualisation import get_chart_data

    # Build Input list dynamically
    inputs = [Input(input_id, "value") for input_id in input_ids if input_id]

    @callback(
        Output(graph_id, "figure"),
        inputs,
        prevent_initial_call=True
    )
    def update_chart(*input_values):
        """Dynamic callback that handles any number of inputs."""
        return get_chart_data(chart_type, input_values, config_overrides)
```

### Phase 4: Implement Pattern-Matching Callbacks for Scalability *(SKIPPED)*

**Timeline**: 2-3 hours
**Priority**: Medium
**Status**: **SKIPPED** - Pattern-matching callbacks are officially recommended by Dash for scalability when handling dynamic/arbitrary numbers of components. However, our current individual callback system works well for the current use case. This phase can be implemented later if performance issues arise or if we need more dynamic chart creation.

**Decision Rationale**:
- Current individual callback system is clean and functional
- Pattern-matching provides most benefit for large numbers of dynamic chart instances
- Can be implemented later as an optimization if needed
- Phase 5 integration is higher priority for immediate value

#### 4.1 Pattern-Matching Chart System *(For Future Reference)*
```python
# tern/tern/components/charts/pattern_matching.py (future implementation)
from dash import callback, Input, Output, MATCH, ctx

def resource_chart_pattern(metric: str, scope: str = "global") -> dmc.Card:
    """Create charts using pattern-matching for scalability."""
    # Following Dash recommendations: use 'type' and descriptive keys
    return dmc.Card(
        id={"type": "resource-chart-card", "metric": metric, "scope": scope},
        children=[
            dmc.Text(f"{metric.title()} Metrics"),
            dcc.Graph(
                id={"type": "resource-chart", "metric": metric, "scope": scope},
                config=DEFAULT_PLOTLY_CONFIG
            )
        ]
    )

@callback(
    Output({"type": "resource-chart", "metric": MATCH, "scope": MATCH}, "figure"),
    [Input({"type": "time-filter", "scope": MATCH}, "value"),
     Input({"type": "position-filter", "scope": MATCH}, "value")],
    prevent_initial_call=True
)
def update_resource_chart_pattern(time_range, position):
    """Single callback handles all resource charts via pattern matching."""
    # Get which specific chart triggered this
    chart_info = ctx.outputs_list[0]["id"]
    metric = chart_info["metric"]
    scope = chart_info["scope"]

    return generate_chart_figure(metric, time_range, position, scope)
```

### Phase 5: Refactor main_layout.py to Use New Chart Factories

**Timeline**: 2-3 hours
**Priority**: Medium

#### 5.1 Simplified Layout Creation
```python
# tern/tern/layout/main_layout.py (refactored)
from ..components.charts.factories import (
    cpu_load_chart, disk_capacity_chart, log_scatter_chart,
    gpu_metrics_chart, io_throughput_chart, memory_chart
)

def create_main_content():
    """Create main content using modular chart factories."""
    return dmc.AppShellMain(
        children=dmc.Container(
            fluid=True,
            p="md",
            children=dmc.Stack(
                gap="md",
                children=[
                    # Log scatter with time range slider input
                    log_scatter_chart(
                        card_id="log-scatter",
                        time_input_ids=["time-range-slider"],
                        position_input_id="position-filter"
                    ),

                    # Memory chart with date picker inputs
                    memory_chart(
                        card_id="memory-details",
                        time_input_ids=["start-date-picker", "end-date-picker"],
                        position_input_id="position-filter"
                    ),

                    # CPU chart with global time filter
                    cpu_load_chart(
                        card_id="cpu-load",
                        time_input_ids=["global-time-range"]
                    ),

                    # Additional charts using individual factories
                    disk_capacity_chart(
                        card_id="disk-capacity",
                        time_input_ids=["global-time-range"]
                    ),

                    gpu_metrics_chart(
                        card_id="gpu-metrics",
                        time_input_ids=["global-time-range"]
                    ),

                    io_throughput_chart(
                        card_id="io-throughput",
                        time_input_ids=["global-time-range"]
                    ),

                    create_log_table_section()  # Keep existing for now
                ]
            )
        )
    )
```

#### 5.2 Migration Strategy
1. **Start with one chart** - Replace `create_cpu_load_section()` first
2. **Test thoroughly** - Ensure functionality is preserved
3. **Migrate incrementally** - One chart at a time
4. **Remove old functions** - Delete original hardcoded implementations
5. **Update tests** - Modify any tests that reference old function names

**Important**: Following Dash best practices:
- Set `app.config.suppress_callback_exceptions = True` for dynamic layouts
- All callbacks must be registered before server startup
- Use `dcc.Store` components for sharing data between charts when needed

### Phase 6: Add Configuration Flexibility and Testing

**Timeline**: 2-3 hours
**Priority**: Low

#### 6.1 Advanced Configuration Options
```python
# Enhanced chart factory with full customization
def enhanced_chart_card(
    card_id: str,
    input_ids: Union[List[str], str],
    *,
    title: str = "",
    chart_type: str = "line",
    height: int = 400,
    theme: str = "light",
    color_palette: Optional[List[str]] = None,
    show_legend: bool = True,
    enable_zoom: bool = False,
    custom_layout: Optional[Dict] = None
):
    """Enhanced chart factory with full customization options."""
    # Implementation with theme support, custom colors, etc.
```

#### 6.2 Testing Strategy
```python
# tests/test_chart_factories.py
def test_cpu_load_chart_creation():
    """Test that CPU load chart factory creates proper structure."""
    chart = cpu_load_chart("test-cpu", ["time-input"])
    assert chart.id == "test-cpu"
    assert "cpu-load" in str(chart)

def test_chart_callback_registration():
    """Test that callbacks are properly registered with correct inputs."""
    # Test callback registration logic

def test_pattern_matching_charts():
    """Test pattern-matching chart system."""
    # Test multiple chart creation and callback routing
```

## Benefits

### Code Reduction
- **Before**: ~800 lines with heavy duplication
- **After**: ~400 lines with reusable components
- **New charts**: 5-10 lines instead of 50-80 lines

### Flexibility
- Charts can listen to **any input source**
- **Global filters**, **local filters**, or **both**
- **Multiple filter precedence** (local overrides global)
- **Easy A/B testing** of different input sources

### Maintainability
- **Single source of truth** for chart styling
- **Centralized configuration** management
- **Type-safe interfaces** with clear expectations
- **Easy to extend** with new chart types

### Scalability
- **Pattern-matching** handles unlimited similar charts
- **No callback registration boilerplate**
- **Automatic ID collision avoidance**

## Migration Timeline

| Phase | Duration | Effort | Dependencies |
|-------|----------|--------|-------------|
| Phase 1 | 2-3 hours | High | None |
| Phase 2 | 1-2 hours | High | Phase 1 |
| Phase 3 | 3-4 hours | High | Phase 1, 2 |
| Phase 4 | ~~2-3 hours~~ | ~~Medium~~ | ~~Phase 3~~ **SKIPPED** |
| Phase 5 | 2-3 hours | Medium | Phase 3 |
| Phase 6 | 2-3 hours | Low | Phase 5 |

**Total Estimated Time**: ~~12-20 hours~~ 9-17 hours (with Phase 4 skipped)
**Immediate Benefits After**: Phase 3 (first working factories)
**Full Benefits After**: Phase 5 (complete migration)

## Implementation Notes

### Best Practices (Following Dash Official Guidelines)
1. **Unique IDs per instance** - Prevent clashes when same factory used multiple times
2. **Callbacks inside factories** - Ensures proper input/output binding
3. **Register before server starts** - All callbacks must be defined at layout time (Dash requirement)
4. **Use `suppress_callback_exceptions=True`** - For dynamic layouts (Dash recommendation)
5. **Leverage `dash.ctx.triggered_id`** - For multi-input debugging
6. **Dictionary-style IDs for pattern-matching** - Use `{"type": "component-type", "index": unique_id}` format
7. **Stateless callback design** - Never modify global variables (Dash core principle)
8. **Use `dcc.Store` for data sharing** - Between callbacks when needed (Dash best practice)

### Gotchas to Avoid (Based on Dash Documentation)
- **Don't create callbacks inside running callbacks** - Register all at layout time (Dash requirement)
- **Avoid hardcoded input assumptions** - Always accept input IDs as parameters
- **Don't share callback functions** - Each instance needs its own callback
- **Test ID uniqueness** - Ensure no conflicts when using same factory multiple times
- **All callback inputs/outputs must exist** - Components must be in layout when app starts
- **Don't modify global variables in callbacks** - Maintains Dash's stateless architecture
- **Avoid blocking operations in callbacks** - Consider clientside callbacks for performance

## Next Steps

1. **Review and approve** this plan
2. **Start with Phase 1** - Create base infrastructure
3. **Pick first chart to migrate** - Recommend CPU load chart as proof of concept
4. **Iterate and refine** - Adjust approach based on learnings
5. **Document patterns** - Create examples for future chart additions

This refactoring will transform the codebase from a monolithic structure to a truly modular, maintainable, and reusable component system.
