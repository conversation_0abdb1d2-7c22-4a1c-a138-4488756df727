# Log Analysis API Tools for LLM Agent

## Data Retrieval Tools

### 1. ParseLogsTool
- **Description**: Parses logs from a specified directory.
- **Arguments**:
  - `base_dir` (string): Directory containing log files to parse
  - `store` (boolean, optional): Whether to store parsed results
  - `output_dir` (string, optional): Output directory for parsed results

### 2. QueryResourceLogsTool
- **Description**: Retrieves resource system logs within a time range with optional column filtering.
- **Arguments**:
  - `start` (datetime): Start timestamp in ISO format
  - `end` (datetime): End timestamp in ISO format
  - `columns` (string, optional): Comma-separated list of columns to include
  - `limit` (integer, optional): Number of records to return (default: 5000)
  - `offset` (integer, optional): Offset for pagination (default: 0)

### 4. QueryMinknowLogsTool
- **Description**: Retrieves Minknow log entries with various filters.
- **Arguments**:
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format
  - `log_event` (string, optional): Filter by log event
  - `process_name` (string, optional): Filter by process name
  - `log_level` (string, optional): Filter by log level (warning, error, etc.)
  - `library` (string, optional): Filter by library
  - `positions` (string, optional): Comma-separated list of positions
  - `limit` (integer, optional): Number of records to return (default: 100)
  - `offset` (integer, optional): Offset for pagination (default: 0)

## Analysis Tools

### 5. GetLogSummaryTool
- **Description**: Retrieves summary statistics of log data.
- **Arguments**:
  - `position` (string, optional): Filter summary by position
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format

### 6. GetErrorDistributionTool
- **Description**: Analyzes error distribution across positions and time.
- **Arguments**:
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format

### 7. GetActionsTool
- **Description**: Retrieves actions matching specified filters.
- **Arguments**:
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format
  - `position_id` (string, optional): Filter by position ID
  - `action_type` (string, optional): Filter by action type
  - `include_children` (boolean, optional): Include child actions (default: True)

### 8. GetActionDurationStatsTool
- **Description**: Provides statistics about action durations.
- **Arguments**:
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format
  - `position_id` (string, optional): Filter by position ID
  - `action_type` (string, optional): Filter by action type

### 9. DetectLogLevelAnomaliesTool
- **Description**: Identifies anomalies in log levels.
- **Arguments**:
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format
  - `position` (string, optional): Filter by position
  - `error_threshold` (float, optional): Error rate threshold (default: 0.05)
  - `warning_threshold` (float, optional): Warning rate threshold (default: 0.40)
  - `window_size` (string, optional): Time window size (default: "10min")

### 10. DetectTimeSeriesAnomaliesTool
- **Description**: Detects anomalies in time series data.
- **Arguments**:
  - `column` (string): Column to analyze for anomalies
  - `method` (string, optional): Anomaly detection method (default: "zscore")
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format
  - `position` (string, optional): Filter by position
  - `window_size` (string, optional): Time window size (default: "1H")
  - `rolling_window` (integer, optional): Number of windows for statistics (default: 24)
  - `min_data_coverage` (float, optional): Minimum percentage of non-null values (default: 0.05)

## Visualization Support Tools

### 11. GetTimeRangeTool
- **Description**: Retrieves timestamp ranges for data analysis.
- **Arguments**:
  - `range_type` (string, optional): Type of range (default: "common")
  - `position` (string, optional): Filter for a specific position

### 12. GetAggregatedLogsTool
- **Description**: Retrieves aggregated log data for heatmap visualization.
- **Arguments**:
  - `start` (datetime): Start timestamp in ISO format
  - `end` (datetime): End timestamp in ISO format
  - `positions` (string, optional): Comma-separated list of positions

### 13. GetPositionsTool
- **Description**: Retrieves list of available positions from logs.
- **Arguments**: None

### 14. GetResourceColumnsTool
- **Description**: Retrieves list of available resource columns.
- **Arguments**: None

### 15. GetHeatmapDataTool
- **Description**: Retrieves paginated heatmap data for visualization.
- **Arguments**:
  - `page` (integer, optional): Page number (default: 1)
  - `page_size` (integer, optional): Number of data points per page (default: 1000)

### 16. GetActionTimelineTool
- **Description**: Retrieves timeline events for actions.
- **Arguments**:
  - `start` (datetime, optional): Start timestamp in ISO format
  - `end` (datetime, optional): End timestamp in ISO format
  - `position_id` (string, optional): Filter by position ID
  - `action_type` (string, optional): Filter by action type
  - `freq` (string, optional): Time frequency for binning (default: "1Min")
