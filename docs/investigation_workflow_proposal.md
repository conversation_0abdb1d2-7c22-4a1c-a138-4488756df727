# Investigation Workflow Proposal

## Overview

This proposal outlines a new investigation-focused workflow for the Tern log analyzer application. The workflow centers around a point-in-time analysis approach, allowing users to investigate specific incidents by providing temporal context around critical events.

## Current State Analysis

### Architecture Overview
The application currently uses:
- **Dash + PyWebview**: Desktop application with web-based UI
- **Dash Mantine Components (DMC)**: Modern component library for UI
- **Modular design**: Separate components for charts, tables, and analysis
- **Feature flag system**: `FEATURE_WORKFLOW` environment variable already implemented

### Current Layout Structure
```
📁 Standard Layout
├── 🔧 Setup Section (navbar)
├── 📊 Global Filters (navbar)
├── 📈 Log Scatter Chart (main)
├── 🔄 Time Range Selector (main)
├── 📊 Resource Charts (main)
└── 📋 Log Table (main)
```

### Existing Components Ready for Reuse
- **Log Table**: Professional DMC-based table with expandable rows
- **Chart Infrastructure**: Modular chart factory system
- **Time Range Controls**: Slider-based time navigation
- **Progress Tracking**: Built-in progress tracking system
- **Data Context**: Cross-analyzer data integration

## Proposed Investigation Workflow

### Core Concept
Transform the analysis from a "browse all logs" approach to a "investigate this moment" approach where users:
1. **Specify investigation target**: Point in time + optional position
2. **Get contextual view**: Centered analysis around that moment
3. **Drill down efficiently**: Accordion-based progressive disclosure

### New Layout Structure
```
📁 Investigation Layout
├── 🎯 Investigation Setup (navbar)
│   ├── 🕐 Target Time Input
│   ├── 📍 Position Filter (optional)
│   └── ⏱️ Time Window Control
├── 🔍 Investigation Results (main)
│   ├── 📊 Context Accordions (above table)
│   │   ├── 🔥 Critical Events Summary
│   │   ├── 📈 Resource Trends
│   │   ├── 🎯 Action Analysis
│   │   └── 🌐 Cross-System View
│   ├── 📋 **Central Log Table** (focal point)
│   └── 📊 Analysis Accordions (below table)
│       ├── 🔄 Timeline Visualization
│       ├── 📊 Performance Metrics
│       ├── 🔗 Related Events
│       └── 🎪 Anomaly Detection
```

### Key Features

#### 1. Investigation Setup Panel
- **Target Time**: DateTime picker for the investigation focus point
- **Position Context**: Optional position filter for spatial relevance
- **Time Window**: Configurable window (±15min, ±1h, ±4h, ±1d)
- **Quick Presets**: "Last Error", "Peak Resource Usage", "System Start"

#### 2. Smart Accordion System
Each accordion child includes:
- **Collapsed Label Intelligence**:
  - Summary metrics (e.g., "3 Critical Events")
  - Visual indicators (🔴 for alerts, 📈 for trends)
  - Relevance scoring (⭐⭐⭐ for high relevance)
- **Progressive Disclosure**: Charts and details load when expanded
- **Contextual Prioritization**: Most relevant accordions auto-expand

#### 3. Central Log Table Enhancement
- **Temporal Centering**: Target time highlighted and centered
- **Contextual Highlighting**: Events related to investigation highlighted
- **Smart Pagination**: Intelligent page sizing based on event density
- **Keyboard Navigation**: Arrow keys for time-based navigation

#### 4. Accordion Content Hierarchy

The investigation workflow follows a storytelling approach with three distinct layers:

**Above Table (Quick Rule-Out Analysis)**:
- **🚨 System Health Check**: Instant CPU/memory/disk status at target time to rule out resource exhaustion
- **⚡ Cross-Position Activity**: Real-time view of what's happening across all positions simultaneously
- **🔥 Critical Events**: Errors/warnings within investigation window with relevance scoring
- **📊 Performance Snapshot**: Resource state comparison against baseline/normal ranges

**Central Table (Investigation Focus)**:
- **📋 Contextual Log Table**: Events centered around target time with intelligent pagination
- **🔍 Multi-Source Correlation**: Position-specific events with cross-system correlations
- **📍 Spatial Context**: Position-based filtering and comparison capabilities

**Below Table (Pattern & Trend Analysis)**:
- **📈 Resource Trends**: Weekly/daily patterns with anomaly detection and historical comparison
- **🔗 Action Timeline**: Protocol/workflow context showing action states across all positions
- **🎯 Similar Incidents**: Historical pattern matching for recurring issues
- **📉 Error Distribution**: Spatial/temporal error clustering and propagation analysis

#### 5. Intelligent Labeling System
```typescript
// Example accordion label intelligence
{
  "title": "Critical Events",
  "badge": "3 Events",
  "priority": "high",
  "icon": "🔴",
  "relevance": 3,
  "summary": "2 errors, 1 warning within 5 minutes"
}
```

## Technical Implementation Strategy

### Phase 1: Core Infrastructure
1. **Create Investigation Layout**: New layout module with feature flag switching
2. **Investigation Setup Component**: Time picker, position filter, window control
3. **Accordion Framework**: Reusable accordion system with smart labeling
4. **Data Service Layer**: Investigation-focused data queries

### Phase 2: Accordion Content
1. **Critical Events Accordion**: Error/warning aggregation around target time
2. **Resource Context Accordion**: System state snapshot capabilities
3. **Timeline Accordion**: Focused scatter plot with target time highlighting
4. **Table Enhancement**: Temporal centering and contextual highlighting

### Phase 3: Intelligence Features
1. **Smart Labeling**: Relevance scoring and summary generation
2. **Auto-Expansion**: Priority-based accordion opening
3. **Quick Presets**: Pre-configured investigation scenarios
4. **Keyboard Navigation**: Efficient time-based browsing

### Phase 4: Advanced Features
1. **Investigation Bookmarks**: Save investigation configurations
2. **Comparative Analysis**: Multiple time point comparison
3. **Export Capabilities**: Investigation report generation
4. **Integration Points**: Link to external incident management

## User Experience Flow

### Investigation Workflow
1. **Launch Investigation Mode**: Click "Investigation Mode" button
2. **Set Target Time**:
   - Manual datetime picker
   - "Go to last error" quick action
   - Click on existing scatter plot point
3. **Configure Context**:
   - Select position filter if relevant
   - Adjust time window (default ±1h)
4. **Analyze Results**:
   - Review auto-expanded high-priority accordions
   - Examine central log table around target time
   - Drill down into specific analysis areas
5. **Navigate Temporally**:
   - Use keyboard arrows for time navigation
   - Click related events to jump to new investigation points

### Quick Investigation Scenarios
- **"What happened at 14:30?"**: Direct time input
- **"Why did position X fail?"**: Time + position filter
- **"Investigate this error"**: Click error in existing view
- **"What led to this state?"**: Contextual back-tracking

## Technical Considerations

### Data Architecture Requirements
The investigation workflow requires several new query patterns currently missing from the log_parser:

**Critical Missing Capabilities**:
- **Point-in-time + Window Queries**: `get_events_around_time()`, `get_system_state_at_time()`
- **Cross-Position Comparative Analysis**: `compare_positions_at_time()`, `find_outlier_positions()`
- **Historical Pattern Matching**: `find_similar_incidents()`, `get_recurring_patterns()`
- **Multi-Source Correlation**: `get_investigation_context()`, `trace_event_propagation()`
- **Error Clustering**: `cluster_errors_by_similarity()`, `get_error_progression_timeline()`
- **Timeline Construction**: `build_unified_timeline()`, `get_critical_event_sequence()`

**Implementation Priority**:
1. **Phase 1**: Core investigation context queries (`get_investigation_summary()`, `query_around_time()`)
2. **Phase 2**: Pattern matching and error clustering capabilities
3. **Phase 3**: Advanced causality tracing and cross-position analysis

### Performance Optimization
- **Lazy Loading**: Accordion content loads only when expanded
- **Data Windowing**: Efficient queries for time-bounded data with optimized indexing
- **Smart Caching**: Cache investigation results and frequently accessed patterns
- **Progressive Enhancement**: Core functionality first, advanced features after

### UI/UX Considerations
- **Responsive Design**: Accordion system adapts to screen size
- **Keyboard Shortcuts**: Power user efficiency features for temporal navigation
- **Visual Hierarchy**: Clear information priority through design and relevance scoring
- **Loading States**: Smooth transitions during data loading with progress indicators

## Migration Strategy

### Feature Flag Implementation
```python
# Current feature flag already exists
FEATURE_WORKFLOW = os.getenv("FEATURE_WORKFLOW", "false").lower() == "true"
```

### Gradual Rollout
1. **Development**: Feature flag enabled for development
2. **Testing**: Controlled testing with specific user groups
3. **Opt-in**: Users can choose between standard and investigation modes
4. **Default**: Investigation mode becomes default after validation
5. **Deprecation**: Eventually phase out standard mode if preferred

### Backward Compatibility
- **Preserve Current Functionality**: Standard mode remains fully functional
- **Shared Components**: Maximize reuse of existing chart and table components
- **Data Compatibility**: Same underlying data structures and APIs
- **Configuration**: Existing settings and preferences carry over

## Benefits and Value Proposition

### For Users
- **Faster Root Cause Analysis**: Direct focus on problematic time periods
- **Reduced Cognitive Load**: Contextual information prioritization
- **Improved Investigation Efficiency**: Less scrolling, more insights
- **Better Pattern Recognition**: Temporal context highlights correlations

### For Development
- **Modular Design**: Accordion system enables incremental feature additions
- **Reusable Components**: Chart and analysis components work in both modes
- **Extensible Framework**: Easy to add new investigation capabilities
- **Performance Benefits**: Focused data queries reduce system load

## Questions and Discussion Points

1. **Accordion Prioritization**: What algorithm should determine accordion relevance and auto-expansion?

2. **Time Window Defaults**: Should default investigation windows vary by log volume or event density?

3. **Quick Preset Configuration**: Which investigation scenarios should have preset buttons?

4. **Keyboard Shortcuts**: What navigation shortcuts would be most valuable for power users?

5. **Multi-Position Investigations**: How should we handle investigations across multiple positions simultaneously?

6. **Integration Points**: Should we provide APIs for external tools to trigger investigations?

7. **Performance Thresholds**: At what data volume should we implement additional optimization strategies?

8. **User Onboarding**: How do we help users transition from browse-mode to investigation-mode thinking?

## Success Metrics

### User Experience
- **Time to Root Cause**: Measure investigation completion time
- **User Satisfaction**: Feedback on investigation efficiency
- **Feature Adoption**: Usage rates of investigation vs. standard mode
- **Error Resolution**: Success rate of incident investigations

### Technical Performance
- **Query Performance**: Time-bounded query response times
- **UI Responsiveness**: Accordion expansion and navigation speed
- **Memory Usage**: Efficient data handling for investigation windows
- **Cache Hit Rates**: Effectiveness of investigation result caching

## Conclusion

The investigation workflow represents a significant enhancement to Tern's log analysis capabilities, transforming it from a general-purpose log browser into a targeted incident investigation tool. By centering the analysis around specific points in time and providing contextual information through intelligent accordions, we can dramatically improve the efficiency of root cause analysis while maintaining the flexibility and power of the existing system.

The modular design and feature flag approach allow for safe, gradual implementation while preserving backward compatibility and enabling continuous improvement based on user feedback.
