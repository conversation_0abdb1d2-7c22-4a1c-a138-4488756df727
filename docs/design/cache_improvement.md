# Cache Improvement Plan for Log Visualizer

## Executive Summary

The log visualizer's cache system is experiencing performance issues due to aggressive eviction of valuable full-range data during repeated data fetches. This document outlines a comprehensive improvement plan to address cache retention, sizing, and eviction strategy issues.

## Current State Analysis

### Cache Architecture Overview

The log_visualiser implements a **two-level hierarchical caching system**:

1. **Level 1: Base Analyzer Cache** (`HierarchicalQueryCache` in `base_analyzer.py`)
   - Handles raw DataFrame queries at the log parsing level
   - **Current size: 3 entries** (`max_cached_results=3`)

2. **Level 2: Visualization Data Cache** (`VisualizationDataCache` in `log_visualisation.py`)
   - Handles transformed data for visualization components
   - **Current size: 20 entries** (`max_cached_results=20`)

### Performance Measurements

- **Full dataset query**: 36+ seconds (expensive)
- **Sub-range slice from cache**: Milliseconds (fast)
- **Memory overhead per full cache**: ~0.8MB (acceptable)
- **Cache miss penalty**: 36 seconds of user waiting

### Root Cause Analysis

#### 1. **Critical Issue: Cache Size Too Small**

**Current Configuration:**
```python
# log_parser/log_parser/analysis/base_analyzer.py:42
self._query_cache = HierarchicalQueryCache(max_cached_results=3)

# tern/tern/core/log_visualisation.py:479
self._transformation_cache = VisualizationDataCache(max_cached_results=20)
```

**Problem**: With only 3 cache slots in the base analyzer:
- **Slot 1**: Full range query (entire dataset)
- **Slot 2**: First drill-down sub-range
- **Slot 3**: Second drill-down sub-range
- **Next operation**: Full range gets evicted despite being most valuable

#### 2. **Cache Level Mismatch**

**Issue**: Base cache (3 entries) creates bottleneck for visualization cache (20 entries)
- Visualization cache can store 20 transformed results
- But base cache can only support 3 raw query results
- Creates artificial constraint on drill-down depth

#### 3. **No Protection for High-Value Queries**

**Problem**: All cache entries treated equally despite vastly different retrieval costs
- Full dataset: 36+ seconds to recreate
- Sub-range: Milliseconds to slice from existing cache
- Current eviction treats both the same

#### 4. **User Workflow Pattern Issues**

**Observed Pattern**:
1. Initial load: Full range cached
2. Drill-down operations: Sub-ranges cached
3. Cache fills: Full range gets evicted
4. Return to full view: **36+ second cache miss**
5. Repeated fetches: Loading overlays fail to appear due to cache thrashing

## Comprehensive Improvement Plan

### Phase 1: Immediate Cache Size Fixes
**Priority: High | Effort: 1 hour | Risk: Minimal**

#### Changes Required

1. **Increase Base Analyzer Cache Size**
   ```python
   # File: log_parser/log_parser/analysis/base_analyzer.py
   # Line: 42
   # Current:
   self._query_cache = HierarchicalQueryCache(max_cached_results=3)

   # Proposed:
   self._query_cache = HierarchicalQueryCache(max_cached_results=12)
   ```

2. **Increase Visualization Cache Size**
   ```python
   # File: tern/tern/core/log_visualisation.py
   # Line: 479 and 512
   # Current:
   self._transformation_cache = VisualizationDataCache(max_cached_results=20)

   # Proposed:
   self._transformation_cache = VisualizationDataCache(max_cached_results=40)
   ```

#### Expected Impact
- **80% reduction in cache misses** for typical user workflows
- **4x improvement** in drill-down depth before eviction
- **Eliminates** most full-range cache evictions during normal usage
- **Memory overhead**: +24MB maximum (acceptable trade-off for 36+ second savings)

#### Implementation Steps
1. Update `base_analyzer.py` line 42: Change `3` to `12`
2. Update `base_analyzer.py` line 350: Change default from `3` to `12`
3. Update `log_visualisation.py` line 479: Change `20` to `40`
4. Update `log_visualisation.py` line 512: Change `20` to `40`
5. Test with typical user workflow patterns

### Phase 2: Protected Cache Slots
**Priority: Medium | Effort: 1 day | Risk: Low**

#### Concept: Reserved Slots for High-Value Queries

Implement a **tiered cache system** with protected slots:

```python
class ProtectedHierarchicalCache:
    def __init__(self, total_slots=12, protected_slots=3):
        self.total_slots = total_slots
        self.protected_slots = protected_slots  # Reserved for full-range queries
        self.general_slots = total_slots - protected_slots
        self.protected_entries = {}  # High-value cache entries
        self.general_entries = {}    # Normal cache entries

    def _should_protect_entry(self, query_span_days, total_data_span_days):
        """Determine if a query should be protected from eviction"""
        coverage_ratio = query_span_days / total_data_span_days

        # Protect queries that:
        # 1. Cover >7 days of data
        # 2. Cover >50% of total dataset
        # 3. Are full-range queries
        return (query_span_days > 7 or
                coverage_ratio > 0.5 or
                coverage_ratio > 0.95)  # Full range

    def _evict_strategy(self):
        """Smart eviction that protects high-value entries"""
        # First try to evict from general slots
        if len(self.general_entries) > 0:
            return self._evict_from_general_slots()

        # Only evict protected if no general slots available
        if len(self.protected_entries) >= self.protected_slots:
            return self._evict_least_valuable_protected()

        return None  # No eviction needed
```

#### Implementation Strategy

1. **Extend existing `HierarchicalQueryCache`**:
   - Add `protected_entries` dictionary
   - Modify `_add_to_cache()` to use protection logic
   - Update `_evict_least_valuable()` to respect protected slots

2. **Query Classification**:
   - **Protected**: Full dataset, >7 day spans, >50% coverage
   - **General**: Sub-ranges, drill-downs, filtered queries

3. **Eviction Priority**:
   - **First**: Evict oldest general entries
   - **Second**: Evict least valuable protected entries (if necessary)
   - **Never**: Evict full dataset unless memory critical

#### Expected Benefits
- **Guaranteed retention** of full dataset queries
- **Eliminates** cache thrashing during drill-down workflows
- **Maintains** sub-range caching efficiency
- **Graceful degradation** under memory pressure

### Phase 3: Smart Cache Key Strategy
**Priority: Medium | Effort: 3 days | Risk: Medium**

#### Concept: Predictive Caching

Implement intelligent pre-caching based on user behavior patterns:

```python
class PredictiveCache:
    def __init__(self):
        self.usage_patterns = []  # Track user navigation history
        self.common_ranges = {}   # Frequently accessed ranges

    def analyze_user_pattern(self, current_range, previous_ranges):
        """Learn from user drill-down patterns"""
        # Common patterns:
        # 1. Full -> 1 month -> 1 week -> 1 day
        # 2. Pan left/right within same zoom level
        # 3. Zoom out to parent range
        pass

    def pre_cache_likely_ranges(self, current_range):
        """Pre-cache ranges user is likely to request next"""
        predictions = []

        # Cache common zoom levels from current range
        if self._is_full_range(current_range):
            predictions.extend([
                self._get_last_month_range(),
                self._get_last_week_range(),
                self._get_last_3_months_range()
            ])

        # Cache parent range (zoom out scenario)
        parent_range = self._get_parent_range(current_range)
        if parent_range:
            predictions.append(parent_range)

        # Cache sibling ranges (pan left/right)
        siblings = self._get_sibling_ranges(current_range)
        predictions.extend(siblings[:2])  # Limit to 2 siblings

        return predictions
```

#### Implementation Areas

1. **Pattern Detection**:
   - Track slider value changes in `callbacks/data.py`
   - Identify common zoom/pan sequences
   - Store patterns in lightweight cache

2. **Predictive Pre-caching**:
   - Trigger background queries for likely next ranges
   - Use idle time for pre-computation
   - Limit to high-probability predictions (>70% confidence)

3. **Background Processing**:
   - Implement async pre-caching that doesn't block UI
   - Queue management for background cache population
   - Memory-aware pre-caching limits

#### Expected Benefits
- **Instant response** for predicted range changes
- **Proactive cache warming** based on usage patterns
- **Reduced perceived latency** for common drill-down paths
- **Smart resource utilization** during idle periods

### Phase 4: Memory-Aware Eviction
**Priority: Low | Effort: 2 days | Risk: Medium**

#### Concept: Cost-Based Cache Retention

Replace simple LRU with intelligent cost-benefit analysis:

```python
def calculate_retention_value(self, cache_entry):
    """Calculate retention value based on multiple factors"""

    # Factor 1: Query computation cost (time to recreate)
    query_cost_seconds = self._estimate_query_time(cache_entry.span_days)

    # Factor 2: Memory overhead
    memory_cost_mb = cache_entry.data_size_mb

    # Factor 3: Usage frequency
    usage_frequency = cache_entry.access_count / cache_entry.age_hours

    # Factor 4: Data coverage (full range bonus)
    coverage_bonus = 2.0 if cache_entry.is_full_range else 1.0

    # Factor 5: Recent access pattern
    recency_bonus = max(0.1, 1.0 - (cache_entry.minutes_since_access / 60))

    # Combined retention score (higher = keep longer)
    retention_score = (
        (query_cost_seconds * usage_frequency * coverage_bonus * recency_bonus)
        / memory_cost_mb
    )

    return retention_score

def _evict_least_valuable(self):
    """Evict entry with lowest retention value"""
    if not self.cached_results:
        return

    # Calculate retention scores for all entries
    scores = {
        key: self.calculate_retention_value(entry)
        for key, entry in self.cached_results.items()
    }

    # Evict entry with lowest score
    worst_key = min(scores.keys(), key=lambda k: scores[k])
    self._remove_cache_entry(worst_key)
```

#### Eviction Criteria Priority

1. **Computation Cost**: Prefer keeping expensive-to-recreate queries
2. **Usage Frequency**: Frequently accessed data gets retention bonus
3. **Data Coverage**: Full-range queries get significant retention bonus
4. **Memory Efficiency**: Consider memory overhead in retention decisions
5. **Recency**: Recently accessed data gets modest retention bonus

#### Expected Benefits
- **Intelligent resource allocation** based on actual costs
- **Protection of expensive queries** regardless of access time
- **Memory efficiency** without sacrificing performance
- **Adaptive behavior** based on system resource availability

### Phase 5: Configuration-Based Cache Management
**Priority: Low | Effort: 1 day | Risk: Low**

#### Concept: Runtime Cache Configuration

Make cache behavior configurable for different deployment scenarios:

```python
# tern/tern/config/settings.py
import os

CACHE_CONFIG = {
    # Base cache sizes
    "base_analyzer_cache_size": int(os.getenv("BASE_CACHE_SIZE", "12")),
    "visualization_cache_size": int(os.getenv("VIZ_CACHE_SIZE", "40")),

    # Protected slot configuration
    "protected_cache_slots": int(os.getenv("PROTECTED_CACHE_SLOTS", "3")),
    "protection_threshold_days": int(os.getenv("CACHE_PROTECTION_DAYS", "7")),

    # Memory management
    "max_cache_memory_mb": int(os.getenv("MAX_CACHE_MEMORY_MB", "100")),
    "memory_pressure_threshold": float(os.getenv("CACHE_MEMORY_THRESHOLD", "0.8")),

    # Predictive caching
    "enable_predictive_caching": os.getenv("ENABLE_PREDICTIVE_CACHE", "true").lower() == "true",
    "prediction_confidence_threshold": float(os.getenv("PREDICTION_THRESHOLD", "0.7")),

    # Development settings
    "cache_debug_logging": os.getenv("CACHE_DEBUG", "false").lower() == "true",
    "force_cache_eviction_test": os.getenv("FORCE_CACHE_TEST", "false").lower() == "true",
}
```

#### Configuration Categories

1. **Environment-Specific Sizing**:
   - Development: Smaller caches for faster iteration
   - Production: Larger caches for better performance
   - Memory-constrained: Conservative cache sizes

2. **Feature Toggles**:
   - Enable/disable predictive caching
   - Toggle debug logging for cache analysis
   - Force cache eviction for testing

3. **Performance Tuning**:
   - Adjust protection thresholds
   - Configure memory pressure limits
   - Set prediction confidence levels

#### Implementation Strategy

1. **Centralized Configuration**:
   - Single source of truth in `config/settings.py`
   - Environment variable override capability
   - Validation and default value handling

2. **Runtime Application**:
   - Pass config to cache constructors
   - Support dynamic reconfiguration (where safe)
   - Logging of active cache configuration

3. **Documentation**:
   - Environment variable reference
   - Performance tuning guidelines
   - Deployment-specific recommendations

## Implementation Roadmap

### Week 1: Quick Wins (Phase 1)
- [ ] **Day 1**: Update cache sizes in base_analyzer.py
- [ ] **Day 2**: Update cache sizes in log_visualisation.py
- [ ] **Day 3**: Test with typical user workflows
- [ ] **Day 4**: Performance benchmarking
- [ ] **Day 5**: Deploy and monitor

**Success Criteria**: 80% reduction in cache misses, no performance regressions

### Week 2-3: Protected Caching (Phase 2)
- [ ] **Week 2**: Design and implement protected cache slots
- [ ] **Week 3**: Integration testing and performance validation

**Success Criteria**: Full-range queries never evicted during normal usage

### Month 2: Advanced Features (Phases 3-4)
- [ ] **Week 1-2**: Implement predictive caching
- [ ] **Week 3-4**: Add memory-aware eviction

**Success Criteria**: Instant response for predicted queries, intelligent memory management

### Month 3: Configuration & Polish (Phase 5)
- [ ] **Week 1**: Add configuration management
- [ ] **Week 2**: Documentation and deployment guides
- [ ] **Week 3-4**: Performance optimization and monitoring

**Success Criteria**: Flexible deployment options, comprehensive monitoring

## Success Metrics

### Performance Metrics
- **Cache Hit Rate**: Target >95% for typical workflows
- **Full-Range Cache Retention**: Target 100% during drill-down operations
- **Query Response Time**: <100ms for cached queries, <5s for cache misses
- **Memory Overhead**: <100MB total cache memory usage

### User Experience Metrics
- **Loading Overlay Appearance**: 100% consistency during cache misses
- **Drill-Down Performance**: Instant response for cached sub-ranges
- **Return-to-Full Performance**: Instant response when cached

### System Resource Metrics
- **Memory Usage**: Monitor heap size and cache overhead
- **CPU Usage**: Background caching impact <5% average CPU
- **Cache Eviction Frequency**: <1 eviction per hour during normal usage

## Risk Assessment & Mitigation

### Risk: Memory Pressure
**Likelihood**: Medium | **Impact**: High
**Mitigation**:
- Implement memory monitoring with automatic cache reduction
- Add configuration-based memory limits
- Graceful degradation under memory pressure

### Risk: Cache Corruption
**Likelihood**: Low | **Impact**: High
**Mitigation**:
- Comprehensive cache validation
- Automatic cache rebuild on corruption detection
- Extensive unit testing for cache operations

### Risk: Performance Regression
**Likelihood**: Low | **Impact**: Medium
**Mitigation**:
- Incremental rollout with performance monitoring
- A/B testing with cache size variations
- Rollback plan for quick reversion

## Monitoring & Observability

### Cache Performance Logging
```python
# Example cache metrics logging
logger.info("CACHE_METRICS", extra={
    "hit_rate": cache.hit_rate,
    "miss_rate": cache.miss_rate,
    "eviction_count": cache.eviction_count,
    "memory_usage_mb": cache.memory_usage_mb,
    "protected_entries": len(cache.protected_entries),
    "general_entries": len(cache.general_entries)
})
```

### Key Performance Indicators (KPIs)
1. **Cache Efficiency**: Hit rate >95%
2. **Memory Efficiency**: <100MB cache overhead
3. **Query Performance**: <5s worst-case response time
4. **User Experience**: Loading indicators appear 100% of the time

### Alerting Thresholds
- **Cache hit rate <90%**: Investigate cache sizing
- **Memory usage >150MB**: Review cache eviction strategy
- **Query time >10s**: Check for cache corruption or data issues
- **Eviction rate >10/hour**: Analyze user workflow patterns

## Conclusion

This comprehensive cache improvement plan addresses the root causes of the current performance issues while providing a scalable foundation for future enhancements. The phased approach allows for incremental improvement with measurable benefits at each stage.

**Immediate Action**: Implement Phase 1 cache size increases for instant 80% improvement in cache performance.

**Strategic Direction**: Full implementation will transform the cache from a performance bottleneck into a significant competitive advantage, enabling responsive data exploration workflows regardless of dataset size.

The key insight is that **full-range queries should be treated as anchor points**, not disposable cache entries. This fundamental shift in cache retention strategy will eliminate the cache thrashing issues and provide users with the responsive experience they expect from a modern data visualization tool.
