# Tern Log Visualizer - User Flow Redesign

## Executive Summary

This document outlines the redesign of <PERSON><PERSON>'s log parsing flow from automatic startup parsing to a user-initiated, feedback-rich experience. The change improves development workflows, user control, and provides better error handling and progress visibility.

### Key Benefits
- **User Control**: Manual log parsing eliminates premature parsing attempts
- **Rich Feedback**: Fish-themed progress indicators with real-time file tracking
- **Native UX**: File dialog integration for easy path selection
- **Robust Error Handling**: Modal dialogs and inline validation
- **Dev-Friendly**: Fixes `--dev` mode reload issues and thread synchronization

### User Flow Transformation
```
🚀 Load Logs → 📊 Parsing... → ✅ Filters Enabled → 📈 Charts Loading → 🎯 Ready
```

## Implementation Phases

### Phase 1: Core UX Restructure 🏗️
**Goal**: Move from automatic to user-initiated parsing

**Key Changes:**
- Remove background parsing thread from startup
- Open navbar by default to show setup section
- Add Browse button with native file dialog support
- Basic callback for manual log loading trigger

**Deliverable**: User can manually trigger log parsing with native folder picker

### Phase 2: Progress & Feedback 📊
**Goal**: Provide rich user feedback during parsing

**Key Changes:**
- Fish-themed progress messages with stage tracking
- File-based progress calculation for accuracy
- Modal error dialogs for critical failures
- Progress bar with elapsed time and current file display

**Deliverable**: User sees detailed progress (~90% accuracy) and helpful errors

### Phase 3: State Management 🔄
**Goal**: Proper UI state transitions

**Key Changes:**
- Show/hide global filters based on parsing status
- Loading overlays during parsing operations
- State persistence across page refreshes
- Auto-collapse navbar after successful load (optional)

**Deliverable**: Smooth UI transitions and state consistency

### Phase 4: Advanced Parsing Features ⚙️
**Goal**: Enhanced parsing capabilities

**Key Changes:**
- Re-parsing functionality (change path and reload)
- Path validation and file system checks
- Parsing cancellation capability
- Memory usage optimization

**Deliverable**: Robust parsing system with user control

### Phase 5: Workflow Foundation 🚀
**Goal**: Enable workflow-based features

**Key Changes:**
- Timestamp centering functionality
- Quick jump to specific time periods
- Basic workflow presets (error investigation, performance review)
- Export/share current view state

**Deliverable**: Foundation for advanced workflow features

## Current State Analysis

### Pain Points Identified

**Automatic Parsing Issues:**
- Background parsing starts immediately on app launch (`app.py:240-246`)
- Relies on `BASE_LOG_FOLDER` environment variable, making development cumbersome
- `--dev` mode reloading recreates parsing threads and loses state
- Early parsing attempts when logs might not be ready

**User Experience Issues:**
- No visual feedback during parsing operations
- Silent failures with errors only in logs
- No way to trigger re-parsing or change directories
- Navbar closed by default, hiding controls when needed most

**Technical Issues:**
- Thread synchronization complexity with reloading
- Environment variable coupling reduces flexibility
- No cancellation or progress tracking capability

## Proposed User Experience

### Initial State (App Launch)
```
┌─ Navbar: OPEN ─────────────────────┐  ┌─ Main Content ─────────────────┐
│ 🔧 Setup                          │  │                                │
│ ┌─────────────────────────────────┐ │  │   Welcome to Tern!             │
│ │ Log Folder Path                 │ │  │                                │
│ │ [/path/from/env/var] [📁 Browse] │ │  │   👈 Please configure your     │
│ │                                 │ │  │      log folder and click      │
│ │ [🚀 Load Logs]                  │ │  │      "Load Logs" to begin      │
│ │                                 │ │  │                                │
│ └─────────────────────────────────┘ │  │                                │
│                                    │  └────────────────────────────────┘
│ 📊 Global Filters                  │
│ (Hidden/Disabled)                  │
└────────────────────────────────────┘
```

### Progress Indication
```
┌─ Progress Section ─────────────────────────────────┐
│ 🌊 Cross-referencing events... connecting schools │
│ ████████████████░░░░░░░░░░░░░░░░░░░░░░░ 65%       │
│                                                    │
│ ⏱️  Elapsed: 00:02:34                             │
│ 📁 Processing: /logs/minknow/sequencing_run.log   │
│ 📊 Files: 15/23 complete                          │
└────────────────────────────────────────────────────┘
```

### Fish-Themed Progress Stages
```
🎣 "Casting nets for logs... hoping for a good catch!"
🐟 "Teaching analyzers to fish... they're learning fast"
🐠 "Diving for MinKnow logs... found some deep data!"
🌊 "Cross-referencing events... connecting schools of data"
📊 "Counting the catch... building resource metrics"
🎯 "Lines are cast! Ready to reel in insights!"
```

## Detailed Phase Specifications

### Phase 1 Details: Core UX Restructure

**Navbar Restructure:**
```python
# Current: navbar collapsed by default
navbar={"collapsed": {"mobile": True, "desktop": True}}

# New: navbar open by default, closes after successful load
navbar={"collapsed": {"mobile": True, "desktop": False}}
```

**Setup Section Components:**
- **Path Input**: Pre-filled from `BASE_LOG_FOLDER` if available
- **Browse Button**: Native file dialog for folder selection
- **Load Button**: Manual trigger for parsing process
- **Progress Area**: Hidden until parsing begins

**File Dialog Integration:**
- **PyWebView Mode (Default)**: Native OS file dialogs
- **Web Mode (`--no-webview`)**: Manual path entry with validation
- **Enhanced Web Mode**: File System Access API for Chromium browsers

### Phase 2 Details: Progress & Feedback

**Progress Calculation Strategy:**

**Stage-Weighted Approach (70% Accuracy):**
```python
STAGE_WEIGHTS = {
    "🎣 Casting nets for logs": 5,      # Directory discovery (fast)
    "🐟 Teaching analyzers to fish": 10, # Parser initialization
    "🐠 Diving for MinKnow logs": 40,    # MinKnow parsing (heavy)
    "🌊 Cross-referencing events": 35,   # Resource parsing (heavy)
    "📊 Counting the catch": 10          # Analysis & finalization
}
```

**File-Based Tracking (90% Accuracy):**
- Pre-scan directories to count total parseable files
- Track files processed vs. total files per stage
- Real-time current file display
- Hybrid calculation combining stage weights with file progress

**Error Handling Strategy:**
- **Modal Dialogs**: Critical errors requiring user attention
- **Inline Errors**: Input validation in setup section
- **Progress Bar Error States**: Shows exactly where parsing failed

### Phase 3 Details: State Management

**UI State Transitions:**
- **No logs**: Setup prominent, filters hidden, navbar open
- **Loading**: Progress visible, filters disabled with overlay, navbar open
- **Loaded**: Setup minimized, filters active, navbar can close
- **Error**: Setup shows error state, filters remain hidden

**Store Structure:**
```javascript
{
  parsing: {
    status: 'idle' | 'parsing' | 'complete' | 'error',
    stage: string,           // "🎣 Casting nets for logs..."
    progress: number,        // 0-100 (calculated using stage weights + file progress)
    current_file: string,    // "/logs/minknow/run_001.log"
    files_processed: number, // Files completed in current stage
    total_files: number,     // Total files in current stage
    start_time: string,
    end_time: string,
    error: object
  },
  setup: {
    logPath: string,
    isValid: boolean,
    environmentPath: string
  },
  ui: {
    navbarOpen: boolean,
    filtersVisible: boolean
  }
}
```

## Technical Implementation Details

### Progress Communication Architecture

**Thread-to-Dash Communication Flow:**
```
Background Thread → Container._parsing_status → Store → Interval Callback → UI Update
```

**Existing Infrastructure (Already Implemented):**
- ✅ `AnalyzerContainer._parsing_status` with status tracking
- ✅ `dcc.Store(id="parsing-status")` for state management
- ✅ `dcc.Interval(id="status-interval", interval=1000)` for polling
- ✅ Thread-safe updates via `_parsing_lock`
- ✅ Dynamic polling (1s during parsing, 60s when stable)

**Enhanced Container Status Structure:**
```python
class AnalyzerContainer:
    def __init__(self):
        self._parsing_status = {
            "status": "idle",        # idle/parsing/complete/error
            "stage": "",             # "🎣 Casting nets for logs..."
            "progress": 0,           # 0-100 percentage
            "current_file": "",      # Current file being processed
            "files_processed": 0,    # Files completed in current stage
            "total_files": 0,        # Total files in current stage
            "start_time": None,      # Existing
            "end_time": None,        # Existing
            "error": None            # Existing
        }

    def update_progress_with_files(self, stage: str, files_processed: int = 0,
                                 total_files: int = 0, current_file: str = ""):
        """Enhanced progress with real file tracking"""
        if total_files > 0:
            stage_progress = (files_processed / total_files) * 100
            overall_progress = self.calculate_weighted_progress(stage, stage_progress)
        else:
            overall_progress = self.calculate_weighted_progress(stage)

        with self._parsing_lock:
            self._parsing_status.update({
                "stage": stage,
                "progress": overall_progress,
                "files_processed": files_processed,
                "total_files": total_files,
                "current_file": current_file,
                "status": "parsing"
            })
```

**Progress Update Integration Example:**
```python
def parse_logs(self, log_folder: str):
    # Phase 1: Discovery
    self.update_progress("🎣 Casting nets for logs...", 0)
    total_files = self.discover_parseable_files(log_folder)

    # Phase 2: MinKnow parsing (with file tracking)
    minknow_files = self.get_minknow_files(log_folder)
    for i, file in enumerate(minknow_files):
        self.parse_minknow_file(file)
        self.update_progress_with_files(
            "🐠 Diving for MinKnow logs...",
            files_processed=i+1,
            total_files=len(minknow_files),
            current_file=file
        )

    # Continue for other stages...
```

### File Dialog Service Architecture

```python
class FileDialogService:
    def __init__(self, webview_window=None):
        self.webview_window = webview_window
        self.has_webview = webview_window is not None

    def select_directory(self, initial_path=None):
        if self.has_webview:
            return self.webview_window.create_file_dialog(
                webview.FOLDER_DIALOG,
                directory=initial_path or os.path.expanduser("~")
            )
        else:
            # Fall back to manual input or web File System Access API
            return self._web_fallback()
```

**File Dialog Scenarios:**
- **PyWebView Mode**: Native OS dialogs (Windows Explorer, macOS Finder, Linux file managers)
- **Web Mode**: Manual path entry with validation hints
- **Enhanced Web Mode**: File System Access API for Chrome/Edge browsers

### Progress Accuracy Levels

**Phase 1 Implementation (Stage-Weighted):**
- **Accuracy**: ~70% - Good enough for user feedback
- **Effort**: Low - Uses estimated stage weights
- **Progress Updates**: 5-10 updates during parsing
- **User Experience**: Immediate progress indication

**Phase 2 Implementation (File-Tracking):**
- **Accuracy**: ~90% - Excellent user experience
- **Effort**: Medium - Requires file discovery pre-scan
- **Progress Updates**: Updates per file processed
- **User Experience**: Professional-grade progress bars

**Phase 3 Implementation (Line-Level):**
- **Accuracy**: ~95% - Professional-grade precision
- **Effort**: High - Requires parser modifications
- **Progress Updates**: Very granular (per large file)
- **User Experience**: Real-time file processing feedback

## Future Workflow Extensions

### Timestamp-Centered Analysis 🎯
```
Setup Section Enhancement:
┌─────────────────────────────────────────┐
│ 📅 Quick Jump                           │
│ Timestamp: [2024-01-15 14:30:22]    🔍 │
│ Window: [±30 minutes] [±1 hour] [±6h]  │
│                                         │
│ [🎯 Center View] [📊 Analyze Period]   │
└─────────────────────────────────────────┘
```

**Use Cases:**
- Incident investigation: Jump to known error timestamp
- Performance analysis: Center on peak usage periods
- Before/after comparison: View system state around changes
- Event correlation: Examine logs around specific events

### Workflow Presets 📋
```
┌─ Saved Workflows ─────────────────────┐
│ 🚨 Error Investigation                │
│ ⚡ Performance Deep Dive              │
│ 🔄 Restart Analysis                   │
│ 📈 Resource Trend Review              │
│ 🔍 Custom: "Weekly Health Check"      │
│                                       │
│ [▶️ Run Workflow] [✏️ Edit] [➕ New]   │
└───────────────────────────────────────┘
```

### Advanced Workflow Ideas
- **Auto-detection**: "System detected unusual pattern at 14:30, investigate?"
- **Scheduled analysis**: "Generate weekly performance report"
- **Alert integration**: "Jump to logs when monitoring alerts fire"
- **Multi-log correlation**: "Load multiple log sources for single analysis"
- **Time-travel debugging**: "Step through events leading to error"

## Testing Strategy

### Phase 1-2: Development Workflow
- Focus on `--dev` mode reload behavior
- Test manual parsing trigger reliability
- Validate progress indication accuracy

### Phase 3-4: Error Scenarios
- Invalid log folder paths
- Permission denied scenarios
- Corrupt or missing log files
- Memory/resource constraints

### Phase 5: User Acceptance
- Real log analysis workflows
- Performance with large log sets
- Workflow preset effectiveness
- User experience feedback

## Migration & Deployment

### Breaking Changes
- Remove automatic parsing on startup
- Change default navbar state
- Require explicit user action for log loading

### Backwards Compatibility
- Maintain `BASE_LOG_FOLDER` environment variable support
- Preserve existing analyzer interfaces
- Keep current callback signatures where possible

### Deployment Strategy
- Feature flag for new vs. old behavior
- Gradual rollout with user feedback
- Rollback capability if issues arise

## Phase 3 Implementation Analysis & Decision

### Implementation Review (January 2025)

After detailed analysis of Phase 3 requirements, the following assessment was made regarding the value proposition of each component:

#### **High Value Components** ✅
- **Memory Optimization**: Critical for handling large log directories (1000+ files)
- **Basic State Management**: Foundation needed for future workflow features

#### **Questionable Value Components** ❓
- **State Persistence Across App Restarts**: Minimal benefit for PyWebView desktop applications
- **UI State Transitions**: Cosmetic polish that doesn't add functional value
- **Complex Error Recovery**: Simple retry logic may be sufficient for current needs

#### **Alternative Approaches Considered**

**Option A: Minimal Phase 3**
- Focus only on memory optimization and basic workflow state management
- Duration: 0.5-1 day instead of 2 days
- Skip file persistence, complex UI transitions, and advanced error recovery

**Option B: Skip to Phase 4**
- Implement memory optimization as part of advanced parsing features
- Defer state management until workflow features are actually needed

**Option C: Full Phase 3**
- Proceed as originally planned but acknowledge it's mostly future-proofing

### Decision: Phase 3 Work Parked 🅿️

**Rationale**: Other development priorities take precedence over workflow optimizations at this time.

**Phase 3 Components Deferred:**
- State persistence system with temporary files
- Memory optimization for large datasets
- Advanced error recovery and retry mechanisms
- UI state transition management
- Parsing summary with quarantine reporting

**Impact on Future Phases:**
- **Phase 4**: Can still be implemented but may need to include basic memory optimization
- **Phase 5**: Will require implementing state management as prerequisite
- **Overall**: No blocking issues, just need to revisit memory/state management when workflow features become priority

**Future Considerations:**
When workflow features become a development priority, consider implementing:
1. **Essential Memory Optimization**: Batch processing for large log directories
2. **Minimal State Management**: Save/load analysis configurations for workflow presets
3. **Skip Complex Features**: Avoid over-engineering until proven necessary

This decision maintains development flexibility while focusing resources on higher-priority features.

---

*This document serves as the comprehensive design specification for the Tern user flow redesign initiative.*
