# Structured Log Table Design Specification

## Overview

This document outlines the design and implementation plan for enhancing the structured log table in Tern to display the rich log data parsed by `@log_parser/log_parser`. The current table shows only basic information (timestamp, level, message), but the parsed data contains much more valuable structured information.

## Current State Analysis

### Existing Table Implementation
- **Location**: `tern/tern/layout/main_layout.py:282-302`
- **Current Columns**: Timestamp, Level, Message
- **Data Source**: Currently empty (`data=[]`)
- **Pagination**: Basic with `page_size=10`

### Available Query Capabilities

**CORRECTION**: The `StructuredLogAnalyzer` does NOT have a `query_logs()` method. The `tern/tern/core/structured_logs.py` is incorrectly calling a non-existent method.

**What's Actually Available**:
- **BaseAnalyzer.query()**: Main query method returning pandas DataFrame
- **StructuredLogAnalyzer._df_to_dict_list()**: Converts DataFrame to list of dictionaries
- **Hierarchical caching**: Optimized for drill-down patterns
- **Rich filtering**: By time range, folder_name, log_level, log_event, process_name, library, and dynamic keys

### Correct Data Access Pattern
```python
# Get filtered DataFrame
filters = {"folder_name": "positionA", "log_level": "ERROR"}
df = analyzer.query(start=start_time, end=end_time, **filters)

# Convert to list of dicts for table display
records = analyzer._df_to_dict_list(df)

# Apply pagination in-memory or implement server-side pagination
page_start = offset
page_end = offset + limit
paginated_records = records[page_start:page_end]
```

### Available Structured Data (from DataFrame)
Each log entry contains:
```json
{
  "timestamp": "2025-03-06T13:58:15.277567",  // ISO format from _df_to_dict_list()
  "log_level": "INFO",
  "log_event": "manager_notified_new_auth_token",
  "library": "util",
  "file_name": "basecall_manager_log-7.txt",
  "folder_name": "",
  "process_name": "basecall_manager",
  "message": "\nexpires_in: 7482102541577242440ns\ntoken_type: internal",
  // Dynamic keys are flattened with 'key_' prefix
  "key_expires_in": "7482102541577242440ns",
  "key_token_type": "internal"
}
```

## Design Goals

1. **Rich Information Display**: Show more meaningful data beyond basic message
2. **Range Slider Integration**: Respect time range filtering from UI controls
3. **Efficient Pagination**: Handle large datasets with smart pagination
4. **Expandable Details**: Allow drilling down into complex data
5. **Future Workflow Support**: Foundation for workflow features from user_flow_redesign.md

## Recommended Table Design

### Phase 1: Enhanced Column Layout

#### Primary Columns (Always Visible)
| Column | Width | Content | Purpose |
|--------|-------|---------|---------|
| **Timestamp** | 180px | `HH:MM:SS.mmm` (relative to range) | Time navigation |
| **Level** | 80px | Colored badge (ERROR=red, WARN=orange, INFO=blue) | Quick severity assessment |
| **Event** | 150px | `log_event` field | Semantic log categorization |
| **Process** | 120px | `process_name` | Source system identification |
| **Library** | 100px | `library` field | Component identification |
| **Context** | 250px | Smart key display | Most relevant contextual info |
| **Expand** | 40px | ▼/▲ button | Row expansion toggle |

#### Context Column Design
The **Context** column intelligently shows the most relevant key-value pairs from potentially numerous keys:
- **Priority order**: `run_id` > `flowcell_id` > `position_id` > `host_serial_number` > `sample_id` > others
- **Format**: `key1: value1 • key2: value2` (truncated to fit)
- **Adaptive**: Shows 2-3 keys based on available space and key importance
- **Truncation**: Long values truncated with "..." (full values shown in expanded row)

### Phase 2: Expandable Row Details

**Key Insight**: The `message` field often contains the same information as `keys` but in text format. The expanded row should intelligently handle this.

When a row is expanded, show:

```
┌─ Expanded Row Details ─────────────────────────────────────────┐
│ 📍 Location: control_server_log-5.txt (P2I-00147-B)          │
│                                                                 │
│ 🏷️  All Context Data (14 items):                              │
│ • run_id: 68efcad4-b675-4547-be6d-076e2272c96a                │
│ • flowcell_id: PBA53476                                       │
│ • position_id: P2I-00147-B                                    │
│ • host_serial_number: P2I-00147                               │
│ • output_path: /data/./no_group/no_sample_id/2025...          │
│ • protocol_group_id: no_group                                 │
│ • script_path: checks/flowcell_check/platform_qc_R10_4...     │
│ • (+ 7 more) [Show All] [Show Less]                          │
│                                                                 │
│ 📄 Raw Message: [Show/Hide Toggle]                            │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ (Hidden by default - only show if user clicks toggle)      │ │
│ │ flowcell_id: PBA53476                                       │ │
│ │ host_serial_number: P2I-00147                               │ │
│ │ identity: ['']                                              │ │
│ │ ...                                                         │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ [📋 Copy Keys as JSON] [🔍 Filter by Event] [⏰ Jump to Time] │
└─────────────────────────────────────────────────────────────────┘
```

**Design Rationale**:
- **Primary focus on structured keys**: Since keys are parsed and structured, they're more useful than raw message text
- **Collapsible key list**: Handle numerous keys (10-20+) with show/hide functionality
- **Raw message as secondary**: Hidden by default since it often duplicates key information
- **Smart truncation**: Show most important keys first, with expand/collapse for the rest

### Phase 3: Log Event Grouping

#### Consecutive Event Grouping Strategy

**Problem**: Structured logs often contain many consecutive identical events that create visual clutter and waste table space.

**Example Scenario**:
```
14:23:39  INFO  heartbeat         control_server  util    run_id: abc123
14:23:41  INFO  heartbeat         control_server  util    run_id: abc123
14:23:43  INFO  heartbeat         control_server  util    run_id: abc123
14:23:45  INFO  heartbeat         control_server  util    run_id: abc123
14:23:47  INFO  heartbeat         control_server  util    run_id: abc123
```

**Solution**: Group consecutive identical events into a single row with count and time range.

#### Grouping Criteria

Events are considered "identical" and can be grouped if they share:
1. **log_event** (required)
2. **log_level** (required)
3. **process_name** (required)
4. **library** (required)
5. **All key values** (required - ensures same context)

**Time constraints**:
- Events must be consecutive (no different events in between)
- Maximum time gap between events: 30 seconds (configurable)
- Maximum group size: 50 events (to prevent infinite groups)

#### Grouped Row Display

```
┌─ Grouped Event Row ────────────────────────────────────────────┐
│ 14:23:39-47  INFO  heartbeat (×5)     control_server  util    │
│ Context: run_id: abc123 • host: P2I-00147                     │
│ [▼ Expand Group]                                               │
└────────────────────────────────────────────────────────────────┘
```

**Column Modifications for Grouped Rows**:
- **Timestamp**: Show range format `HH:MM:SS-SS` (start-end)
- **Event**: Add count suffix `event_name (×N)`
- **Context**: Same as individual rows
- **Expand**: Shows all individual events in the group

#### Expanded Group View

When a grouped row is expanded:

```
┌─ Expanded Group Details (5 events, 8 seconds) ───────────────┐
│ 📊 Event Summary:                                             │
│ • First: 14:23:39.741561                                     │
│ • Last:  14:23:47.892014                                     │
│ • Frequency: Every ~2 seconds                                │
│ • Context: run_id: abc123, host_serial_number: P2I-00147     │
│                                                               │
│ 🕐 Individual Events: [Show All] [Show First/Last Only]      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 14:23:39.741  heartbeat  (1/5)                             │ │
│ │ 14:23:41.832  heartbeat  (2/5)                             │ │
│ │ 14:23:43.901  heartbeat  (3/5)                             │ │
│ │ 14:23:45.756  heartbeat  (4/5)                             │ │
│ │ 14:23:47.892  heartbeat  (5/5)                             │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                               │
│ [📋 Copy Group] [🔍 Filter Out] [⏰ Jump to First/Last]      │
└─────────────────────────────────────────────────────────────────┘
```

### Phase 4: Advanced Features

#### Smart Filtering Integration
- **Event-based filtering**: Click log_event to filter table by that event type
- **Process filtering**: Click process_name to show only that process
- **Level filtering**: Click log_level badge to filter by severity
- **Time-based jumping**: Click timestamp to center range slider on that time
- **Key-based filtering**: In expanded row, click any key value to filter by that criteria

#### Contextual Actions (in expanded row)
- **Copy to clipboard**: Keys as JSON, formatted text, or specific fields
- **Jump to timeline**: Center the time range on this log entry
- **Filter by event**: Quick filter to show only this log_event type
- **Related logs**: Show other logs from same process around this time (future feature)

## Pagination Strategy

### Range-Aware Pagination

The table pagination must respect the time range slider selection:

#### Core Principles
1. **Time-First Approach**: Pagination operates only on logs within the selected time range
2. **Smart Page Sizing**: Adaptive page sizes based on data density
3. **Efficient Loading**: Virtual scrolling for large datasets
4. **Range Synchronization**: Updates when range slider changes

#### Implementation Strategy

```python
def get_paginated_logs(
    analyzer: StructuredLogAnalyzer,
    start_time: datetime,
    end_time: datetime,
    page: int = 0,
    page_size: int = 50,
    filters: dict = None
) -> dict:
    """
    Get paginated logs for the specified time range using existing analyzer methods.

    Returns:
    {
        'logs': [...],           # Log entries for this page
        'total_in_range': 1500,  # Total logs in time range (after filters)
        'total_all_time': 10000, # Total logs in dataset
        'page': 0,
        'page_size': 50,
        'pages_in_range': 30,    # Total pages for current range
        'time_range': {...}      # Actual time range of returned logs
    }
    """
    # Step 1: Get filtered DataFrame using existing query() method
    df_filtered = analyzer.query(start=start_time, end=end_time, **(filters or {}))
    total_in_range = len(df_filtered)

    # Step 2: Apply pagination to DataFrame
    start_idx = page * page_size
    end_idx = start_idx + page_size
    df_page = df_filtered.iloc[start_idx:end_idx]

    # Step 3: Convert to list of dicts using existing method
    logs = analyzer._df_to_dict_list(df_page)

    # Step 4: Get total dataset size for context
    df_all = analyzer.query()  # No filters = full dataset
    total_all_time = len(df_all)

    return {
        'logs': logs,
        'total_in_range': total_in_range,
        'total_all_time': total_all_time,
        'page': page,
        'page_size': page_size,
        'pages_in_range': (total_in_range + page_size - 1) // page_size,
        'time_range': {
            'start': start_time.isoformat() if start_time else None,
            'end': end_time.isoformat() if end_time else None
        }
    }
```

#### Page Size Strategy
- **Dense periods** (>100 logs/hour): Use smaller pages (25-30 entries)
- **Sparse periods** (<10 logs/hour): Use larger pages (75-100 entries)
- **User preference**: Allow manual page size selection (10, 25, 50, 100)

#### Performance Optimizations
1. **Index by timestamp**: Primary index on timestamp for range queries
2. **Batch loading**: Pre-load adjacent pages for smooth scrolling
3. **Memory management**: Unload pages outside viewport when memory constrained
4. **Query optimization**: Use database-style range queries, not full-table scans

### Pagination UI Components

#### Page Controls Layout
```
┌─ Pagination Controls ──────────────────────────────────────────┐
│ Showing 51-100 of 1,247 logs in selected range                │
│ (4,829 total logs in dataset)                                 │
│                                                                │
│ [◄◄] [◄] Page 2 of 25 [►] [►►]     [25 ▼] per page          │
│                                                                │
│ [🔍 Jump to Log ID] [📊 Show Density] [⚙️ Table Settings]    │
└────────────────────────────────────────────────────────────────┘
```

#### Advanced Pagination Features
- **Jump to timestamp**: Input field to jump to specific time
- **Log density visualization**: Miniature chart showing log frequency across time range
- **Range optimization**: "Zoom to data" button to fit range to actual log times
- **Bookmarking**: Save specific page/time combinations for later

## Integration with User Workflows

### Foundation for Workflow Features

This table design supports future workflow features outlined in `user_flow_redesign.md`:

#### Timestamp-Centered Analysis
- **Jump to time**: Click any timestamp to center the time range slider
- **Context window**: Show logs ±N minutes around selected timestamp
- **Event correlation**: Highlight related logs when one is selected

#### Workflow Presets
- **Error Investigation**: Auto-filter to ERROR/WARN levels, expand context
- **Performance Analysis**: Focus on specific processes, show resource keys
- **Event Tracing**: Follow log_event sequences across processes

#### Export and Sharing
- **Filtered exports**: Save current table view as CSV/JSON
- **Shareable URLs**: Encode current filters/time range in URL parameters
- **Report generation**: Format selected logs for incident reports

## Implementation Plan

### Phase 1: Basic Enhanced Table (1-2 days)
1. **Update column structure** in `main_layout.py`
2. **Implement data loading** from structured log analyzer
3. **Add basic pagination** with range filtering
4. **Create context column** with dynamic key display

**Deliverable**: Table shows rich log data with proper pagination

### Phase 2: In-Table Expandable Details (1-2 days)
1. **Research latest Dash DataTable capabilities** for in-table expansion ✅ **COMPLETED**
2. **Implement in-table row expansion** using dynamic data manipulation approach
3. **Add keyboard navigation support** (arrow keys for navigation, enter to expand/collapse)
4. **Design expanded content** that fits within table structure
5. **Implement copy/filter actions** within expanded rows

**Deliverable**: Users can drill down into log details within the table using keyboard navigation

#### Research Findings Summary (Phase 2.1 - COMPLETED)

**✅ Research Phase Complete**: Comprehensive investigation of Dash DataTable and DMC expansion capabilities

**Key Findings**:
- **❌ No Native Support**: Dash DataTable does NOT have built-in row expansion functionality
- **📚 Community Workarounds**: Various custom implementations exist but require manual development
- **🎯 Recommended Approach**: Dynamic data manipulation with row insertion (Option B)

**Implementation Options Evaluated**:
1. **Option A**: Native DataTable expansion ❌ - Not available in current Dash version
2. **Option B**: Dynamic data manipulation ✅ - **RECOMMENDED** - Full control, stable foundation
3. **Option C**: Dash-Mantine-React-Table ⚠️ - Available but inactive maintenance, compatibility concerns

**Selected Approach: Dynamic Data Manipulation**
- **Rationale**: Maintains current architecture, full customization, stable foundation
- **Method**: Insert detail rows directly into table data structure using callbacks
- **Benefits**: Works with existing DataTable, complete keyboard navigation support, reliable performance
- **Implementation**: Use row insertion/removal with custom styling for detail rows

#### Implementation Strategy: Dynamic Row Insertion

**Core Concept**: Instead of expanding rows "in place", we dynamically insert detail rows into the table data structure itself. This creates the visual effect of in-table expansion while working within DataTable's capabilities.

**Technical Approach**:

1. **Enhanced Data Structure**: Each table row includes metadata for expansion state tracking
2. **Row Insertion Logic**: When a row is expanded, detail rows are inserted immediately after the main row
3. **Visual Differentiation**: Detail rows are styled differently (indented, background color, typography)
4. **State Management**: Expansion state is tracked and persists across table interactions
5. **Keyboard Navigation**: Custom keyboard event handling for arrow keys and enter/escape

**Visual Design for In-Table Expansion**:
```
┌─ Regular Log Row ──────────────────────────────────────────┐
│ 14:23:39  manager_auth_token  control_server  run_id: a... │ ▼
├─ Detail Row 1 (indented) ─────────────────────────────────┤
│   📍 Location: control_server_log-5.txt (P2I-00147-B)      │
├─ Detail Row 2 (indented) ─────────────────────────────────┤
│   🏷️ run_id: 68efcad4-b675-4547-be6d-076e2272c96a         │
├─ Detail Row 3 (indented) ─────────────────────────────────┤
│   🏷️ flowcell_id: PBA53476                                │
├─ Regular Log Row ──────────────────────────────────────────┤
│ 14:23:41  heartbeat          control_server  run_id: a... │ ▼
└────────────────────────────────────────────────────────────┘
```

**Keyboard Navigation Behavior**:
- **Arrow Keys**: Navigate between all rows (main and detail)
- **Enter**: Toggle expansion of main rows (no effect on detail rows)
- **Escape**: Collapse any expanded rows
- **Tab**: Navigate through interactive elements within detail rows

### Phase 3: Log Event Grouping (1-2 days)
1. **Implement grouping algorithm** for consecutive identical events
2. **Create grouped row display** with count and time range
3. **Add group expansion** showing individual events
4. **Add grouping toggle** (enable/disable grouping)

**Deliverable**: Reduced visual clutter through intelligent event grouping

### Phase 4: Smart Filtering Integration (1 day)
1. **Connect table actions** to global filters
2. **Implement click-to-filter** for all columns
3. **Add jump-to-time** functionality
4. **Create contextual menus** for advanced actions

**Deliverable**: Seamless integration with existing UI controls

### Phase 5: Performance & UX Polish (0.5 days)
1. **Optimize pagination** queries
2. **Add loading states** and error handling
3. **Implement keyboard shortcuts**
4. **Add accessibility features**

**Deliverable**: Production-ready table component

## Technical Implementation Details

### Data Loading Pipeline with Grouping

```python
# Fix the StructuredLogs class first - remove the non-existent async query_logs method
def get_paginated_logs(
    analyzer: StructuredLogAnalyzer,
    start_time: datetime,
    end_time: datetime,
    page: int = 0,
    page_size: int = 50,
    enable_grouping: bool = True,
    **filters
) -> dict:
    """Get paginated structured logs with optional event grouping."""
    # Use the existing query() method that actually exists
    df_filtered = analyzer.query(start=start_time, end=end_time, **filters)

    # Convert to list of dicts using existing _df_to_dict_list method
    logs = analyzer._df_to_dict_list(df_filtered)

    # Apply grouping if enabled
    if enable_grouping:
        logs = group_consecutive_events(logs)

    total_in_range = len(logs)

    # Apply pagination to processed logs
    start_idx = page * page_size
    end_idx = start_idx + page_size
    paginated_logs = logs[start_idx:end_idx]

    return {
        'logs': paginated_logs,
        'total_in_range': total_in_range,
        'page': page,
        'page_size': page_size,
        'pages_in_range': (total_in_range + page_size - 1) // page_size,
        'grouped_events': sum(1 for log in logs if log.get('is_grouped', False)),
        'individual_events': sum(1 for log in logs if not log.get('is_grouped', False))
    }

# In callbacks/data.py or similar
@app.callback(
    [Output('log-table', 'data'),
     Output('log-table', 'page_count'),
     Output('log-table-info', 'children')],
    [Input('time-range-slider', 'value'),
     Input('log-table', 'page_current'),
     Input('position-filter', 'value'),
     # ... other filter inputs
    ]
)
def update_log_table(time_range, current_page, position_filter, ...):
    # Convert slider values to datetime
    start_time, end_time = convert_slider_to_datetime(time_range)

    # Build filters dictionary
    filters = {}
    if position_filter:
        filters['folder_name'] = position_filter
    # Add other filters as needed

    # Get the analyzer from container
    analyzer = container.get_structured_log_analyzer()
    if not analyzer:
        return [], 0, "No log data available"

    # Query structured logs with pagination using existing methods
    result = get_paginated_logs(
        analyzer=analyzer,
        start_time=start_time,
        end_time=end_time,
        page=current_page,
        page_size=50,
        **filters
    )

    # Format data for DataTable
    formatted_data = format_logs_for_table(result['logs'])

    return formatted_data, result['pages_in_range'], create_info_text(result)
```

### Column Configuration

```python
def create_log_table_columns():
    return [
        {
            "name": "Time",
            "id": "timestamp",
            "type": "datetime",
            "format": FormatTemplate.datetime(datetime_format='HH:mm:ss.SSS')
        },
        {
            "name": "Level",
            "id": "log_level",
            "presentation": "markdown"  # For colored badges
        },
        {
            "name": "Event",
            "id": "log_event",
            "presentation": "markdown"  # For clickable links
        },
        {
            "name": "Process",
            "id": "process_name"
        },
        {
            "name": "Library",
            "id": "library"
        },
        {
            "name": "Context",
            "id": "context_summary",  # Computed field
            "presentation": "markdown"
        },
        {
            "name": "",
            "id": "expand_button",
            "presentation": "markdown"  # For expand/collapse button
        }
    ]
```

### Context Summary Generation

```python
def generate_context_summary(log_entry: dict) -> str:
    """Generate a summary of key-value pairs for the context column."""
    # Extract keys from both the keys dict and flattened key_ columns
    keys = {}

    # Add keys from the original keys dict if present
    if 'keys' in log_entry and log_entry['keys']:
        keys.update(log_entry['keys'])

    # Add flattened key_ columns from DataFrame
    for col, value in log_entry.items():
        if col.startswith('key_') and value is not None:
            key_name = col[4:]  # Remove 'key_' prefix
            keys[key_name] = value

    if not keys:
        return ""

    # Priority order for key display based on common log patterns
    priority_keys = [
        'run_id', 'flowcell_id', 'position_id', 'host_serial_number',
        'sample_id', 'device_id', 'hostname', 'version', 'output_path'
    ]

    # Build summary with priority keys first
    summary_parts = []
    for key in priority_keys:
        if key in keys:
            value = str(keys[key])
            # Truncate long values (like paths) for context column
            if len(value) > 20:
                value = value[:17] + "..."
            summary_parts.append(f"{key}: {value}")
            if len(summary_parts) >= 2:  # Max 2 keys in summary for space
                break

    # Add additional keys if space allows
    remaining_keys = set(keys.keys()) - set(priority_keys)
    remaining_slots = 3 - len(summary_parts)  # Allow up to 3 total keys
    for key in list(remaining_keys)[:remaining_slots]:
        value = str(keys[key])
        if len(value) > 15:
            value = value[:12] + "..."
        summary_parts.append(f"{key}: {value}")

    # Format with separator and overall truncation
    summary = " • ".join(summary_parts)
    if len(summary) > 60:  # Increased limit for wider context column
        summary = summary[:57] + "..."

    return summary
```

### Event Grouping Algorithm

```python
def group_consecutive_events(
    logs: list[dict],
    max_time_gap_seconds: int = 30,
    max_group_size: int = 50
) -> list[dict]:
    """
    Group consecutive identical log events to reduce table clutter.

    Args:
        logs: List of log entries (sorted by timestamp)
        max_time_gap_seconds: Maximum time gap between events to group
        max_group_size: Maximum number of events in a single group

    Returns:
        List of logs with grouped events replaced by group entries
    """
    if not logs:
        return logs

    grouped_logs = []
    current_group = []

    for i, log in enumerate(logs):
        # Check if this log can be added to current group
        if current_group and can_group_with_current(log, current_group, max_time_gap_seconds, max_group_size):
            current_group.append(log)
        else:
            # Finalize current group if it exists
            if current_group:
                if len(current_group) >= 2:  # Only group if 2+ events
                    grouped_logs.append(create_grouped_entry(current_group))
                else:
                    grouped_logs.extend(current_group)  # Add individual event

            # Start new group
            current_group = [log]

    # Handle final group
    if current_group:
        if len(current_group) >= 2:
            grouped_logs.append(create_grouped_entry(current_group))
        else:
            grouped_logs.extend(current_group)

    return grouped_logs

def can_group_with_current(
    log: dict,
    current_group: list[dict],
    max_time_gap: int,
    max_group_size: int
) -> bool:
    """Check if a log entry can be grouped with the current group."""
    if len(current_group) >= max_group_size:
        return False

    last_log = current_group[-1]

    # Parse timestamps for time gap check
    try:
        from datetime import datetime
        current_time = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
        last_time = datetime.fromisoformat(last_log['timestamp'].replace('Z', '+00:00'))
        time_gap = (current_time - last_time).total_seconds()

        if time_gap > max_time_gap:
            return False
    except (ValueError, KeyError):
        return False

    # Check if events are identical (same grouping criteria)
    return events_are_identical(log, last_log)

def events_are_identical(log1: dict, log2: dict) -> bool:
    """Check if two log events are identical for grouping purposes."""
    # Required fields that must match
    required_fields = ['log_event', 'log_level', 'process_name', 'library']

    for field in required_fields:
        if log1.get(field) != log2.get(field):
            return False

    # Check all key_ fields (dynamic keys must match)
    log1_keys = {k: v for k, v in log1.items() if k.startswith('key_')}
    log2_keys = {k: v for k, v in log2.items() if k.startswith('key_')}

    return log1_keys == log2_keys

def create_grouped_entry(group: list[dict]) -> dict:
    """Create a grouped entry from a list of identical events."""
    if not group:
        return {}

    first_event = group[0]
    last_event = group[-1]

    # Calculate time range
    try:
        from datetime import datetime
        start_time = datetime.fromisoformat(first_event['timestamp'].replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(last_event['timestamp'].replace('Z', '+00:00'))
        duration = (end_time - start_time).total_seconds()

        # Format timestamp range
        timestamp_range = f"{start_time.strftime('%H:%M:%S')}-{end_time.strftime('%S')}"
        if duration >= 60:  # Show minutes if duration > 1 minute
            timestamp_range = f"{start_time.strftime('%H:%M:%S')}-{end_time.strftime('%M:%S')}"
    except (ValueError, KeyError):
        timestamp_range = f"{first_event.get('timestamp', '')} - {last_event.get('timestamp', '')}"

    # Create grouped entry
    grouped_entry = first_event.copy()
    grouped_entry.update({
        'timestamp': timestamp_range,
        'log_event': f"{first_event.get('log_event', '')} (×{len(group)})",
        'is_grouped': True,
        'group_count': len(group),
        'group_duration_seconds': duration if 'duration' in locals() else 0,
        'grouped_events': group,  # Store original events for expansion
        'first_timestamp': first_event['timestamp'],
        'last_timestamp': last_event['timestamp']
    })

    return grouped_entry

def format_logs_for_table(logs: list[dict]) -> list[dict]:
    """Format logs for DataTable display, handling both individual and grouped entries."""
    formatted_logs = []

    for log in logs:
        formatted_log = log.copy()

        # Generate context summary
        formatted_log['context_summary'] = generate_context_summary(log)

        # Add expand button
        if log.get('is_grouped', False):
            formatted_log['expand_button'] = f"▼ ({log['group_count']} events)"
        else:
            formatted_log['expand_button'] = "▼"

        # Format timestamp for display
        if not log.get('is_grouped', False):
            # Individual event - show time only (date is in range slider)
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                formatted_log['timestamp_display'] = dt.strftime('%H:%M:%S.%f')[:-3]  # Include milliseconds
            except (ValueError, KeyError):
                formatted_log['timestamp_display'] = log.get('timestamp', '')
        else:
            # Grouped event - already formatted as range
            formatted_log['timestamp_display'] = formatted_log['timestamp']

        formatted_logs.append(formatted_log)

    return formatted_logs
```

## Key Implementation Notes

### Critical Fix Required

**URGENT**: The `tern/tern/core/structured_logs.py` file has a **broken `query_logs()` method** that calls a non-existent async method on the analyzer. This must be fixed first:

```python
# REMOVE this broken method from StructuredLogs class:
async def query_logs(self, ...):  # ❌ BROKEN - analyzer doesn't have this method
    return await self.analyzer.query_logs(...)  # ❌ This method doesn't exist

# REPLACE with this working method:
def query_logs(
    self,
    start: datetime,
    end: datetime,
    page: int = 0,
    page_size: int = 50,
    **filters
) -> dict[str, Any]:
    """Query logs using existing analyzer capabilities."""
    try:
        # Use existing query() method that actually exists
        df_filtered = self.analyzer.query(start=start, end=end, **filters)
        total_in_range = len(df_filtered)

        # Apply pagination
        start_idx = page * page_size
        end_idx = start_idx + page_size
        df_page = df_filtered.iloc[start_idx:end_idx]

        # Convert to list of dicts using existing method
        logs = self.analyzer._df_to_dict_list(df_page)

        return {
            'logs': logs,
            'total_in_range': total_in_range,
            'page': page,
            'page_size': page_size,
            'pages_in_range': (total_in_range + page_size - 1) // page_size,
        }
    except Exception as e:
        logger.exception("Error querying logs")
        return {"error": str(e), "logs": []}
```

### Available Query Capabilities Summary

The analyzer provides these **existing methods** that work perfectly for the table:

1. **`analyzer.query(start, end, **filters)`** - Returns filtered pandas DataFrame
2. **`analyzer._df_to_dict_list(df)`** - Converts DataFrame to list of dicts
3. **`analyzer.get_filters()`** - Gets available column names for filtering
4. **`analyzer.get_positions()`** - Gets unique folder_name values
5. **`analyzer.get_log_levels()`** - Gets unique log_level values
6. **Hierarchical caching** - Already optimized for drill-down patterns

## Grouping Configuration & User Control

### Grouping Settings

**User Controls**:
- **Enable/Disable Toggle**: Allow users to turn grouping on/off
- **Group Size Threshold**: Minimum events required to form a group (default: 2)
- **Time Gap Limit**: Maximum time between events to group (default: 30s)
- **Max Group Size**: Prevent overly large groups (default: 50 events)

**Settings UI**:
```
┌─ Table Settings ───────────────────────────────────────────────┐
│ 🔧 Log Grouping                                               │
│ ☑️ Enable event grouping                                      │
│                                                                │
│ Minimum group size: [2  ▼] events                            │
│ Max time gap:       [30 ▼] seconds                           │
│ Max group size:     [50 ▼] events                            │
│                                                                │
│ [Apply Settings] [Reset to Defaults]                          │
└────────────────────────────────────────────────────────────────┘
```

### Common Grouping Scenarios

**High-Value Grouping Examples**:
1. **Heartbeat events**: Regular status pings from services
2. **Progress updates**: Sequential progress reports (1%, 2%, 3%...)
3. **Polling events**: Regular data polling with identical parameters
4. **Retry attempts**: Multiple identical retry events
5. **Batch processing**: Identical operations on different items

**Events NOT to Group**:
1. **Different error types**: Even if same process/library
2. **Different key values**: run_id changes, different contexts
3. **Non-consecutive events**: Broken by different event types
4. **Large time gaps**: Events too far apart in time

### Grouping Impact Analysis

**Space Savings Example**:
```
Before Grouping (50 heartbeat events):
┌────────────────────────────────────────────────────────────────┐
│ 14:23:39  INFO  heartbeat  control_server  util               │
│ 14:23:41  INFO  heartbeat  control_server  util               │
│ 14:23:43  INFO  heartbeat  control_server  util               │
│ ... (47 more identical rows) ...                              │
│ 14:25:37  INFO  heartbeat  control_server  util               │
└────────────────────────────────────────────────────────────────┘
Table rows: 50

After Grouping:
┌────────────────────────────────────────────────────────────────┐
│ 14:23:39-25:37  INFO  heartbeat (×50)  control_server  util   │
└────────────────────────────────────────────────────────────────┘
Table rows: 1

Space reduction: 98% (49 rows saved)
```

**Typical Savings**:
- **Heartbeat/status logs**: 90-95% reduction
- **Progress sequences**: 80-90% reduction
- **Polling events**: 85-95% reduction
- **Overall average**: 40-60% reduction in total table rows

## Future Enhancement Opportunities

### Advanced Analytics Integration
- **Log pattern detection**: Highlight unusual patterns or sequences
- **Correlation analysis**: Show relationships between different log events
- **Anomaly highlighting**: Flag unusual log entries based on historical patterns
- **Integration with existing analyzers**: Use `detect_log_level_anomalies()` and `detect_time_series_anomalies()`

### Collaborative Features
- **Annotations**: Add comments/notes to specific log entries
- **Sharing**: Share specific log views with team members
- **Incident linking**: Connect logs to external incident tracking systems

### Performance Optimizations
- **Full-text search**: Search across message content and keys
- **Intelligent caching**: Leverage existing hierarchical cache system
- **Background loading**: Pre-load likely next pages based on user behavior

## Testing Strategy

### Functional Testing
- **Range filtering**: Verify logs are properly filtered by time range
- **Pagination**: Test page navigation with different data sizes
- **Expansion**: Validate row expansion and detail display
- **Actions**: Test all click-to-filter and context actions

### Performance Testing
- **Large datasets**: Test with 10,000+ log entries
- **Memory usage**: Monitor memory consumption during pagination
- **Loading times**: Measure table update performance
- **Concurrent users**: Test multiple browser tabs/users

### User Experience Testing
- **Workflow scenarios**: Test common log analysis tasks
- **Accessibility**: Keyboard navigation and screen reader support
- **Mobile responsiveness**: Table usability on different screen sizes
- **Error handling**: Graceful handling of loading failures and empty data

---

This design provides a solid foundation for displaying rich structured log data while maintaining performance and supporting future workflow enhancements. The phased approach allows for iterative development and user feedback incorporation.
