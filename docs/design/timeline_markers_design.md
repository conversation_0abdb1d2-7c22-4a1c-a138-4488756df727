# Timeline Markers Design

## Overview

Timeline Markers extend the TimelineAnalyzer to track single-point events that occur within timeline events. Unlike timeline events which have start/end pairs, markers represent unique timepoints with associated metadata.

## Problem Statement

The original TimelineAnalyzer could only track events with start/end pairs (e.g., protocol start → protocol end). However, many important events are single timepoints that occur within these timelines:

- Protocol phase changes (PHASE_INITIALISING → PHASE_RUNNING → PHASE_FINALIZING)
- Error events during execution
- Checkpoint/milestone events
- Progress updates

## Solution Design

### Data Structures

#### TimelineMarker
```python
@dataclass
class TimelineMarker:
    id: str                    # Unique identifier
    name: str                  # Marker type (e.g., "protocol_phase")
    timestamp: datetime        # When the marker occurred
    metadata: dict[str, Any]   # Extracted metadata (e.g., {"phase": "PHASE_INITIALISING"})
    log_event: str            # Original log event name
    position_id: str          # Position this marker belongs to
    original_log_entry: Any   # Full original log entry
```

#### TimelineEvent Enhancement
```python
@dataclass
class TimelineEvent:
    # ... existing fields ...
    markers: list[TimelineMarker]  # NEW: markers within this timeline
```

### Configuration

Markers are configured in the same TOML file as timeline events:

```toml
[[timeline_event]]
name = "protocol"
start_event = "protocol_started"
end_event = "protocol_finished_successfully"
identifier_key = "run_id"
children = ["data_acquisition"]
markers = ["protocol_phase"]  # NEW: markers this timeline can contain

[[marker]]
name = "protocol_phase"
log_event = "protocol_phase_changed"
process_filter = "control_server"  # Optional process filtering
```

### Architecture

#### Relationship Model
- **Parent → Child**: Timeline events declare which markers they can contain
- **Time-Based Association**: Markers are automatically associated with timeline events if they fall within the timeline's time range
- **Hierarchical**: Markers can be associated with child timeline events as well as parent events

#### Collection Algorithm
1. **Timeline Building**: After timeline events are created and hierarchy is established
2. **Marker Discovery**: For each timeline event, find all configured marker types
3. **Time Filtering**: Filter markers to those within the timeline's start/end time range
4. **Process Filtering**: Apply process filters if specified in marker configuration
5. **Metadata Extraction**: Extract metadata from original log entries (not DataFrame to avoid pandas NaN issues)
6. **Association**: Add markers to the timeline event's markers list

### Implementation Details

#### Marker Collection Flow
```python
def _collect_timeline_markers(self, timeline_events, pos_df, minknow_entries):
    for timeline_event in timeline_events:
        # Get timeline config to find which markers it can contain
        timeline_config = get_timeline_config(timeline_event.name)

        for marker_name in timeline_config.get("markers", []):
            marker_config = get_marker_config(marker_name)

            # Find marker events in DataFrame
            marker_events = pos_df[pos_df["log_event"] == marker_config["log_event"]]

            # Apply process filter
            if "process_filter" in marker_config:
                marker_events = filter_by_process(marker_events, marker_config["process_filter"])

            # Filter by time range
            for marker_event in marker_events:
                if timeline_event.start_time <= marker_event.timestamp <= timeline_event.end_time:
                    # Extract metadata from original log entry
                    original_entry = find_original_entry(marker_event, minknow_entries)
                    metadata = extract_metadata(original_entry)

                    # Create and add marker
                    marker = TimelineMarker(...)
                    timeline_event.markers.append(marker)
```

#### Metadata Extraction
Markers use the same metadata extraction approach as timeline events:
- Extract directly from original `StructuredLogEntry` objects
- Avoid pandas DataFrame values to prevent NaN issues
- Apply `clean_metadata_dict()` for consistent cleaning

### API Design

#### Query Methods
```python
# Query all markers
all_markers = analyzer.query_markers()

# Query by marker type
phase_markers = analyzer.query_markers(marker_type="protocol_phase")

# Query by timeline event
markers = analyzer.query_markers(timeline_event_id="specific_id")

# Query by metadata
init_markers = analyzer.query_markers(metadata_filters={"phase": "PHASE_INITIALISING"})

# Query by time range
recent_markers = analyzer.query_markers(start_time=datetime.now() - timedelta(hours=1))

# Get markers for specific timeline event
markers = analyzer.get_markers_for_timeline_event(timeline_event_id)
```

#### Serialization
Markers are included in all timeline event serialization:

```python
# Timeline event data includes markers
timeline_data = analyzer.get_timeline_event_timeline()
# timeline_data[0]["markers"] = [{"id": "...", "name": "...", "timestamp": "...", ...}]

# Tree structure includes markers
tree_data = analyzer.get_timeline_event_tree()
# tree_data["position"]["markers"] = [...]
```

### Use Cases

#### Protocol Phase Tracking
```python
# Track protocol phases within a protocol timeline
protocol_events = analyzer.query_timeline_events(timeline_event_type="protocol")
protocol = protocol_events[0]

# Get all phase changes
for marker in protocol.markers:
    if marker.name == "protocol_phase":
        print(f"Phase changed to {marker.metadata['phase']} at {marker.timestamp}")

# Query specific phase
init_markers = analyzer.query_markers(
    timeline_event_id=protocol.id,
    metadata_filters={"phase": "PHASE_INITIALISING"}
)
```

#### Error Tracking
```python
# Configure error markers
[[marker]]
name = "error_event"
log_event = "error_occurred"
process_filter = "control_server"

# Query errors within a timeline
error_markers = analyzer.query_markers(
    timeline_event_id=timeline_id,
    marker_type="error_event"
)
```

#### Progress Monitoring
```python
# Configure progress markers
[[marker]]
name = "progress_update"
log_event = "progress_changed"

# Track progress within a timeline
progress_markers = analyzer.query_markers(
    timeline_event_id=timeline_id,
    marker_type="progress_update"
)
for marker in sorted(progress_markers, key=lambda m: m.timestamp):
    print(f"Progress: {marker.metadata['percentage']}% at {marker.timestamp}")
```

### Benefits

1. **Single-Point Event Tracking**: Capture events that don't have start/end pairs
2. **Contextual Association**: Markers are automatically associated with relevant timeline events
3. **Rich Metadata**: Full metadata extraction from original log entries
4. **Flexible Querying**: Multiple query methods with various filters
5. **Consistent API**: Follows same patterns as timeline events
6. **Process Filtering**: Respect process boundaries like timeline events
7. **Serialization Support**: Included in all timeline event serialization methods

### Technical Considerations

#### Performance
- Marker collection happens after timeline building to leverage existing DataFrame filtering
- Uses same metadata extraction optimizations as timeline events
- Efficient time-based filtering using pandas DataFrame operations

#### Memory
- Markers reuse existing log entry data structures
- Metadata is cleaned and deduplicated using existing utilities
- No additional DataFrame storage required

#### Extensibility
- Configuration-driven marker types
- Support for new marker types via TOML configuration
- Hierarchical marker association (parent/child timeline events)

### Testing

Comprehensive test coverage includes:
- Basic marker functionality and collection
- Marker querying with various filters
- Serialization and data structure integrity
- Process filtering and time range filtering
- Integration with incomplete timeline events
- Configuration validation and error handling

The implementation maintains backward compatibility while adding powerful new capabilities for tracking single-point events within timeline contexts.
