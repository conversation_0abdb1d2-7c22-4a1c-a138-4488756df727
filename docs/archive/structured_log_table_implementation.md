# Structured Log Table Implementation Plan - COMPLETED ✅

**Implementation Status**: COMPLETED - The enhanced structured log table has been implemented with all major features functional.

## Overview

This document provided the detailed implementation plan for the enhanced structured log table feature in Tern. The implementation has been completed including rich column layout, expandable details, event grouping, and filtering integration.

## Critical Pre-Implementation Fix

### URGENT: Fix Broken StructuredLogs Class

**Issue**: `tern/tern/core/structured_logs.py` contains a broken `query_logs()` method that calls non-existent analyzer methods.

**File**: `tern/tern/core/structured_logs.py:33-60`

**Required Fix**:
```python
# REMOVE the broken async method:
async def query_logs(self, ...):  # ❌ BROKEN
    return await self.analyzer.query_logs(...)  # ❌ Method doesn't exist

# REPLACE with working implementation:
def query_logs(
    self,
    start: datetime,
    end: datetime,
    page: int = 0,
    page_size: int = 50,
    **filters
) -> dict[str, Any]:
    """Query logs using existing analyzer capabilities."""
    try:
        # Use existing query() method that actually exists
        df_filtered = self.analyzer.query(start=start, end=end, **filters)
        total_in_range = len(df_filtered)

        # Apply pagination
        start_idx = page * page_size
        end_idx = start_idx + page_size
        df_page = df_filtered.iloc[start_idx:end_idx]

        # Convert to list of dicts using existing method
        logs = self.analyzer._df_to_dict_list(df_page)

        return {
            'logs': logs,
            'total_in_range': total_in_range,
            'page': page,
            'page_size': page_size,
            'pages_in_range': (total_in_range + page_size - 1) // page_size,
        }
    except Exception as e:
        logger.exception("Error querying logs")
        return {"error": str(e), "logs": []}
```

**Verification**: Test that structured logs can be queried without errors.

---

## Phase 1: Basic Enhanced Table (1-2 days)

### Objectives
- Replace basic 3-column table with rich 5-column layout (removed level and library)
- Implement proper data loading from StructuredLogAnalyzer
- Add range-aware pagination with loading overlay
- Create intelligent context column with row background colors
- Fix critical StructuredLogs.query_logs() method

### Requirements

#### 1.1 Update Table Layout
**File**: `tern/tern/layout/main_layout.py:282-302`

**Current Structure**:
```python
dash_table.DataTable(
    id="log-table",
    columns=[
        {"name": "Timestamp", "id": "timestamp"},
        {"name": "Level", "id": "level"},
        {"name": "Message", "id": "message"},
    ],
    data=[],
    page_size=10,
)
```

**New Structure** (Updated based on user feedback):
```python
def create_log_table_columns():
    return [
        {
            "name": "Time",
            "id": "timestamp_display",
            "type": "text",  # Custom formatted time
        },
        # Level column REMOVED - using row background colors instead
        {
            "name": "Event",
            "id": "log_event",
            "type": "text"  # Log event type
        },
        {
            "name": "Event",
            "id": "log_event",
            "presentation": "markdown",  # For clickable links
            "type": "text"
        },
        {
            "name": "Process",
            "id": "process_name",
            "type": "text"
        },
        {
            "name": "Library",
            "id": "library",
            "type": "text"
        },
        {
            "name": "Context",
            "id": "context_summary",  # Computed field
            "presentation": "markdown",
            "type": "text"
        },
        {
            "name": "",
            "id": "expand_button",
            "presentation": "markdown",  # For expand/collapse button
            "type": "text"
        }
    ]

# Update the table creation
def create_log_table_section():
    return dmc.Stack(
        gap="md",
        children=[
            dmc.Text("Log Entries", fw=500, size="lg"),
            dash_table.DataTable(
                id="log-table",
                columns=create_log_table_columns(),
                data=[],
                page_size=50,  # Increased from 10
                page_action="custom",  # Enable custom pagination
                style_table={"overflowX": "auto"},
                style_cell={
                    'textAlign': 'left',
                    'padding': '8px',
                    'fontFamily': 'monospace'
                },
                style_data_conditional=[
                    {
                        'if': {'filter_query': '{log_level} = ERROR'},
                        'backgroundColor': '#ffebee',
                        'color': 'black',
                    },
                    {
                        'if': {'filter_query': '{log_level} = WARN'},
                        'backgroundColor': '#fff3e0',
                        'color': 'black',
                    }
                ]
            ),
            # Add pagination info
            dmc.Text(id="log-table-info", size="sm", c="dimmed"),
        ],
    )
```

#### 1.2 Implement Context Summary Function
**New File**: `tern/tern/utils/log_formatting.py`

```python
def generate_context_summary(log_entry: dict) -> str:
    """Generate a summary of key-value pairs for the context column."""
    # Extract keys from both the keys dict and flattened key_ columns
    keys = {}

    # Add keys from the original keys dict if present
    if 'keys' in log_entry and log_entry['keys']:
        keys.update(log_entry['keys'])

    # Add flattened key_ columns from DataFrame
    for col, value in log_entry.items():
        if col.startswith('key_') and value is not None:
            key_name = col[4:]  # Remove 'key_' prefix
            keys[key_name] = value

    if not keys:
        return ""

    # Priority order for key display based on common log patterns
    priority_keys = [
        'run_id', 'flowcell_id', 'position_id', 'host_serial_number',
        'sample_id', 'device_id', 'hostname', 'version', 'output_path'
    ]

    # Build summary with priority keys first
    summary_parts = []
    for key in priority_keys:
        if key in keys:
            value = str(keys[key])
            # Truncate long values (like paths) for context column
            if len(value) > 20:
                value = value[:17] + "..."
            summary_parts.append(f"{key}: {value}")
            if len(summary_parts) >= 2:  # Max 2 keys in summary for space
                break

    # Add additional keys if space allows
    remaining_keys = set(keys.keys()) - set(priority_keys)
    remaining_slots = 3 - len(summary_parts)  # Allow up to 3 total keys
    for key in list(remaining_keys)[:remaining_slots]:
        value = str(keys[key])
        if len(value) > 15:
            value = value[:12] + "..."
        summary_parts.append(f"{key}: {value}")

    # Format with separator and overall truncation
    summary = " • ".join(summary_parts)
    if len(summary) > 60:  # Increased limit for wider context column
        summary = summary[:57] + "..."

    return summary

def format_logs_for_table(logs: list[dict]) -> list[dict]:
    """Format logs for DataTable display."""
    formatted_logs = []

    for log in logs:
        formatted_log = log.copy()

        # Generate context summary
        formatted_log['context_summary'] = generate_context_summary(log)

        # Add expand button
        formatted_log['expand_button'] = "▼"

        # Format timestamp for display (time only, date in range slider)
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
            formatted_log['timestamp_display'] = dt.strftime('%H:%M:%S.%f')[:-3]  # Include milliseconds
        except (ValueError, KeyError):
            formatted_log['timestamp_display'] = log.get('timestamp', '')

        # Format log level with color
        level = log.get('log_level', '')
        if level == 'ERROR':
            formatted_log['log_level'] = f"🔴 {level}"
        elif level == 'WARN':
            formatted_log['log_level'] = f"🟡 {level}"
        elif level == 'INFO':
            formatted_log['log_level'] = f"🔵 {level}"
        else:
            formatted_log['log_level'] = level

        formatted_logs.append(formatted_log)

    return formatted_logs
```

#### 1.3 Implement Data Loading Callback
**File**: `tern/tern/callbacks/data.py` (or create new file)

```python
@app.callback(
    [Output('log-table', 'data'),
     Output('log-table', 'page_count'),
     Output('log-table-info', 'children')],
    [Input('time-range-slider', 'value'),
     Input('log-table', 'page_current'),
     Input('position-filter', 'value'),
     Input('log-table', 'page_size')],
    prevent_initial_call=False
)
def update_log_table(time_range, current_page, position_filter, page_size):
    """Update log table based on time range and filters."""
    try:
        # Convert slider values to datetime
        start_time, end_time = convert_slider_to_datetime(time_range)

        # Build filters dictionary
        filters = {}
        if position_filter:
            filters['folder_name'] = position_filter

        # Get the analyzer from container
        analyzer = container.get_structured_log_analyzer()
        if not analyzer:
            return [], 0, "No log data available"

        # Query structured logs with pagination using existing methods
        df_filtered = analyzer.query(start=start_time, end=end_time, **filters)
        total_in_range = len(df_filtered)

        # Apply pagination
        start_idx = current_page * page_size
        end_idx = start_idx + page_size
        df_page = df_filtered.iloc[start_idx:end_idx]

        # Convert to list of dicts
        logs = analyzer._df_to_dict_list(df_page)

        # Format for table display
        from tern.utils.log_formatting import format_logs_for_table
        formatted_data = format_logs_for_table(logs)

        # Calculate pagination info
        total_pages = (total_in_range + page_size - 1) // page_size

        # Create info text
        start_entry = start_idx + 1 if logs else 0
        end_entry = min(start_idx + len(logs), total_in_range)
        info_text = f"Showing {start_entry}-{end_entry} of {total_in_range} logs in selected range"

        return formatted_data, total_pages, info_text

    except Exception as e:
        logger.exception("Error updating log table")
        return [], 0, f"Error loading logs: {str(e)}"

def convert_slider_to_datetime(time_range):
    """Convert time range slider values to datetime objects."""
    # This function needs to be implemented based on your slider implementation
    # Should return (start_datetime, end_datetime)
    pass
```

### Testing Strategy - Phase 1

#### Unit Tests
**File**: `tests/test_log_formatting.py`

```python
import pytest
from datetime import datetime
from tern.utils.log_formatting import generate_context_summary, format_logs_for_table

class TestLogFormatting:
    def test_generate_context_summary_with_priority_keys(self):
        """Test context summary generation prioritizes important keys."""
        log_entry = {
            'run_id': 'abc123',
            'flowcell_id': 'PBA53476',
            'hostname': 'P2I-00147',
            'version': '6.4.0',
            'key_run_id': 'abc123',
            'key_flowcell_id': 'PBA53476'
        }

        summary = generate_context_summary(log_entry)

        assert 'run_id: abc123' in summary
        assert 'flowcell_id: PBA53476' in summary
        assert ' • ' in summary  # Separator

    def test_generate_context_summary_truncates_long_values(self):
        """Test that long values are truncated with ellipsis."""
        log_entry = {
            'key_output_path': '/very/long/path/that/should/be/truncated/because/it/is/too/long'
        }

        summary = generate_context_summary(log_entry)

        assert '...' in summary
        assert len(summary) <= 60

    def test_format_logs_for_table_adds_required_fields(self):
        """Test that formatting adds all required fields for table display."""
        logs = [{
            'timestamp': '2025-03-06T13:58:15.277567',
            'log_level': 'INFO',
            'log_event': 'test_event',
            'process_name': 'test_process',
            'library': 'test_lib'
        }]

        formatted = format_logs_for_table(logs)

        assert len(formatted) == 1
        assert 'context_summary' in formatted[0]
        assert 'expand_button' in formatted[0]
        assert 'timestamp_display' in formatted[0]
        assert '13:58:15' in formatted[0]['timestamp_display']

    def test_format_logs_for_table_handles_log_levels(self):
        """Test that log levels are formatted with appropriate colors."""
        logs = [
            {'log_level': 'ERROR', 'timestamp': '2025-03-06T13:58:15.277567'},
            {'log_level': 'WARN', 'timestamp': '2025-03-06T13:58:15.277567'},
            {'log_level': 'INFO', 'timestamp': '2025-03-06T13:58:15.277567'}
        ]

        formatted = format_logs_for_table(logs)

        assert '🔴 ERROR' in formatted[0]['log_level']
        assert '🟡 WARN' in formatted[1]['log_level']
        assert '🔵 INFO' in formatted[2]['log_level']
```

#### Integration Tests
**File**: `tests/test_log_table_integration.py`

```python
import pytest
from unittest.mock import Mock, patch
from tern.callbacks.data import update_log_table

class TestLogTableIntegration:
    @patch('tern.callbacks.data.container')
    def test_update_log_table_with_valid_data(self, mock_container):
        """Test log table update with valid analyzer data."""
        # Setup mock analyzer
        mock_analyzer = Mock()
        mock_df = Mock()
        mock_df.iloc = Mock()
        mock_df.iloc.return_value = Mock()

        mock_analyzer.query.return_value = mock_df
        mock_analyzer._df_to_dict_list.return_value = [
            {
                'timestamp': '2025-03-06T13:58:15.277567',
                'log_level': 'INFO',
                'log_event': 'test_event',
                'process_name': 'test_process',
                'library': 'test_lib'
            }
        ]

        mock_container.get_structured_log_analyzer.return_value = mock_analyzer

        # Test callback
        with patch('tern.callbacks.data.convert_slider_to_datetime') as mock_convert:
            mock_convert.return_value = (datetime.now(), datetime.now())

            data, page_count, info = update_log_table([0, 100], 0, None, 50)

            assert len(data) == 1
            assert page_count >= 0
            assert "Showing" in info

    @patch('tern.callbacks.data.container')
    def test_update_log_table_with_no_analyzer(self, mock_container):
        """Test log table update when no analyzer is available."""
        mock_container.get_structured_log_analyzer.return_value = None

        data, page_count, info = update_log_table([0, 100], 0, None, 50)

        assert data == []
        assert page_count == 0
        assert "No log data available" in info
```

#### Manual Testing Checkpoints

1. **Table Display**:
   - [ ] Navigate to the main application
   - [ ] Load logs using the existing log loading mechanism
   - [ ] Verify table shows 7 columns: Time, Level, Event, Process, Library, Context, Expand
   - [ ] Check that time format shows HH:MM:SS.mmm
   - [ ] Verify log levels show colored indicators

2. **Context Column**:
   - [ ] Check that Context column shows relevant key-value pairs
   - [ ] Verify truncation works for long values
   - [ ] Confirm priority keys (run_id, flowcell_id) appear first

3. **Pagination**:
   - [ ] Test pagination controls work
   - [ ] Change page size and verify it applies
   - [ ] Check pagination info text updates correctly

4. **Range Filtering**:
   - [ ] Adjust time range slider
   - [ ] Verify table updates to show only logs in selected range
   - [ ] Check pagination resets to page 1 when range changes

---

## Phase 2: In-Table Expandable Details (1-2 days)

### Objectives
- Research latest Dash DataTable in-table expansion capabilities
- Implement in-table row expansion functionality (not below-table)
- Add keyboard navigation support (arrow keys + enter)
- Design expanded content that fits within table structure
- Implement copy and filter actions within expanded rows

### Requirements

#### 2.1 Research Phase: Dash DataTable In-Table Expansion ✅ **COMPLETED**
**Objective**: Research latest Dash DataTable documentation to determine best approach for in-table expansion

**Research Areas**:
1. **Native DataTable Features**: Check if Dash DataTable supports built-in row expansion ✅
2. **DMC Integration**: Investigate Dash-Mantine-Components table options ✅
3. **Custom Implementation**: Explore dynamic data manipulation for row insertion ✅
4. **Keyboard Events**: Research keyboard event handling in Dash DataTable ✅

**Documentation Sources**:
- Official Dash DataTable documentation (latest version) ✅
- Dash-Mantine-Components table components ✅
- Community examples of table expansion patterns ✅
- Accessibility guidelines for keyboard navigation ✅

#### Research Findings Summary ✅ **COMPLETED**

**Key Findings**:
- **❌ No Native Support**: Dash DataTable does NOT have built-in row expansion functionality
- **📚 Community Workarounds**: Various custom implementations exist but require manual development
- **🎯 Recommended Approach**: Dynamic data manipulation with row insertion (Option B)

**Implementation Options Evaluated**:
1. **Option A**: Native DataTable expansion ❌ - Not available in current Dash version
2. **Option B**: Dynamic data manipulation ✅ - **RECOMMENDED** - Full control, stable foundation
3. **Option C**: Dash-Mantine-React-Table ⚠️ - Available but inactive maintenance, compatibility concerns

**Selected Approach: Dynamic Data Manipulation**
- **Rationale**: Maintains current architecture, full customization, stable foundation
- **Method**: Insert detail rows directly into table data structure using callbacks
- **Benefits**: Works with existing DataTable, complete keyboard navigation support, reliable performance
- **Implementation**: Use row insertion/removal with custom styling for detail rows

#### 2.2 Implementation Strategy Selection
Based on research findings, choose one of these approaches:

**Option A: Native DataTable Expansion** (if available)
```python
# Use built-in DataTable row expansion features
dash_table.DataTable(
    id="log-table",
    columns=columns,
    data=data,
    # Native expansion properties
    expandable_rows=True,
    expanded_rows=[],
    row_expansion_callback=...
)
```

**Option B: Dynamic Data Manipulation**
```python
# Insert detail rows dynamically into table data
def expand_row_in_table_data(table_data, row_index, log_details):
    """Insert detailed rows directly into table data structure."""
    expanded_data = table_data.copy()

    # Insert detail rows after the selected row
    detail_rows = create_detail_rows(log_details)
    for i, detail_row in enumerate(detail_rows):
        expanded_data.insert(row_index + 1 + i, detail_row)

    return expanded_data

def create_detail_rows(log_details):
    """Create rows that contain expanded log information."""
    detail_rows = []

    # Location row
    detail_rows.append({
        'row_type': 'detail',
        'detail_type': 'location',
        'timestamp_display': '',
        'log_event': f"📍 {log_details['file_name']} ({log_details['folder_name']})",
        'process_name': '',
        'context_summary': '',
        'expand_button': ''
    })

    # Context rows (one per key-value pair)
    for key, value in log_details['context_keys'].items():
        detail_rows.append({
            'row_type': 'detail',
            'detail_type': 'context',
            'timestamp_display': '',
            'log_event': f"🏷️ {key}:",
            'process_name': str(value),
            'context_summary': '',
            'expand_button': ''
        })

    return detail_rows
```

**Option C: DMC Alternative**
```python
# Use DMC components for better table control
import dash_mantine_components as dmc

def create_expandable_table():
    """Create table using DMC with custom expansion."""
    return dmc.Table([
        dmc.Thead([
            dmc.Tr([
                dmc.Th("Time"),
                dmc.Th("Event"),
                dmc.Th("Process"),
                dmc.Th("Context"),
                dmc.Th("")
            ])
        ]),
        dmc.Tbody(id="table-body")
    ])
```

#### 2.3 Keyboard Navigation Implementation
**Objective**: Add full keyboard support for table navigation and expansion

**Requirements**:
1. **Arrow Key Navigation**: Use Up/Down arrows to navigate between table rows
2. **Enter Key Expansion**: Press Enter to expand/collapse selected row
3. **Escape Key**: Press Escape to collapse any expanded row
4. **Tab Navigation**: Tab through interactive elements within expanded content

**Implementation**:
```python
# Add keyboard event listeners to DataTable
@app.callback(
    [Output('log-table', 'data'),
     Output('log-table', 'active_cell')],
    [Input('log-table', 'n_key_events'),
     Input('log-table', 'active_cell')],
    [State('log-table', 'data'),
     State('log-table', 'derived_virtual_data')],
    prevent_initial_call=True
)
def handle_keyboard_navigation(key_events, active_cell, current_data, virtual_data):
    """Handle keyboard navigation and expansion."""
    if not key_events or not key_events.get('key'):
        return no_update, no_update

    key = key_events['key']

    if key == 'Enter' and active_cell:
        # Toggle expansion for current row
        row_index = active_cell['row']
        return toggle_row_expansion(current_data, row_index), no_update

    elif key == 'Escape':
        # Collapse all expanded rows
        return collapse_all_expansions(current_data), no_update

    elif key in ['ArrowUp', 'ArrowDown']:
        # Navigate between rows (handled by DataTable natively)
        return no_update, no_update

    return no_update, no_update

def toggle_row_expansion(table_data, row_index):
    """Toggle expansion state for a specific row."""
    if not table_data or row_index >= len(table_data):
        return table_data

    row = table_data[row_index]

    # Check if row is already expanded (has detail rows following it)
    if is_row_expanded(table_data, row_index):
        return collapse_row(table_data, row_index)
    else:
        return expand_row(table_data, row_index)

def is_row_expanded(table_data, row_index):
    """Check if a row is currently expanded."""
    if row_index + 1 >= len(table_data):
        return False

    next_row = table_data[row_index + 1]
    return next_row.get('row_type') == 'detail'

def expand_row(table_data, row_index):
    """Expand a row by inserting detail rows."""
    from ..utils.log_formatting import create_expanded_log_details

    log_entry = table_data[row_index]
    details = create_expanded_log_details(log_entry)
    detail_rows = create_detail_rows(details)

    # Insert detail rows after the main row
    new_data = table_data.copy()
    for i, detail_row in enumerate(detail_rows):
        new_data.insert(row_index + 1 + i, detail_row)

    # Update expand button to show collapsed state
    new_data[row_index]['expand_button'] = "▲"

    return new_data

def collapse_row(table_data, row_index):
    """Collapse a row by removing detail rows."""
    new_data = table_data.copy()

    # Remove all detail rows following this row
    i = row_index + 1
    while i < len(new_data) and new_data[i].get('row_type') == 'detail':
        new_data.pop(i)

    # Update expand button to show expanded state
    new_data[row_index]['expand_button'] = "▼"

    return new_data
```

#### 2.4 Styling for In-Table Expansion
**Objective**: Style expanded rows to integrate seamlessly with table

**CSS Styling**:
```css
/* Add to assets/style.css */
.detail-row {
    background-color: #f8f9fa !important;
    border-left: 3px solid #007bff !important;
    font-style: italic;
}

.detail-row td {
    padding-left: 20px !important;
    border-top: 1px dashed #dee2e6 !important;
}

.context-detail {
    color: #6c757d;
    font-size: 0.9em;
}

.location-detail {
    color: #28a745;
    font-weight: 500;
}
```

**DataTable Styling**:
```python
# Update table style_data_conditional
style_data_conditional = [
    # Existing log level styles...
    {
        'if': {'filter_query': '{row_type} = detail'},
        'backgroundColor': '#f8f9fa',
        'borderLeft': '3px solid #007bff',
        'fontStyle': 'italic'
    },
    {
        'if': {'filter_query': '{detail_type} = location'},
        'color': '#28a745',
        'fontWeight': '500'
    },
    {
        'if': {'filter_query': '{detail_type} = context'},
        'color': '#6c757d',
        'fontSize': '0.9em'
    }
]
```

### Testing Strategy - Phase 2

#### Manual Testing Checkpoints

1. **Research Validation**:
   - [ ] Research completed for all 3 implementation options
   - [ ] Best approach selected based on technical feasibility
   - [ ] Documentation sources reviewed and bookmarked

2. **In-Table Expansion**:
   - [ ] Click expand button (▼) shows details within table
   - [ ] Expanded content doesn't break table layout
   - [ ] Detail rows are clearly styled and differentiated
   - [ ] Collapse button (▲) properly removes detail rows

3. **Keyboard Navigation**:
   - [ ] Arrow keys navigate between main table rows only
   - [ ] Enter key expands/collapses selected row
   - [ ] Escape key collapses any expanded rows
   - [ ] Tab navigation works within expanded content

4. **Content Organization**:
   - [ ] Location information displayed in organized format
   - [ ] Context keys shown with clear key: value structure
   - [ ] Long values properly truncated or wrapped
   - [ ] Raw message hidden by default (if shown)

#### Technical Validation

1. **Performance**:
   - [ ] Expansion doesn't cause table re-render delays
   - [ ] Large number of context keys handled efficiently
   - [ ] Keyboard events don't interfere with normal typing

2. **Accessibility**:
   - [ ] Screen readers can navigate expanded content
   - [ ] Keyboard-only navigation fully functional
   - [ ] Focus management works correctly during expansion



---

## Phase 3: Log Event Grouping (1-2 days)

### Objectives
- Implement consecutive event grouping algorithm
- Create grouped row display with count and time range
- Add group expansion showing individual events
- Add grouping configuration toggle

### Requirements

#### 3.1 Grouping Algorithm Implementation
**File**: `tern/tern/utils/log_grouping.py`

```python
from datetime import datetime
from typing import List, Dict, Any

def group_consecutive_events(
    logs: List[Dict[str, Any]],
    max_time_gap_seconds: int = 30,
    max_group_size: int = 50,
    min_group_size: int = 2
) -> List[Dict[str, Any]]:
    """
    Group consecutive identical log events to reduce table clutter.

    Args:
        logs: List of log entries (sorted by timestamp)
        max_time_gap_seconds: Maximum time gap between events to group
        max_group_size: Maximum number of events in a single group
        min_group_size: Minimum events required to form a group

    Returns:
        List of logs with grouped events replaced by group entries
    """
    if not logs:
        return logs

    grouped_logs = []
    current_group = []

    for log in logs:
        # Check if this log can be added to current group
        if current_group and can_group_with_current(log, current_group, max_time_gap_seconds, max_group_size):
            current_group.append(log)
        else:
            # Finalize current group if it exists
            if current_group:
                if len(current_group) >= min_group_size:
                    grouped_logs.append(create_grouped_entry(current_group))
                else:
                    grouped_logs.extend(current_group)  # Add individual events

            # Start new group
            current_group = [log]

    # Handle final group
    if current_group:
        if len(current_group) >= min_group_size:
            grouped_logs.append(create_grouped_entry(current_group))
        else:
            grouped_logs.extend(current_group)

    return grouped_logs

def can_group_with_current(
    log: Dict[str, Any],
    current_group: List[Dict[str, Any]],
    max_time_gap: int,
    max_group_size: int
) -> bool:
    """Check if a log entry can be grouped with the current group."""
    if len(current_group) >= max_group_size:
        return False

    last_log = current_group[-1]

    # Parse timestamps for time gap check
    try:
        current_time = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
        last_time = datetime.fromisoformat(last_log['timestamp'].replace('Z', '+00:00'))
        time_gap = (current_time - last_time).total_seconds()

        if time_gap > max_time_gap:
            return False
    except (ValueError, KeyError):
        return False

    # Check if events are identical (same grouping criteria)
    return events_are_identical(log, last_log)

def events_are_identical(log1: Dict[str, Any], log2: Dict[str, Any]) -> bool:
    """Check if two log events are identical for grouping purposes."""
    # Required fields that must match
    required_fields = ['log_event', 'log_level', 'process_name', 'library']

    for field in required_fields:
        if log1.get(field) != log2.get(field):
            return False

    # Check all key_ fields (dynamic keys must match)
    log1_keys = {k: v for k, v in log1.items() if k.startswith('key_')}
    log2_keys = {k: v for k, v in log2.items() if k.startswith('key_')}

    return log1_keys == log2_keys

def create_grouped_entry(group: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create a grouped entry from a list of identical events."""
    if not group:
        return {}

    first_event = group[0]
    last_event = group[-1]

    # Calculate time range
    try:
        start_time = datetime.fromisoformat(first_event['timestamp'].replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(last_event['timestamp'].replace('Z', '+00:00'))
        duration = (end_time - start_time).total_seconds()

        # Format timestamp range
        if duration < 60:  # Less than 1 minute
            timestamp_range = f"{start_time.strftime('%H:%M:%S')}-{end_time.strftime('%S')}"
        else:  # 1 minute or more
            timestamp_range = f"{start_time.strftime('%H:%M:%S')}-{end_time.strftime('%M:%S')}"
    except (ValueError, KeyError):
        timestamp_range = f"{first_event.get('timestamp', '')} - {last_event.get('timestamp', '')}"
        duration = 0

    # Create grouped entry
    grouped_entry = first_event.copy()
    grouped_entry.update({
        'timestamp': timestamp_range,
        'log_event': f"{first_event.get('log_event', '')} (×{len(group)})",
        'is_grouped': True,
        'group_count': len(group),
        'group_duration_seconds': duration,
        'grouped_events': group,  # Store original events for expansion
        'first_timestamp': first_event['timestamp'],
        'last_timestamp': last_event['timestamp']
    })

    return grouped_entry
```

#### 3.2 Update Data Loading with Grouping
**File**: Update `tern/tern/callbacks/data.py`

```python
from tern.utils.log_grouping import group_consecutive_events

# Add grouping settings store
@app.callback(
    Output('grouping-settings-store', 'data'),
    [Input('enable-grouping-toggle', 'checked'),
     Input('min-group-size-input', 'value'),
     Input('max-time-gap-input', 'value'),
     Input('max-group-size-input', 'value')],
    prevent_initial_call=False
)
def update_grouping_settings(enable_grouping, min_group_size, max_time_gap, max_group_size):
    """Update grouping settings."""
    return {
        'enable_grouping': enable_grouping if enable_grouping is not None else True,
        'min_group_size': min_group_size or 2,
        'max_time_gap': max_time_gap or 30,
        'max_group_size': max_group_size or 50
    }

# Update main table callback
@app.callback(
    [Output('log-table', 'data'),
     Output('log-table', 'page_count'),
     Output('log-table-info', 'children')],
    [Input('time-range-slider', 'value'),
     Input('log-table', 'page_current'),
     Input('position-filter', 'value'),
     Input('log-table', 'page_size'),
     Input('grouping-settings-store', 'data')],  # Add grouping settings
    prevent_initial_call=False
)
def update_log_table(time_range, current_page, position_filter, page_size, grouping_settings):
    """Update log table with optional grouping."""
    try:
        # Convert slider values to datetime
        start_time, end_time = convert_slider_to_datetime(time_range)

        # Build filters dictionary
        filters = {}
        if position_filter:
            filters['folder_name'] = position_filter

        # Get the analyzer from container
        analyzer = container.get_structured_log_analyzer()
        if not analyzer:
            return [], 0, "No log data available"

        # Query all filtered logs first
        df_filtered = analyzer.query(start=start_time, end=end_time, **filters)
        logs = analyzer._df_to_dict_list(df_filtered)

        # Apply grouping if enabled
        if grouping_settings and grouping_settings.get('enable_grouping', True):
            logs = group_consecutive_events(
                logs,
                max_time_gap_seconds=grouping_settings.get('max_time_gap', 30),
                max_group_size=grouping_settings.get('max_group_size', 50),
                min_group_size=grouping_settings.get('min_group_size', 2)
            )

        total_in_range = len(logs)

        # Apply pagination to processed logs
        start_idx = current_page * page_size
        end_idx = start_idx + page_size
        paginated_logs = logs[start_idx:end_idx]

        # Format for table display
        from tern.utils.log_formatting import format_logs_for_table_with_grouping
        formatted_data = format_logs_for_table_with_grouping(paginated_logs)

        # Calculate pagination info
        total_pages = (total_in_range + page_size - 1) // page_size

        # Create info text with grouping stats
        start_entry = start_idx + 1 if paginated_logs else 0
        end_entry = min(start_idx + len(paginated_logs), total_in_range)

        grouped_count = sum(1 for log in logs if log.get('is_grouped', False))
        individual_count = len(logs) - grouped_count

        info_text = f"Showing {start_entry}-{end_entry} of {total_in_range} entries"
        if grouped_count > 0:
            info_text += f" ({grouped_count} groups, {individual_count} individual)"

        return formatted_data, total_pages, info_text

    except Exception as e:
        logger.exception("Error updating log table")
        return [], 0, f"Error loading logs: {str(e)}"
```

#### 3.3 Grouping Settings UI
**File**: Update `tern/tern/layout/main_layout.py`

```python
def create_grouping_settings_section():
    """Create grouping settings panel."""
    return dmc.Paper(
        withBorder=True,
        p="md",
        style={"display": "none"},  # Hidden by default
        id="grouping-settings-panel",
        children=[
            dmc.Stack([
                dmc.Group([
                    dmc.Text("🔧 Log Grouping", fw=500, size="lg"),
                    dmc.ActionIcon(
                        DashIconify(icon="mdi:close"),
                        id="close-grouping-settings",
                        variant="subtle"
                    )
                ], justify="space-between"),

                dmc.Switch(
                    id="enable-grouping-toggle",
                    label="Enable event grouping",
                    checked=True,
                    size="md"
                ),

                dmc.NumberInput(
                    id="min-group-size-input",
                    label="Minimum group size",
                    value=2,
                    min=2,
                    max=10,
                    step=1,
                    style={"width": "200px"}
                ),

                dmc.NumberInput(
                    id="max-time-gap-input",
                    label="Max time gap (seconds)",
                    value=30,
                    min=1,
                    max=300,
                    step=1,
                    style={"width": "200px"}
                ),

                dmc.NumberInput(
                    id="max-group-size-input",
                    label="Max group size",
                    value=50,
                    min=5,
                    max=200,
                    step=5,
                    style={"width": "200px"}
                ),

                dmc.Group([
                    dmc.Button("Apply Settings", id="apply-grouping-settings", color="blue"),
                    dmc.Button("Reset to Defaults", id="reset-grouping-settings", variant="outline")
                ])
            ], gap="md")
        ]
    )

# Add settings button to table section
def create_log_table_section():
    return dmc.Stack(
        gap="md",
        children=[
            dmc.Group([
                dmc.Text("Log Entries", fw=500, size="lg"),
                dmc.ActionIcon(
                    DashIconify(icon="mdi:cog"),
                    id="open-grouping-settings",
                    variant="subtle",
                    size="lg"
                )
            ], justify="space-between"),

            create_grouping_settings_section(),  # Add settings panel

            dash_table.DataTable(
                id="log-table",
                columns=create_log_table_columns(),
                data=[],
                page_size=50,
                page_action="custom",
                # ... rest of table config
            ),

            dmc.Box(id="expanded-row-content"),
            dmc.Text(id="log-table-info", size="sm", c="dimmed"),

            # Hidden store for grouping settings
            dcc.Store(id="grouping-settings-store", data={
                'enable_grouping': True,
                'min_group_size': 2,
                'max_time_gap': 30,
                'max_group_size': 50
            })
        ],
    )
```

### Testing Strategy - Phase 3

#### Unit Tests
**File**: `tests/test_log_grouping.py`

```python
import pytest
from datetime import datetime, timedelta
from tern.utils.log_grouping import (
    group_consecutive_events,
    events_are_identical,
    can_group_with_current,
    create_grouped_entry
)

class TestLogGrouping:
    def test_events_are_identical_same_events(self):
        """Test that identical events are correctly identified."""
        log1 = {
            'log_event': 'heartbeat',
            'log_level': 'INFO',
            'process_name': 'server',
            'library': 'util',
            'key_run_id': 'abc123'
        }
        log2 = log1.copy()

        assert events_are_identical(log1, log2) == True

    def test_events_are_identical_different_events(self):
        """Test that different events are correctly identified."""
        log1 = {
            'log_event': 'heartbeat',
            'log_level': 'INFO',
            'process_name': 'server',
            'library': 'util'
        }
        log2 = {
            'log_event': 'error',  # Different event
            'log_level': 'INFO',
            'process_name': 'server',
            'library': 'util'
        }

        assert events_are_identical(log1, log2) == False

    def test_events_are_identical_different_keys(self):
        """Test that events with different keys are not identical."""
        log1 = {'log_event': 'test', 'key_run_id': 'abc123'}
        log2 = {'log_event': 'test', 'key_run_id': 'xyz789'}

        assert events_are_identical(log1, log2) == False

    def test_can_group_with_current_time_gap_too_large(self):
        """Test that events with large time gaps cannot be grouped."""
        base_time = datetime.now()

        log1 = {'timestamp': base_time.isoformat()}
        log2 = {'timestamp': (base_time + timedelta(minutes=2)).isoformat()}

        # Time gap of 2 minutes > 30 seconds limit
        assert can_group_with_current(log2, [log1], 30, 50) == False

    def test_create_grouped_entry_formats_correctly(self):
        """Test that grouped entries are formatted correctly."""
        base_time = datetime.now()
        group = [
            {
                'timestamp': base_time.isoformat(),
                'log_event': 'heartbeat',
                'log_level': 'INFO'
            },
            {
                'timestamp': (base_time + timedelta(seconds=30)).isoformat(),
                'log_event': 'heartbeat',
                'log_level': 'INFO'
            }
        ]

        result = create_grouped_entry(group)

        assert result['is_grouped'] == True
        assert result['group_count'] == 2
        assert '(×2)' in result['log_event']
        assert 'grouped_events' in result

    def test_group_consecutive_events_groups_identical(self):
        """Test that consecutive identical events are grouped."""
        base_time = datetime.now()
        logs = [
            {
                'timestamp': base_time.isoformat(),
                'log_event': 'heartbeat',
                'log_level': 'INFO',
                'process_name': 'server',
                'library': 'util'
            },
            {
                'timestamp': (base_time + timedelta(seconds=10)).isoformat(),
                'log_event': 'heartbeat',
                'log_level': 'INFO',
                'process_name': 'server',
                'library': 'util'
            },
            {
                'timestamp': (base_time + timedelta(seconds=20)).isoformat(),
                'log_event': 'different',  # This breaks the group
                'log_level': 'INFO',
                'process_name': 'server',
                'library': 'util'
            }
        ]

        result = group_consecutive_events(logs)

        # Should have 2 entries: 1 grouped (2 heartbeats) + 1 individual
        assert len(result) == 2
        assert result[0]['is_grouped'] == True
        assert result[0]['group_count'] == 2
        assert result[1].get('is_grouped', False) == False

    def test_group_consecutive_events_respects_min_size(self):
        """Test that groups below minimum size are not created."""
        logs = [
            {
                'timestamp': datetime.now().isoformat(),
                'log_event': 'single',
                'log_level': 'INFO',
                'process_name': 'server',
                'library': 'util'
            }
        ]

        result = group_consecutive_events(logs, min_group_size=2)

        # Single event should not be grouped
        assert len(result) == 1
        assert result[0].get('is_grouped', False) == False
```

#### Integration Tests
**File**: `tests/test_grouping_integration.py`

```python
import pytest
from unittest.mock import Mock, patch
from tern.callbacks.data import update_log_table

class TestGroupingIntegration:
    @patch('tern.callbacks.data.container')
    def test_update_log_table_with_grouping_enabled(self, mock_container):
        """Test log table update with grouping enabled."""
        # Setup mock data with repetitive events
        mock_analyzer = Mock()
        mock_df = Mock()

        base_time = datetime.now()
        repetitive_logs = [
            {
                'timestamp': (base_time + timedelta(seconds=i*10)).isoformat(),
                'log_event': 'heartbeat',
                'log_level': 'INFO',
                'process_name': 'server',
                'library': 'util'
            }
            for i in range(5)  # 5 identical events
        ]

        mock_analyzer.query.return_value = mock_df
        mock_analyzer._df_to_dict_list.return_value = repetitive_logs
        mock_container.get_structured_log_analyzer.return_value = mock_analyzer

        grouping_settings = {
            'enable_grouping': True,
            'min_group_size': 2,
            'max_time_gap': 30,
            'max_group_size': 50
        }

        with patch('tern.callbacks.data.convert_slider_to_datetime') as mock_convert:
            mock_convert.return_value = (datetime.now(), datetime.now())

            data, page_count, info = update_log_table(
                [0, 100], 0, None, 50, grouping_settings
            )

            # Should have 1 grouped entry instead of 5 individual
            assert len(data) == 1
            assert data[0].get('is_grouped', False) == True
            assert "(×5)" in data[0]['log_event']
            assert "groups" in info

    @patch('tern.callbacks.data.container')
    def test_update_log_table_with_grouping_disabled(self, mock_container):
        """Test log table update with grouping disabled."""
        # Same setup as above
        mock_analyzer = Mock()
        mock_df = Mock()

        repetitive_logs = [{'log_event': 'test'} for _ in range(5)]

        mock_analyzer.query.return_value = mock_df
        mock_analyzer._df_to_dict_list.return_value = repetitive_logs
        mock_container.get_structured_log_analyzer.return_value = mock_analyzer

        grouping_settings = {'enable_grouping': False}

        with patch('tern.callbacks.data.convert_slider_to_datetime') as mock_convert:
            mock_convert.return_value = (datetime.now(), datetime.now())

            data, page_count, info = update_log_table(
                [0, 100], 0, None, 50, grouping_settings
            )

            # Should have 5 individual entries
            assert len(data) == 5
            assert all(not entry.get('is_grouped', False) for entry in data)
```

#### Manual Testing Checkpoints

1. **Grouping Functionality**:
   - [ ] Load logs with repetitive events (e.g., heartbeats)
   - [ ] Verify consecutive identical events are grouped
   - [ ] Check that grouped rows show count (×N) and time range
   - [ ] Confirm different events break grouping

2. **Grouping Settings**:
   - [ ] Click settings gear icon
   - [ ] Toggle grouping on/off and verify table updates
   - [ ] Adjust minimum group size and verify smaller groups disappear
   - [ ] Change time gap limit and verify distant events aren't grouped
   - [ ] Test maximum group size limit

3. **Group Expansion**:
   - [ ] Click expand button on grouped row
   - [ ] Verify expanded view shows individual events in chronological order
   - [ ] Check frequency calculation and time span display
   - [ ] Test "Show All" vs "Show First/Last Only" options

4. **Edge Cases**:
   - [ ] Test with logs that have no repetitive events
   - [ ] Verify single events are not grouped
   - [ ] Test with events that have slightly different keys
   - [ ] Check behavior with large time gaps between events

---

## Phase 4: Smart Filtering Integration (1 day)

### Objectives
- Connect table actions to global filters
- Implement click-to-filter for all columns
- Add jump-to-time functionality
- Create contextual menus for advanced actions

### Requirements

#### 4.1 Click-to-Filter Implementation
**File**: `tern/tern/callbacks/filtering.py`

```python
@app.callback(
    [Output('position-filter', 'value'),
     Output('log-level-filter', 'value'),
     Output('process-filter', 'value')],
    [Input({'type': 'filter-column', 'column': ALL, 'value': ALL}, 'n_clicks')],
    [State('position-filter', 'value'),
     State('log-level-filter', 'value'),
     State('process-filter', 'value')],
    prevent_initial_call=True
)
def handle_column_filtering(n_clicks_list, current_position, current_level, current_process):
    """Handle click-to-filter actions from table columns."""
    if not any(n_clicks_list):
        raise PreventUpdate

    ctx = callback_context
    if not ctx.triggered:
        raise PreventUpdate

    # Extract filter info from button ID
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    filter_info = eval(button_id)

    column = filter_info['column']
    value = filter_info['value']

    # Update appropriate filter based on column
    new_position = current_position
    new_level = current_level
    new_process = current_process

    if column == 'folder_name':
        new_position = [value] if value not in (current_position or []) else current_position
    elif column == 'log_level':
        new_level = [value] if value not in (current_level or []) else current_level
    elif column == 'process_name':
        new_process = [value] if value not in (current_process or []) else current_process

    return new_position, new_level, new_process

@app.callback(
    Output('time-range-slider', 'value'),
    [Input({'type': 'jump-to-time', 'timestamp': ALL}, 'n_clicks')],
    [State('time-range-slider', 'min'),
     State('time-range-slider', 'max')],
    prevent_initial_call=True
)
def handle_jump_to_time(n_clicks_list, slider_min, slider_max):
    """Handle jump-to-time actions from expanded rows."""
    if not any(n_clicks_list):
        raise PreventUpdate

    ctx = callback_context
    if not ctx.triggered:
        raise PreventUpdate

    # Extract timestamp from button ID
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    timestamp_str = eval(button_id)['timestamp']

    try:
        # Parse timestamp and convert to slider value
        target_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        slider_value = convert_datetime_to_slider_value(target_time)

        # Center the range around the target time (±30 minutes)
        range_width = 1800  # 30 minutes in seconds
        new_start = max(slider_min, slider_value - range_width)
        new_end = min(slider_max, slider_value + range_width)

        return [new_start, new_end]

    except (ValueError, KeyError):
        raise PreventUpdate

def convert_datetime_to_slider_value(dt: datetime) -> float:
    """Convert datetime to slider value based on your slider implementation."""
    # This needs to match your existing slider value conversion
    pass
```

#### 4.2 Update Table Formatting for Clickable Elements
**File**: Update `tern/tern/utils/log_formatting.py`

```python
def format_logs_for_table_with_filtering(logs: list[dict]) -> list[dict]:
    """Format logs for DataTable display with clickable filtering elements."""
    formatted_logs = []

    for log in logs:
        formatted_log = log.copy()

        # Generate context summary
        formatted_log['context_summary'] = generate_context_summary(log)

        # Add clickable expand button
        if log.get('is_grouped', False):
            formatted_log['expand_button'] = f"▼ ({log['group_count']} events)"
        else:
            formatted_log['expand_button'] = "▼"

        # Format timestamp for display
        if not log.get('is_grouped', False):
            try:
                dt = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                time_str = dt.strftime('%H:%M:%S.%f')[:-3]
                # Make timestamp clickable for jump-to-time
                formatted_log['timestamp_display'] = f"[{time_str}](javascript:void(0))"
                formatted_log['timestamp_display_id'] = {
                    'type': 'jump-to-time',
                    'timestamp': log['timestamp']
                }
            except (ValueError, KeyError):
                formatted_log['timestamp_display'] = log.get('timestamp', '')
        else:
            formatted_log['timestamp_display'] = formatted_log['timestamp']

        # Format log level with clickable filter
        level = log.get('log_level', '')
        if level:
            level_display = f"🔴 {level}" if level == 'ERROR' else \
                           f"🟡 {level}" if level == 'WARN' else \
                           f"🔵 {level}" if level == 'INFO' else level

            # Make level clickable for filtering
            formatted_log['log_level'] = f"[{level_display}](javascript:void(0))"
            formatted_log['log_level_filter_id'] = {
                'type': 'filter-column',
                'column': 'log_level',
                'value': level
            }

        # Format log event with clickable filter
        event = log.get('log_event', '')
        if event:
            # Handle grouped events (remove count suffix for filtering)
            base_event = event.split(' (×')[0] if '(×' in event else event
            formatted_log['log_event'] = f"[{event}](javascript:void(0))"
            formatted_log['log_event_filter_id'] = {
                'type': 'filter-column',
                'column': 'log_event',
                'value': base_event
            }

        # Format process name with clickable filter
        process = log.get('process_name', '')
        if process:
            formatted_log['process_name'] = f"[{process}](javascript:void(0))"
            formatted_log['process_filter_id'] = {
                'type': 'filter-column',
                'column': 'process_name',
                'value': process
            }

        # Format library with clickable filter
        library = log.get('library', '')
        if library:
            formatted_log['library'] = f"[{library}](javascript:void(0))"
            formatted_log['library_filter_id'] = {
                'type': 'filter-column',
                'column': 'library',
                'value': library
            }

        formatted_logs.append(formatted_log)

    return formatted_logs
```

#### 4.3 Enhanced Expanded Row with Filtering
**File**: Update `tern/tern/callbacks/ui.py`

```python
def create_expanded_row_content_with_filtering(log_entry: dict):
    """Create expanded row content with filtering capabilities."""
    # Extract all keys
    keys = {}
    if 'keys' in log_entry and log_entry['keys']:
        keys.update(log_entry['keys'])

    # Add flattened key_ columns
    for col, value in log_entry.items():
        if col.startswith('key_') and value is not None:
            key_name = col[4:]
            keys[key_name] = value

    # Create clickable key display
    key_items = []
    for key, value in keys.items():
        # Create clickable key-value pairs for filtering
        key_button = dmc.Button(
            f"{key}: {value}",
            variant="subtle",
            size="xs",
            id={'type': 'filter-key', 'key': key, 'value': str(value)},
            style={"textAlign": "left", "justifyContent": "flex-start"}
        )
        key_items.append(key_button)

    return dmc.Card([
        dmc.CardSection([
            dmc.Group([
                dmc.Text("📍", size="lg"),
                dmc.Text(
                    f"Location: {log_entry.get('file_name', 'Unknown')} ({log_entry.get('folder_name', 'No position')})",
                    fw=500
                )
            ], gap="xs")
        ]),

        dmc.CardSection([
            dmc.Text("🏷️ All Context Data (click to filter):", fw=500, mb="sm"),
            dmc.Group(key_items, gap="xs") if keys else dmc.Text("No context keys", c="dimmed")
        ]) if keys else None,

        dmc.CardSection([
            dmc.Collapse(
                dmc.Stack([
                    dmc.Text("📄 Raw Message:", fw=500),
                    dmc.Code(
                        log_entry.get('message', 'No message'),
                        block=True,
                        style={"whiteSpace": "pre-wrap"}
                    )
                ]),
                id=f"message-collapse-{log_entry.get('timestamp', '')}"
            ),
            dmc.Button(
                "Show/Hide Raw Message",
                id=f"toggle-message-{log_entry.get('timestamp', '')}",
                variant="subtle",
                size="sm"
            )
        ]),

        dmc.CardSection([
            dmc.Group([
                dmc.Button(
                    "📋 Copy as JSON",
                    id={"type": "copy-json", "index": log_entry.get('timestamp', '')},
                    variant="light",
                    size="sm"
                ),
                dmc.Button(
                    "🔍 Filter by Event",
                    id={
                        "type": "filter-column",
                        "column": "log_event",
                        "value": log_entry.get('log_event', '').split(' (×')[0]  # Remove count
                    },
                    variant="light",
                    size="sm"
                ),
                dmc.Button(
                    "⏰ Jump to Time",
                    id={"type": "jump-to-time", "timestamp": log_entry.get('timestamp', '')},
                    variant="light",
                    size="sm"
                ),
                dmc.Button(
                    "📁 Filter by Position",
                    id={
                        "type": "filter-column",
                        "column": "folder_name",
                        "value": log_entry.get('folder_name', '')
                    },
                    variant="light",
                    size="sm"
                ) if log_entry.get('folder_name') else None
            ], gap="sm")
        ])
    ], withBorder=True, p="md", mt="sm")
```

### Testing Strategy - Phase 4

#### Unit Tests
**File**: `tests/test_filtering_integration.py`

```python
import pytest
from datetime import datetime
from tern.utils.log_formatting import format_logs_for_table_with_filtering

class TestFilteringIntegration:
    def test_format_logs_creates_clickable_elements(self):
        """Test that log formatting creates clickable filter elements."""
        logs = [{
            'timestamp': '2025-03-06T13:58:15.277567',
            'log_level': 'ERROR',
            'log_event': 'test_event',
            'process_name': 'test_process',
            'library': 'test_lib'
        }]

        formatted = format_logs_for_table_with_filtering(logs)

        assert len(formatted) == 1
        # Check that clickable elements are created
        assert '[' in formatted[0]['log_level']  # Markdown link format
        assert 'log_level_filter_id' in formatted[0]
        assert formatted[0]['log_level_filter_id']['type'] == 'filter-column'

    def test_format_logs_handles_grouped_events(self):
        """Test that grouped events are handled correctly for filtering."""
        logs = [{
            'timestamp': '14:23:39-47',
            'log_event': 'heartbeat (×5)',
            'log_level': 'INFO',
            'is_grouped': True,
            'group_count': 5
        }]

        formatted = format_logs_for_table_with_filtering(logs)

        # Should extract base event name for filtering
        assert formatted[0]['log_event_filter_id']['value'] == 'heartbeat'
        assert '(×5)' not in formatted[0]['log_event_filter_id']['value']
```

#### Integration Tests
**File**: `tests/test_filtering_callbacks.py`

```python
import pytest
from unittest.mock import Mock, patch
from tern.callbacks.filtering import handle_column_filtering, handle_jump_to_time

class TestFilteringCallbacks:
    def test_handle_column_filtering_adds_filter(self):
        """Test that column filtering adds new filter values."""
        # Mock callback context
        with patch('tern.callbacks.filtering.callback_context') as mock_ctx:
            mock_ctx.triggered = [{'prop_id': "{'type': 'filter-column', 'column': 'log_level', 'value': 'ERROR'}.n_clicks"}]

            result = handle_column_filtering([1], [], [], [])

            # Should add ERROR to log level filter
            position, level, process = result
            assert level == ['ERROR']

    def test_handle_jump_to_time_centers_range(self):
        """Test that jump to time centers the time range."""
        with patch('tern.callbacks.filtering.callback_context') as mock_ctx:
            mock_ctx.triggered = [{'prop_id': "{'type': 'jump-to-time', 'timestamp': '2025-03-06T13:58:15.277567'}.n_clicks"}]

            with patch('tern.callbacks.filtering.convert_datetime_to_slider_value') as mock_convert:
                mock_convert.return_value = 1000

                result = handle_jump_to_time([1], 0, 10000)

                # Should return centered range
                assert isinstance(result, list)
                assert len(result) == 2
                assert result[0] < result[1]  # Start < End
```

#### Manual Testing Checkpoints

1. **Click-to-Filter Functionality**:
   - [ ] Click on any log level badge in table
   - [ ] Verify that log level filter updates in sidebar
   - [ ] Click on process name and check process filter updates
   - [ ] Click on event name and verify event filtering
   - [ ] Test clicking on library names

2. **Jump-to-Time Feature**:
   - [ ] Click on timestamp in any log entry
   - [ ] Verify time range slider centers on that timestamp
   - [ ] Check that table updates to show logs around that time
   - [ ] Test with grouped event timestamps

3. **Expanded Row Filtering**:
   - [ ] Expand any log entry
   - [ ] Click on individual key-value pairs
   - [ ] Verify appropriate filters are applied
   - [ ] Test "Filter by Position" button
   - [ ] Test "Filter by Event" button

4. **Multiple Filter Interaction**:
   - [ ] Apply multiple filters using click-to-filter
   - [ ] Verify filters combine correctly (AND logic)
   - [ ] Test clearing individual filters
   - [ ] Check filter interaction with time range changes

---

## Phase 5: Performance & UX Polish (0.5 days)

### Objectives
- Optimize pagination queries and caching
- Add loading states and error handling
- Implement keyboard shortcuts
- Add accessibility features

### Requirements

#### 5.1 Performance Optimizations
**File**: `tern/tern/utils/performance.py`

```python
import functools
import time
from typing import Any, Callable

def cache_with_ttl(ttl_seconds: int = 300):
    """Cache function results with time-to-live."""
    def decorator(func: Callable) -> Callable:
        cache = {}

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key = str(args) + str(sorted(kwargs.items()))
            current_time = time.time()

            # Check if we have a valid cached result
            if key in cache:
                result, timestamp = cache[key]
                if current_time - timestamp < ttl_seconds:
                    return result

            # Execute function and cache result
            result = func(*args, **kwargs)
            cache[key] = (result, current_time)

            # Cleanup old entries
            cache = {k: v for k, v in cache.items()
                    if current_time - v[1] < ttl_seconds}

            return result

        return wrapper
    return decorator

@cache_with_ttl(ttl_seconds=60)  # Cache pagination results for 1 minute
def get_cached_paginated_logs(analyzer, start_time, end_time, page, page_size, **filters):
    """Cached version of pagination query."""
    df_filtered = analyzer.query(start=start_time, end=end_time, **filters)
    logs = analyzer._df_to_dict_list(df_filtered)
    return logs

def optimize_log_loading(logs: list[dict], enable_grouping: bool = True) -> dict:
    """Optimized log loading with performance monitoring."""
    start_time = time.time()

    try:
        # Apply grouping if enabled
        if enable_grouping:
            from tern.utils.log_grouping import group_consecutive_events
            logs = group_consecutive_events(logs)

        # Track performance metrics
        processing_time = time.time() - start_time

        return {
            'logs': logs,
            'performance': {
                'processing_time_ms': processing_time * 1000,
                'original_count': len(logs),
                'grouped_count': sum(1 for log in logs if log.get('is_grouped', False))
            }
        }

    except Exception as e:
        logger.exception("Error in optimized log loading")
        return {
            'logs': [],
            'error': str(e),
            'performance': {'processing_time_ms': (time.time() - start_time) * 1000}
        }
```

#### 5.2 Loading States and Error Handling
**File**: Update `tern/tern/callbacks/data.py`

```python
@app.callback(
    [Output('log-table', 'data'),
     Output('log-table', 'page_count'),
     Output('log-table-info', 'children'),
     Output('log-table-loading', 'children'),  # Add loading indicator
     Output('log-table-error', 'children')],   # Add error display
    [Input('time-range-slider', 'value'),
     Input('log-table', 'page_current'),
     Input('position-filter', 'value'),
     Input('log-table', 'page_size'),
     Input('grouping-settings-store', 'data')],
    prevent_initial_call=False
)
def update_log_table_with_states(time_range, current_page, position_filter, page_size, grouping_settings):
    """Update log table with loading states and error handling."""
    # Show loading state
    loading_component = dmc.Center([
        dmc.Loader(size="lg"),
        dmc.Text("Loading logs...", size="sm", c="dimmed", mt="sm")
    ])

    try:
        # Clear any previous errors
        error_component = []

        # Convert slider values to datetime
        start_time, end_time = convert_slider_to_datetime(time_range)

        # Build filters dictionary
        filters = {}
        if position_filter:
            filters['folder_name'] = position_filter

        # Get the analyzer from container
        analyzer = container.get_structured_log_analyzer()
        if not analyzer:
            return [], 0, "No log data available", [], [
                dmc.Alert(
                    "No structured log analyzer available. Please load logs first.",
                    title="No Data",
                    color="yellow",
                    icon=DashIconify(icon="mdi:alert")
                )
            ]

        # Use optimized loading
        from tern.utils.performance import optimize_log_loading, get_cached_paginated_logs

        # Get all filtered logs with caching
        all_logs = get_cached_paginated_logs(
            analyzer, start_time, end_time, 0, 10000, **filters  # Large page to get all
        )

        # Apply grouping and optimization
        optimized_result = optimize_log_loading(
            all_logs,
            enable_grouping=grouping_settings.get('enable_grouping', True)
        )

        if 'error' in optimized_result:
            return [], 0, "Error processing logs", [], [
                dmc.Alert(
                    optimized_result['error'],
                    title="Processing Error",
                    color="red",
                    icon=DashIconify(icon="mdi:alert-circle")
                )
            ]

        processed_logs = optimized_result['logs']
        performance = optimized_result['performance']

        total_in_range = len(processed_logs)

        # Apply pagination to processed logs
        start_idx = current_page * page_size
        end_idx = start_idx + page_size
        paginated_logs = processed_logs[start_idx:end_idx]

        # Format for table display
        from tern.utils.log_formatting import format_logs_for_table_with_filtering
        formatted_data = format_logs_for_table_with_filtering(paginated_logs)

        # Calculate pagination info
        total_pages = (total_in_range + page_size - 1) // page_size

        # Create enhanced info text with performance metrics
        start_entry = start_idx + 1 if paginated_logs else 0
        end_entry = min(start_idx + len(paginated_logs), total_in_range)

        grouped_count = sum(1 for log in processed_logs if log.get('is_grouped', False))
        individual_count = len(processed_logs) - grouped_count

        info_parts = [f"Showing {start_entry}-{end_entry} of {total_in_range} entries"]

        if grouped_count > 0:
            info_parts.append(f"({grouped_count} groups, {individual_count} individual)")

        if performance['processing_time_ms'] > 100:  # Show if processing took >100ms
            info_parts.append(f"• Processed in {performance['processing_time_ms']:.1f}ms")

        info_text = " ".join(info_parts)

        # Clear loading state
        return formatted_data, total_pages, info_text, [], []

    except Exception as e:
        logger.exception("Error updating log table")
        return [], 0, "Error loading logs", [], [
            dmc.Alert(
                f"Failed to load logs: {str(e)}",
                title="Loading Error",
                color="red",
                icon=DashIconify(icon="mdi:alert-circle"),
                action=dmc.Button("Retry", id="retry-load-logs", variant="light", size="sm")
            )
        ]
```

#### 5.3 Keyboard Shortcuts
**File**: `tern/tern/callbacks/keyboard.py`

```python
@app.callback(
    [Output('log-table', 'page_current'),
     Output('expanded-row-content', 'children'),
     Output('grouping-settings-panel', 'style')],
    [Input('keyboard-shortcuts', 'n_events')],
    [State('keyboard-shortcuts', 'event'),
     State('log-table', 'page_current'),
     State('log-table', 'page_count'),
     State('log-table', 'selected_rows'),
     State('log-table', 'data'),
     State('grouping-settings-panel', 'style')],
    prevent_initial_call=True
)
def handle_keyboard_shortcuts(n_events, event, current_page, page_count, selected_rows, table_data, panel_style):
    """Handle keyboard shortcuts for table navigation."""
    if not event:
        raise PreventUpdate

    key = event.get('key', '')
    ctrl_key = event.get('ctrlKey', False)

    new_page = current_page
    new_expanded = dash.no_update
    new_panel_style = panel_style

    # Page navigation shortcuts
    if key == 'ArrowLeft' and ctrl_key:
        # Ctrl+Left: Previous page
        new_page = max(0, current_page - 1)
    elif key == 'ArrowRight' and ctrl_key:
        # Ctrl+Right: Next page
        new_page = min(page_count - 1, current_page + 1)
    elif key == 'Home' and ctrl_key:
        # Ctrl+Home: First page
        new_page = 0
    elif key == 'End' and ctrl_key:
        # Ctrl+End: Last page
        new_page = page_count - 1

    # Row expansion shortcuts
    elif key == 'Enter' and selected_rows and table_data:
        # Enter: Expand selected row
        selected_row_data = table_data[selected_rows[0]]
        new_expanded = create_expanded_row_content_with_filtering(selected_row_data)
    elif key == 'Escape':
        # Escape: Collapse expanded row
        new_expanded = []

    # Settings panel shortcuts
    elif key == 'g' and ctrl_key:
        # Ctrl+G: Toggle grouping settings
        current_display = panel_style.get('display', 'none')
        new_display = 'block' if current_display == 'none' else 'none'
        new_panel_style = {**panel_style, 'display': new_display}

    return new_page, new_expanded, new_panel_style

# Add keyboard event listener to layout
def add_keyboard_shortcuts_to_layout():
    """Add keyboard shortcuts component to main layout."""
    return dcc.EventListener(
        id='keyboard-shortcuts',
        events=[{'event': 'keydown', 'props': ['key', 'ctrlKey', 'altKey', 'shiftKey']}]
    )
```

#### 5.4 Accessibility Features
**File**: `tern/tern/utils/accessibility.py`

```python
def create_accessible_table_columns():
    """Create table columns with accessibility features."""
    return [
        {
            "name": "Time",
            "id": "timestamp_display",
            "type": "text",
            # Add ARIA labels for screen readers
            "presentation": "markdown",
        },
        {
            "name": "Level",
            "id": "log_level",
            "presentation": "markdown",
            "type": "text",
            # Add role information
        },
        {
            "name": "Event",
            "id": "log_event",
            "presentation": "markdown",
            "type": "text"
        },
        {
            "name": "Process",
            "id": "process_name",
            "type": "text"
        },
        {
            "name": "Library",
            "id": "library",
            "type": "text"
        },
        {
            "name": "Context",
            "id": "context_summary",
            "presentation": "markdown",
            "type": "text"
        },
        {
            "name": "Actions",
            "id": "expand_button",
            "presentation": "markdown",
            "type": "text"
        }
    ]

def create_accessible_table():
    """Create table with full accessibility features."""
    return dash_table.DataTable(
        id="log-table",
        columns=create_accessible_table_columns(),
        data=[],
        page_size=50,
        page_action="custom",
        style_table={"overflowX": "auto"},
        style_cell={
            'textAlign': 'left',
            'padding': '8px',
            'fontFamily': 'monospace'
        },
        # Accessibility features
        row_selectable="single",
        selected_rows=[],
        # Add ARIA labels
        aria_label="Structured log entries table",
        # Add table description
        tooltip_header={
            'timestamp_display': 'Log entry timestamp - click to jump to time',
            'log_level': 'Log severity level - click to filter',
            'log_event': 'Log event type - click to filter',
            'process_name': 'Source process - click to filter',
            'library': 'Source library - click to filter',
            'context_summary': 'Key contextual information',
            'expand_button': 'Expand to see full details'
        },
        # Improve keyboard navigation
        style_data_conditional=[
            {
                'if': {'state': 'selected'},
                'backgroundColor': '#e3f2fd',
                'border': '2px solid #2196f3'
            },
            {
                'if': {'filter_query': '{log_level} = ERROR'},
                'backgroundColor': '#ffebee',
                'color': 'black',
            },
            {
                'if': {'filter_query': '{log_level} = WARN'},
                'backgroundColor': '#fff3e0',
                'color': 'black',
            }
        ],
        # Enhanced navigation
        style_header={
            'backgroundColor': '#f5f5f5',
            'fontWeight': 'bold'
        }
    )
```

### Testing Strategy - Phase 5

#### Performance Tests
**File**: `tests/test_performance.py`

```python
import pytest
import time
from unittest.mock import Mock
from tern.utils.performance import cache_with_ttl, optimize_log_loading

class TestPerformance:
    def test_cache_with_ttl_caches_results(self):
        """Test that caching decorator caches function results."""
        call_count = 0

        @cache_with_ttl(ttl_seconds=60)
        def test_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2

        # First call
        result1 = test_function(5)
        assert result1 == 10
        assert call_count == 1

        # Second call with same args should use cache
        result2 = test_function(5)
        assert result2 == 10
        assert call_count == 1  # Not incremented

        # Different args should call function
        result3 = test_function(10)
        assert result3 == 20
        assert call_count == 2

    def test_optimize_log_loading_performance(self):
        """Test that log loading optimization tracks performance."""
        logs = [{'log_event': 'test', 'timestamp': '2025-01-01T00:00:00'} for _ in range(100)]

        result = optimize_log_loading(logs, enable_grouping=False)

        assert 'performance' in result
        assert 'processing_time_ms' in result['performance']
        assert result['performance']['processing_time_ms'] >= 0
        assert result['performance']['original_count'] == 100

    def test_optimize_log_loading_with_large_dataset(self):
        """Test performance with large dataset."""
        # Create large dataset
        logs = [
            {
                'log_event': 'heartbeat',
                'log_level': 'INFO',
                'process_name': 'server',
                'library': 'util',
                'timestamp': f'2025-01-01T00:{i:02d}:00'
            }
            for i in range(1000)  # 1000 identical events
        ]

        start_time = time.time()
        result = optimize_log_loading(logs, enable_grouping=True)
        end_time = time.time()

        # Should complete quickly even with large dataset
        assert (end_time - start_time) < 5.0  # Less than 5 seconds

        # Should reduce the number of entries through grouping
        assert len(result['logs']) < len(logs)
        assert result['performance']['grouped_count'] > 0
```

#### Accessibility Tests
**File**: `tests/test_accessibility.py`

```python
import pytest
from tern.utils.accessibility import create_accessible_table_columns, create_accessible_table

class TestAccessibility:
    def test_accessible_table_has_required_attributes(self):
        """Test that accessible table has required ARIA attributes."""
        table = create_accessible_table()

        # Check for accessibility attributes
        assert 'aria_label' in table.__dict__ or hasattr(table, 'aria_label')
        assert 'tooltip_header' in table.__dict__ or hasattr(table, 'tooltip_header')

    def test_accessible_columns_have_descriptions(self):
        """Test that columns have accessible descriptions."""
        columns = create_accessible_table_columns()

        # All columns should have identifiable names
        for column in columns:
            assert 'name' in column
            assert 'id' in column
            assert column['name'] is not None
```

#### Manual Testing Checkpoints

1. **Performance**:
   - [ ] Load large dataset (1000+ logs) and measure loading time
   - [ ] Test pagination performance with large datasets
   - [ ] Verify grouping performance with repetitive logs
   - [ ] Check memory usage during extended use

2. **Loading States**:
   - [ ] Verify loading spinner appears during data loading
   - [ ] Check that error messages display when loading fails
   - [ ] Test retry functionality after errors
   - [ ] Verify loading states clear properly

3. **Keyboard Shortcuts**:
   - [ ] Test Ctrl+Left/Right for page navigation
   - [ ] Test Ctrl+Home/End for first/last page
   - [ ] Test Enter to expand selected row
   - [ ] Test Escape to collapse expanded row
   - [ ] Test Ctrl+G to toggle grouping settings

4. **Accessibility**:
   - [ ] Test with screen reader (if available)
   - [ ] Verify tab navigation works through all interactive elements
   - [ ] Check that ARIA labels are present
   - [ ] Test high contrast mode compatibility
   - [ ] Verify keyboard-only navigation

5. **Error Handling**:
   - [ ] Test with corrupted log data
   - [ ] Test with no log data
   - [ ] Test with invalid time ranges
   - [ ] Test with analyzer errors
   - [ ] Verify graceful degradation

---

## Integration Testing Strategy

### End-to-End Test Scenarios

#### Scenario 1: Complete Log Analysis Workflow
**Test Steps**:
1. Load log data using existing mechanism
2. Adjust time range slider to specific period
3. Apply position filter
4. Verify table updates with filtered data
5. Click on log level to apply additional filter
6. Expand a log entry to see details
7. Click "Jump to Time" from expanded row
8. Verify time range centers on selected timestamp
9. Test grouping toggle on/off
10. Export or copy log data

**Expected Results**:
- All filtering operations work correctly
- Time range updates properly
- Grouping reduces repetitive entries
- Expanded details show complete information
- Performance remains acceptable throughout

#### Scenario 2: Large Dataset Performance
**Test Steps**:
1. Load large log dataset (10,000+ entries)
2. Enable grouping and verify space savings
3. Test pagination through multiple pages
4. Apply various filters and measure response time
5. Test keyboard navigation shortcuts
6. Verify memory usage remains stable

**Expected Results**:
- Large datasets load within reasonable time (<10 seconds)
- Grouping significantly reduces displayed entries
- Pagination remains responsive
- Memory usage stays below 500MB for browser tab

#### Scenario 3: Error Recovery Testing
**Test Steps**:
1. Start with working log data
2. Simulate analyzer error (disconnect/corruption)
3. Verify error message displays
4. Test retry functionality
5. Restore working data
6. Verify table recovers properly

**Expected Results**:
- Error messages are clear and actionable
- Retry functionality works
- No application crashes
- Full functionality restored after recovery

### Manual Integration Test Checklist

#### Basic Functionality ✅
- [ ] Table displays with all 7 columns
- [ ] Time range slider integration works
- [ ] Position filtering integration works
- [ ] Pagination controls function correctly
- [ ] Context column shows relevant information

#### Expansion and Details ✅
- [ ] Row expansion shows complete log details
- [ ] All keys are displayed in organized format
- [ ] Raw message toggle works
- [ ] Copy functionality works
- [ ] Action buttons function correctly

#### Grouping Features ✅
- [ ] Consecutive identical events are grouped
- [ ] Grouped rows display count and time range
- [ ] Group expansion shows individual events
- [ ] Grouping settings can be adjusted
- [ ] Grouping toggle works immediately

#### Filtering Integration ✅
- [ ] Click-to-filter works for all columns
- [ ] Jump-to-time centers range slider
- [ ] Multiple filters combine correctly
- [ ] Filter clearing works properly
- [ ] Filter state persists during pagination

#### Performance and Polish ✅
- [ ] Loading states display during operations
- [ ] Error messages are helpful and actionable
- [ ] Keyboard shortcuts work as documented
- [ ] Accessibility features function properly
- [ ] Performance remains acceptable with large datasets

### Automated Test Coverage Goals

- **Unit Tests**: 90%+ coverage for all utility functions
- **Integration Tests**: All callback functions tested
- **Performance Tests**: Load testing with 1K, 10K, 100K entries
- **Accessibility Tests**: Automated a11y scanning
- **Error Handling Tests**: All error scenarios covered

---

## Deployment Checklist

### Pre-Deployment Requirements

#### Code Quality ✅
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed
- [ ] Performance benchmarks met
- [ ] Accessibility requirements verified

#### Documentation ✅
- [ ] User documentation updated
- [ ] API documentation current
- [ ] Installation instructions verified
- [ ] Troubleshooting guide updated

#### Compatibility ✅
- [ ] Browser compatibility tested (Chrome, Firefox, Safari, Edge)
- [ ] Python version compatibility verified
- [ ] Dependency conflicts resolved
- [ ] Database/analyzer compatibility confirmed

### Post-Deployment Verification

#### Functionality Verification ✅
- [ ] All features work in production environment
- [ ] Performance meets expectations
- [ ] Error handling works correctly
- [ ] User workflows complete successfully

#### Monitoring Setup ✅
- [ ] Performance monitoring enabled
- [ ] Error tracking configured
- [ ] User analytics (if applicable) set up
- [ ] Health checks functioning

---

This comprehensive implementation plan provides detailed guidance for each phase of development, complete with testing strategies and integration points for manual verification. Each phase builds upon the previous one, ensuring a solid foundation while adding increasingly sophisticated features.
