# Investigation Workflow Implementation Plan

## Overview

This document provides detailed implementation guidance for the investigation workflow feature. The implementation follows a phased approach to minimize risk while delivering incremental value.

## Phase 1: Core Investigation Infrastructure (Weeks 1-3)

### 1.1 Log Parser Enhancements

#### New InvestigationAnalyzer Class
```python
# log_parser/log_parser/analysis/investigation_analyzer.py
class InvestigationAnalyzer:
    """Specialized analyzer for investigation workflow queries"""

    def __init__(self, resource_analyzer, structured_log_analyzer, action_analyzer):
        self.resource_analyzer = resource_analyzer
        self.structured_log_analyzer = structured_log_analyzer
        self.action_analyzer = action_analyzer

    def get_investigation_summary(self, target_time: datetime,
                                 window_minutes: int = 60,
                                 position: str = None) -> Dict[str, Any]:
        """Core investigation context aggregation"""
        # Implementation details in separate technical document

    def get_system_state_at_time(self, target_time: datetime,
                                position: str = None) -> Dict[str, Any]:
        """Resource snapshot at specific moment"""

    def get_events_around_time(self, target_time: datetime,
                              window_minutes: int = 60,
                              position: str = None) -> List[Dict[str, Any]]:
        """Events within time window with relevance scoring"""
```

#### Enhanced Base Analyzer Methods
```python
# log_parser/log_parser/analysis/base_analyzer.py
class BaseAnalyzer:
    def query_around_time(self, target_time: datetime,
                         window_minutes: int = 60, **filters) -> pd.DataFrame:
        """Optimized time window queries"""

    def get_temporal_density(self, start: datetime, end: datetime,
                           bucket_size_minutes: int = 5) -> pd.Series:
        """Event density for hotspot identification"""
```

#### Action Analyzer Extensions
```python
# log_parser/log_parser/analysis/action_analyzer.py
class ActionAnalyzer:
    def get_actions_at_time(self, target_time: datetime,
                           position_id: str = None) -> List[Action]:
        """Actions active at specific time"""

    def get_action_context_window(self, target_time: datetime,
                                 window_minutes: int = 30) -> Dict[str, Any]:
        """Action context around target time"""
```

### 1.2 Dash Application Infrastructure

#### Investigation Layout Module
```python
# tern/tern/layout/investigation_layout.py
def create_investigation_layout():
    """Investigation-focused layout with accordion structure"""

def create_investigation_setup_panel():
    """Time picker, position filter, window controls"""

def create_investigation_accordions():
    """Accordion framework with smart labeling"""
```

#### Investigation Data Service
```python
# tern/tern/services/investigation_service.py
class InvestigationService:
    def __init__(self, analyzer_container):
        self.investigation_analyzer = analyzer_container.get_analyzer("investigation")

    def get_investigation_data(self, target_time: datetime,
                              window_minutes: int = 60,
                              position: str = None) -> Dict[str, Any]:
        """Coordinate investigation data retrieval"""
```

### 1.3 UI Components

#### Investigation Setup Component
```python
# tern/tern/components/investigation_setup.py
def create_investigation_setup_panel():
    """Target time picker, position filter, window control"""

def create_quick_investigation_presets():
    """Preset buttons for common investigation scenarios"""
```

#### Smart Accordion System
```python
# tern/tern/components/smart_accordion.py
def create_smart_accordion(accordion_id: str,
                          title: str,
                          content_callback: Callable,
                          relevance_score: float = 0.5,
                          auto_expand: bool = False):
    """Reusable accordion with intelligent labeling"""
```

### 1.4 Phase 1 Deliverables
- [ ] InvestigationAnalyzer with core methods
- [ ] Investigation layout framework
- [ ] Basic investigation setup panel
- [ ] Smart accordion component system
- [ ] Investigation data service layer
- [ ] Feature flag toggle between layouts
- [ ] Unit tests for new analyzer methods

## Phase 2: Accordion Content Implementation (Weeks 4-6)

### 2.1 Above Table Accordions

#### System Health Check Accordion
```python
# tern/tern/components/accordions/system_health.py
def create_system_health_accordion(target_time: datetime, position: str = None):
    """
    Content:
    - CPU usage at target time vs baseline
    - Memory usage snapshot with trend indicator
    - Disk space status across mounts
    - Quick pass/fail indicators for resource exhaustion
    """
```

#### Cross-Position Activity Accordion
```python
# tern/tern/components/accordions/cross_position.py
def create_cross_position_accordion(target_time: datetime, window_minutes: int = 60):
    """
    Content:
    - Position status matrix (active/idle/error)
    - Concurrent action summary across positions
    - Resource usage heatmap by position
    - Error count comparison between positions
    """
```

#### Critical Events Accordion
```python
# tern/tern/components/accordions/critical_events.py
def create_critical_events_accordion(target_time: datetime, window_minutes: int = 60):
    """
    Content:
    - Error/warning timeline with severity indicators
    - Event relevance scoring and filtering
    - Related event clustering
    - Quick summary badges (e.g., "3 errors, 2 warnings")
    """
```

#### Performance Snapshot Accordion
```python
# tern/tern/components/accordions/performance_snapshot.py
def create_performance_snapshot_accordion(target_time: datetime):
    """
    Content:
    - Resource metrics comparison vs baseline
    - Anomaly detection results
    - Performance trend indicators
    - Quick comparison against normal operating ranges
    """
```

### 2.2 Below Table Accordions

#### Resource Trends Accordion
```python
# tern/tern/components/accordions/resource_trends.py
def create_resource_trends_accordion(target_time: datetime, lookback_days: int = 7):
    """
    Content:
    - Weekly/daily resource patterns
    - Anomaly detection with historical context
    - Trend comparison charts
    - Pattern deviation indicators
    """
```

#### Action Timeline Accordion
```python
# tern/tern/components/accordions/action_timeline.py
def create_action_timeline_accordion(target_time: datetime, window_minutes: int = 120):
    """
    Content:
    - Protocol/workflow timeline across positions
    - Action dependency visualization
    - Action failure context
    - Inter-position action coordination
    """
```

### 2.3 Enhanced Log Parser Capabilities

#### Error Clustering Methods
```python
# log_parser/log_parser/analysis/investigation_analyzer.py
class InvestigationAnalyzer:
    def cluster_errors_by_similarity(self, start: datetime, end: datetime,
                                   cluster_threshold: float = 0.8) -> Dict[str, List[Dict[str, Any]]]:
        """Group similar errors by content and temporal proximity"""

    def get_error_progression_timeline(self, start: datetime, end: datetime,
                                     position: str = None) -> List[Dict[str, Any]]:
        """Trace error escalation patterns"""
```

#### Timeline Construction Methods
```python
def build_unified_timeline(self, start: datetime, end: datetime,
                         position: str = None) -> List[Dict[str, Any]]:
    """Merge actions, events, and resource changes"""

def get_critical_event_sequence(self, target_time: datetime,
                              window_minutes: int = 30) -> List[Dict[str, Any]]:
    """Extract critical event sequences"""
```

### 2.4 Phase 2 Deliverables
- [ ] All above-table accordion implementations
- [ ] Enhanced log parser with clustering capabilities
- [ ] Timeline construction methods
- [ ] Accordion content caching system
- [ ] Relevance scoring algorithms
- [ ] Integration tests for accordion data flows

## Phase 3: Advanced Investigation Features (Weeks 7-9)

### 3.1 Historical Pattern Matching

#### Pattern Matching Service
```python
# tern/tern/services/pattern_matching_service.py
class PatternMatchingService:
    def find_similar_incidents(self, target_time: datetime,
                             similarity_window: int = 60,
                             lookback_days: int = 30) -> List[Dict[str, Any]]:
        """Historical incident pattern matching"""

    def get_recurring_patterns(self, start: datetime, end: datetime) -> List[Dict[str, Any]]:
        """Identify recurring issue patterns"""
```

#### Similar Incidents Accordion
```python
# tern/tern/components/accordions/similar_incidents.py
def create_similar_incidents_accordion(target_time: datetime, lookback_days: int = 30):
    """
    Content:
    - Historical incident matches with similarity scores
    - Pattern correlation analysis
    - Resolution timeline comparisons
    - Links to jump to similar incident times
    """
```

### 3.2 Cross-Position Analysis

#### Cross-Position Analyzer Extensions
```python
# log_parser/log_parser/analysis/investigation_analyzer.py
class InvestigationAnalyzer:
    def compare_positions_at_time(self, target_time: datetime,
                                 metrics: List[str],
                                 window_minutes: int = 30) -> Dict[str, Dict[str, float]]:
        """Compare resource metrics across positions"""

    def find_outlier_positions(self, target_time: datetime,
                             metrics: List[str]) -> List[Dict[str, Any]]:
        """Identify positions with unusual patterns"""
```

### 3.3 Advanced Visualization Types

#### Causality Timeline Visualization
```python
# tern/tern/components/charts/causality_timeline.py
def create_causality_timeline_chart(target_time: datetime,
                                  window_minutes: int = 60) -> dmc.Group:
    """
    Interactive timeline showing:
    - Event causality relationships
    - Resource impact propagation
    - Action dependency chains
    - Cross-position event correlation
    """
```

#### Resource Correlation Heatmap
```python
# tern/tern/components/charts/correlation_heatmap.py
def create_correlation_heatmap(target_time: datetime,
                              metrics: List[str]) -> dmc.Group:
    """
    Heatmap visualization showing:
    - Resource metric correlations
    - Position-wise metric comparison
    - Anomaly hotspots
    - Temporal correlation patterns
    """
```

#### Error Propagation Network
```python
# tern/tern/components/charts/error_network.py
def create_error_propagation_network(target_time: datetime,
                                   window_minutes: int = 30) -> dmc.Group:
    """
    Network visualization showing:
    - Error propagation paths
    - Position interaction networks
    - Failure cascade patterns
    - Recovery timeline visualization
    """
```

### 3.4 Phase 3 Deliverables
- [ ] Historical pattern matching capabilities
- [ ] Cross-position comparative analysis
- [ ] Advanced visualization components
- [ ] Causality analysis algorithms
- [ ] Error propagation tracking
- [ ] Performance optimization for complex queries

## Phase 4: User Experience Enhancements (Weeks 10-12)

### 4.1 Keyboard Navigation System

#### Navigation Service
```python
# tern/tern/services/navigation_service.py
class NavigationService:
    def handle_keyboard_navigation(self, key: str, current_time: datetime) -> datetime:
        """Handle temporal navigation via keyboard"""

    def get_navigation_context(self, current_time: datetime) -> Dict[str, Any]:
        """Provide navigation context and suggestions"""
```

#### Keyboard Shortcuts
```python
# tern/tern/components/keyboard_handler.py
INVESTIGATION_SHORTCUTS = {
    'ArrowLeft': 'Navigate backward in time',
    'ArrowRight': 'Navigate forward in time',
    'ArrowUp': 'Zoom out time window',
    'ArrowDown': 'Zoom in time window',
    'Enter': 'Focus on selected event time',
    'Escape': 'Return to investigation overview',
    'Space': 'Toggle accordion expansion'
}
```

### 4.2 Investigation Bookmarks

#### Bookmark Service
```python
# tern/tern/services/bookmark_service.py
class BookmarkService:
    def save_investigation(self, target_time: datetime,
                          position: str,
                          window_minutes: int,
                          name: str) -> str:
        """Save investigation configuration"""

    def load_investigation(self, bookmark_id: str) -> Dict[str, Any]:
        """Load saved investigation"""

    def get_investigation_history(self) -> List[Dict[str, Any]]:
        """Get recent investigation history"""
```

### 4.3 Export and Reporting

#### Report Generation Service
```python
# tern/tern/services/report_service.py
class ReportService:
    def generate_investigation_report(self, target_time: datetime,
                                    window_minutes: int,
                                    position: str = None) -> Dict[str, Any]:
        """Generate comprehensive investigation report"""

    def export_investigation_data(self, target_time: datetime,
                                format: str = 'json') -> bytes:
        """Export investigation data in various formats"""
```

### 4.4 Phase 4 Deliverables
- [ ] Keyboard navigation system
- [ ] Investigation bookmarks and history
- [ ] Export and reporting capabilities
- [ ] User onboarding and help system
- [ ] Performance monitoring and optimization
- [ ] Comprehensive documentation

## Database Schema Changes

### Investigation History Table
```sql
CREATE TABLE investigation_history (
    id UUID PRIMARY KEY,
    target_time TIMESTAMP NOT NULL,
    position VARCHAR(255),
    window_minutes INTEGER DEFAULT 60,
    name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    investigation_data JSONB
);
```

### Investigation Bookmarks Table
```sql
CREATE TABLE investigation_bookmarks (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    target_time TIMESTAMP NOT NULL,
    position VARCHAR(255),
    window_minutes INTEGER DEFAULT 60,
    accordion_states JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Performance Optimization Strategy

### 1. Query Optimization
- **Time-based indexing**: Create optimized indices for target_time ± window queries
- **Composite indices**: (position, timestamp) for position-specific investigations
- **Partial indices**: Sparse indices for error events and anomalies

### 2. Caching Strategy
- **Investigation result caching**: Cache complete investigation contexts for 1 hour
- **Pattern caching**: Cache frequently accessed historical patterns for 24 hours
- **Accordion content caching**: Cache individual accordion data for 30 minutes

### 3. Data Loading Optimization
- **Lazy loading**: Load accordion content only when expanded
- **Progressive loading**: Load critical data first, details on demand
- **Parallel processing**: Execute multi-source queries concurrently
- **Connection pooling**: Optimize database connections for concurrent queries

### 4. Memory Management
- **Window-based loading**: Only load data for active investigation windows
- **Result streaming**: Stream large result sets rather than loading in memory
- **Cleanup strategies**: Automatic cleanup of investigation caches after 2 hours

## Testing Strategy

### Unit Tests
- [ ] InvestigationAnalyzer method tests
- [ ] Accordion component rendering tests
- [ ] Service layer integration tests
- [ ] Query optimization validation tests

### Integration Tests
- [ ] End-to-end investigation workflow tests
- [ ] Cross-analyzer data flow tests
- [ ] Performance benchmarking tests
- [ ] Error handling and recovery tests

### Performance Tests
- [ ] Large dataset investigation performance
- [ ] Concurrent investigation handling
- [ ] Memory usage optimization validation
- [ ] Query response time benchmarks

## Deployment Strategy

### Environment Configuration
```python
# Investigation-specific settings
INVESTIGATION_CACHE_TTL = 3600  # 1 hour
INVESTIGATION_MAX_LOOKBACK_DAYS = 90
INVESTIGATION_DEFAULT_WINDOW_MINUTES = 60
INVESTIGATION_PARALLEL_QUERIES = 4
INVESTIGATION_BOOKMARK_RETENTION_DAYS = 365
```

### Feature Flag Management
```python
# Gradual rollout configuration
FEATURE_FLAGS = {
    'investigation_workflow': {
        'enabled': True,
        'rollout_percentage': 100,
        'user_whitelist': [],
        'position_whitelist': []
    }
}
```

### Monitoring and Observability
- Investigation workflow usage metrics
- Query performance monitoring
- Error rate tracking
- User adoption analytics
- Performance degradation alerts

## Risk Mitigation

### Technical Risks
- **Performance degradation**: Implement comprehensive caching and query optimization
- **Data consistency**: Ensure proper transaction boundaries and data validation
- **Memory usage**: Implement strict memory management and cleanup procedures

### User Experience Risks
- **Complexity**: Provide clear onboarding and progressive disclosure
- **Learning curve**: Implement contextual help and guided tours
- **Feature adoption**: Maintain backward compatibility with existing workflows

### Operational Risks
- **Deployment issues**: Implement blue-green deployment with rollback capabilities
- **Data migration**: Ensure seamless migration of existing user configurations
- **Performance impact**: Monitor system performance during rollout

## Success Metrics

### User Experience Metrics
- **Investigation completion time**: Target 50% reduction in root cause analysis time
- **Feature adoption rate**: Target 80% of users trying investigation mode within 30 days
- **User satisfaction**: Target 4.5/5 satisfaction rating through user surveys

### Technical Performance Metrics
- **Query response time**: Target <2 seconds for investigation summary queries
- **UI responsiveness**: Target <100ms for accordion expansion
- **Memory usage**: Target <500MB additional memory per investigation session
- **Cache hit rate**: Target >80% cache hit rate for investigation queries

### Business Impact Metrics
- **Incident resolution time**: Target 40% reduction in average incident resolution time
- **Investigation accuracy**: Target 90% accuracy in root cause identification
- **User productivity**: Target 30% increase in issues resolved per analyst per day

## Conclusion

This implementation plan provides a structured approach to delivering the investigation workflow feature while maintaining system stability and user experience. The phased approach allows for iterative feedback and continuous improvement throughout the development process.

The plan prioritizes core functionality in Phase 1, builds out comprehensive accordion content in Phase 2, adds advanced analysis capabilities in Phase 3, and polishes the user experience in Phase 4. Each phase delivers incremental value while building toward the complete investigation workflow vision.
