[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "tern"
version = "0.1.0"
description = "Log visualization tool"
readme = "README.md"
requires-python = ">=3.10"
license = "MIT"
keywords = ["logs", "visualization", "dash"]
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
]
dependencies = [
    "dash>=2.14.2",
    "dash-bootstrap-components>=1.5.0",
    "plotly>=5.18.0",
    "pandas>=2.1.4",
    "pywebview>=5.0.6",
    "psutil",
    "pydantic_settings",
    "dependency_injector",
    "adtk>=0.6.2",
    "toml",
    "dash_mantine_components",
    "dash_iconify",
]

[project.optional-dependencies]
# Backend API dependencies
backend = [
    "fastapi",
    "uvicorn",
    "python-dateutil>=2.8.2",
    "requests>=2.31.0",
]

# CLI tools
cli = [
    "typer>=0.9.0",
    "rich>=13.7.0",
    "plotext>=5.2.8",
    "numpy>=1.20.0",
]

# Development and testing tools
dev = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.10.0",
    "pytest-asyncio>=0.21.0",
    "httpx",
    "black>=23.11.0",
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "pre-commit>=3.5.0",
    "bandit[toml]>=1.7.5",
    "pydocstyle>=6.3.0",
    "isort>=5.12.0",
    "pylint>=3.0.0",
    "safety>=2.3.0",
]

# Additional analysis tools
analysis = [
    "matplotlib",
    "regex",
    "python-dotenv",
]

# Full development environment (includes everything)
full = [
    "tern[backend,cli,dev,analysis]",
]

[project.urls]
Documentation = "https://github.com/unknown/tern#readme"
Issues = "https://github.com/unknown/tern/issues"
Source = "https://github.com/unknown/tern"

[project.scripts]
tern = "tern.cli:main"

[tool.hatch.build.hooks.custom]
path = "build_hook.py"

[tool.hatch.build.targets.wheel]
packages = ["tern/tern"]

[tool.hatch.envs.default]
dependencies = [
    "pytest",
    "pytest-cov",
    "pytest-asyncio",
    "black",
    "ruff",
    "mypy",
    "pre-commit",
]

[tool.hatch.envs.default.scripts]
test = [
    "pip install -e ./log_parser",
    "pip install -e .",
    "pytest log_parser/tests || true",
    "pytest tern/tests"
]
test-cov = [
    "pip install -e ./log_parser",
    "pip install -e .",
    "pytest --cov-report=term-missing --cov-config=pyproject.toml --cov=tern --cov=log_parser log_parser/tests",
    "pytest --cov-report=term-missing --cov-config=pyproject.toml --cov=tern --cov=log_parser tern/tests"
]
test-tern = [
    "pip install -e ./log_parser",
    "pip install -e .",
    "pytest --verbose {args:tern/tests}"
]
test-all = [
    "pip install -e ./log_parser",
    "pip install -e .",
    "pytest log_parser/tests",
    "pytest tern/tests"
]
lint = [
    "ruff check {args:.}",
    "black --check {args:.}",
    "mypy {args:tern log_parser}",
]
fmt = [
    "ruff check --fix {args:.}",
    "black {args:.}",
]
dev = [
    "pip install -e ./log_parser",
    "pip install -e .",
    "python -m tern.main {args:}"
]

[tool.hatch.metadata]
allow-direct-references = true

[tool.pytest.ini_options]
addopts = "-ra -q"
asyncio_mode = "auto"
testpaths = [
    "log_parser/tests",
    "tern/tests",
]

[tool.coverage.run]
source_pkgs = ["tern", "log_parser"]
branch = true

[tool.coverage.report]
exclude_lines = [
    "no cov",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]

[tool.ruff]
target-version = "py310"
line-length = 140
extend-exclude = [
    ".git",
    ".venv",
    "venv",
    "dist",
    "build",
    "*.egg-info",
    "coverage_html",
    "intermediate_data",
    "tests",
    "*/tests",
    "**/tests/**",
    "archive",
    "*/archive",
    "**/archive/**",
]

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "C",    # mccabe
    "I",    # isort
    "N",    # pep8-naming
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "YTT",  # flake8-2020
    "ANN",  # flake8-annotations
    "S",    # flake8-bandit
    "BLE",  # flake8-blind-except
    "FBT",  # flake8-boolean-trap
    "B",    # flake8-bugbear
    "A",    # flake8-builtins
    "COM",  # flake8-commas
    "C4",   # flake8-comprehensions
    "DTZ",  # flake8-datetimez
    "T10",  # flake8-debugger
    "EM",   # flake8-errmsg
    "EXE",  # flake8-executable
    "FA",   # flake8-future-annotations
    "ISC",  # flake8-implicit-str-concat
    "ICN",  # flake8-import-conventions
    "G",    # flake8-logging-format
    "INP",  # flake8-no-pep420
    "PIE",  # flake8-pie
    "T20",  # flake8-print
    "PYI",  # flake8-pyi
    "PT",   # flake8-pytest-style
    "Q",    # flake8-quotes
    "RSE",  # flake8-raise
    "RET",  # flake8-return
    "SLF",  # flake8-self
    "SIM",  # flake8-simplify
    "TID",  # flake8-tidy-imports
    "TCH",  # flake8-type-checking
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib
    "ERA",  # eradicate
    "PD",   # pandas-vet
    "PGH",  # pygrep-hooks
    "PL",   # pylint
    "TRY",  # tryceratops
    "FLY",  # flynt
    "NPY",  # numpy-specific
    "PERF", # perflint
    "RUF",  # ruff-specific
]
ignore = [
    "D100",    # Missing docstring in public module
    "D104",    # Missing docstring in public package
    "D107",    # Missing docstring in __init__
    "COM812",  # Trailing comma missing (conflicts with formatter)
    "ISC001",  # Implicitly concatenated string literals (conflicts with formatter)
    "FBT003",  # Boolean positional value in function call
    "PLR0913", # Too many arguments in function definition
    "D203",    # 1 blank line required before class docstring (conflicts with D211)
    "D213",    # Multi-line docstring summary should start at the second line (conflicts with D212)
    "TID252",  # Relative imports from parent modules are banned
]

[tool.ruff.lint.isort]
known-first-party = ["tern", "log_parser"]
required-imports = ["from __future__ import annotations"]

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true
ignore-fully-untyped = true

[tool.ruff.lint.flake8-bandit]
check-typed-exception = true

[tool.ruff.lint.per-file-ignores]
"tests/**/*" = ["S101", "PLR2004", "ANN", "D"]  # Allow assert, magic values, missing annotations, missing docstrings in tests
"examples/**/*" = ["D", "ANN", "T20", "EXE001", "PLR0915", "BLE001", "DTZ005"]  # Allow missing docstrings, annotations, prints, executables, long functions, blind exceptions, naive datetime in examples
"**/conftest.py" = ["D", "ANN"]  # Allow missing docstrings and annotations in conftest
"build_hook.py" = ["D", "ANN"]  # Allow missing docstrings and annotations in build hook

[tool.bandit]
exclude_dirs = ["tests", "*/tests", "**/tests/**", "examples", "*/examples", "**/examples/**"]
skips = ["B101"]  # Skip assert_used warnings in test files

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[[tool.mypy.overrides]]
module = [
    "dash.*",
    "dash_bootstrap_components.*",
    "plotly.*",
    "pandas.*",
    "pywebview.*",
]
ignore_missing_imports = true
