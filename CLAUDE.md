# CLAUDE.md – Project “Tern” (Dash + PyWebview)

> Claude, this file is **always in scope**.
> Treat every bullet that starts with “**YOU MUST**” as mandatory for gating merges.

---

## 📦 Quick start

| Task | Command |
|------|---------|
| Set up full dev env | `python3 -m venv .venv && python -m pip install -e '.[full]' && pre-commit install` |
| Run the app locally | `hatch run dev` (opens the PyWebview window) |
| Run complete test suite | `hatch run test-all` |
| Run fast feedback loop | `hatch run test-tern -k <pattern>` |
| See coverage | `hatch run test-cov` |
| Auto-format & lint | `hatch run fmt && hatch run lint` |

---

## ✅ Pre-commit Merge Gate

1. **YOU MUST** run `hatch run test` (unit + integration) and confirm zero failures.
2. **YOU MUST** run `hatch run lint`; the repo enforces:
   - **Black** (line length = 140)
   - **Mypy** in strict-ish mode (see `[tool.mypy]`)
3. **YOU MUST** stage only green files; no `todo` or `print` left in code.
4. If you touch UI files, **YOU MUST** complete the Manual QA checklist below.

_All tests & linters pass locally before `git commit --no-verify` is disabled in CI._

---

## 🖐️ Manual QA checklist

1. **YOU MUST** stop and check with the user if manual QA testing has suceeded

---

## 🔧 Project conventions

### Code style
- Prefer **pydantic models** for config / settings.
- One component per file inside `tern/components/`.
- Avoid `print`; use the project’s structured logger (`tern.logging.get_logger`).

### Branch & commit
- `feature/<short-description>` for work branches.
- Squash-merge via PR; commit message body auto-generated by `cz conventional-commits`, but **YOU MUST** keep the first line ≤ 72 chars.

---

## 👩‍💻 Core directories & key files

| Path | Why it matters |
|------|----------------|
| `tern/main.py` | App entry point invoked by `hatch run dev` |
| `tern/layout.py` | Top-level Dash layout factory |
| `tern/components/` | Reusable Dash/dbc components |
| `log_parser/` | Separate package for heavy-lift log parsing; installed editable in tests |
| `build_hook.py` | Custom Hatch build hook (adds assets) |

---

## 🔬 Testing pointers

- Tests live in `log_parser/tests` and `tern/tests`; they both use **pytest-asyncio** where appropriate.
- Use `pytest --ff` for fastest rerun of recent failures.
- To focus: `hatch run test-tern -k <test_case>`.

---

## 🐍 Virtual env & tooling notes

- Project requires **Python ≥ 3.10**; use `pyenv local 3.12.x` if available.
- Hatch handles isolated envs; avoid `pip install` outside Hatch unless debugging.

---

## 🛠️ Common one-liners

```bash
# Freeze exact deps (CI reproducibility)
hatch env run python -m pip freeze > requirements.lock

# Update all optional groups in one go
hatch env create full && hatch env run pip list
