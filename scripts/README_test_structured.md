# Structured Endpoint Integration Test

This directory contains an integration test script for the new `/structured` endpoint.

## Prerequisites

1. **Backend Running**: The backend must be running with parsed data loaded
2. **Dependencies**: Install the required Python packages

```bash
pip install -r requirements_test.txt
```

## Usage

### Basic Usage

```bash
python test_structured_endpoint.py
```

This will test against `http://localhost:8000` by default.

### Custom Backend URL

```bash
python test_structured_endpoint.py --url http://localhost:9000
```

### Verbose Output

```bash
python test_structured_endpoint.py --verbose
```

## What the Test Covers

The test script validates all endpoints provided by the `/structured` route:

### Basic Endpoints
- `GET /structured/positions` - Get available positions
- `GET /structured/log-levels` - Get available log levels
- `GET /structured/filters` - Get available filters
- `GET /structured/time-range` - Get overall time range

### Query Endpoints
- `GET /structured` - Query structured log entries with filters and pagination
- `GET /structured` with various filters (log_level, process_name, etc.)

### Position-Specific Endpoints
- `GET /structured/position/{position}/time-range` - Get time range for specific position

### Aggregation Endpoints
- `GET /structured/log-events` - Get counts of log events by type
- `GET /structured/log-levels/counts` - Get counts of log levels

### Timeline Endpoints
- `GET /structured/event-timeline` - Get event timeline for visualization
- `GET /structured/log-level-timeline` - Get log level timeline for visualization

### Advanced Features
- Pagination testing
- Position filtering
- Response structure validation
- Error handling

## Test Output

The script provides detailed output showing:
- ✅ PASS/❌ FAIL status for each test
- Details about what was tested
- Summary statistics
- Any errors encountered

## Example Output

```
🚀 Starting /structured endpoint integration tests...
📡 Testing against: http://localhost:8000
============================================================

✅ PASS Health Check
   Status: 200

📋 Running endpoint tests...

✅ PASS GET /structured/positions
   Found 2 positions: ['positionA', 'positionB']

✅ PASS GET /structured/log-levels
   Found 4 log levels: ['DEBUG', 'ERROR', 'INFO', 'WARNING']

✅ PASS GET /structured/filters
   Found 12 filters: ['timestamp', 'log_level', 'log_event', 'process_name', 'library', 'message', 'folder_name', 'key_action', 'key_device', 'key_event', 'key_level', 'key_message']

...

============================================================
📊 Test Summary:
✅ Passed: 12
❌ Failed: 0
📈 Success Rate: 100.0%
```

## Troubleshooting

### Backend Not Running
If you see "Backend is not running or not accessible", make sure:
1. The backend is started
2. It's running on the expected URL (default: http://localhost:8000)
3. The `/health` endpoint is accessible

### No Data Available
If tests pass but show "0 entries" or "no positions", the backend may not have parsed any log data. Make sure:
1. Log files are available in the configured directory
2. The backend has successfully parsed the logs
3. The parsed data contains structured log entries

### Timeout Errors
If you see timeout errors, the backend might be:
1. Processing large amounts of data
2. Under heavy load
3. Not responding quickly enough

You can increase the timeout in the script if needed.

## Integration with CI/CD

This test script can be integrated into CI/CD pipelines:

```bash
# Run tests and exit with failure code if any tests fail
python test_structured_endpoint.py --url $BACKEND_URL
```

The script exits with code 0 on success, 1 on failure.
