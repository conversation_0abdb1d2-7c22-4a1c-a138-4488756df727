#!/usr/bin/env python3
"""
Integration test script for the /timeline endpoint.

This script tests all the endpoints provided by the /timeline route:
- GET /timeline - Get timeline events with filtering options
- GET /timeline/config - Get timeline event configuration

Assumes the backend is already running with parsed data loaded.
"""

import json
import sys
import time
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any, Dict, List

import requests


class TimelineEndpointTester:
    """Test class for the /timeline endpoint."""

    def __init__(self, base_url: str = "http://localhost:8000", verbose: bool = False, response_limit: int = 1000):
        """Initialize the tester with the base URL."""
        self.base_url = base_url
        self.verbose = verbose
        self.response_limit = response_limit
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})

        # Test results
        self.results = {"passed": 0, "failed": 0, "errors": []}

        # Create output directory for large responses
        self.output_dir = Path("test_outputs")
        self.output_dir.mkdir(exist_ok=True)

    def format_json(self, data: Any, max_lines: int = 100) -> str:
        """Format JSON data with optional truncation for readability."""
        try:
            formatted = json.dumps(data, indent=2, default=str)
            lines = formatted.split("\n")

            if len(lines) > max_lines:
                # Truncate and add summary
                truncated = "\n".join(lines[:max_lines])
                truncated += f"\n... (truncated, showing {max_lines} of {len(lines)} lines)"
                return truncated
            else:
                return formatted
        except Exception as e:
            return f"<Error formatting JSON: {e}>"

    def save_large_response(self, endpoint: str, data: Any, params: Dict[str, Any] = None):
        """Save large responses to a file for manual inspection."""
        try:
            # Create a filename based on endpoint and timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            endpoint_clean = endpoint.replace("/", "_").replace(":", "_")
            filename = f"timeline_{endpoint_clean}_{timestamp}.json"
            filepath = self.output_dir / filename

            # Prepare metadata
            metadata = {
                "endpoint": endpoint,
                "timestamp": timestamp,
                "params": params,
                "data_size": len(json.dumps(data, default=str)),
                "data_type": type(data).__name__,
            }

            # Save the response
            with open(filepath, "w") as f:
                json.dump({"metadata": metadata, "data": data}, f, indent=2, default=str)

            print(f"💾 Large response saved to: {filepath}")
            return str(filepath)
        except Exception as e:
            print(f"⚠️  Failed to save large response: {e}")
            return None

    def log_request(self, method: str, endpoint: str, params: Dict[str, Any] = None):
        """Log the request being made."""
        if not self.verbose:
            return

        print(f"\n🔍 REQUEST:")
        print(f"   Method: {method}")
        print(f"   URL: {self.base_url}{endpoint}")
        if params:
            print(f"   Params: {self.format_json(params, max_lines=20)}")
        print()

    def log_response(self, status_code: int, data: Any, response_time: float = None):
        """Log the response received."""
        if not self.verbose:
            return

        print(f"📡 RESPONSE:")
        print(f"   Status: {status_code}")
        if response_time:
            print(f"   Time: {response_time:.3f}s")

        # Check if response is large and save it
        response_size = len(json.dumps(data, default=str))
        if response_size > self.response_limit:
            print(f"   ⚠️  Large response detected ({response_size} chars)")
            saved_file = self.save_large_response("", data)
            if saved_file:
                print(f"   📁 Response saved to: {saved_file}")

        print(f"   Data: {self.format_json(data)}")
        print()

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")

        if success:
            self.results["passed"] += 1
        else:
            self.results["failed"] += 1
            self.results["errors"].append(f"{test_name}: {details}")

    def make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a request to the API and return the JSON response."""
        url = f"{self.base_url}{endpoint}"

        # Log request
        self.log_request("GET", endpoint, params)

        try:
            start_time = time.time()
            response = self.session.get(url, params=params, timeout=30)
            response_time = time.time() - start_time

            response.raise_for_status()
            data = response.json()

            # Log response
            self.log_response(response.status_code, data, response_time)

            return data
        except requests.exceptions.RequestException as e:
            if self.verbose:
                print(f"📡 RESPONSE ERROR: {e}")
            raise Exception(f"Request failed: {e}")

    def test_health_check(self):
        """Test that the backend is running."""
        try:
            self.log_request("GET", "/health")

            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            response_time = time.time() - start_time

            success = response.status_code == 200
            data = response.json() if success else {"error": "Health check failed"}

            self.log_response(response.status_code, data, response_time)
            self.log_test("Health Check", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Health Check", False, str(e))
            return False

    def test_get_timeline_config(self):
        """Test GET /timeline/config."""
        try:
            data = self.make_request("/timeline/config")

            # Validate response structure
            assert "config" in data, "Response missing 'config' field"
            assert "timeline_event_types" in data["config"], "Response missing 'timeline_event_types' field"
            assert isinstance(data["config"]["timeline_event_types"], dict), "Timeline event types should be a dictionary"

            success = True
            event_types = list(data["config"]["timeline_event_types"].keys())
            details = f"Found {len(event_types)} timeline event types: {event_types[:5]}{'...' if len(event_types) > 5 else ''}"

            self.log_test("GET /timeline/config", success, details)
            return data["config"]["timeline_event_types"]
        except Exception as e:
            self.log_test("GET /timeline/config", False, str(e))
            return {}

    def test_get_timeline_events_basic(self):
        """Test GET /timeline with basic query."""
        try:
            data = self.make_request("/timeline")

            # Validate response structure
            assert "timeline_events" in data, "Response missing 'timeline_events' field"
            assert isinstance(data["timeline_events"], list), "Timeline events should be a list"

            success = True
            details = f"Found {len(data['timeline_events'])} timeline events"

            self.log_test("GET /timeline (basic query)", success, details)
            return data["timeline_events"]
        except Exception as e:
            self.log_test("GET /timeline (basic query)", False, str(e))
            return []

    def test_get_timeline_events_with_time_range(self):
        """Test GET /timeline with time range filtering."""
        try:
            # Use a reasonable time range for testing (last 24 hours)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)

            params = {"start": start_time.isoformat(), "end": end_time.isoformat()}

            data = self.make_request("/timeline", params)

            # Validate response structure
            assert "timeline_events" in data, "Response missing 'timeline_events' field"
            assert isinstance(data["timeline_events"], list), "Timeline events should be a list"

            # Validate that events are within the time range
            for event in data["timeline_events"]:
                assert "start_time" in event, "Event missing start_time"
                assert "end_time" in event, "Event missing end_time"
                assert "is_complete" in event, "Event missing is_complete"

                event_start = datetime.fromisoformat(event["start_time"].replace("Z", "+00:00"))

                # Events should start within or before the requested time range
                assert event_start <= end_time, f"Event starts after end time: {event_start} > {end_time}"

                # Only validate end time if the event is complete
                if event["is_complete"] and event["end_time"] is not None:
                    event_end = datetime.fromisoformat(event["end_time"].replace("Z", "+00:00"))
                    assert event_end >= start_time, f"Event ends before start time: {event_end} < {start_time}"

            success = True
            details = f"Found {len(data['timeline_events'])} timeline events in time range"

            self.log_test("GET /timeline (with time range)", success, details)
            return data["timeline_events"]
        except Exception as e:
            self.log_test("GET /timeline (with time range)", False, str(e))
            return []

    def test_get_timeline_events_with_position_filter(self):
        """Test GET /timeline with position filtering."""
        try:
            # First get some timeline events to find available positions
            basic_data = self.make_request("/timeline")
            events = basic_data.get("timeline_events", [])

            if not events:
                self.log_test("GET /timeline (with position filter)", True, "Skipped - no timeline events available")
                return []

            # Find a position from the events
            positions = set()
            for event in events:
                if "position_id" in event and event["position_id"]:
                    positions.add(event["position_id"])

            if not positions:
                self.log_test("GET /timeline (with position filter)", True, "Skipped - no positions found in events")
                return []

            # Test with first available position
            test_position = list(positions)[0]
            params = {"position_id": test_position}

            data = self.make_request("/timeline", params)

            # Validate response structure
            assert "timeline_events" in data, "Response missing 'timeline_events' field"
            assert isinstance(data["timeline_events"], list), "Timeline events should be a list"

            # Validate that all events have the correct position
            for event in data["timeline_events"]:
                assert (
                    event.get("position_id") == test_position
                ), f"Event should have position {test_position}, got {event.get('position_id')}"

            success = True
            details = f"Filtered by position {test_position}, found {len(data['timeline_events'])} events"

            self.log_test("GET /timeline (with position filter)", success, details)
            return data["timeline_events"]
        except Exception as e:
            self.log_test("GET /timeline (with position filter)", False, str(e))
            return []

    def test_get_timeline_events_with_event_type_filter(self):
        """Test GET /timeline with event type filtering."""
        try:
            # First get the timeline config to find available event types
            config_data = self.make_request("/timeline/config")
            event_types = list(config_data.get("timeline_event_types", {}).keys())

            if not event_types:
                self.log_test("GET /timeline (with event type filter)", True, "Skipped - no event types available")
                return []

            # Test with first available event type
            test_event_type = event_types[0]
            params = {"timeline_event_type": test_event_type}

            data = self.make_request("/timeline", params)

            # Validate response structure
            assert "timeline_events" in data, "Response missing 'timeline_events' field"
            assert isinstance(data["timeline_events"], list), "Timeline events should be a list"

            # Validate that all events have the correct event type
            for event in data["timeline_events"]:
                assert event.get("name") == test_event_type, f"Event should have type {test_event_type}, got {event.get('name')}"

            success = True
            details = f"Filtered by event type {test_event_type}, found {len(data['timeline_events'])} events"

            self.log_test("GET /timeline (with event type filter)", success, details)
            return data["timeline_events"]
        except Exception as e:
            self.log_test("GET /timeline (with event type filter)", False, str(e))
            return []

    def test_get_timeline_events_with_children_filter(self):
        """Test GET /timeline with include_children filter."""
        try:
            # Test with include_children=True (default)
            params_true = {"include_children": True}
            data_true = self.make_request("/timeline", params_true)

            # Test with include_children=False
            params_false = {"include_children": False}
            data_false = self.make_request("/timeline", params_false)

            # Validate response structure
            assert "timeline_events" in data_true, "Response missing 'timeline_events' field"
            assert "timeline_events" in data_false, "Response missing 'timeline_events' field"

            success = True
            details = f"include_children=True: {len(data_true['timeline_events'])} events, include_children=False: {len(data_false['timeline_events'])} events"

            self.log_test("GET /timeline (with children filter)", success, details)
            return data_true["timeline_events"]
        except Exception as e:
            self.log_test("GET /timeline (with children filter)", False, str(e))
            return []

    def test_get_timeline_events_combined_filters(self):
        """Test GET /timeline with multiple filters combined."""
        try:
            # Get some basic data to find available positions and event types
            basic_data = self.make_request("/timeline")
            events = basic_data.get("timeline_events", [])

            if not events:
                self.log_test("GET /timeline (combined filters)", True, "Skipped - no timeline events available")
                return []

            # Find available positions and event types
            positions = set()
            event_types = set()
            for event in events:
                if "position_id" in event and event["position_id"]:
                    positions.add(event["position_id"])
                if "name" in event and event["name"]:
                    event_types.add(event["name"])

            if not positions or not event_types:
                self.log_test("GET /timeline (combined filters)", True, "Skipped - insufficient data for combined filtering")
                return []

            # Use a reasonable time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=12)

            params = {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "position_id": list(positions)[0],
                "timeline_event_type": list(event_types)[0],
                "include_children": True,
            }

            data = self.make_request("/timeline", params)

            # Validate response structure
            assert "timeline_events" in data, "Response missing 'timeline_events' field"
            assert isinstance(data["timeline_events"], list), "Timeline events should be a list"

            success = True
            details = f"Combined filters applied, found {len(data['timeline_events'])} events"

            self.log_test("GET /timeline (combined filters)", success, details)
            return data["timeline_events"]
        except Exception as e:
            self.log_test("GET /timeline (combined filters)", False, str(e))
            return []

    def test_timeline_event_structure(self):
        """Test the structure of timeline events."""
        try:
            data = self.make_request("/timeline")
            events = data.get("timeline_events", [])

            if not events:
                self.log_test("Timeline Event Structure Test", True, "Skipped - no timeline events available")
                return True

            # Validate structure of first event
            event = events[0]
            required_fields = ["id", "name", "start_time", "end_time", "duration", "position_id", "metadata", "has_children", "is_complete"]

            for field in required_fields:
                assert field in event, f"Event missing required field: {field}"

            # Validate data types
            assert isinstance(event["id"], str), "ID should be a string"
            assert isinstance(event["name"], str), "Name should be a string"
            assert isinstance(event["start_time"], str), "Start time should be a string"
            assert isinstance(event["position_id"], str), "Position ID should be a string"
            assert isinstance(event["metadata"], dict), "Metadata should be a dictionary"
            assert isinstance(event["has_children"], bool), "Has children should be a boolean"
            assert isinstance(event["is_complete"], bool), "Is complete should be a boolean"

            # Validate end_time and duration based on completion status
            if event["is_complete"]:
                assert isinstance(event["end_time"], str), "End time should be a string for complete events"
                assert isinstance(event["duration"], (int, float)), "Duration should be a number for complete events"

                # Validate time consistency for complete events
                start_time = datetime.fromisoformat(event["start_time"].replace("Z", "+00:00"))
                end_time = datetime.fromisoformat(event["end_time"].replace("Z", "+00:00"))
                calculated_duration = (end_time - start_time).total_seconds()

                # Allow for small floating point differences
                assert abs(event["duration"] - calculated_duration) < 1, f"Duration mismatch: {event['duration']} vs {calculated_duration}"
            else:
                # For incomplete events, end_time and duration should be None
                assert event["end_time"] is None, "End time should be None for incomplete events"
                assert event["duration"] is None, "Duration should be None for incomplete events"

            # Validate children structure if present
            if event["has_children"]:
                assert "children" in event, "Event with children should have children field"
                assert isinstance(event["children"], list), "Children should be a list"

            success = True
            details = f"Validated structure of {len(events)} timeline events"

            self.log_test("Timeline Event Structure Test", success, details)
            return True
        except Exception as e:
            self.log_test("Timeline Event Structure Test", False, str(e))
            return False

    def test_timeline_event_hierarchy(self):
        """Test timeline event hierarchy and parent-child relationships."""
        try:
            data = self.make_request("/timeline")
            events = data.get("timeline_events", [])

            if not events:
                self.log_test("Timeline Event Hierarchy Test", True, "Skipped - no timeline events available")
                return True

            # Find events with children
            parent_events = [event for event in events if event.get("has_children", False)]
            child_events = [event for event in events if not event.get("has_children", False)]

            if not parent_events:
                self.log_test("Timeline Event Hierarchy Test", True, "Skipped - no parent events found")
                return True

            # Validate parent-child relationships
            parent_ids = set()
            child_ids = set()

            for event in events:
                if event.get("has_children", False):
                    parent_ids.add(event["id"])
                    if "children" in event:
                        child_ids.update(event["children"])

            # Check that child IDs exist in the events list
            all_event_ids = {event["id"] for event in events}
            missing_children = child_ids - all_event_ids

            success = True
            details = f"Found {len(parent_events)} parent events and {len(child_events)} child events"

            if missing_children:
                self.log_test("Timeline Event Hierarchy Test", False, f"Missing child events: {missing_children}")
                return False

            self.log_test("Timeline Event Hierarchy Test", success, details)
            return True
        except Exception as e:
            self.log_test("Timeline Event Hierarchy Test", False, str(e))
            return False

    def test_timeline_event_metadata(self):
        """Test timeline event metadata handling."""
        try:
            data = self.make_request("/timeline")
            events = data.get("timeline_events", [])

            if not events:
                self.log_test("Timeline Event Metadata Test", True, "Skipped - no timeline events available")
                return True

            # Check metadata structure
            for event in events:
                assert "metadata" in event, "Event missing metadata field"
                assert isinstance(event["metadata"], dict), "Metadata should be a dictionary"

                # Check that metadata values are serializable (no NaN, inf, etc.)
                for key, value in event["metadata"].items():
                    if isinstance(value, float):
                        # Check for NaN or inf values
                        if value != value or value in (float("inf"), float("-inf")):
                            self.log_test("Timeline Event Metadata Test", False, f"Invalid metadata value: {key}={value}")
                            return False

            success = True
            details = f"Validated metadata for {len(events)} timeline events"

            self.log_test("Timeline Event Metadata Test", success, details)
            return True
        except Exception as e:
            self.log_test("Timeline Event Metadata Test", False, str(e))
            return False

    def test_invalid_parameters(self):
        """Test error handling for invalid parameters."""
        try:
            # Test with invalid time format
            params = {"start": "invalid-time-format"}

            try:
                self.make_request("/timeline", params)
                # If we get here, the request succeeded when it should have failed
                self.log_test("Invalid Parameters Test", False, "Request succeeded with invalid time format")
                return False
            except Exception as e:
                if "400" in str(e) or "validation" in str(e).lower():
                    self.log_test("Invalid Parameters Test", True, "Correctly rejected invalid time format")
                    return True
                else:
                    self.log_test("Invalid Parameters Test", False, f"Unexpected error: {e}")
                    return False

        except Exception as e:
            self.log_test("Invalid Parameters Test", False, str(e))
            return False

    def run_all_tests(self):
        """Run all tests for the /timeline endpoint."""
        print("🚀 Starting /timeline endpoint integration tests...")
        print(f"📡 Testing against: {self.base_url}")
        print(f"📁 Large responses (> {self.response_limit} chars) will be saved to: {self.output_dir}")
        if self.verbose:
            print("🔍 Verbose mode enabled - showing requests and responses")
        print("=" * 60)

        # Test health check first
        if not self.test_health_check():
            print("❌ Backend is not running or not accessible. Please start the backend first.")
            return False

        print("\n📋 Running endpoint tests...")

        # Test configuration endpoint
        event_types = self.test_get_timeline_config()

        # Test basic timeline events query
        self.test_get_timeline_events_basic()

        # Test filtering options
        self.test_get_timeline_events_with_time_range()
        self.test_get_timeline_events_with_position_filter()
        self.test_get_timeline_events_with_event_type_filter()
        self.test_get_timeline_events_with_children_filter()
        self.test_get_timeline_events_combined_filters()

        # Test data structure and validation
        self.test_timeline_event_structure()
        self.test_timeline_event_hierarchy()
        self.test_timeline_event_metadata()

        # Test error handling
        self.test_invalid_parameters()

        # Print summary
        print("\n" + "=" * 60)
        print("📊 Test Summary:")
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        print(f"📈 Success Rate: {self.results['passed'] / (self.results['passed'] + self.results['failed']) * 100:.1f}%")

        if self.results["errors"]:
            print("\n❌ Errors:")
            for error in self.results["errors"]:
                print(f"   - {error}")

        return self.results["failed"] == 0


def main():
    """Main function to run the integration tests."""
    import argparse

    parser = argparse.ArgumentParser(description="Test the /timeline endpoint")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the backend API")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output with requests and responses")
    parser.add_argument("--response-limit", type=int, default=1000, help="Character limit for responses before saving to file")

    args = parser.parse_args()

    # Create tester and run tests
    tester = TimelineEndpointTester(args.url, verbose=args.verbose, response_limit=args.response_limit)

    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
