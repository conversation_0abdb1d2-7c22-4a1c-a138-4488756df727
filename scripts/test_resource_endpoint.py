#!/usr/bin/env python3
"""
Integration test script for the /resource endpoint.

This script tests all the endpoints provided by the /resource route:
- GET /resource - Query Resource-System logs by time range with pagination
- GET /resource/columns - Get available resource columns

Assumes the backend is already running with parsed data loaded.
"""

import json
import sys
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List

import requests


class ResourceEndpointTester:
    """Test class for the /resource endpoint."""

    def __init__(self, base_url: str = "http://localhost:8000", verbose: bool = False):
        """Initialize the tester with the base URL."""
        self.base_url = base_url
        self.verbose = verbose
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})

        # Test results
        self.results = {"passed": 0, "failed": 0, "errors": []}

        # Store available columns and time range for testing
        self.available_columns = []
        self.time_range = None

    def format_json(self, data: Any, max_lines: int = 100) -> str:
        """Format JSON data with optional truncation for readability."""
        try:
            formatted = json.dumps(data, indent=2, default=str)
            lines = formatted.split("\n")

            if len(lines) > max_lines:
                # Truncate and add summary
                truncated = "\n".join(lines[:max_lines])
                truncated += f"\n... (truncated, showing {max_lines} of {len(lines)} lines)"
                return truncated
            else:
                return formatted
        except Exception as e:
            return f"<Error formatting JSON: {e}>"

    def log_request(self, method: str, endpoint: str, params: Dict[str, Any] = None):
        """Log the request being made."""
        if not self.verbose:
            return

        print(f"\n🔍 REQUEST:")
        print(f"   Method: {method}")
        print(f"   URL: {self.base_url}{endpoint}")
        if params:
            print(f"   Params: {self.format_json(params, max_lines=20)}")
        print()

    def log_response(self, status_code: int, data: Any, response_time: float = None):
        """Log the response received."""
        if not self.verbose:
            return

        print(f"📡 RESPONSE:")
        print(f"   Status: {status_code}")
        if response_time:
            print(f"   Time: {response_time:.3f}s")
        print(f"   Data: {self.format_json(data)}")
        print()

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")

        if success:
            self.results["passed"] += 1
        else:
            self.results["failed"] += 1
            self.results["errors"].append(f"{test_name}: {details}")

    def make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a request to the API and return the JSON response."""
        url = f"{self.base_url}{endpoint}"

        # Log request
        self.log_request("GET", endpoint, params)

        try:
            start_time = time.time()
            response = self.session.get(url, params=params, timeout=30)
            response_time = time.time() - start_time

            response.raise_for_status()
            data = response.json()

            # Log response
            self.log_response(response.status_code, data, response_time)

            return data
        except requests.exceptions.RequestException as e:
            if self.verbose:
                print(f"📡 RESPONSE ERROR: {e}")
            raise Exception(f"Request failed: {e}")

    def test_health_check(self):
        """Test that the backend is running."""
        try:
            self.log_request("GET", "/health")

            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            response_time = time.time() - start_time

            success = response.status_code == 200
            data = response.json() if success else {"error": "Health check failed"}

            self.log_response(response.status_code, data, response_time)
            self.log_test("Health Check", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Health Check", False, str(e))
            return False

    def test_get_resource_columns(self):
        """Test GET /resource/columns."""
        try:
            data = self.make_request("/resource/columns")

            # Validate response structure
            assert "columns" in data, "Response missing 'columns' field"
            assert "column_stats" in data, "Response missing 'column_stats' field"
            assert isinstance(data["columns"], list), "Columns should be a list"
            assert isinstance(data["column_stats"], dict), "Column stats should be a dictionary"

            # Store available columns for other tests
            self.available_columns = data["columns"]

            success = True
            details = f"Found {len(data['columns'])} columns with statistics"

            self.log_test("GET /resource/columns", success, details)
            return data
        except Exception as e:
            self.log_test("GET /resource/columns", False, str(e))
            return {}

    def test_query_resource_basic(self):
        """Test GET /resource with basic time range query."""
        if not self.available_columns:
            self.log_test("GET /resource (basic query)", True, "Skipped - no columns available")
            return {}

        try:
            # Use a reasonable time range for testing (last 24 hours)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)

            params = {"start": start_time.isoformat(), "end": end_time.isoformat(), "limit": 10}

            data = self.make_request("/resource", params)

            # Validate response structure
            assert "records" in data, "Response missing 'records' field"
            assert "total" in data, "Response missing 'total' field"
            assert "limit" in data, "Response missing 'limit' field"
            assert "offset" in data, "Response missing 'offset' field"
            assert "start" in data, "Response missing 'start' field"
            assert "end" in data, "Response missing 'end' field"
            assert "available_columns" in data, "Response missing 'available_columns' field"
            assert isinstance(data["records"], list), "Records should be a list"
            assert isinstance(data["total"], int), "Total should be an integer"

            success = True
            details = f"Found {data['total']} total records, returned {len(data['records'])} (limit: {data['limit']})"

            self.log_test("GET /resource (basic query)", success, details)
            return data
        except Exception as e:
            self.log_test("GET /resource (basic query)", False, str(e))
            return {}

    def test_query_resource_with_columns(self):
        """Test GET /resource with specific column filtering."""
        if not self.available_columns:
            self.log_test("GET /resource (with column filter)", True, "Skipped - no columns available")
            return {}

        try:
            # Use first few available columns for testing
            test_columns = self.available_columns[:3] if len(self.available_columns) >= 3 else self.available_columns

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)  # Shorter time range

            params = {"start": start_time.isoformat(), "end": end_time.isoformat(), "columns": ",".join(test_columns), "limit": 5}

            data = self.make_request("/resource", params)

            # Validate response structure
            assert "records" in data, "Response missing 'records' field"
            assert "available_columns" in data, "Response missing 'available_columns' field"

            # Validate that returned records have the requested columns
            if data["records"]:
                for record in data["records"]:
                    for col in test_columns:
                        assert col in record, f"Record missing requested column: {col}"

            success = True
            details = f"Filtered by columns {test_columns}, found {len(data.get('records', []))} records"

            self.log_test("GET /resource (with column filter)", success, details)
            return data
        except Exception as e:
            self.log_test("GET /resource (with column filter)", False, str(e))
            return {}

    def test_query_resource_pagination(self):
        """Test pagination functionality."""
        if not self.available_columns:
            self.log_test("Pagination Test", True, "Skipped - no columns available")
            return False

        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=6)

            # Get first page
            params1 = {"start": start_time.isoformat(), "end": end_time.isoformat(), "limit": 5, "offset": 0}
            data1 = self.make_request("/resource", params1)

            # Get second page
            params2 = {"start": start_time.isoformat(), "end": end_time.isoformat(), "limit": 5, "offset": 5}
            data2 = self.make_request("/resource", params2)

            # Validate pagination
            assert data1["limit"] == 5, "First page should have limit 5"
            assert data2["limit"] == 5, "Second page should have limit 5"
            assert data1["offset"] == 0, "First page should have offset 0"
            assert data2["offset"] == 5, "Second page should have offset 5"

            # Check that records are different (if there are enough records)
            if len(data1["records"]) > 0 and len(data2["records"]) > 0:
                assert data1["records"] != data2["records"], "Pages should have different records"

            success = True
            details = f"Pagination works: page 1 has {len(data1['records'])} records, page 2 has {len(data2['records'])} records"

            self.log_test("Pagination Test", success, details)
            return True
        except Exception as e:
            self.log_test("Pagination Test", False, str(e))
            return False

    def test_query_resource_invalid_time_range(self):
        """Test error handling for invalid time range."""
        try:
            # Use invalid time range (end before start)
            start_time = datetime.now()
            end_time = start_time - timedelta(hours=1)

            params = {"start": start_time.isoformat(), "end": end_time.isoformat(), "limit": 10}

            # This should fail with a validation error
            try:
                self.make_request("/resource", params)
                # If we get here, the request succeeded when it should have failed
                self.log_test("Invalid Time Range Test", False, "Request succeeded when it should have failed")
                return False
            except Exception as e:
                if "400" in str(e) or "validation" in str(e).lower():
                    self.log_test("Invalid Time Range Test", True, "Correctly rejected invalid time range")
                    return True
                else:
                    self.log_test("Invalid Time Range Test", False, f"Unexpected error: {e}")
                    return False

        except Exception as e:
            self.log_test("Invalid Time Range Test", False, str(e))
            return False

    def test_query_resource_invalid_columns(self):
        """Test error handling for invalid column names."""
        if not self.available_columns:
            self.log_test("Invalid Columns Test", True, "Skipped - no columns available")
            return True

        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)

            # Use invalid column names
            invalid_columns = ["nonexistent_column_1", "invalid_column_2"]

            params = {"start": start_time.isoformat(), "end": end_time.isoformat(), "columns": ",".join(invalid_columns), "limit": 10}

            # This should either fail or return empty results
            try:
                data = self.make_request("/resource", params)

                # If it succeeds, it should return empty results or handle gracefully
                if data.get("total", 0) == 0:
                    self.log_test("Invalid Columns Test", True, "Correctly handled invalid columns (empty results)")
                    return True
                else:
                    self.log_test("Invalid Columns Test", True, "Request succeeded with invalid columns (graceful handling)")
                    return True

            except Exception as e:
                if "400" in str(e) or "validation" in str(e).lower():
                    self.log_test("Invalid Columns Test", True, "Correctly rejected invalid columns")
                    return True
                else:
                    self.log_test("Invalid Columns Test", False, f"Unexpected error: {e}")
                    return False

        except Exception as e:
            self.log_test("Invalid Columns Test", False, str(e))
            return False

    def test_query_resource_large_limit(self):
        """Test handling of large limit values."""
        if not self.available_columns:
            self.log_test("Large Limit Test", True, "Skipped - no columns available")
            return True

        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)

            params = {"start": start_time.isoformat(), "end": end_time.isoformat(), "limit": 10000}  # Large limit

            data = self.make_request("/resource", params)

            # Should handle large limit gracefully
            success = True
            details = f"Handled large limit gracefully, returned {len(data.get('records', []))} records"

            self.log_test("Large Limit Test", success, details)
            return True
        except Exception as e:
            self.log_test("Large Limit Test", False, str(e))
            return False

    def test_query_resource_response_structure(self):
        """Test detailed response structure validation."""
        if not self.available_columns:
            self.log_test("Response Structure Test", True, "Skipped - no columns available")
            return True

        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)

            params = {"start": start_time.isoformat(), "end": end_time.isoformat(), "limit": 5}

            data = self.make_request("/resource", params)

            # Validate all required fields
            required_fields = ["records", "total", "limit", "offset", "start", "end", "available_columns"]
            for field in required_fields:
                assert field in data, f"Response missing required field: {field}"

            # Validate data types
            assert isinstance(data["records"], list), "Records should be a list"
            assert isinstance(data["total"], int), "Total should be an integer"
            assert isinstance(data["limit"], int), "Limit should be an integer"
            assert isinstance(data["offset"], int), "Offset should be an integer"
            assert isinstance(data["available_columns"], list), "Available columns should be a list"

            # Validate record structure if records exist
            if data["records"]:
                for record in data["records"]:
                    assert isinstance(record, dict), "Each record should be a dictionary"
                    assert "timestamp" in record, "Each record should have a timestamp"
                    assert isinstance(record["timestamp"], str), "Timestamp should be a string"

            success = True
            details = f"Response structure validated successfully, {len(data.get('records', []))} records"

            self.log_test("Response Structure Test", success, details)
            return True
        except Exception as e:
            self.log_test("Response Structure Test", False, str(e))
            return False

    def run_all_tests(self):
        """Run all tests for the /resource endpoint."""
        print("🚀 Starting /resource endpoint integration tests...")
        print(f"📡 Testing against: {self.base_url}")
        if self.verbose:
            print("🔍 Verbose mode enabled - showing requests and responses")
        print("=" * 60)

        # Test health check first
        if not self.test_health_check():
            print("❌ Backend is not running or not accessible. Please start the backend first.")
            return False

        print("\n📋 Running endpoint tests...")

        # Test basic endpoints
        columns_data = self.test_get_resource_columns()

        # Test query endpoints
        self.test_query_resource_basic()
        self.test_query_resource_with_columns()

        # Test pagination
        self.test_query_resource_pagination()

        # Test error handling
        self.test_query_resource_invalid_time_range()
        self.test_query_resource_invalid_columns()

        # Test edge cases
        self.test_query_resource_large_limit()
        self.test_query_resource_response_structure()

        # Print summary
        print("\n" + "=" * 60)
        print("📊 Test Summary:")
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        print(f"📈 Success Rate: {self.results['passed'] / (self.results['passed'] + self.results['failed']) * 100:.1f}%")

        if self.results["errors"]:
            print("\n❌ Errors:")
            for error in self.results["errors"]:
                print(f"   - {error}")

        return self.results["failed"] == 0


def main():
    """Main function to run the integration tests."""
    import argparse

    parser = argparse.ArgumentParser(description="Test the /resource endpoint")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the backend API")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output with requests and responses")

    args = parser.parse_args()

    # Create tester and run tests
    tester = ResourceEndpointTester(args.url, verbose=args.verbose)

    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
