#!/usr/bin/env python3
"""
Integration test script for the new /structured endpoint.

This script tests all the endpoints provided by the /structured route:
- GET /structured - Query structured log entries
- GET /structured/positions - Get available positions
- GET /structured/log-levels - Get available log levels
- GET /structured/filters - Get available filters
- GET /structured/time-range - Get time range
- GET /structured/position/{position}/time-range - Get position time range
- GET /structured/log-events - Get log event counts
- GET /structured/log-levels/counts - Get log level counts
- GET /structured/event-timeline - Get event timeline
- GET /structured/log-level-timeline - Get log level timeline

Assumes the backend is already running with parsed data loaded.
"""

import json
import sys
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List

import requests


class StructuredEndpointTester:
    """Test class for the /structured endpoint."""

    def __init__(self, base_url: str = "http://localhost:8000", verbose: bool = False):
        """Initialize the tester with the base URL."""
        self.base_url = base_url
        self.verbose = verbose
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})

        # Test results
        self.results = {"passed": 0, "failed": 0, "errors": []}

    def format_json(self, data: Any, max_lines: int = 100) -> str:
        """Format JSON data with optional truncation for readability."""
        try:
            formatted = json.dumps(data, indent=2, default=str)
            lines = formatted.split("\n")

            if len(lines) > max_lines:
                # Truncate and add summary
                truncated = "\n".join(lines[:max_lines])
                truncated += f"\n... (truncated, showing {max_lines} of {len(lines)} lines)"
                return truncated
            else:
                return formatted
        except Exception as e:
            return f"<Error formatting JSON: {e}>"

    def log_request(self, method: str, endpoint: str, params: Dict[str, Any] = None):
        """Log the request being made."""
        if not self.verbose:
            return

        print(f"\n🔍 REQUEST:")
        print(f"   Method: {method}")
        print(f"   URL: {self.base_url}{endpoint}")
        if params:
            print(f"   Params: {self.format_json(params, max_lines=20)}")
        print()

    def log_response(self, status_code: int, data: Any, response_time: float = None):
        """Log the response received."""
        if not self.verbose:
            return

        print(f"📡 RESPONSE:")
        print(f"   Status: {status_code}")
        if response_time:
            print(f"   Time: {response_time:.3f}s")
        print(f"   Data: {self.format_json(data)}")
        print()

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")

        if success:
            self.results["passed"] += 1
        else:
            self.results["failed"] += 1
            self.results["errors"].append(f"{test_name}: {details}")

    def make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a request to the API and return the JSON response."""
        url = f"{self.base_url}{endpoint}"

        # Log request
        self.log_request("GET", endpoint, params)

        try:
            start_time = time.time()
            response = self.session.get(url, params=params, timeout=30)
            response_time = time.time() - start_time

            response.raise_for_status()
            data = response.json()

            # Log response
            self.log_response(response.status_code, data, response_time)

            return data
        except requests.exceptions.RequestException as e:
            if self.verbose:
                print(f"📡 RESPONSE ERROR: {e}")
            raise Exception(f"Request failed: {e}")

    def test_health_check(self):
        """Test that the backend is running."""
        try:
            self.log_request("GET", "/health")

            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            response_time = time.time() - start_time

            success = response.status_code == 200
            data = response.json() if success else {"error": "Health check failed"}

            self.log_response(response.status_code, data, response_time)
            self.log_test("Health Check", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Health Check", False, str(e))
            return False

    def test_get_positions(self):
        """Test GET /structured/positions."""
        try:
            data = self.make_request("/structured/positions")

            # Validate response structure
            assert "positions" in data, "Response missing 'positions' field"
            assert isinstance(data["positions"], list), "Positions should be a list"

            # Filter out empty positions
            valid_positions = [pos for pos in data["positions"] if pos and pos.strip()]

            success = True
            details = f"Found {len(data['positions'])} total positions, {len(valid_positions)} valid: {valid_positions[:3]}{'...' if len(valid_positions) > 3 else ''}"

            self.log_test("GET /structured/positions", success, details)
            return valid_positions
        except Exception as e:
            self.log_test("GET /structured/positions", False, str(e))
            return []

    def test_get_log_levels(self):
        """Test GET /structured/log-levels."""
        try:
            data = self.make_request("/structured/log-levels")

            # Validate response structure
            assert "log_levels" in data, "Response missing 'log_levels' field"
            assert isinstance(data["log_levels"], list), "Log levels should be a list"

            success = True
            details = f"Found {len(data['log_levels'])} log levels: {data['log_levels']}"

            self.log_test("GET /structured/log-levels", success, details)
            return data["log_levels"]
        except Exception as e:
            self.log_test("GET /structured/log-levels", False, str(e))
            return []

    def test_get_filters(self):
        """Test GET /structured/filters."""
        try:
            data = self.make_request("/structured/filters")

            # Validate response structure
            assert "filters" in data, "Response missing 'filters' field"
            assert isinstance(data["filters"], list), "Filters should be a list"

            success = True
            details = f"Found {len(data['filters'])} filters: {data['filters'][:5]}{'...' if len(data['filters']) > 5 else ''}"

            self.log_test("GET /structured/filters", success, details)
            return data["filters"]
        except Exception as e:
            self.log_test("GET /structured/filters", False, str(e))
            return []

    def test_get_time_range(self):
        """Test GET /structured/time-range."""
        try:
            data = self.make_request("/structured/time-range")

            # Validate response structure
            assert "common_start" in data, "Response missing 'common_start' field"
            assert "common_end" in data, "Response missing 'common_end' field"

            success = True
            start_str = data["common_start"] if data["common_start"] else "None"
            end_str = data["common_end"] if data["common_end"] else "None"
            details = f"Time range: {start_str} to {end_str}"

            self.log_test("GET /structured/time-range", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured/time-range", False, str(e))
            return {}

    def test_get_position_time_range(self, position: str):
        """Test GET /structured/position/{position}/time-range."""
        try:
            data = self.make_request(f"/structured/position/{position}/time-range")

            # Validate response structure
            assert "position_start" in data, "Response missing 'position_start' field"
            assert "position_end" in data, "Response missing 'position_end' field"

            success = True
            start_str = data["position_start"] if data["position_start"] else "None"
            end_str = data["position_end"] if data["position_end"] else "None"
            details = f"Position {position} time range: {start_str} to {end_str}"

            self.log_test(f"GET /structured/position/{position}/time-range", success, details)
            return data
        except Exception as e:
            self.log_test(f"GET /structured/position/{position}/time-range", False, str(e))
            return {}

    def test_query_structured_logs(self, start_time: str = None, end_time: str = None):
        """Test GET /structured with basic query."""
        try:
            params = {}
            if start_time:
                params["start"] = start_time
            if end_time:
                params["end"] = end_time
            params["limit"] = 10

            data = self.make_request("/structured", params)

            # Validate response structure
            assert "entries" in data, "Response missing 'entries' field"
            assert "total" in data, "Response missing 'total' field"
            assert "limit" in data, "Response missing 'limit' field"
            assert "offset" in data, "Response missing 'offset' field"
            assert isinstance(data["entries"], list), "Entries should be a list"
            assert isinstance(data["total"], int), "Total should be an integer"

            success = True
            details = f"Found {data['total']} total entries, returned {len(data['entries'])} (limit: {data['limit']})"

            self.log_test("GET /structured (basic query)", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured (basic query)", False, str(e))
            return {}

    def test_query_structured_logs_with_filters(self):
        """Test GET /structured with various filters."""
        try:
            # Test with log level filter
            params = {"log_level": "INFO", "limit": 5}

            data = self.make_request("/structured", params)

            # Validate that all returned entries have INFO level
            for entry in data.get("entries", []):
                assert entry.get("log_level") == "INFO", f"Entry should have INFO level, got {entry.get('log_level')}"

            success = True
            details = f"Filtered by log_level=INFO, found {len(data.get('entries', []))} entries"

            self.log_test("GET /structured (with log_level filter)", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured (with log_level filter)", False, str(e))
            return {}

    def test_get_log_events(self):
        """Test GET /structured/log-events."""
        try:
            data = self.make_request("/structured/log-events")

            # Validate response structure
            assert isinstance(data, dict), "Response should be a dictionary"

            success = True
            event_count = len(data)
            total_events = sum(data.values())
            details = f"Found {event_count} event types with {total_events} total events"

            self.log_test("GET /structured/log-events", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured/log-events", False, str(e))
            return {}

    def test_get_log_level_counts(self):
        """Test GET /structured/log-levels/counts."""
        try:
            data = self.make_request("/structured/log-levels/counts")

            # Validate response structure
            assert isinstance(data, dict), "Response should be a dictionary"

            success = True
            level_count = len(data)
            total_logs = sum(data.values())
            details = f"Found {level_count} log levels with {total_logs} total logs"

            self.log_test("GET /structured/log-levels/counts", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured/log-levels/counts", False, str(e))
            return {}

    def test_get_event_timeline(self):
        """Test GET /structured/event-timeline."""
        try:
            params = {"freq": "1Min", "limit": 10}

            data = self.make_request("/structured/event-timeline", params)

            # Validate response structure
            assert "timestamps" in data, "Response missing 'timestamps' field"
            assert "counts" in data, "Response missing 'counts' field"
            assert "event_types" in data, "Response missing 'event_types' field"
            assert isinstance(data["timestamps"], list), "Timestamps should be a list"
            assert isinstance(data["counts"], list), "Counts should be a list"

            success = True
            details = f"Generated timeline with {len(data['timestamps'])} time points and {len(data['event_types'])} event types"

            self.log_test("GET /structured/event-timeline", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured/event-timeline", False, str(e))
            return {}

    def test_get_log_level_timeline(self):
        """Test GET /structured/log-level-timeline."""
        try:
            params = {"freq": "1Min", "limit": 10}

            data = self.make_request("/structured/log-level-timeline", params)

            # Validate response structure
            assert "timestamps" in data, "Response missing 'timestamps' field"
            assert "counts" in data, "Response missing 'counts' field"
            assert "log_levels" in data, "Response missing 'log_levels' field"
            assert isinstance(data["timestamps"], list), "Timestamps should be a list"
            assert isinstance(data["counts"], list), "Counts should be a list"

            success = True
            details = f"Generated timeline with {len(data['timestamps'])} time points and {len(data['log_levels'])} log levels"

            self.log_test("GET /structured/log-level-timeline", success, details)
            return data
        except Exception as e:
            self.log_test("GET /structured/log-level-timeline", False, str(e))
            return {}

    def test_pagination(self):
        """Test pagination functionality."""
        try:
            # Get first page
            params1 = {"limit": 5, "offset": 0}
            data1 = self.make_request("/structured", params1)

            # Get second page
            params2 = {"limit": 5, "offset": 5}
            data2 = self.make_request("/structured", params2)

            # Validate pagination
            assert data1["limit"] == 5, "First page should have limit 5"
            assert data2["limit"] == 5, "Second page should have limit 5"
            assert data1["offset"] == 0, "First page should have offset 0"
            assert data2["offset"] == 5, "Second page should have offset 5"

            # Check that entries are different (if there are enough entries)
            if len(data1["entries"]) > 0 and len(data2["entries"]) > 0:
                assert data1["entries"] != data2["entries"], "Pages should have different entries"

            success = True
            details = f"Pagination works: page 1 has {len(data1['entries'])} entries, page 2 has {len(data2['entries'])} entries"

            self.log_test("Pagination Test", success, details)
            return True
        except Exception as e:
            self.log_test("Pagination Test", False, str(e))
            return False

    def test_position_filtering(self, positions: List[str]):
        """Test filtering by positions."""
        if not positions:
            self.log_test("Position Filtering Test", True, "Skipped - no positions available")
            return True

        try:
            position = positions[0]  # Use first available position

            params = {"positions": position, "limit": 10}

            data = self.make_request("/structured", params)

            # Validate that all entries have the correct position
            for entry in data.get("entries", []):
                assert entry.get("position") == position, f"Entry should have position {position}, got {entry.get('position')}"

            success = True
            details = f"Filtered by position {position}, found {len(data.get('entries', []))} entries"

            self.log_test("Position Filtering Test", success, details)
            return True
        except Exception as e:
            self.log_test("Position Filtering Test", False, str(e))
            return False

    def run_all_tests(self):
        """Run all tests for the /structured endpoint."""
        print("🚀 Starting /structured endpoint integration tests...")
        print(f"📡 Testing against: {self.base_url}")
        if self.verbose:
            print("🔍 Verbose mode enabled - showing requests and responses")
        print("=" * 60)

        # Test health check first
        if not self.test_health_check():
            print("❌ Backend is not running or not accessible. Please start the backend first.")
            return False

        print("\n📋 Running endpoint tests...")

        # Test basic endpoints
        positions = self.test_get_positions()
        log_levels = self.test_get_log_levels()
        filters = self.test_get_filters()
        time_range = self.test_get_time_range()

        # Test position-specific endpoints
        if positions and len(positions) > 0:
            # Filter out empty positions
            valid_positions = [pos for pos in positions if pos and pos.strip()]
            if valid_positions:
                self.test_get_position_time_range(valid_positions[0])
            else:
                self.log_test("GET /structured/position/{position}/time-range", True, "Skipped - no valid positions available")
        else:
            self.log_test("GET /structured/position/{position}/time-range", True, "Skipped - no positions available")

        # Test query endpoints
        self.test_query_structured_logs()
        self.test_query_structured_logs_with_filters()

        # Test aggregation endpoints
        self.test_get_log_events()
        self.test_get_log_level_counts()

        # Test timeline endpoints
        self.test_get_event_timeline()
        self.test_get_log_level_timeline()

        # Test advanced functionality
        self.test_pagination()
        self.test_position_filtering(positions)

        # Print summary
        print("\n" + "=" * 60)
        print("📊 Test Summary:")
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        print(f"📈 Success Rate: {self.results['passed'] / (self.results['passed'] + self.results['failed']) * 100:.1f}%")

        if self.results["errors"]:
            print("\n❌ Errors:")
            for error in self.results["errors"]:
                print(f"   - {error}")

        return self.results["failed"] == 0


def main():
    """Main function to run the integration tests."""
    import argparse

    parser = argparse.ArgumentParser(description="Test the /structured endpoint")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the backend API")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output with requests and responses")

    args = parser.parse_args()

    # Create tester and run tests
    tester = StructuredEndpointTester(args.url, verbose=args.verbose)

    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
