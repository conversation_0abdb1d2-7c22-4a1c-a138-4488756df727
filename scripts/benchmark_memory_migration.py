#!/usr/bin/env python3
"""
Performance benchmark script for get_memory_data() pipeline migration.

This script benchmarks the original get_memory_data() method against the new
pipeline implementation using real sample data from logs-P2I-00147.
"""

import asyncio
import statistics
import sys
import time
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict, List

# Add tern module to path
tern_path = Path(__file__).parent.parent / "tern"
sys.path.insert(0, str(tern_path))

from tern.core.log_visualisation import LogVisualisation, VisualizationDataCache
from tern.core.memory_pipeline import MemoryDataPipeline
from tern.core.pipeline import CachedTransformationPipeline, DataSources
from tern.core.pipeline.transformations import BaseTransformations


class MockResourceAnalyzer:
    """Mock resource analyzer using real sample data for benchmarking."""

    def __init__(self, sample_data_path: str):
        self.sample_data_path = Path(sample_data_path)
        self.sample_data = self._load_sample_data()
        self.call_count = 0

    def _load_sample_data(self) -> List[Dict[str, Any]]:
        """Load and parse real sample data from CSV file."""
        csv_file = self.sample_data_path / "ont-resource-logger" / "resource-system.log"

        if not csv_file.exists():
            # Return synthetic data if sample file not found
            return self._generate_synthetic_data()

        data = []
        try:
            with open(csv_file, "r") as f:
                lines = f.readlines()

            # Parse CSV data (skip header if present)
            for line in lines[1:1000]:  # Use first 1000 records for benchmarking
                parts = line.strip().split(",")
                if len(parts) >= 7:  # Ensure we have enough columns
                    try:
                        record = {
                            "timestamp": datetime.fromisoformat(parts[0].replace("Z", "+00:00")),
                            "MemTotalBytes": int(float(parts[1])) if parts[1] else 0,
                            "MemAvailableBytes": int(float(parts[2])) if parts[2] else 0,
                            "MemUsedBytes": int(float(parts[3])) if parts[3] else 0,
                            "SwapTotalBytes": int(float(parts[4])) if parts[4] else 0,
                            "SwapUsedBytes": int(float(parts[5])) if parts[5] else 0,
                        }
                        data.append(record)
                    except (ValueError, IndexError):
                        continue  # Skip malformed lines

        except FileNotFoundError:
            return self._generate_synthetic_data()

        return data[:500]  # Limit to 500 records for consistent benchmarking

    def _generate_synthetic_data(self) -> List[Dict[str, Any]]:
        """Generate synthetic data for benchmarking when real data unavailable."""
        base_time = datetime.now()
        data = []

        for i in range(500):
            record = {
                "timestamp": base_time + timedelta(seconds=i * 60),
                "MemTotalBytes": 8589934592,  # 8GB
                "MemAvailableBytes": 4294967296 + (i * 1048576),  # 4GB + variation
                "MemUsedBytes": 4294967296 - (i * 1048576),  # 4GB - variation
                "SwapTotalBytes": 2147483648,  # 2GB
                "SwapUsedBytes": i * 2097152,  # Increasing swap usage
            }
            data.append(record)

        return data

    def get_resource_data_for_columns(self, columns, start_time, end_time, limit=5000, offset=0):
        """Mock resource data retrieval using sample data."""
        self.call_count += 1

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data[offset : offset + limit]:
            if start_time <= record["timestamp"] <= end_time:
                # Only include requested columns
                filtered_record = {"timestamp": record["timestamp"]}
                for col in columns:
                    if col in record:
                        filtered_record[col] = record[col]
                filtered_data.append(filtered_record)

        return filtered_data

    def get_column_stats(self):
        """Mock column stats for pipeline DataSources."""
        return {
            "MemTotalBytes": {"count": len(self.sample_data), "mean": 8589934592},
            "MemAvailableBytes": {"count": len(self.sample_data), "mean": 4294967296},
            "MemUsedBytes": {"count": len(self.sample_data), "mean": 4294967296},
            "SwapTotalBytes": {"count": len(self.sample_data), "mean": 2147483648},
            "SwapUsedBytes": {"count": len(self.sample_data), "mean": 1073741824},
        }

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start is None or end is None or (start <= record["timestamp"] <= end):
                filtered_data.append(record)

        if not filtered_data:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(filtered_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        return df


class OriginalMemoryDataMethod:
    """Original get_memory_data() implementation for comparison."""

    def __init__(self, resource_analyzer):
        self.resource_analyzer = resource_analyzer
        self._transformation_cache = VisualizationDataCache(max_cached_results=40)

    def get_memory_data(self, start_time=None, end_time=None):
        """Original memory data method implementation."""
        if start_time is None:
            start_time = datetime.now() - timedelta(hours=1)
        if end_time is None:
            end_time = datetime.now()

        # Original caching logic
        cached_result = self._transformation_cache.get_cached_transformation(
            method_name="get_memory_data", start_time=start_time, end_time=end_time
        )
        if cached_result:
            return cached_result

        # Fetch raw data
        memory_columns = ["MemTotalBytes", "MemAvailableBytes", "MemUsedBytes", "SwapTotalBytes", "SwapUsedBytes"]
        raw_data = self.resource_analyzer.get_resource_data_for_columns(memory_columns, start_time, end_time)

        # Convert to records format
        records_data = {"records": raw_data}

        # Convert units (bytes to GB)
        for record in records_data["records"]:
            for key, value in record.items():
                if isinstance(value, (int, float)) and "bytes" in key.lower():
                    record[key] = value / (1024 * 1024 * 1024)  # Convert to GB

        # Format as time series
        timestamps = []
        value_arrays = {}

        if records_data["records"]:
            first_record = records_data["records"][0]
            for key in first_record.keys():
                if key != "timestamp":
                    value_arrays[key] = []

        for record in records_data["records"]:
            timestamps.append(record.get("timestamp"))
            for key in value_arrays.keys():
                value = record.get(key, 0)
                if isinstance(value, (int, float)):
                    value_arrays[key].append(value)
                else:
                    value_arrays[key].append(0)

        result = {"timestamps": timestamps, **value_arrays}

        # Cache result
        self._transformation_cache.add_to_cache(
            query_key=self._transformation_cache._generate_cache_key("get_memory_data", start_time, end_time),
            result=result,
            method_name="get_memory_data",
            start_time=start_time,
            end_time=end_time,
        )
        return result


class BenchmarkRunner:
    """Runs performance benchmarks comparing original vs pipeline implementations."""

    def __init__(self, sample_data_path: str):
        self.sample_data_path = sample_data_path
        self.mock_analyzer = MockResourceAnalyzer(sample_data_path)

        # Setup original implementation
        self.original_impl = OriginalMemoryDataMethod(self.mock_analyzer)

        # Setup pipeline implementation
        self.data_sources = DataSources(cross_analyzer=None, resource_analyzer=self.mock_analyzer, timeline_analyzer=None)

    def run_benchmark(self, iterations: int = 10) -> Dict[str, Any]:
        """Run comprehensive benchmark comparing implementations."""
        print(f"🚀 Starting benchmark with {iterations} iterations using real sample data...")
        print(f"📂 Sample data path: {self.sample_data_path}")
        print(f"📊 Loaded {len(self.mock_analyzer.sample_data)} sample records")

        # Test time ranges
        base_time = datetime.now()
        test_ranges = [
            (base_time - timedelta(hours=1), base_time),
            (base_time - timedelta(hours=2), base_time - timedelta(hours=1)),
            (base_time - timedelta(hours=4), base_time - timedelta(hours=2)),
        ]

        results = {
            "original": {"times": [], "cache_hits": 0, "cache_misses": 0},
            "pipeline": {"times": [], "cache_hits": 0, "cache_misses": 0},
            "iterations": iterations,
            "test_ranges": len(test_ranges),
        }

        # Benchmark original implementation
        print("\n⏱️  Benchmarking original implementation...")
        for i in range(iterations):
            start_time, end_time = test_ranges[i % len(test_ranges)]

            start = time.perf_counter()
            result = self.original_impl.get_memory_data(start_time, end_time)
            end = time.perf_counter()

            execution_time = end - start
            results["original"]["times"].append(execution_time)

            # Check if result was cached (heuristic: very fast execution)
            if execution_time < 0.001:
                results["original"]["cache_hits"] += 1
            else:
                results["original"]["cache_misses"] += 1

            print(f"  Iteration {i+1}: {execution_time:.4f}s ({'cached' if execution_time < 0.001 else 'computed'})")

        # Benchmark pipeline implementation
        print("\n⏱️  Benchmarking pipeline implementation...")
        for i in range(iterations):
            start_time, end_time = test_ranges[i % len(test_ranges)]

            # Create fresh pipeline for each test (no cache pollution)
            cache = VisualizationDataCache(max_cached_results=40)
            pipeline = MemoryDataPipeline(cache=cache, method_name="get_memory_data", start_time=start_time, end_time=end_time)
            pipeline.inject_sources(self.data_sources)
            pipeline.build_memory_pipeline(start_time, end_time)

            start = time.perf_counter()
            result = asyncio.run(self._execute_pipeline_async(pipeline))
            end = time.perf_counter()

            execution_time = end - start
            results["pipeline"]["times"].append(execution_time)

            # Check if result was cached (heuristic: very fast execution)
            if execution_time < 0.001:
                results["pipeline"]["cache_hits"] += 1
            else:
                results["pipeline"]["cache_misses"] += 1

            print(f"  Iteration {i+1}: {execution_time:.4f}s ({'cached' if execution_time < 0.001 else 'computed'})")

        return results

    async def _execute_pipeline_async(self, pipeline):
        """Execute pipeline asynchronously."""
        return await pipeline.execute()

    def test_cache_behavior(self, cache_size: int = 5) -> Dict[str, Any]:
        """Test cache behavior with smaller cache size for controlled testing."""
        print(f"\n🧪 Testing cache behavior with cache size: {cache_size}")

        # Create many different time ranges to trigger cache eviction
        base_time = datetime.now()
        time_ranges = []
        for i in range(cache_size + 3):  # More ranges than cache can hold
            start = base_time - timedelta(hours=i + 1)
            end = base_time - timedelta(hours=i)
            time_ranges.append((start, end))

        # Test original implementation cache behavior
        original_cache = VisualizationDataCache(max_cached_results=cache_size)
        original_impl = OriginalMemoryDataMethod(self.mock_analyzer)
        original_impl._transformation_cache = original_cache

        original_results = []
        for i, (start_time, end_time) in enumerate(time_ranges):
            start = time.perf_counter()
            result = original_impl.get_memory_data(start_time, end_time)
            end = time.perf_counter()

            execution_time = end - start
            is_cached = execution_time < 0.001
            original_results.append({"iteration": i, "time": execution_time, "cached": is_cached})
            print(f"  Original iter {i+1}: {execution_time:.4f}s ({'cached' if is_cached else 'computed'})")

        # Test pipeline implementation cache behavior
        pipeline_cache = VisualizationDataCache(max_cached_results=cache_size)
        pipeline_results = []

        for i, (start_time, end_time) in enumerate(time_ranges):
            pipeline = MemoryDataPipeline(cache=pipeline_cache, method_name="get_memory_data", start_time=start_time, end_time=end_time)
            pipeline.inject_sources(self.data_sources)
            pipeline.build_memory_pipeline(start_time, end_time)

            start = time.perf_counter()
            result = asyncio.run(self._execute_pipeline_async(pipeline))
            end = time.perf_counter()

            execution_time = end - start
            is_cached = execution_time < 0.001
            pipeline_results.append({"iteration": i, "time": execution_time, "cached": is_cached})
            print(f"  Pipeline iter {i+1}: {execution_time:.4f}s ({'cached' if is_cached else 'computed'})")

        return {"cache_size": cache_size, "total_requests": len(time_ranges), "original": original_results, "pipeline": pipeline_results}

    def validate_output_consistency(self) -> Dict[str, Any]:
        """Validate that both implementations produce identical output."""
        print("\n🔍 Validating output consistency...")

        base_time = datetime.now()
        start_time = base_time - timedelta(hours=1)
        end_time = base_time

        # Get results from both implementations
        original_result = self.original_impl.get_memory_data(start_time, end_time)

        pipeline = MemoryDataPipeline(
            cache=VisualizationDataCache(max_cached_results=40), method_name="get_memory_data", start_time=start_time, end_time=end_time
        )
        pipeline.inject_sources(self.data_sources)
        pipeline.build_memory_pipeline(start_time, end_time)
        pipeline_result = asyncio.run(self._execute_pipeline_async(pipeline))

        # Compare structures
        consistency_results = {
            "structure_match": set(original_result.keys()) == set(pipeline_result.keys()),
            "timestamp_count_match": len(original_result.get("timestamps", [])) == len(pipeline_result.get("timestamps", [])),
            "data_fields_match": True,
            "differences": [],
        }

        # Compare data fields
        for key in original_result.keys():
            if key in pipeline_result:
                orig_val = original_result[key]
                pipe_val = pipeline_result[key]

                if isinstance(orig_val, list) and isinstance(pipe_val, list):
                    if len(orig_val) != len(pipe_val):
                        consistency_results["data_fields_match"] = False
                        consistency_results["differences"].append(f"Length mismatch in {key}: {len(orig_val)} vs {len(pipe_val)}")
            else:
                consistency_results["data_fields_match"] = False
                consistency_results["differences"].append(f"Missing field in pipeline result: {key}")

        print(f"  Structure match: {consistency_results['structure_match']}")
        print(f"  Timestamp count match: {consistency_results['timestamp_count_match']}")
        print(f"  Data fields match: {consistency_results['data_fields_match']}")

        if consistency_results["differences"]:
            print("  Differences found:")
            for diff in consistency_results["differences"]:
                print(f"    - {diff}")

        return consistency_results

    def print_summary(self, benchmark_results: Dict[str, Any], cache_results: Dict[str, Any], consistency_results: Dict[str, Any]):
        """Print comprehensive benchmark summary."""
        print("\n" + "=" * 80)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 80)

        # Performance comparison
        orig_times = benchmark_results["original"]["times"]
        pipe_times = benchmark_results["pipeline"]["times"]

        orig_mean = statistics.mean(orig_times)
        pipe_mean = statistics.mean(pipe_times)
        orig_median = statistics.median(orig_times)
        pipe_median = statistics.median(pipe_times)

        print(f"\n⏱️  PERFORMANCE METRICS:")
        print(f"   Original - Mean: {orig_mean:.4f}s, Median: {orig_median:.4f}s")
        print(f"   Pipeline - Mean: {pipe_mean:.4f}s, Median: {pipe_median:.4f}s")
        print(
            f"   Performance Ratio: {pipe_mean/orig_mean:.2f}x {'🟢' if pipe_mean <= orig_mean * 1.05 else '🟡' if pipe_mean <= orig_mean * 1.2 else '🔴'}"
        )

        # Cache effectiveness
        print(f"\n💾 CACHE EFFECTIVENESS:")
        print(f"   Original - Hits: {benchmark_results['original']['cache_hits']}, Misses: {benchmark_results['original']['cache_misses']}")
        print(f"   Pipeline - Hits: {benchmark_results['pipeline']['cache_hits']}, Misses: {benchmark_results['pipeline']['cache_misses']}")

        # Cache behavior validation
        print(f"\n🧪 CACHE BEHAVIOR (cache size: {cache_results['cache_size']}):")
        original_cache_pattern = [r["cached"] for r in cache_results["original"]]
        pipeline_cache_pattern = [r["cached"] for r in cache_results["pipeline"]]
        print(f"   Original pattern: {original_cache_pattern}")
        print(f"   Pipeline pattern: {pipeline_cache_pattern}")
        cache_behavior_match = original_cache_pattern == pipeline_cache_pattern
        print(f"   Cache behavior match: {cache_behavior_match} {'🟢' if cache_behavior_match else '🔴'}")

        # Output consistency
        print(f"\n🔍 OUTPUT CONSISTENCY:")
        print(f"   Structure match: {consistency_results['structure_match']} {'🟢' if consistency_results['structure_match'] else '🔴'}")
        print(
            f"   Data fields match: {consistency_results['data_fields_match']} {'🟢' if consistency_results['data_fields_match'] else '🔴'}"
        )

        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        performance_good = pipe_mean <= orig_mean * 1.05
        cache_good = cache_behavior_match
        consistency_good = consistency_results["structure_match"] and consistency_results["data_fields_match"]

        overall_pass = performance_good and cache_good and consistency_good
        print(f"   Performance: {'PASS' if performance_good else 'FAIL'} {'🟢' if performance_good else '🔴'}")
        print(f"   Cache Behavior: {'PASS' if cache_good else 'FAIL'} {'🟢' if cache_good else '🔴'}")
        print(f"   Output Consistency: {'PASS' if consistency_good else 'FAIL'} {'🟢' if consistency_good else '🔴'}")
        print(f"   Overall: {'PASS ✅' if overall_pass else 'FAIL ❌'}")

        print("=" * 80)


def main():
    """Main benchmark execution."""
    # Use real sample data path
    sample_data_path = "/Users/<USER>/Workspace/examples/logs-P2I-00147"

    if not Path(sample_data_path).exists():
        print(f"⚠️  Sample data path not found: {sample_data_path}")
        print("🔄 Using synthetic data for benchmarking...")

    runner = BenchmarkRunner(sample_data_path)

    # Run all benchmarks
    benchmark_results = runner.run_benchmark(iterations=10)
    cache_results = runner.test_cache_behavior(cache_size=5)
    consistency_results = runner.validate_output_consistency()

    # Print summary
    runner.print_summary(benchmark_results, cache_results, consistency_results)


if __name__ == "__main__":
    main()
