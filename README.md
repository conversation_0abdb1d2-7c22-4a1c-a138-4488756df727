# <img src="./tern/tern/assets/tern_transparent.png" alt="Tern Logo" width="40" style="vertical-align: middle;"/> Tern - Log Visualization Monorepo

> ## ⚠️ **EXPERIMENTAL PROJECT DISCLAIMER**
>
> **This is an experimental proof-of-concept developed through AI-assisted "vibe coding" using Cursor IDE and ChatGPT.**
>
> - 🤖 **~99% AI-generated code** - Not production ready
> - 🧪 **Experimental/learning project** - Built to explore AI-assisted development workflows
> - 🚫 **Not security audited** - Do not use with sensitive data
> - 🐛 **Limited testing** - Expect bugs and incomplete features
> - 📚 **For learning/inspiration only** - Feel free to fork and improve!
>


A one‑stop workspace for parsing device logs and exploring them through rich UIs, headless APIs, and CLI tools.

| Component           | Tech                        | What it does                                            |
| ------------------- | --------------------------- | ------------------------------------------------------- |
| **Tern**            | Dash + Plotly (+ PyWebView) | Primary GUI (desktop or browser) with integrated parser |
| **FastAPI backend** | FastAPI + Pydantic          | Headless REST API built on the same `log_parser` engine |
| **Console CLI**     | Typer + Rich                | Dev‑focused terminal analysis & plotting                |
| **log\_parser**     | Pure‑Python                 | Shared parsing & anomaly‑detection library              |

> ### TL;DR (End Users)
>
> ```bash
> pip install dist/tern-0.1.0-py3-none-any.whl   # self‑contained wheel
> tern                                           # open the UI
> ```
>
> **🎉 New:** No setup required! Use the **Browse** button in the app to select your log folder, then click **Load Logs** to start analyzing.
>
> **📁 Log Structure:** Your log folder should contain `minknow` and `ont-resource-logger` subdirectories. [See directory structure requirements](#expected-directory-structure).

---

## 🚀 Quick Start for Developers

```bash
# Clone & enter repo
git clone <repo‑url>
cd log_visualiser

# Python ≥ 3.10
python -m venv .venv && source .venv/bin/activate

# Install every optional group + parser in editable mode
pip install -e ".[full]" -e ./log_parser

# Optional: Set default log folder path for convenience
export BASE_LOG_FOLDER=/path/to/your/log/files

# Launch Tern with hot‑reload in browser
hatch run dev --debug --no-webview
```

*No Node.js required 🎉*

---

## 📦 Monorepo Map

```
backend/       # FastAPI service
console/       # Typer CLI
log_parser/    # Shared parser lib
tern/          # Dash/Plotly GUI
build_hook.py  # Vendoring logic (see BUILD.md)
docs/          # Deep‑dive docs
```

---

## ✨ Key Features (Tern)

* 🎣 **User-Controlled Parsing**: Browse and load log folders manually with rich progress feedback
* 🔄 **Real-Time Progress**: Detailed progress tracking with file-by-file updates (90% accuracy)
* 🕒 Interactive time‑series & event overlay (zoom‑select)
* 🔍 Smart filtering by position & timeframe
* ⚡ Built‑in anomaly detection (z‑score, IQR, control‑chart)
* 🖼️ Runs **native window** or **browser** (`--no-webview`)
* 🛡️ **Enhanced Error Handling**: Modal dialogs with retry/recovery actions

---

## 🔧 Common Dev Commands

| Task          | Command                           |
| ------------- | --------------------------------- |
| Run tests     | `hatch run test`                  |
| Coverage      | `hatch run test-cov`              |
| Lint & format | `hatch run lint && hatch run fmt` |
| Build wheel   | `hatch build`                     |

---

## 🧩 Optional Dependency Groups

| Extra      | Installs                                        | Use case                                        |
| ---------- | ----------------------------------------------- | ----------------------------------------------- |
| `backend`  | FastAPI, Uvicorn, Pydantic Settings, Requests   | Run REST API service                            |
| `cli`      | Typer, Rich, Plotext, NumPy                     | Console analysis tools                          |
| `analysis` | Matplotlib, regex, ADTK, python‑dotenv          | Deep‑dive analytics / anomaly R\&D              |
| `dev`      | Pytest, coverage, Black, Ruff, Mypy, Pre‑commit | Local dev & CI linting                          |
| `full`     | `tern[backend,cli,dev,analysis]`                | Everything above – recommended for contributors |

*Example:*

```bash
# Install just the API stack
pip install -e ".[backend]"

# Install the full dev environment (as shown in Quick Start)
pip install -e ".[full]" -e ./log_parser
```

---

## 🚢 Building the Wheel (for Production Deployment)

When you're ready to ship a standalone package, create a self‑contained wheel:

```bash
# From repo root
hatch build
```

* **Output** → `dist/tern-0.1.0-py3-none-any.whl`
* The build hook automatically vendors the **log\_parser** code so end‑users don't need to install it separately.
* Install the wheel anywhere with:

```bash
pip install dist/tern-0.1.0-py3-none-any.whl
```

---

## 📚 Further Reading

* **Tern details →** `./tern/README.md`
* **REST API →** `./backend/README.md`
* **Console CLI →** `./console/README.md`
* **Build internals →** `BUILD.md`
* **Architecture & deep dives →** `docs/`

---

## 🛠 Requirements

| Tool    | Version                          |
| ------- | -------------------------------- |
| Python  | 3.10 or newer                    |
| OS      | macOS, Linux, Windows            |
| Browser | Modern (if using `--no-webview`) |

---

## 🌍 Environment Variables

| Variable           | Required | Description                                          | Example                        |
| ------------------ | -------- | ---------------------------------------------------- | ------------------------------ |
| `BASE_LOG_FOLDER`  | No       | Default path pre-filled in the Browse dialog        | `/data/logs` or `./sample_logs` |
| `LOG_LEVEL`        | No       | Logging verbosity (DEBUG, INFO, WARNING, ERROR)     | `INFO` (default)               |

> **🎉 New Workflow:** `BASE_LOG_FOLDER` is now optional and only pre-fills the path input. Use the **Browse** button in the app to select your log folder, then click **Load Logs** to start parsing.

### Expected Directory Structure

The `BASE_LOG_FOLDER` should contain the following subdirectories:

```
your-log-folder/
├── minknow
└── ont-resource-logger
```

This is the minimum required structure. Log archives may also include system logs, but these are not currently processed by Tern.

*Example usage:*

```bash
# Launch app and use Browse button (recommended)
tern

# Or pre-fill log path for convenience
export BASE_LOG_FOLDER=/path/to/your/logs
tern

# Development with pre-filled path
BASE_LOG_FOLDER=/path/to/your/logs tern --debug --no-webview
```

---

## 📜 License

MIT License – see `LICENSE` file for details.

---

*Built with ❤️ using [Hatch](https://hatch.pypa.io), [Dash](https://dash.plotly.com), and [PyWebView](https://pywebview.flowrl.com)*
