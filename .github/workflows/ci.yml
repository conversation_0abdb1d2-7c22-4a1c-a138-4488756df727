name: CI

# ------------------------------------------------------------------
# Run the workflow ONLY for pull-requests aimed at `main`
# ------------------------------------------------------------------
on:
  pull_request:
    branches: [ "main" ]

# ------------------------------------------------------------------
# 1. Linting / formatting / static checks via pre-commit
# ------------------------------------------------------------------
jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: pip

      - name: Run pre-commit hooks
        run: |
          pip install pre-commit
          pre-commit run --all-files --show-diff-on-failure

# ------------------------------------------------------------------
# 2. Test-suite (with coverage & artefacts) on supported interpreters
# ------------------------------------------------------------------
  tests:
    needs: pre-commit
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: pip

      # ------------------------------------------------------------
      # Install hatch and run tests using hatch environments
      # ------------------------------------------------------------
      - name: Install hatch
        run: |
          pip install --upgrade pip hatch

      - name: Run tests with hatch
        run: |
          hatch run test-tern

      # ------------------------------------------------------------
      # Upload artefacts for this interpreter
      # ------------------------------------------------------------
      - name: Upload coverage artefact
        uses: actions/upload-artifact@v4
        with:
          name: coverage-${{ matrix.python-version }}
          path: coverage.xml

      - name: Upload test results artefact
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.python-version }}
          path: test-results-${{ matrix.python-version }}.xml
