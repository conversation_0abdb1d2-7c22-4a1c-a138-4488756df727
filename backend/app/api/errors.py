from __future__ import annotations

from typing import Any

from app.utils.logging import logger
from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON>NResponse


# Custom exceptions
class AnalyzerNotFoundError(Exception):
    """Raised when analyzer is not available."""


class AnalyzerProcessingError(Exception):
    """Raised when analyzer encounters processing errors."""


class InvalidTimestampError(Exception):
    """Raised when timestamp is invalid or out of range."""


# Error handling functions
async def analyzer_not_found_handler(request: Request, exc: AnalyzerNotFoundError) -> JSONResponse:
    """Handler for AnalyzerNotFoundError."""
    logger.error("Analyzer not found: %s", exc)
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={"detail": "No analyzer available. Please parse logs first."},
    )


async def analyzer_processing_error_handler(request: Request, exc: AnalyzerProcessingError) -> JSONResponse:
    """Handler for AnalyzerProcessingError."""
    logger.error("Analyzer processing error: %s", exc)
    return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"detail": str(exc)})


async def invalid_timestamp_error_handler(request: Request, exc: InvalidTimestampError) -> JSONResponse:
    """Handler for InvalidTimestampError."""
    logger.error("Invalid timestamp: %s", exc)
    return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content={"detail": str(exc)})


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handler for RequestValidationError with more user-friendly error messages."""
    logger.error("Validation error: %s", exc)
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": format_validation_errors(exc.errors())},
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Generic exception handler for unhandled exceptions."""
    logger.error("Unhandled exception: %s", exc)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "An unexpected error occurred. Please contact the administrator."},
    )


def format_validation_errors(errors: list[dict[str, Any]]) -> list[dict[str, Any]]:
    """Format validation errors to be more user-friendly.

    Args:
        errors: List of validation errors

    Returns:
        List of formatted error dictionaries
    """
    formatted_errors = []
    for error in errors:
        formatted_errors.append(
            {
                "field": ".".join([str(loc) for loc in error["loc"]]) if error.get("loc") else None,
                "message": error.get("msg", ""),
                "type": error.get("type", ""),
            }
        )
    return formatted_errors
