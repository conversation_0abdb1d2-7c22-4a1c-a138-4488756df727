from __future__ import annotations

from datetime import datetime

from fastapi import APIRouter

# Create router
router = APIRouter(tags=["general"])


# Global variable to track API start time
_start_time = datetime.utcnow()
_parsing_status = {"status": "idle", "start_time": None, "end_time": None, "error": None}


@router.get("/", include_in_schema=False)
async def root():
    """API root / health check endpoint."""
    return {"message": "Log Visualizer API is running"}


@router.get("/health", include_in_schema=False)
async def health_check():
    """Health check endpoint for monitoring."""
    try:
        # Check if cross analyzer is available and initialized
        from app.api.dependencies import analyzers

        startup_status = {
            "status": "ok",
            "api_uptime": str(datetime.utcnow() - _start_time),
            "initialized": False,
            "details": {},
        }

        if "cross" in analyzers:
            cross_analyzer = analyzers["cross"]
            if hasattr(cross_analyzer, "initialized"):
                startup_status["initialized"] = cross_analyzer.initialized

                # Add details about what has been initialized
                startup_status["details"] = {
                    "common_time_range": cross_analyzer._common_time_range is not None,
                    "positions_loaded": (len(cross_analyzer._positions) if cross_analyzer._positions else 0),
                    "column_availability": (len(cross_analyzer._column_availability) if cross_analyzer._column_availability else 0),
                    "log_summary": cross_analyzer._log_summary is not None,
                }

                if not cross_analyzer.initialized:
                    startup_status["status"] = "starting"
            else:
                startup_status["status"] = "starting"
        else:
            startup_status["status"] = "starting"

        return startup_status
    except Exception as e:
        return {"status": "error", "error": str(e), "initialized": False}
