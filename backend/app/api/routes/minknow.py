from __future__ import annotations

from datetime import datetime

import pandas as pd
from app.api.dependencies import get_minknow_analyzer, normalize_timestamp, parse_positions_param
from app.models import (
    StructuredLogResponse,
)
from app.utils.logging import logger
from fastapi import APIRouter, Depends, Query

# Create router
router = APIRouter(prefix="/minknow", tags=["minknow"])


@router.get(
    "",
    summary="Query Minknow log entries with filters and pagination",
    response_model=StructuredLogResponse,
)
async def query_minknow(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    log_event: str | None = Query(None, description="Filter by log event"),
    process_name: str | None = Query(None, description="Filter by process name"),
    log_level: str | None = Query(None, description="Filter by log level (e.g., warning, error)"),
    library: str | None = Query(None, description="Filter by library"),
    positions: str | None = Query(None, description="Comma-separated list of positions to filter by"),
    limit: int = Query(100, description="Number of records to return"),
    offset: int = Query(0, description="Offset for pagination"),
    analyzer=Depends(get_minknow_analyzer),
    position_list: list[str] | None = Depends(parse_positions_param),
):
    """Retrieves Minknow log entries filtered by time range and additional parameters."""
    logger.info("Querying Minknow logs: start=%s, end=%s, positions=%s", start, end, position_list)

    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    # Query using our filters
    filters = {}
    if log_event:
        filters["log_event"] = log_event
    if process_name:
        filters["process_name"] = process_name
    if log_level:
        filters["log_level"] = log_level
    if library:
        filters["library"] = library

    # Get all entries that match any of the selected positions
    all_entries = []
    if position_list:
        logger.info("Querying for specific positions: %s", position_list)
        for position in position_list:
            position_filters = filters.copy()
            position_filters["folder_name"] = position
            df = analyzer.query(start=start, end=end, **position_filters)
            entries = analyzer._df_to_dict_list(df)
            logger.info("Found %d entries for position %s", len(entries), position)
            all_entries.extend(entries)
    else:
        logger.info("No positions specified, querying all positions")
        df = analyzer.query(start=start, end=end, **filters)
        all_entries = analyzer._df_to_dict_list(df)
        logger.info("Found %d total entries", len(all_entries))

    if not all_entries:
        logger.info("No entries found")
        return StructuredLogResponse(total=0, limit=limit, offset=offset, entries=[])

    # Sort and paginate
    sorted_entries = sorted(all_entries, key=lambda x: x["timestamp"])
    total = len(sorted_entries)
    paginated_entries = sorted_entries[offset : offset + limit]

    # Map folder_name to position for each entry
    for entry in paginated_entries:
        if "folder_name" in entry:
            entry["position"] = entry.pop("folder_name")

        # Extract key_ columns into details field
        details = {}
        key_columns = [col for col in entry.keys() if col.startswith("key_")]
        for col in key_columns:
            value = entry.pop(col)
            if pd.notna(value):  # Only include non-nan values
                key_name = col[4:]  # Remove "key_" prefix
                details[key_name] = value

        if details:  # Only add details field if there are non-nan values
            entry["details"] = details

    logger.info("Returning %d entries (total=%d)", len(paginated_entries), total)
    return StructuredLogResponse(total=total, limit=limit, offset=offset, entries=paginated_entries)
