from __future__ import annotations

from datetime import datetime

from app.api.dependencies import get_cross_analyzer, get_minknow_analyzer, get_resource_analyzer
from app.models import LogSummaryResponse, TimestampRangeResponse
from app.utils.logging import logger
from app.utils.serialization import prepare_data_for_response
from fastapi import APIRouter, Depends, HTTPException, Query

from log_parser.analysis.cross_analyzer import CrossAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.utils.time_utils import to_pandas_timestamp

# Create router
router = APIRouter(prefix="/analysis", tags=["analysis"])


@router.get(
    "/timestamp_range",
    summary="Get timestamp ranges for data analysis",
    response_model=TimestampRangeResponse,
)
async def get_timestamp_range(
    range_type: str = Query("common", description="Type of range to return (common, resource, minknow, full)"),
    position: str | None = Query(None, description="Filter ranges for a specific position"),
    analyzer=Depends(get_cross_analyzer),
):
    """Returns timestamp ranges for data analysis.

    - range_type=common: Returns common time range across all data sources
    - position: When specified, also returns position-specific time range
    """
    logger.info("Getting timestamp range: type=%s, position=%s", range_type, position)

    # Get system-wide ranges
    common_start, common_end = analyzer.get_common_time_range()
    response = TimestampRangeResponse(common_start=common_start, common_end=common_end)

    # Calculate column availability for the common range
    if common_start and common_end:
        try:
            # Ensure timestamps are properly handled
            pandas_start = to_pandas_timestamp(common_start)
            pandas_end = to_pandas_timestamp(common_end)

            if pandas_start is not None and pandas_end is not None:
                column_availability = analyzer.get_column_availability(pandas_start, pandas_end)
                # Convert NumPy types to Python native types
                response.column_availability = prepare_data_for_response(column_availability)
            else:
                logger.warning("Invalid timestamp range: start=%s, end=%s", common_start, common_end)
        except Exception:
            logger.exception("Error calculating column availability")

    # If position is specified, get position-specific range
    if position:
        position_start, position_end = analyzer.minknow_analyzer.get_position_time_range(position)
        response.position_start = position_start
        response.position_end = position_end

        # Calculate column availability for position-specific range
        if position_start and position_end:
            try:
                # Ensure timestamps are properly handled
                pandas_start = to_pandas_timestamp(position_start)
                pandas_end = to_pandas_timestamp(position_end)

                if pandas_start is not None and pandas_end is not None:
                    position_column_availability = analyzer.get_column_availability(pandas_start, pandas_end)
                    # Convert NumPy types to Python native types
                    response.position_column_availability = prepare_data_for_response(position_column_availability)
                else:
                    logger.warning("Invalid position timestamp range: start=%s, end=%s", position_start, position_end)
            except Exception:
                logger.exception("Error calculating position column availability")

    logger.info("Timestamp range: common=%s to %s", common_start, common_end)
    # Return the response directly
    return response


@router.get(
    "/log_summary",
    summary="Get summary statistics of the log data with filters",
    response_model=LogSummaryResponse,
)
async def get_log_summary(
    position: str | None = Query(None, description="Filter summary by position"),
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    analyzer=Depends(get_cross_analyzer),
):
    """Returns summary statistics of the log data including counts by position and level,
    with optional filters for position, start time, and end time.
    """
    logger.info("Getting log summary with filters: position=%s, start=%s, end=%s", position, start, end)

    # Fetch the log summary using the provided filters
    summary = analyzer.get_log_summary(position=position, start=start, end=end)

    # Convert the summary to a LogSummaryResponse model instance
    response = LogSummaryResponse(**summary)
    return response


@router.get(
    "/log_level_anomalies",
    summary="Detect anomalies in log levels",
    response_description="List of detected log level anomalies",
)
async def detect_log_level_anomalies(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position: str | None = Query(None, description="Filter by position"),
    error_threshold: float = Query(0.05, description="Error rate threshold (0.0 to 1.0)"),
    warning_threshold: float = Query(0.40, description="Warning rate threshold (0.0 to 1.0)"),
    window_size: str = Query("10min", description="Time window size for analysis"),
    minknow_analyzer: StructuredLogAnalyzer = Depends(get_minknow_analyzer),
):
    """Detect anomalies in log levels based on error and warning rate thresholds."""
    logger.info("Detecting log level anomalies: start=%s, end=%s, position=%s", start, end, position)

    try:
        # Get log level anomalies from minknow analyzer
        anomalies = minknow_analyzer.detect_log_level_anomalies(
            start=start,
            end=end,
            error_threshold=error_threshold,
            warning_threshold=warning_threshold,
            window_size=window_size,
            folder_name=position,
        )

        # Convert timestamps to ISO format
        for anomaly in anomalies:
            if isinstance(anomaly["start"], datetime):
                anomaly["start"] = anomaly["start"].isoformat()
            if isinstance(anomaly["end"], datetime):
                anomaly["end"] = anomaly["end"].isoformat()

        return anomalies
    except Exception:
        logger.exception("Error detecting log level anomalies")
        raise


@router.get(
    "/time_series_anomalies",
    summary="Detect anomalies in time series data",
    response_description="List of detected time series anomalies",
)
async def detect_time_series_anomalies(
    column: str = Query(..., description="Column to analyze for anomalies"),
    method: str = Query(
        "zscore",
        description="Anomaly detection method (zscore, iqr, control_chart, persist, level_shift)",
    ),
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position: str | None = Query(None, description="Filter by position"),
    window_size: str = Query("1h", description="Time window size for analysis"),
    rolling_window: int = Query(24, description="Number of windows for rolling statistics"),
    zscore_threshold: float | None = Query(None, description="Threshold for z-score method"),
    iqr_factor: float | None = Query(None, description="Factor for IQR method"),
    control_chart_std: float | None = Query(None, description="Standard deviations for control chart"),
    min_data_coverage: float = Query(0.05, description="Minimum percentage of non-null values required (0.0 to 1.0)"),
    resource_analyzer: ResourceAnalyzer = Depends(get_resource_analyzer),
    minknow_analyzer: StructuredLogAnalyzer = Depends(get_minknow_analyzer),
    cross_analyzer: CrossAnalyzer = Depends(get_cross_analyzer),
):
    """Detect anomalies in time series data using various statistical methods."""
    logger.info("Detecting time series anomalies: column=%s, method=%s, start=%s, end=%s", column, method, start, end)

    try:
        # Check if the column exists in the available columns
        column_exists = False

        # Get timestamp range to check column availability
        common_start, common_end = cross_analyzer.get_common_time_range()
        if common_start and common_end:
            # Convert to pandas timestamps
            pandas_start = to_pandas_timestamp(common_start)
            pandas_end = to_pandas_timestamp(common_end)

            if pandas_start is not None and pandas_end is not None:
                # Get column availability
                column_availability = cross_analyzer.get_column_availability(pandas_start, pandas_end)

                # Check if the column exists in any source
                for source, columns in column_availability.items():
                    if column in columns:
                        column_exists = True
                        break

        if not column_exists:
            logger.warning("Column '%s' not found in available columns", column)
            raise HTTPException(
                status_code=404,
                detail=f"Column '{column}' not found. Use the /analysis/timestamp_range endpoint to see available columns.",
            )

        # Determine which analyzer to use based on the column
        if column in resource_analyzer.get_column_stats():
            # Use resource analyzer for resource metrics
            anomalies = resource_analyzer.detect_time_series_anomalies(
                column=column,
                method=method,
                start=start,
                end=end,
                window_size=window_size,
                rolling_window=rolling_window,
                zscore_threshold=zscore_threshold,
                iqr_factor=iqr_factor,
                control_chart_std=control_chart_std,
                min_data_coverage=min_data_coverage,
            )
        else:
            # Use minknow analyzer for log-related columns
            # Always pass folder_name parameter, even if None
            anomalies = minknow_analyzer.detect_time_series_anomalies(
                column=column,
                method=method,
                start=start,
                end=end,
                window_size=window_size,
                rolling_window=rolling_window,
                zscore_threshold=zscore_threshold,
                iqr_factor=iqr_factor,
                control_chart_std=control_chart_std,
                min_data_coverage=min_data_coverage,
                folder_name=position,  # This will be None when no position is specified
            )

        # Convert timestamps to ISO format and ensure all values are JSON serializable
        serializable_anomalies = []
        for anomaly in anomalies:
            # Create a new dict with converted values
            serializable_anomaly = {}

            # Convert timestamps
            if isinstance(anomaly["start"], datetime):
                serializable_anomaly["start"] = anomaly["start"].isoformat()
            else:
                serializable_anomaly["start"] = anomaly["start"]

            if isinstance(anomaly["end"], datetime):
                serializable_anomaly["end"] = anomaly["end"].isoformat()
            else:
                serializable_anomaly["end"] = anomaly["end"]

            # Copy other fields
            serializable_anomaly["type"] = anomaly["type"]

            # Convert value to native Python type if it's a NumPy type
            if hasattr(anomaly["value"], "item"):
                serializable_anomaly["value"] = anomaly["value"].item()
            else:
                serializable_anomaly["value"] = anomaly["value"]

            # Convert details to native Python types
            serializable_details = {}
            for key, value in anomaly["details"].items():
                if hasattr(value, "item"):
                    serializable_details[key] = value.item()
                else:
                    serializable_details[key] = value

            serializable_anomaly["details"] = serializable_details
            serializable_anomalies.append(serializable_anomaly)

        return serializable_anomalies
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error detecting time series anomalies")
        raise HTTPException(status_code=500, detail=f"Error detecting time series anomalies: {e!s}")
