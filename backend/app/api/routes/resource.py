from __future__ import annotations

from datetime import datetime

from app.api.dependencies import get_resource_analyzer, parse_columns_param, validate_timerange
from app.models import (
    ResourceResponse,
)
from app.utils.logging import logger
from app.utils.serialization import convert_to_serializable
from fastapi import APIRouter, Depends, Query, status

# Create router
router = APIRouter(prefix="/resource", tags=["resource"])


@router.get(
    "",
    summary="Query Resource-System logs by time range with pagination",
    response_model=ResourceResponse,
    status_code=status.HTTP_200_OK,
)
async def query_resource(
    start: datetime = Query(..., description="Start timestamp (ISO format)"),
    end: datetime = Query(..., description="End timestamp (ISO format)"),
    columns: str | None = Query(None, description="Comma-separated list of columns to include"),
    limit: int = Query(5000, description="Number of records to return"),
    offset: int = Query(0, description="Offset for pagination"),
    analyzer=Depends(get_resource_analyzer),
    normalized_timerange: tuple[datetime, datetime] = Depends(validate_timerange),
    column_list: list[str] | None = Depends(parse_columns_param),
):
    """Returns all Resource-System log records whose timestamps fall within the provided range.
    The results are sorted by timestamp (ascending) and paginated.
    Optionally filter the returned columns using the columns parameter.
    """
    start_time, end_time = normalized_timerange
    logger.info("Querying resource logs: %s to %s, columns=%s", start_time, end_time, column_list)

    # Get records from analyzer
    if column_list:
        # Query with specific columns
        records = []
        for column in column_list:
            data = analyzer.get_resource_column(column, start_time, end_time)
            if data["timestamps"]:
                for i, ts in enumerate(data["timestamps"]):
                    # Find or create a record for this timestamp
                    record = next((r for r in records if r["timestamp"] == ts), None)
                    if not record:
                        record = {"timestamp": ts}
                        records.append(record)
                    # Add column value
                    record[column] = data["values"][i]
    else:
        # Query all columns within the time range
        df = analyzer.query(start_time, end_time)
        records = []
        for idx, row in df.iterrows():
            record = {"timestamp": idx.isoformat()}
            for col in df.columns:
                record[col] = row[col]
            records.append(record)

    if not records:
        logger.info("No records found")
        return ResourceResponse(
            start=start_time,
            end=end_time,
            total=0,
            limit=limit,
            offset=offset,
            available_columns=[],
            records=[],
        )

    # Sort by timestamp
    sorted_records = sorted(records, key=lambda x: x["timestamp"])
    total = len(sorted_records)
    paginated_records = sorted_records[offset : offset + limit]

    # Make sure all values are properly serializable
    serialized_records = convert_to_serializable(paginated_records)

    available_columns = list(serialized_records[0].keys()) if serialized_records else []

    logger.info("Returning %d records (total=%d)", len(serialized_records), total)
    return ResourceResponse(
        start=start_time,
        end=end_time,
        total=total,
        limit=limit,
        offset=offset,
        available_columns=available_columns,
        records=serialized_records,
    )


@router.get(
    "/columns",
    summary="Get available resource columns",
)
async def get_resource_columns(analyzer=Depends(get_resource_analyzer)):
    """Returns a list of all available columns in the Resource-System logs.
    This includes both top-level columns and nested columns (e.g., CPU[0].usage).
    Also provides information about data completeness for each column.
    """
    logger.info("Getting resource columns")

    # Get column statistics from analyzer
    column_stats = analyzer.get_column_stats()
    columns = list(column_stats.keys())

    # Make sure all values are properly serializable
    serialized_stats = convert_to_serializable(column_stats)

    logger.info("Found %d resource columns", len(columns))
    return {"columns": columns, "column_stats": serialized_stats}
