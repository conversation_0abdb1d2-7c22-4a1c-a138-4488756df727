from __future__ import annotations

from datetime import datetime

from app.api.dependencies import get_timeline_analyzer
from app.utils.logging import logger
from fastapi import APIRouter, Depends, Query

# Create router
router = APIRouter(prefix="/timeline", tags=["timeline"])


@router.get(
    "",
    summary="Get timeline events with filtering options",
    response_description="List of timeline events matching the filter criteria",
)
async def get_timeline_events(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position_id: str | None = Query(None, description="Filter timeline events by position ID"),
    timeline_event_type: str | None = Query(None, description="Filter timeline events by timeline event type"),
    include_children: bool = Query(True, description="Include child timeline events in response"),
    analyzer=Depends(get_timeline_analyzer),
):
    """Returns timeline events matching the specified filters."""
    logger.info(
        "Getting timeline events: start=%s, end=%s, position_id=%s, timeline_event_type=%s, include_children=%s",
        start,
        end,
        position_id,
        timeline_event_type,
        include_children,
    )

    try:
        # Query timeline events with filters
        timeline_events = analyzer.query_timeline_events(
            start_time=start,
            end_time=end,
            position_id=position_id,
            timeline_event_type=timeline_event_type,
            include_children=include_children,
        )

        # Convert timeline events to serializable format
        serializable_timeline_events = []
        for timeline_event in timeline_events:
            # Convert TimelineEvent object to dict
            timeline_event_dict = {
                "id": timeline_event.id,
                "name": timeline_event.name,
                "start_time": timeline_event.start_time.isoformat(),
                "end_time": timeline_event.end_time.isoformat() if timeline_event.end_time is not None else None,
                "duration": (
                    (timeline_event.end_time - timeline_event.start_time).total_seconds() if timeline_event.end_time is not None else None
                ),
                "position_id": timeline_event.position_id,
                "metadata": {},
                "is_complete": timeline_event.is_complete(),
            }

            # Clean metadata to ensure it's serializable (no NaN values)
            for key, value in timeline_event.metadata.items():
                # Handle NaN values
                if isinstance(value, float) and (value != value or value in (float("inf"), float("-inf"))):
                    timeline_event_dict["metadata"][key] = None
                else:
                    timeline_event_dict["metadata"][key] = value

            # Add children reference if they exist
            if hasattr(timeline_event, "children") and timeline_event.children:
                timeline_event_dict["children"] = [child.id for child in timeline_event.children]
                timeline_event_dict["has_children"] = True
            else:
                timeline_event_dict["has_children"] = False

            # Add markers if they exist
            if hasattr(timeline_event, "markers") and timeline_event.markers:
                timeline_event_dict["markers"] = []
                for marker in timeline_event.markers:
                    marker_dict = {
                        "id": marker.id,
                        "name": marker.name,
                        "timestamp": marker.timestamp.isoformat(),
                        "metadata": marker.metadata,
                        "log_event": marker.log_event,
                        "position_id": marker.position_id,
                    }
                    timeline_event_dict["markers"].append(marker_dict)
                timeline_event_dict["has_markers"] = True
            else:
                timeline_event_dict["markers"] = []
                timeline_event_dict["has_markers"] = False

            serializable_timeline_events.append(timeline_event_dict)

    except Exception:
        logger.exception("Error getting timeline events")
        raise
    else:
        return {"timeline_events": serializable_timeline_events}


@router.get("/config", summary="Get timeline event configuration")
async def get_timeline_config(analyzer=Depends(get_timeline_analyzer)):
    """Returns the timeline event configuration."""
    logger.info("Getting timeline event configuration")

    try:
        # Use the get_available_timeline_events method instead of accessing config directly
        timeline_events = analyzer.get_available_timeline_events()
        markers = analyzer.get_available_markers()
        return {
            "config": {
                "timeline_event_types": {timeline_event["name"]: timeline_event for timeline_event in timeline_events},
                "marker_types": {marker["name"]: marker for marker in markers},
            }
        }
    except Exception:
        logger.exception("Error getting timeline event configuration")
        raise


@router.get(
    "/markers",
    summary="Get timeline markers with filtering options",
    response_description="List of timeline markers matching the filter criteria",
)
async def get_timeline_markers(
    marker_type: str | None = Query(None, description="Filter markers by type"),
    timeline_event_id: str | None = Query(None, description="Filter markers by timeline event ID"),
    position_id: str | None = Query(None, description="Filter markers by position ID"),
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    analyzer=Depends(get_timeline_analyzer),
):
    """Returns timeline markers matching the specified filters."""
    logger.info(
        "Getting timeline markers: marker_type=%s, timeline_event_id=%s, position_id=%s, start=%s, end=%s",
        marker_type,
        timeline_event_id,
        position_id,
        start,
        end,
    )

    try:
        # Query markers with filters
        markers = analyzer.query_markers(
            marker_type=marker_type,
            timeline_event_id=timeline_event_id,
            position_id=position_id,
            start_time=start,
            end_time=end,
        )

        # Convert markers to serializable format
        serializable_markers = []
        for marker in markers:
            marker_dict = {
                "id": marker.id,
                "name": marker.name,
                "timestamp": marker.timestamp.isoformat(),
                "metadata": marker.metadata,
                "log_event": marker.log_event,
                "position_id": marker.position_id,
            }
            serializable_markers.append(marker_dict)

        return {"markers": serializable_markers}

    except Exception:
        logger.exception("Error getting timeline markers")
        raise
