from __future__ import annotations

from datetime import datetime
from typing import Any

from app.api.dependencies import get_minknow_analyzer, normalize_timestamp, parse_positions_param
from app.models.response.base import (
    LogSummaryResponse,
    PositionsResponse,
    StructuredLogResponse,
    TimestampRangeResponse,
)
from app.utils.logging import logger
from fastapi import APIRouter, Depends, Query

# Create router
router = APIRouter(prefix="/structured", tags=["structured"])


@router.get(
    "",
    summary="Query structured log entries with filters and pagination",
    response_model=StructuredLogResponse,
)
async def query_structured_logs(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    log_event: str | None = Query(None, description="Filter by log event"),
    process_name: str | None = Query(None, description="Filter by process name"),
    log_level: str | None = Query(None, description="Filter by log level (e.g., warning, error)"),
    library: str | None = Query(None, description="Filter by library"),
    positions: str | None = Query(None, description="Comma-separated list of positions to filter by"),
    limit: int = Query(100, description="Number of records to return"),
    offset: int = Query(0, description="Offset for pagination"),
    analyzer=Depends(get_minknow_analyzer),
    position_list: list[str] | None = Depends(parse_positions_param),
):
    """Retrieves structured log entries filtered by time range and additional parameters."""
    logger.info("Querying structured logs: start=%s, end=%s, positions=%s", start, end, position_list)

    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    # Query using our filters
    filters = {}
    if log_event:
        filters["log_event"] = log_event
    if process_name:
        filters["process_name"] = process_name
    if log_level:
        filters["log_level"] = log_level
    if library:
        filters["library"] = library

    # Get all entries that match any of the selected positions
    all_entries = []
    if position_list:
        logger.info("Querying for specific positions: %s", position_list)
        for position in position_list:
            position_filters = filters.copy()
            position_filters["folder_name"] = position
            df = analyzer.query(start=start, end=end, **position_filters)
            entries = analyzer._df_to_dict_list(df)
            logger.info("Found %d entries for position %s", len(entries), position)
            all_entries.extend(entries)
    else:
        logger.info("No positions specified, querying all positions")
        df = analyzer.query(start=start, end=end, **filters)
        all_entries = analyzer._df_to_dict_list(df)
        logger.info("Found %d total entries", len(all_entries))

    if not all_entries:
        logger.info("No entries found")
        return StructuredLogResponse(total=0, limit=limit, offset=offset, entries=[])

    # Sort and paginate
    sorted_entries = sorted(all_entries, key=lambda x: x["timestamp"])
    total = len(sorted_entries)
    paginated_entries = sorted_entries[offset : offset + limit]

    # Map folder_name to position for each entry
    for entry in paginated_entries:
        if "folder_name" in entry:
            entry["position"] = entry.pop("folder_name")

        # Extract key_ columns into details field
        details = {}
        key_columns = [col for col in entry.keys() if col.startswith("key_")]
        for col in key_columns:
            value = entry.pop(col)
            if value is not None:  # Only include non-null values
                key_name = col[4:]  # Remove "key_" prefix
                details[key_name] = value

        if details:  # Only add details field if there are non-null values
            entry["details"] = details

    logger.info("Returning %d entries (total=%d)", len(paginated_entries), total)
    return StructuredLogResponse(total=total, limit=limit, offset=offset, entries=paginated_entries)


@router.get(
    "/positions",
    summary="Get list of available positions",
    response_model=PositionsResponse,
)
async def get_positions(analyzer=Depends(get_minknow_analyzer)):
    """Get list of unique positions (folder names)."""
    positions = analyzer.get_positions()
    logger.info("Found %d positions: %s", len(positions), positions)
    return PositionsResponse(positions=positions)


@router.get(
    "/log-levels",
    summary="Get list of available log levels",
    response_model=dict[str, list[str]],
)
async def get_log_levels(analyzer=Depends(get_minknow_analyzer)):
    """Get list of unique log levels."""
    log_levels = analyzer.get_log_levels()
    logger.info("Found %d log levels: %s", len(log_levels), log_levels)
    return {"log_levels": log_levels}


@router.get(
    "/filters",
    summary="Get available filters",
    response_model=dict[str, list[str]],
)
async def get_filters(analyzer=Depends(get_minknow_analyzer)):
    """Get list of available filters (column headers)."""
    filters = analyzer.get_filters()
    logger.info("Found %d filters: %s", len(filters), filters)
    return {"filters": filters}


@router.get(
    "/time-range",
    summary="Get time range covered by the logs",
    response_model=TimestampRangeResponse,
)
async def get_time_range(
    filter: str | None = Query(None, description="Name of the column to filter logs"),
    value: str | None = Query(None, description="Value to filter the specified column"),
    analyzer=Depends(get_minknow_analyzer),
):
    """Get the time range covered by the logs, optionally filtered by a specified column."""
    start, end = analyzer.get_time_range(filter=filter, value=value)
    logger.info("Time range: start=%s, end=%s", start, end)
    return TimestampRangeResponse(
        common_start=start,
        common_end=end,
        position_start=start,
        position_end=end,
    )


@router.get(
    "/position/{position}/time-range",
    summary="Get time range for a specific position",
    response_model=TimestampRangeResponse,
)
async def get_position_time_range(
    position: str,
    analyzer=Depends(get_minknow_analyzer),
):
    """Get the time range for a specific position."""
    start, end = analyzer.get_position_time_range(position)
    logger.info("Time range for position %s: start=%s, end=%s", position, start, end)
    return TimestampRangeResponse(
        position_start=start,
        position_end=end,
    )


@router.get(
    "/log-events",
    summary="Get counts of log events by type",
    response_model=dict[str, int],
)
async def get_log_events(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position: str | None = Query(None, description="Filter by position name"),
    analyzer=Depends(get_minknow_analyzer),
):
    """Get counts of log events by type."""
    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    event_counts = analyzer.get_log_events(start=start, end=end, position=position)
    logger.info("Found %d log event types", len(event_counts))
    return event_counts


@router.get(
    "/log-levels/counts",
    summary="Get counts of log levels",
    response_model=dict[str, int],
)
async def get_log_level_counts(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position: str | None = Query(None, description="Filter by position name"),
    analyzer=Depends(get_minknow_analyzer),
):
    """Get counts of log levels."""
    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    level_counts = analyzer.get_log_level_counts(start=start, end=end, position=position)
    logger.info("Found %d log level types", len(level_counts))
    return level_counts


@router.get(
    "/event-timeline",
    summary="Get event timeline for visualization",
    response_model=dict[str, Any],
)
async def get_event_timeline(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position: str | None = Query(None, description="Filter by position name"),
    freq: str = Query("1Min", description="Time frequency for binning ('1Min', '1h', etc.)"),
    event_type: str | None = Query(None, description="Filter by event type"),
    analyzer=Depends(get_minknow_analyzer),
):
    """Get event timeline for visualization."""
    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    timeline_data = analyzer.get_event_timeline(start=start, end=end, position=position, freq=freq, event_type=event_type)
    logger.info("Generated event timeline with %d time points", len(timeline_data.get("timestamps", [])))
    return timeline_data


@router.get(
    "/log-level-timeline",
    summary="Get log level timeline for visualization",
    response_model=dict[str, Any],
)
async def get_log_level_timeline(
    start: datetime | None = Query(None, description="Start timestamp (ISO format)"),
    end: datetime | None = Query(None, description="End timestamp (ISO format)"),
    position: str | None = Query(None, description="Filter by position name"),
    freq: str = Query("1Min", description="Time frequency for binning ('1Min', '1h', etc.)"),
    log_level: str | None = Query(None, description="Filter by log level"),
    analyzer=Depends(get_minknow_analyzer),
):
    """Get log level timeline for visualization."""
    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    timeline_data = analyzer.get_log_level_timeline(start=start, end=end, position=position, freq=freq, log_level=log_level)
    logger.info("Generated log level timeline with %d time points", len(timeline_data.get("timestamps", [])))
    return timeline_data
