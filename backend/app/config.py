from __future__ import annotations

from enum import Enum
from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict


class EnvironmentType(str, Enum):
    DEV = "dev"
    TEST = "test"
    PROD = "prod"


class Settings(BaseSettings):
    env: EnvironmentType = EnvironmentType.DEV
    base_log_folder: str = ""
    debug: bool = False
    log_level: str = "INFO"

    # Log file paths (these were in the .env file but not declared in the model)
    data_path: str = ""
    resource_log_file: str = ""
    event_log_file: str = ""

    # Debug output settings
    save_intermediate_data: bool = False
    intermediate_data_dir: str = "intermediate_data"

    # Different configuration based on environment
    @property
    def is_development(self):
        return self.env == EnvironmentType.DEV

    @property
    def is_testing(self):
        return self.env == EnvironmentType.TEST

    @property
    def is_production(self):
        return self.env == EnvironmentType.PROD

    # In Pydantic v2, Config is replaced with model_config
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="allow",  # Allow extra fields from .env file
    )


@lru_cache
def get_settings():
    """Get settings with caching."""
    return Settings()


settings = get_settings()
