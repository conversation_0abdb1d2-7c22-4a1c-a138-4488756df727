from __future__ import annotations

from datetime import datetime
from typing import Any

import numpy as np
from pydantic import BaseModel, Field


class CustomSerializableModel(BaseModel):
    """Base model with custom serialization to handle NumPy types."""

    def dict(self, *args, **kwargs):
        """Override dict method to convert NumPy types to Python native types."""
        result = super().dict(*args, **kwargs)
        return self._convert_numpy_types(result)

    def _convert_numpy_types(self, obj):
        """Recursively convert NumPy types to Python native types."""
        if isinstance(obj, dict):
            return {k: self._convert_numpy_types(v) for k, v in obj.items()}
        if isinstance(obj, list):
            return [self._convert_numpy_types(v) for v in obj]
        if isinstance(obj, tuple):
            return tuple(self._convert_numpy_types(v) for v in obj)
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.bool_):
            return bool(obj)
        if isinstance(obj, np.str_):
            return str(obj)
        return obj


class ParseResponse(CustomSerializableModel):
    """Response model for parse operation."""

    message: str = Field(..., description="Status message")
    success: bool = Field(..., description="Whether parsing was successful")


class ErrorResponse(CustomSerializableModel):
    """Base response model for errors."""

    detail: Any = Field(..., description="Error details")


class PaginatedResponse(CustomSerializableModel):
    """Base response model for paginated results."""

    limit: int = Field(..., description="Number of records per page")
    offset: int = Field(..., description="Offset from start")
    total: int = Field(..., description="Total number of records")


class ResourceResponse(PaginatedResponse):
    """Response model for resource queries."""

    start: datetime = Field(..., description="Start timestamp")
    end: datetime = Field(..., description="End timestamp")
    available_columns: list[str] = Field(..., description="Available columns in response")
    records: list[dict[str, Any]] = Field(..., description="Resource records")


class MinknowLogEntry(CustomSerializableModel):
    """Model for a Minknow log entry."""

    timestamp: datetime = Field(..., description="Timestamp")
    position: str | None = Field(None, description="Position identifier")
    process_name: str | None = Field(None, description="Process name")
    log_level: str | None = Field(None, description="Log level")
    log_event: str | None = Field(None, description="Log event")
    library: str | None = Field(None, description="Library")
    message: str = Field(..., description="Log message")
    details: dict[str, Any] | None = Field(None, description="Additional details")


class StructuredLogResponse(PaginatedResponse):
    """Response model for Minknow log queries."""

    entries: list[MinknowLogEntry] = Field(..., description="Minknow log entries")


class AggregatedLogData(CustomSerializableModel):
    """Response model for aggregated log data."""

    data: dict[str, list[dict[str, Any]]] = Field(..., description="Aggregated log data by level")
    maxCounts: dict[str, int] = Field(..., description="Maximum counts by level")
    bucketSize: int = Field(..., description="Bucket size in seconds")


class LogSummaryResponse(CustomSerializableModel):
    """Response model for log summary."""

    total_entries: int = Field(..., description="Total number of log entries")
    total_resource_entries: int = Field(0, description="Total number of resource log entries")
    counts_by_position: dict[str, int] = Field(..., description="Entry counts by position")
    counts_by_level: dict[str, int] = Field(..., description="Entry counts by log level")
    errors_by_position: dict[str, int] = Field(..., description="Error counts by position")
    time_range: dict[str, Any] = Field(..., description="Overall time range")
    positions: dict[str, Any] | None = Field(None, description="Detailed position information")
    resource: dict[str, Any] | None = Field(None, description="Resource metrics summary")
    position: str | None = Field(None, description="Position filter if specified")


class PositionsResponse(CustomSerializableModel):
    """Response model for positions list."""

    positions: list[str] = Field(..., description="Available positions")


class ColumnAvailability(CustomSerializableModel):
    """Model for column availability data."""

    completeness: float = Field(..., description="Percentage of completeness")
    first_seen: str | None = Field(None, description="First timestamp the column was seen")
    last_seen: str | None = Field(None, description="Last timestamp the column was seen")
    non_null_count: int = Field(..., description="Count of non-null values")
    total_entries: int = Field(..., description="Total number of entries for this column")


class TimestampRangeResponse(CustomSerializableModel):
    """Response model for timestamp range queries."""

    common_start: datetime | None = Field(None, description="Common start timestamp")
    common_end: datetime | None = Field(None, description="Common end timestamp")
    column_availability: dict[str, dict[str, Any]] | None = Field(None, description="Column availability data")
    position_start: datetime | None = Field(None, description="Position-specific start timestamp")
    position_end: datetime | None = Field(None, description="Position-specific end timestamp")
    position_column_availability: dict[str, dict[str, Any]] | None = Field(None, description="Position-specific column availability")


class ResourceColumnResponse(CustomSerializableModel):
    """Response model for resource column queries."""

    column: str = Field(..., description="Column name")
    data: list[dict[str, Any]] = Field(..., description="Column data")
