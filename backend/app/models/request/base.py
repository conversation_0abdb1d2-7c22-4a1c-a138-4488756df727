from __future__ import annotations

from datetime import datetime

from pydantic import BaseModel, Field, field_validator

# For backward compatibility with Pydantic v1
try:
    from pydantic import validator
except ImportError:
    # In Pydantic v2, validator is replaced by field_validator
    validator = field_validator


class ParseRequest(BaseModel):
    """Request model for parsing logs."""

    base_dir: str = Field(..., description="Base directory containing log files")
    store: bool = Field(False, description="Whether to store parsed results")
    output_dir: str | None = Field(None, description="Directory to store parsed results")


class TimeRangeRequest(BaseModel):
    """Base request model for time range queries."""

    start: datetime = Field(..., description="Start timestamp (ISO format)")
    end: datetime = Field(..., description="End timestamp (ISO format)")

    # Use validator for Pydantic v1 compatibility
    @validator("start", "end")
    def ensure_timezone_naive(cls, v):
        """Convert timezone-aware datetimes to timezone-naive."""
        if v and v.tzinfo:
            return v.replace(tzinfo=None)
        return v

    # Use validator for Pydantic v1 compatibility
    @validator("end")
    def end_after_start(cls, v, values):
        """Validate that end is after start."""
        if "start" in values and v < values["start"]:
            raise ValueError("End timestamp must be after start timestamp")
        return v


class ResourceQueryRequest(TimeRangeRequest):
    """Request model for resource queries."""

    columns: str | None = Field(None, description="Comma-separated list of columns to include")
    limit: int = Field(5000, description="Number of records to return")
    offset: int = Field(0, description="Offset for pagination")


class MinknowQueryRequest(TimeRangeRequest):
    """Request model for Minknow log queries."""

    log_event: str | None = Field(None, description="Filter by log event")
    process_name: str | None = Field(None, description="Filter by process name")
    log_level: str | None = Field(None, description="Filter by log level (e.g., warning, error)")
    library: str | None = Field(None, description="Filter by library")
    positions: str | None = Field(None, description="Comma-separated list of positions to filter by")
    limit: int = Field(100, description="Number of records to return")
    offset: int = Field(0, description="Offset for pagination")

    def get_positions_list(self) -> list[str] | None:
        """Convert positions string to list."""
        if not self.positions:
            return None
        return [pos.strip() for pos in self.positions.split(",") if pos.strip()]
