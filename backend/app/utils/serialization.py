"""Serialization Utility Functions

This module provides utility functions for converting non-standard types
(like NumPy types) to Python native types that can be safely serialized to JSON.
"""

from __future__ import annotations

import logging
from typing import Any

import numpy as np

logger = logging.getLogger(__name__)


def convert_to_serializable(obj: Any) -> Any:
    """Recursively convert an object with NumPy types to serializable Python types.

    Args:
        obj: The object to convert

    Returns:
        A version of the object with NumPy types converted to native Python types
    """
    if obj is None:
        return None

    # Handle numpy types
    if isinstance(obj, np.integer):
        return int(obj)
    if isinstance(obj, np.floating):
        return float(obj)
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    if isinstance(obj, np.bool_):
        return bool(obj)
    if isinstance(obj, np.str_):
        return str(obj)

    # Handle dictionaries
    if isinstance(obj, dict):
        return {k: convert_to_serializable(v) for k, v in obj.items()}

    # Handle lists, tuples, and sets
    if isinstance(obj, (list, tuple, set)):
        return [convert_to_serializable(item) for item in obj]

    # Return other types as is
    return obj


def prepare_data_for_response(data: Any) -> Any:
    """Prepare data for FastAPI response by ensuring all values are serializable.

    Args:
        data: The data to prepare

    Returns:
        Data with all values converted to serializable types
    """
    try:
        return convert_to_serializable(data)
    except Exception:
        logger.exception("Error converting data for serialization")
        # Return a simplified version of the object if conversion fails
        if isinstance(data, dict):
            # Try to convert individual dictionary items
            result = {}
            for k, v in data.items():
                try:
                    result[k] = convert_to_serializable(v)
                except Exception:
                    # Skip values that can't be converted
                    result[k] = str(v)
            return result

        # For other types, convert to string as a fallback
        return str(data)
