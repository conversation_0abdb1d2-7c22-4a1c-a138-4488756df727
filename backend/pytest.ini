[pytest]
testpaths = tests
addopts = --disable-warnings --ignore=tests/test_log_service.py --ignore=tests/test_event_service.py --ignore=tests/test_api_resource.py --ignore=tests/test_api_minknow.py --ignore=tests/test_api_analysis.py
norecursedirs = __pycache__
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Suppress deprecation warnings that aren't relevant to our tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
