# MinKNOW Log Analyzer Tests

This directory contains unit and integration tests for the MinKNOW Log Analyzer application.

## Test Organization

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test end-to-end functionality with real data from sample logs

## Sample Data Structure

The `samples` directory is organized to mimic a typical log directory structure:

```
samples/
├── minknow/
│   └── positionA/
│       ├── control_server_log-0.txt
│       ├── control_server_log-1.txt
│       └── ...
└── ont-resource-logger/
    └── resource-system.log
```

This structure allows us to test with realistic log data organization.

## Integration Tests

Integration tests use the actual sample log files to test the full API pipeline, including:

1. Log parsing
2. Data analysis
3. API endpoints
4. Serialization handling (especially NaN values)

### Key Test Strategies

- **Real Data Processing**: Tests use real sample log files from the `samples` directory
- **Dependency Overrides**: FastAPI's dependency override system is used to inject real analyzers
- **Monkey Patching**: For specific tests (like NaN handling), we patch methods to inject test values
- **Cleanup**: All tests clean up their dependency overrides to avoid affecting other tests

### Running Tests

To run all tests:

```bash
python -m pytest
```

To run tests with coverage:

```bash
./run_tests_with_coverage.sh
```

To run specific test files:

```bash
python -m pytest tests/test_api_actions.py
```

## Creating New Tests

When adding new tests:

1. Follow the existing patterns for test organization
2. For integration tests, prefer using real data over excessive mocking
3. Use dependency overrides to insert real analyzers with test data
4. Always clean up overrides in finally blocks
5. Consider edge cases like empty results and invalid inputs
