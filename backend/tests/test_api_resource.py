import os
import sys
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest
from fastapi.testclient import TestClient

# Set test environment to avoid actual resource analyzer creation
os.environ["TESTING"] = "1"

from app.main import app

# Create a client that doesn't actually start up the application
client = TestClient(app)


@pytest.fixture
def mock_resource_analyzer():
    """Create a mock resource analyzer with predefined data."""
    mock_analyzer = MagicMock()

    # Create a sample DataFrame for the query method
    date_range = pd.date_range(start="2023-01-01", periods=10, freq="5min")
    df = pd.DataFrame(
        {
            "CPU[0].usage": [10.0, 15.0, 20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0],
            "Memory.used": [1024, 1124, 1224, 1324, 1424, 1524, 1624, 1724, 1824, 1924],
            "Disk[0].io": [5.0, 10.0, 15.0, 20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0],
        },
        index=date_range,
    )

    # Setup the mock's return values
    mock_analyzer.query.return_value = df

    # Setup get_resource_column method
    def mock_get_resource_column(column, start=None, end=None):
        if column == "CPU[0].usage":
            return {"timestamps": df.index.tolist(), "values": df["CPU[0].usage"].tolist()}
        elif column == "Memory.used":
            return {"timestamps": df.index.tolist(), "values": df["Memory.used"].tolist()}
        else:
            return {"timestamps": df.index.tolist(), "values": df[column].tolist() if column in df.columns else []}

    mock_analyzer.get_resource_column.side_effect = mock_get_resource_column

    # Setup get_column_stats method
    mock_analyzer.get_column_stats.return_value = {
        "CPU[0].usage": {"count": 10, "min": 10.0, "max": 55.0, "mean": 32.5},
        "Memory.used": {"count": 10, "min": 1024, "max": 1924, "mean": 1474},
        "Disk[0].io": {"count": 10, "min": 5.0, "max": 50.0, "mean": 27.5},
    }

    return mock_analyzer


class TestResourceAPI:

    @pytest.mark.asyncio
    async def test_query_resource_all_columns(self):
        """Test querying resource logs with all columns."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Configure a sample dataframe for the analyzer's query method
        date_range = pd.date_range(start="2023-01-01", periods=10, freq="5min")
        df = pd.DataFrame(
            {
                "CPU[0].usage": [10.0, 15.0, 20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0],
                "Memory.used": [1024, 1124, 1224, 1324, 1424, 1524, 1624, 1724, 1824, 1924],
            },
            index=date_range,
        )
        mock_analyzer.query.return_value = df

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_resource_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            with patch(
                "app.api.dependencies.validate_timerange", new=AsyncMock(return_value=(datetime(2023, 1, 1), datetime(2023, 1, 1, 1)))
            ):
                with patch("app.api.dependencies.parse_columns_param", new=AsyncMock(return_value=None)):
                    response = client.get("/resource?start=2023-01-01T00:00:00&end=2023-01-01T01:00:00&limit=10&offset=0")

                    assert response.status_code == 200
                    data = response.json()

                    # Verify structure
                    assert "records" in data
                    assert "total" in data
                    assert "limit" in data
                    assert "offset" in data
                    assert "available_columns" in data

                    # Verify data
                    assert isinstance(data["total"], int)
                    assert isinstance(data["records"], list)
                    assert isinstance(data["available_columns"], list)

    @pytest.mark.asyncio
    async def test_query_resource_specific_columns(self):
        """Test querying resource logs with specific columns."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Setup column data
        def mock_get_resource_column(column, start=None, end=None):
            return {
                "timestamps": ["2023-01-01T00:00:00", "2023-01-01T00:05:00"],
                "values": [10.0, 15.0] if column == "CPU[0].usage" else [1024, 1124],
            }

        mock_analyzer.get_resource_column.side_effect = mock_get_resource_column

        # Create a test column list
        columns = ["CPU[0].usage", "Memory.used"]

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_resource_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            with patch(
                "app.api.dependencies.validate_timerange", new=AsyncMock(return_value=(datetime(2023, 1, 1), datetime(2023, 1, 1, 1)))
            ):
                with patch("app.api.dependencies.parse_columns_param", new=AsyncMock(return_value=columns)):
                    response = client.get(
                        "/resource?start=2023-01-01T00:00:00&end=2023-01-01T01:00:00&columns=CPU[0].usage,Memory.used&limit=10&offset=0"
                    )

                    assert response.status_code == 200
                    data = response.json()

                    # Verify data structure
                    assert "records" in data
                    assert "available_columns" in data

                    # Some records may be returned (could be empty if no data matches)
                    assert isinstance(data["records"], list)
                    assert isinstance(data["available_columns"], list)

    @pytest.mark.asyncio
    async def test_resource_columns(self):
        """Test getting available resource columns."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Setup column stats
        mock_analyzer.get_column_stats.return_value = {
            "CPU[0].usage": {"count": 10, "min": 10.0, "max": 55.0, "mean": 32.5},
            "Memory.used": {"count": 10, "min": 1024, "max": 1924, "mean": 1474},
        }

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_resource_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            response = client.get("/resource/columns")

            assert response.status_code == 200
            data = response.json()

            # Verify structure and data
            assert "columns" in data
            assert "column_stats" in data
            assert isinstance(data["columns"], list)
            assert isinstance(data["column_stats"], dict)

    @pytest.mark.asyncio
    async def test_get_resource_column(self):
        """Test getting a specific resource column."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Use a hardcoded column name to avoid dependency on columns response
        test_column = "CPU[0].usage"

        # Setup column data
        mock_analyzer.get_resource_column.return_value = {
            "timestamps": ["2023-01-01T00:00:00", "2023-01-01T00:05:00"],
            "values": [10.0, 15.0],
        }

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_resource_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            with patch("app.api.dependencies.normalize_timestamp", new=AsyncMock(side_effect=lambda x: x)):
                response = client.get(f"/resource/column?name={test_column}&start=2023-01-01T00:00:00&end=2023-01-01T01:00:00")

                assert response.status_code == 200
                data = response.json()

                # Verify structure
                assert "column" in data
                assert "data" in data
                assert data["column"] == test_column

                # Verify data
                assert "timestamps" in data["data"]
                assert "values" in data["data"]
                assert isinstance(data["data"]["timestamps"], list)
                assert isinstance(data["data"]["values"], list)
                assert len(data["data"]["timestamps"]) == len(data["data"]["values"])

    @pytest.mark.asyncio
    async def test_query_resource_empty_result(self):
        """Test querying resource logs with a time range that has no data."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Mock the analyzer to return empty DataFrame for the future date range
        mock_analyzer.query.return_value = pd.DataFrame()

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_resource_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            # Override validate_timerange to handle future dates
            future_start = datetime(2050, 1, 1)
            future_end = datetime(2050, 1, 1, 1)
            with patch("app.api.dependencies.validate_timerange", new=AsyncMock(return_value=(future_start, future_end))):
                with patch("app.api.dependencies.parse_columns_param", new=AsyncMock(return_value=None)):
                    response = client.get("/resource?start=2050-01-01T00:00:00&end=2050-01-01T01:00:00&limit=10&offset=0")

                    assert response.status_code == 200
                    data = response.json()

                    # Verify empty results
                    assert data["total"] == 0
                    assert len(data["records"]) == 0
                    assert isinstance(data["available_columns"], list)
