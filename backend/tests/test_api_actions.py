import json
import logging
import os
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import numpy as np
import pandas as pd
import pytest
from fastapi.testclient import TestClient

# Set up logging for tests
logger = logging.getLogger(__name__)

from app.api.dependencies import get_timeline_analyzer
from app.api.routes.analysis import get_actions
from app.main import app

from log_parser.analysis.timeline_analyzer import TimelineAnalyzer
from log_parser.models import StructuredLogEntry
from log_parser.parsers.structured_log_parser import StructuredLogParser

client = TestClient(app)


@pytest.fixture
def sample_logs_path():
    """Return the path to the sample log files directory."""
    return Path(__file__).parent.parent / "samples"


@pytest.fixture
def minknow_log_entries(sample_logs_path):
    """Load a sample of MinKNOW log entries from the sample files with the new directory structure."""
    from app.parsers.structured_log_parser import StructuredLogParser

    # Use the reorganized directory structure
    minknow_dir = sample_logs_path / "minknow" / "positionA"

    # Load sample log files
    log_files = [minknow_dir / "control_server_log-0.txt", minknow_dir / "control_server_log-1.txt"]

    # Create parser and parse log files
    parser = StructuredLogParser()
    all_entries = []

    for log_file in log_files:
        if log_file.exists():
            # Use proper position name as folder_name
            entries = parser.parse_file(str(log_file), file_name=log_file.name, folder_name="positionA")
            all_entries.extend(entries)

    return all_entries


@pytest.fixture
def real_timeline_analyzer(sample_logs_path):
    """Create a real TimelineAnalyzer using the sample log files."""
    # Parse the sample log files
    minknow_dir = sample_logs_path / "minknow" / "positionA"
    parser = StructuredLogParser()
    all_entries = []

    # Process all control server logs to maximize chance of finding timeline events
    for log_file in minknow_dir.glob("control_server_log-*.txt"):
        if log_file.exists():
            entries = parser.parse_file(str(log_file), file_name=log_file.name, folder_name="positionA")
            all_entries.extend(entries)

    # Create a timeline analyzer with these entries
    analyzer = TimelineAnalyzer(all_entries)

    # If no timeline events were found, create synthetic test timeline events
    if not any(timeline_events for timeline_events in analyzer.position_timeline_events.values()):
        # Create a synthetic timeline event for testing
        from datetime import datetime, timedelta

        from log_parser.models import StructuredLogEntry

        # Create synthetic start and end events
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=5)

        # Create synthetic log entries
        start_entry = StructuredLogEntry(
            timestamp=start_time,
            log_event="protocol_start",
            folder_name="positionA",
            message="Protocol started",
            keys={"protocol_id": "test_protocol_123"},
        )

        end_entry = StructuredLogEntry(
            timestamp=end_time,
            log_event="protocol_end",
            folder_name="positionA",
            message="Protocol ended",
            keys={"protocol_id": "test_protocol_123"},
        )

        # Add to analyzer's data
        analyzer.log_entries = [start_entry, end_entry]
        analyzer.identify_timeline_events()

    return analyzer


@pytest.fixture
def mock_timeline_analyzer():
    """Create a mock TimelineAnalyzer with predefined timeline events for testing."""
    mock_analyzer = MagicMock(spec=TimelineAnalyzer)
    mock_analyzer.analyzer_type = "timeline"

    # Mock timeline events
    from datetime import datetime, timedelta

    position_id = "positionA"
    base_time = datetime(2025, 2, 7, 19, 35, 42)

    # Create mock timeline events
    mock_timeline_events = [
        {
            "id": str(uuid.uuid4()),
            "name": "protocol",
            "start_time": base_time,
            "end_time": base_time + timedelta(minutes=30),
            "duration": 1800.0,
            "position_id": position_id,
            "metadata": {"run_id": "test_run_123", "flowcell_id": "test_flowcell"},
            "has_children": True,
            "children": [str(uuid.uuid4())],
        },
        {
            "id": str(uuid.uuid4()),
            "name": "data_acquisition",
            "start_time": base_time + timedelta(minutes=5),
            "end_time": base_time + timedelta(minutes=25),
            "duration": 1200.0,
            "position_id": position_id,
            "metadata": {"acquisition_run_id": "test_acq_123"},
            "has_children": False,
            "children": [],
        },
    ]

    # Mock the query_timeline_events method
    mock_analyzer.query_timeline_events.return_value = mock_timeline_events

    # Mock the get_available_timeline_events method
    mock_analyzer.get_available_timeline_events.return_value = [
        {"name": "protocol", "start_event": "protocol_start", "end_event": "protocol_end"},
        {"name": "data_acquisition", "start_event": "data_acquisition_start", "end_event": "data_acquisition_end"},
    ]

    return mock_analyzer


@pytest.fixture
def mock_timeline_analyzer_with_nan(mock_timeline_analyzer):
    """Create a mock TimelineAnalyzer with NaN values in metadata for testing serialization issues."""
    # Get a deep copy of the mock
    mock_analyzer = mock_timeline_analyzer

    # Add NaN values to the mock query_timeline_events result
    original_side_effect = mock_analyzer.query_timeline_events.side_effect

    def modified_query_timeline_events(*args, **kwargs):
        timeline_events = original_side_effect(*args, **kwargs)

        # Add NaN values to metadata
        for timeline_event in timeline_events:
            timeline_event["metadata"]["nan_value"] = float("nan")
            timeline_event["metadata"]["inf_value"] = float("inf")
            timeline_event["metadata"]["neg_inf_value"] = float("-inf")
            timeline_event["metadata"]["numpy_nan"] = np.nan

        return timeline_events

    mock_analyzer.query_timeline_events.side_effect = modified_query_timeline_events

    # Also modify the get_timeline_event_timeline method
    def modified_get_timeline_event_timeline(*args, **kwargs):
        timeline_events = [
            {
                "id": "test_id",
                "name": "test_timeline_event",
                "start": "2025-02-07T19:35:42",
                "end": "2025-02-07T20:05:42",
                "level": 0,
                "parent_id": None,
                "metadata": {
                    "nan_value": float("nan"),
                    "inf_value": float("inf"),
                    "neg_inf_value": float("-inf"),
                    "numpy_nan": np.nan,
                },
            }
        ]
        return timeline_events

    mock_analyzer.get_timeline_event_timeline.side_effect = modified_get_timeline_event_timeline

    # Also modify the get_timeline_event_duration_stats method
    def modified_get_timeline_event_duration_stats(*args, **kwargs):
        return {
            "count": 1,
            "mean_duration": 1800.0,
            "min_duration": 1800.0,
            "max_duration": 1800.0,
            "std_duration": 0.0,
            "grouped_stats": {
                "test_group": {
                    "count": 1,
                    "mean_duration": 1800.0,
                    "min_duration": 1800.0,
                    "max_duration": 1800.0,
                    "std_duration": 0.0,
                }
            },
        }

    mock_analyzer.get_timeline_event_duration_stats.side_effect = modified_get_timeline_event_duration_stats

    return mock_analyzer


@pytest.fixture
def real_timeline_analyzer_with_nan(real_timeline_analyzer):
    """Create a real TimelineAnalyzer with NaN values in metadata for testing serialization issues."""
    # Add NaN values to the timeline events
    for timeline_events in real_timeline_analyzer.position_timeline_events.values():
        for timeline_event in timeline_events:
            timeline_event.metadata["nan_value"] = float("nan")
            timeline_event.metadata["inf_value"] = float("inf")
            timeline_event.metadata["neg_inf_value"] = float("-inf")
            timeline_event.metadata["numpy_nan"] = np.nan

            # Add to children metadata
            for child in timeline_event.children:
                child.metadata["nan_value"] = float("nan")
                child.metadata["inf_value"] = float("inf")
                child.metadata["neg_inf_value"] = float("-inf")
                child.metadata["numpy_nan"] = np.nan

    return real_timeline_analyzer


class TestTimelineAPI:
    """Test cases for the timeline API endpoints."""

    @pytest.mark.integration
    def test_get_timeline_events_from_samples(self, real_timeline_analyzer):
        """Test getting timeline events from sample log files."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=real_timeline_analyzer):
            response = client.get("/timeline")
            assert response.status_code == 200

            data = response.json()
            assert "timeline_events" in data
            timeline_events = data["timeline_events"]

            # Check that we got some timeline events
            assert len(timeline_events) > 0

            # Check structure of timeline events
            for timeline_event in timeline_events:
                assert "id" in timeline_event
                assert "name" in timeline_event
                assert "start_time" in timeline_event
                assert "end_time" in timeline_event
                assert "duration" in timeline_event
                assert "position_id" in timeline_event
                assert "metadata" in timeline_event
                assert "has_children" in timeline_event

                # Check that duration is calculated correctly
                start_time = datetime.fromisoformat(timeline_event["start_time"])
                end_time = datetime.fromisoformat(timeline_event["end_time"])
                expected_duration = (end_time - start_time).total_seconds()
                assert timeline_event["duration"] == expected_duration

    @pytest.mark.integration
    def test_get_timeline_events_with_filters(self, mock_timeline_analyzer):
        """Test getting timeline events with various filters."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=mock_timeline_analyzer):
            # Test with position filter
            response = client.get("/timeline?position_id=positionA")
            assert response.status_code == 200

            # Test with timeline event type filter
            response = client.get("/timeline?timeline_event_type=protocol")
            assert response.status_code == 200

            # Test with time range filter
            response = client.get("/timeline?start=2025-02-07T19:00:00&end=2025-02-07T20:00:00")
            assert response.status_code == 200

            # Test with multiple filters
            response = client.get("/timeline?position_id=positionA&timeline_event_type=protocol&include_children=false")
            assert response.status_code == 200

    @pytest.mark.integration
    def test_timeline_events_with_nan_values(self, real_timeline_analyzer_with_nan):
        """Test that timeline events with NaN values are handled correctly."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=real_timeline_analyzer_with_nan):
            response = client.get("/timeline")
            assert response.status_code == 200

            data = response.json()
            timeline_events = data["timeline_events"]

            # Check that NaN values are converted to None
            for timeline_event in timeline_events:
                metadata = timeline_event["metadata"]
                assert metadata.get("nan_value") is None
                assert metadata.get("inf_value") is None
                assert metadata.get("neg_inf_value") is None
                assert metadata.get("numpy_nan") is None

    @pytest.mark.integration
    def test_timeline_events_duration_stats_with_nan_values(self, real_timeline_analyzer_with_nan):
        """Test that timeline event duration stats with NaN values are handled correctly."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=real_timeline_analyzer_with_nan):
            # Mock the get_timeline_event_duration_stats method to return NaN values
            def stats_with_nan(*args, **kwargs):
                # Get the original stats
                original_stats = real_timeline_analyzer_with_nan.get_timeline_event_duration_stats(*args, **kwargs)
                # Add NaN values
                original_stats["nan_value"] = float("nan")
                original_stats["inf_value"] = float("inf")
                original_stats["neg_inf_value"] = float("-inf")
                original_stats["numpy_nan"] = np.nan
                return original_stats

            real_timeline_analyzer_with_nan.get_timeline_event_duration_stats = stats_with_nan

            # This would normally be a separate endpoint, but for now we'll just test the method
            stats = real_timeline_analyzer_with_nan.get_timeline_event_duration_stats("protocol")
            assert "nan_value" in stats
            assert "inf_value" in stats
            assert "neg_inf_value" in stats
            assert "numpy_nan" in stats

    @pytest.mark.integration
    def test_timeline_events_timeline_with_nan_values(self, real_timeline_analyzer_with_nan):
        """Test that timeline event timeline with NaN values are handled correctly."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=real_timeline_analyzer_with_nan):
            # Mock the get_timeline_event_timeline method to return NaN values
            def timeline_with_nan(*args, **kwargs):
                # Get the original timeline events
                original_timeline_events = real_timeline_analyzer_with_nan.get_timeline_event_timeline(*args, **kwargs)
                # Add NaN values to the first timeline event
                if original_timeline_events:
                    original_timeline_events[0]["metadata"]["nan_value"] = float("nan")
                    original_timeline_events[0]["metadata"]["inf_value"] = float("inf")
                    original_timeline_events[0]["metadata"]["neg_inf_value"] = float("-inf")
                    original_timeline_events[0]["metadata"]["numpy_nan"] = np.nan
                return original_timeline_events

            real_timeline_analyzer_with_nan.get_timeline_event_timeline = timeline_with_nan

            # This would normally be a separate endpoint, but for now we'll just test the method
            timeline_events = real_timeline_analyzer_with_nan.get_timeline_event_timeline()
            if timeline_events:
                metadata = timeline_events[0]["metadata"]
                assert "nan_value" in metadata
                assert "inf_value" in metadata
                assert "neg_inf_value" in metadata
                assert "numpy_nan" in metadata

    @pytest.mark.integration
    def test_empty_timeline_events_query(self, real_timeline_analyzer):
        """Test querying timeline events with filters that return no results."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=real_timeline_analyzer):
            # Test with a timeline event type that doesn't exist
            response = client.get("/timeline?timeline_event_type=nonexistent")
            assert response.status_code == 200

            data = response.json()
            assert "timeline_events" in data
            timeline_events = data["timeline_events"]
            assert len(timeline_events) == 0

            # Test with a position that doesn't exist
            response = client.get("/timeline?position_id=nonexistent")
            assert response.status_code == 200

            data = response.json()
            assert "timeline_events" in data
            timeline_events = data["timeline_events"]
            assert len(timeline_events) == 0

            # Test with a time range that doesn't contain any timeline events
            response = client.get("/timeline?start=2020-01-01T00:00:00&end=2020-01-01T01:00:00")
            assert response.status_code == 200

            data = response.json()
            assert "timeline_events" in data
            timeline_events = data["timeline_events"]
            assert len(timeline_events) == 0

    @pytest.mark.integration
    def test_timeline_config_endpoint(self, real_timeline_analyzer):
        """Test the timeline configuration endpoint."""
        with patch("app.api.dependencies.get_timeline_analyzer", return_value=real_timeline_analyzer):
            response = client.get("/timeline/config")
            assert response.status_code == 200

            data = response.json()
            assert "config" in data
            config = data["config"]
            assert "timeline_event_types" in config

            # Check that we have some timeline event types defined
            timeline_event_types = config["timeline_event_types"]
            assert len(timeline_event_types) > 0

            # Check structure of timeline event types
            for timeline_event_type_name, timeline_event_type_config in timeline_event_types.items():
                assert "name" in timeline_event_type_config
                assert "start_event" in timeline_event_type_config
                assert "end_event" in timeline_event_type_config
