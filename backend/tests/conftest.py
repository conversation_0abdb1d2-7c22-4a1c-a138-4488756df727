"""Test configuration and fixtures for backend API tests."""

import os
from datetime import datetime
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pandas as pd
import pytest

from log_parser.analysis.cross_analyzer import CrossAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.parsers.parser_factory import ParserFactory


# Mock app.api.dependencies global variables to avoid actual parsing during tests
@pytest.fixture(autouse=True)
def mock_globals():
    """Mock global dictionaries in app.api.dependencies."""
    with patch("app.api.dependencies.parsed_logs", {"resource": [], "minknow": []}):
        with patch("app.api.dependencies.analyzers", {}):
            yield


@pytest.fixture(scope="session")
def sample_dir():
    """Get the path to the samples directory."""
    # Get the base directory of the project
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    samples_dir = os.path.join(base_dir, "samples")
    return samples_dir


@pytest.fixture
def real_resource_log_entries(sample_dir):
    """Parse real resource log entries from sample files with updated directory structure."""
    # Create a resource parser
    resource_parser = ParserFactory.create_parser("resource")

    # Parse the sample resource log file with updated path
    resource_file = os.path.join(sample_dir, "ont-resource-logger", "resource-system.log")
    if os.path.exists(resource_file):
        entries = resource_parser.parse_file(resource_file, file_name="resource-system.log", folder_name="ont-resource-logger")
        return entries
    return []


@pytest.fixture
def real_minknow_log_entries(sample_dir):
    """Parse real MinKNOW log entries from sample files with updated directory structure."""
    # Create a MinKNOW parser
    structured_log_parser = ParserFactory.create_parser("minknow")

    # Parse all MinKNOW sample log files
    entries = []

    # Update to use new directory structure
    minknow_position_dir = os.path.join(sample_dir, "minknow", "positionA")

    if os.path.exists(minknow_position_dir):
        for filename in os.listdir(minknow_position_dir):
            if filename.startswith("control_server_log-"):
                file_path = os.path.join(minknow_position_dir, filename)
                file_entries = structured_log_parser.parse_file(
                    file_path, file_name=filename, folder_name="positionA"  # Use the position folder name
                )
                entries.extend(file_entries)

    return entries


@pytest.fixture
def real_resource_analyzer(real_resource_log_entries):
    """Create a ResourceAnalyzer with real sample data."""
    return ResourceAnalyzer(real_resource_log_entries)


@pytest.fixture
def real_minknow_analyzer(real_minknow_log_entries):
    """Create a MinknowAnalyzer with real sample data."""
    return StructuredLogAnalyzer(real_minknow_log_entries)


@pytest.fixture
def real_cross_analyzer(real_resource_analyzer, real_minknow_analyzer):
    """Create a CrossAnalyzer with real sample data."""
    return CrossAnalyzer(resource_analyzer=real_resource_analyzer, minknow_analyzer=real_minknow_analyzer)


@pytest.fixture
async def mock_get_resource_analyzer(real_resource_analyzer):
    """Mock the get_resource_analyzer dependency."""

    async def _get_resource_analyzer():
        return real_resource_analyzer

    with patch("app.api.routes.resource.get_resource_analyzer", new=AsyncMock(side_effect=_get_resource_analyzer)):
        yield real_resource_analyzer


@pytest.fixture
async def mock_get_minknow_analyzer(real_minknow_analyzer):
    """Mock the get_minknow_analyzer dependency."""

    async def _get_minknow_analyzer():
        return real_minknow_analyzer

    with patch("app.api.routes.minknow.get_minknow_analyzer", new=AsyncMock(side_effect=_get_minknow_analyzer)):
        yield real_minknow_analyzer


@pytest.fixture
async def mock_get_cross_analyzer(real_cross_analyzer):
    """Mock the get_cross_analyzer dependency."""

    async def _get_cross_analyzer():
        return real_cross_analyzer

    with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(side_effect=_get_cross_analyzer)):
        yield real_cross_analyzer
