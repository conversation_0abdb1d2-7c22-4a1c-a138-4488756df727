import json
import os
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient

# Set test environment to avoid actual analyzer creation
os.environ["TESTING"] = "1"

from app.main import app

client = TestClient(app)


@pytest.fixture
def mock_minknow_analyzer():
    """Create a mock MinKnow analyzer with predefined data."""
    mock_analyzer = MagicMock()

    # Sample events data
    events = [
        {"timestamp": datetime(2023, 1, 1, 10, 0, 0), "level": "INFO", "source": "mk_manager", "message": "MinKnow service started"},
        {"timestamp": datetime(2023, 1, 1, 10, 5, 0), "level": "WARNING", "source": "mk_manager", "message": "Low disk space detected"},
        {
            "timestamp": datetime(2023, 1, 1, 10, 10, 0),
            "level": "ERROR",
            "source": "basecall_manager",
            "message": "Failed to initialize GPU",
        },
        {
            "timestamp": datetime(2023, 1, 1, 10, 15, 0),
            "level": "INFO",
            "source": "basecall_manager",
            "message": "Basecalling pipeline initialized",
        },
        {
            "timestamp": datetime(2023, 1, 1, 10, 20, 0),
            "level": "DEBUG",
            "source": "mk_manager",
            "message": "Configuration loaded from default path",
        },
    ]

    # Setup the mock methods
    def mock_get_events(start=None, end=None, level=None, source=None, limit=None):
        filtered_events = events.copy()

        # Apply filters
        if start:
            filtered_events = [e for e in filtered_events if e["timestamp"] >= start]
        if end:
            filtered_events = [e for e in filtered_events if e["timestamp"] <= end]
        if level:
            filtered_events = [e for e in filtered_events if e["level"] == level]
        if source:
            filtered_events = [e for e in filtered_events if e["source"] == source]

        # Apply limit
        if limit and len(filtered_events) > limit:
            filtered_events = filtered_events[:limit]

        return filtered_events

    mock_analyzer.get_events.side_effect = mock_get_events

    # Setup count_events_by_level method
    def mock_count_events_by_level(start=None, end=None):
        filtered_events = mock_get_events(start, end)
        counts = {}
        for event in filtered_events:
            level = event["level"]
            counts[level] = counts.get(level, 0) + 1
        return counts

    mock_analyzer.count_events_by_level.side_effect = mock_count_events_by_level

    # Setup get_event_sources method
    def mock_get_event_sources():
        sources = set()
        for event in events:
            sources.add(event["source"])
        return list(sources)

    mock_analyzer.get_event_sources.side_effect = mock_get_event_sources

    # Setup count_events_by_source method
    def mock_count_events_by_source(start=None, end=None):
        filtered_events = mock_get_events(start, end)
        counts = {}
        for event in filtered_events:
            source = event["source"]
            counts[source] = counts.get(source, 0) + 1
        return counts

    mock_analyzer.count_events_by_source.side_effect = mock_count_events_by_source

    return mock_analyzer


class TestMinknowAPI:

    @pytest.mark.asyncio
    async def test_query_minknow(self):
        """Test querying MinKnow logs."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Configure the mock for this test
        entries = [
            {
                "timestamp": datetime(2023, 1, 1, 10, 0, 0),
                "level": "INFO",
                "process_name": "mk_manager",
                "message": "MinKnow service started",
                "log_event": "startup",
            },
            {
                "timestamp": datetime(2023, 1, 1, 10, 5, 0),
                "level": "WARNING",
                "process_name": "mk_manager",
                "message": "Low disk space detected",
                "log_event": "disk_warning",
            },
        ]

        # Set up the mock to return our test entries
        mock_analyzer._df_to_dict_list.return_value = entries

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_minknow_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            with patch("app.api.dependencies.normalize_timestamp", new=AsyncMock(side_effect=lambda x: x)):
                with patch("app.api.dependencies.parse_positions_param", new=AsyncMock(return_value=None)):
                    response = client.get("/minknow?start=2023-01-01T00:00:00&end=2023-01-01T23:59:59")

                    assert response.status_code == 200
                    data = response.json()

                    # Verify structure
                    assert "entries" in data
                    assert "total" in data
                    assert "limit" in data
                    assert "offset" in data

                    # Verify data
                    assert isinstance(data["total"], int)
                    assert isinstance(data["entries"], list)
                    assert data["total"] == len(entries)

    @pytest.mark.asyncio
    async def test_query_minknow_with_filters(self):
        """Test querying MinKnow logs with filters."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Configure the mock for filtered results
        filtered_entries = [
            {
                "timestamp": datetime(2023, 1, 1, 10, 10, 0),
                "level": "ERROR",
                "process_name": "basecall_manager",
                "message": "Failed to initialize GPU",
                "log_event": "error",
            }
        ]

        # Set up the mock to return our filtered entries
        mock_analyzer._df_to_dict_list.return_value = filtered_entries

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_minknow_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            with patch("app.api.dependencies.normalize_timestamp", new=AsyncMock(side_effect=lambda x: x)):
                with patch("app.api.dependencies.parse_positions_param", new=AsyncMock(return_value=None)):
                    response = client.get("/minknow?start=2023-01-01T00:00:00&end=2023-01-01T23:59:59&log_level=ERROR")

                    assert response.status_code == 200
                    data = response.json()

                    # Verify filtered results
                    assert len(data["entries"]) == 1
                    assert data["entries"][0]["level"] == "ERROR"

    @pytest.mark.asyncio
    async def test_get_aggregated_logs(self):
        """Test getting aggregated log data for heatmap visualization."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Configure the mock to return timeline data
        timeline_data = {
            "timestamps": ["2023-01-01T10:00:00", "2023-01-01T10:30:00", "2023-01-01T11:00:00"],
            "events": {"info": [5, 3, 7], "warning": [1, 2, 0], "error": [0, 1, 2]},
        }

        # Set up positions
        positions = ["pos1", "pos2"]
        mock_analyzer.get_positions.return_value = positions
        mock_analyzer.get_event_timeline.return_value = timeline_data

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_minknow_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            with patch("app.api.dependencies.normalize_timestamp", new=AsyncMock(side_effect=lambda x: x)):
                with patch("app.api.dependencies.parse_positions_param", new=AsyncMock(return_value=None)):
                    response = client.get("/minknow/aggregated?start=2023-01-01T00:00:00&end=2023-01-01T23:59:59")

                    assert response.status_code == 200
                    data = response.json()

                    # Verify structure
                    assert "data" in data
                    assert "maxCounts" in data
                    assert "bucketSize" in data

                    # Verify positions data
                    for position in positions:
                        assert position in data["data"]

    @pytest.mark.asyncio
    async def test_get_minknow_positions(self):
        """Test getting available positions from Minknow logs."""
        # Create a fresh mock
        mock_analyzer = MagicMock()

        # Configure the mock to return positions
        positions = ["pos1", "pos2", "pos3"]
        mock_analyzer.get_positions.return_value = positions

        # Mock dependencies needed by the endpoint
        with patch("app.api.dependencies.get_minknow_analyzer", new=AsyncMock(return_value=mock_analyzer)):
            response = client.get("/minknow/positions")

            assert response.status_code == 200
            data = response.json()

            # Verify positions data
            assert "positions" in data
            assert len(data["positions"]) == len(positions)
            for pos in positions:
                assert pos in data["positions"]
