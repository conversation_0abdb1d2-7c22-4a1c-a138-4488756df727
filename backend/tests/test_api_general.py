import os
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

import pytest
from app.main import app
from fastapi.testclient import TestClient

client = TestClient(app)


class TestGeneralEndpoints:
    def test_root_endpoint(self):
        """Test the root endpoint returns the expected message."""
        response = client.get("/")
        assert response.status_code == 200
        assert response.json() == {"message": "Log Visualizer API is running"}

    def test_health_check(self):
        """Test the health check endpoint returns a valid response."""
        response = client.get("/health")
        assert response.status_code == 200
        assert "status" in response.json()

    def test_api_status(self):
        """Test the API status endpoint returns the expected structure."""
        with (
            patch("app.api.routes.general.get_resource_analyzer", new_callable=AsyncMock) as mock_resource,
            patch("app.api.routes.general.get_minknow_analyzer", new_callable=AsyncMock) as mock_minknow,
        ):
            # Set up mocks
            mock_resource.return_value = "mocked_resource_analyzer"
            mock_minknow.return_value = "mocked_minknow_analyzer"

            # Call the endpoint
            response = client.get("/status")

            # Verify response
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ok"
            assert "version" in data
            assert "uptime_seconds" in data
            assert "uptime_human" in data
            assert data["analyzer_ready"] is True

    def test_parse_logs_endpoint_directory_not_found(self):
        """Test parse logs endpoint when directory doesn't exist."""
        # Create a request with non-existent directory
        non_existent_dir = "/non_existent_dir"
        payload = {"base_dir": non_existent_dir}

        # Mock os.path.exists to return False
        with patch("os.path.exists", return_value=False):
            response = client.post("/parse", json=payload)

            # Check that we get an error response
            assert response.status_code == 500
            data = response.json()
            assert "detail" in data
            assert "Directory not found" in data["detail"]

    def test_parse_logs_endpoint_success(self):
        """Test parse logs endpoint with a valid directory."""
        # Create a request with a directory that exists
        existing_dir = "/tmp"
        payload = {"base_dir": existing_dir}

        # Mock necessary functions
        with (
            patch("os.path.exists", return_value=True),
            patch("app.api.routes.general.parse_logs", new_callable=AsyncMock) as mock_parse_logs,
        ):
            response = client.post("/parse", json=payload)

            # Check response is accepted
            assert response.status_code == 202
            assert response.json()["success"] is True
            assert "Log parsing started" in response.json()["message"]

            # Verify parse_logs was called with the correct directory
            mock_parse_logs.assert_not_called()  # Not called synchronously

    def test_parse_status_endpoint(self):
        """Test the parse status endpoint."""
        response = client.get("/parse/status")
        assert response.status_code == 200
        assert "status" in response.json()

    def test_test_endpoint(self):
        """Test the simple test endpoint."""
        response = client.get("/test")
        assert response.status_code == 200
        assert response.json() == {"message": "Hello, world!"}
