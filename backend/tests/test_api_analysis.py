from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import numpy as np
import pandas as pd
import pytest
from app.main import app
from fastapi.testclient import TestClient

client = TestClient(app)


@pytest.fixture
def mock_cross_analyzer():
    """Create a mock cross-analysis analyzer with predefined data."""
    mock_analyzer = MagicMock()

    # Sample time range
    start_time = datetime(2023, 1, 1, 10, 0, 0)
    end_time = datetime(2023, 1, 1, 11, 0, 0)

    # Setup common_time_range method
    mock_analyzer.get_common_time_range.return_value = (start_time, end_time)

    # Setup available_columns method
    mock_analyzer.get_available_columns.return_value = {
        "resource": ["CPU[0].usage", "Memory.used", "Disk[0].io"],
        "minknow": ["event_counts", "error_counts"],
    }

    # Setup correlate method
    def mock_correlate(column1, column2, start=None, end=None):
        # Simulate correlation calculation
        if column1 == "CPU[0].usage" and column2 == "Memory.used":
            correlation = 0.85
        elif column1 == "CPU[0].usage" and column2 == "Disk[0].io":
            correlation = 0.65
        else:
            correlation = 0.3  # Default correlation for other combinations

        # Create sample data points
        date_range = pd.date_range(start=start or start_time, end=end or end_time, periods=12)

        if column1 == "CPU[0].usage":
            values1 = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65]
        elif column1 == "Memory.used":
            values1 = [1024, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100]
        else:
            values1 = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60]

        if column2 == "CPU[0].usage":
            values2 = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65]
        elif column2 == "Memory.used":
            values2 = [1024, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100]
        else:
            values2 = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60]

        return {
            "correlation": correlation,
            "p_value": 0.001,
            "data_points": {"timestamps": date_range.tolist(), "values1": values1, "values2": values2},
        }

    mock_analyzer.correlate.side_effect = mock_correlate

    # Setup correlation_matrix method
    def mock_correlation_matrix(columns, start=None, end=None):
        matrix = {
            "CPU[0].usage": {"CPU[0].usage": 1.0, "Memory.used": 0.85, "Disk[0].io": 0.65},
            "Memory.used": {"CPU[0].usage": 0.85, "Memory.used": 1.0, "Disk[0].io": 0.45},
            "Disk[0].io": {"CPU[0].usage": 0.65, "Memory.used": 0.45, "Disk[0].io": 1.0},
        }

        # Filter based on columns
        if columns:
            filtered_matrix = {}
            for col1 in columns:
                if col1 in matrix:
                    filtered_matrix[col1] = {}
                    for col2 in columns:
                        if col2 in matrix[col1]:
                            filtered_matrix[col1][col2] = matrix[col1][col2]
            return filtered_matrix

        return matrix

    mock_analyzer.correlation_matrix.side_effect = mock_correlation_matrix

    # Setup get_anomalies method
    def mock_get_anomalies(column, threshold=2.0, start=None, end=None):
        date_range = pd.date_range(start=start or start_time, end=end or end_time, periods=12)

        if column == "CPU[0].usage":
            values = [10, 15, 20, 25, 30, 35, 90, 45, 50, 55, 60, 65]  # Anomaly at index 6
            anomaly_indices = [6]
        elif column == "Memory.used":
            values = [1024, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 5000, 1900, 2000, 2100]  # Anomaly at index 8
            anomaly_indices = [8]
        else:
            values = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60]
            anomaly_indices = []

        return {
            "anomalies": [{"timestamp": date_range[i], "value": values[i]} for i in anomaly_indices],
            "data_points": {
                "timestamps": date_range.tolist(),
                "values": values,
                "z_scores": [1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 3.5, 1.7, 3.2, 1.9, 2.0, 2.1],
            },
        }

    mock_analyzer.get_anomalies.side_effect = mock_get_anomalies

    # Setup event_correlation method
    def mock_event_correlation(resource_column, event_level=None, window_minutes=5, start=None, end=None):
        date_range = pd.date_range(start=start or start_time, end=end or end_time, periods=12)

        # Sample resource values
        if resource_column == "CPU[0].usage":
            values = [10, 15, 20, 25, 30, 35, 90, 45, 50, 55, 60, 65]
        elif resource_column == "Memory.used":
            values = [1024, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 5000, 1900, 2000, 2100]
        else:
            values = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60]

        # Sample events near resource spikes
        events = [
            {
                "timestamp": date_range[6] - pd.Timedelta(minutes=1),
                "level": "ERROR",
                "source": "basecall_manager",
                "message": "Failed to initialize GPU",
            },
            {
                "timestamp": date_range[8] - pd.Timedelta(minutes=2),
                "level": "WARNING",
                "source": "mk_manager",
                "message": "Low disk space detected",
            },
        ]

        # Filter by level if needed
        if event_level:
            events = [e for e in events if e["level"] == event_level]

        return {
            "resource_column": resource_column,
            "window_minutes": window_minutes,
            "event_count": len(events),
            "events": events,
            "data_points": {"timestamps": date_range.tolist(), "values": values},
        }

    mock_analyzer.event_correlation.side_effect = mock_event_correlation

    return mock_analyzer


class TestAnalysisAPI:

    @pytest.mark.asyncio
    async def test_get_common_time_range(self, mock_cross_analyzer):
        """Test getting common time range across all log sources."""
        with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(return_value=mock_cross_analyzer)):
            response = client.get("/analysis/common_time_range")

            assert response.status_code == 200
            data = response.json()

            # Verify structure
            assert "start" in data
            assert "end" in data

            # Verify format (ISO format string)
            start = datetime.fromisoformat(data["start"].replace("Z", "+00:00"))
            end = datetime.fromisoformat(data["end"].replace("Z", "+00:00"))
            assert start < end

    @pytest.mark.asyncio
    async def test_get_available_columns(self, mock_cross_analyzer):
        """Test getting available columns for cross-analysis."""
        with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(return_value=mock_cross_analyzer)):
            response = client.get("/analysis/available_columns")

            assert response.status_code == 200
            data = response.json()

            # Verify structure
            assert "resource" in data
            assert "minknow" in data

            # Check for specific columns
            assert "CPU[0].usage" in data["resource"]
            assert "Memory.used" in data["resource"]

    @pytest.mark.asyncio
    async def test_correlate_columns(self, mock_cross_analyzer):
        """Test correlating two columns."""
        with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(return_value=mock_cross_analyzer)):
            response = client.get(
                "/analysis/correlate?column1=CPU[0].usage&column2=Memory.used&start=2023-01-01T10:00:00&end=2023-01-01T11:00:00"
            )

            assert response.status_code == 200
            data = response.json()

            # Verify structure
            assert "correlation" in data
            assert "p_value" in data
            assert "data_points" in data

            # Verify data points
            assert "timestamps" in data["data_points"]
            assert "values1" in data["data_points"]
            assert "values2" in data["data_points"]

            # Verify correlation value for these specific columns
            assert data["correlation"] == 0.85

    @pytest.mark.asyncio
    async def test_correlation_matrix(self, mock_cross_analyzer):
        """Test getting correlation matrix for multiple columns."""
        with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(return_value=mock_cross_analyzer)):
            response = client.get(
                "/analysis/correlation_matrix?columns=CPU[0].usage,Memory.used,Disk[0].io&start=2023-01-01T10:00:00&end=2023-01-01T11:00:00"
            )

            assert response.status_code == 200
            data = response.json()

            # Verify structure - should be a nested dict
            assert "CPU[0].usage" in data
            assert "Memory.used" in data
            assert "Disk[0].io" in data

            # Check specific correlation values
            assert data["CPU[0].usage"]["CPU[0].usage"] == 1.0  # Self correlation
            assert data["CPU[0].usage"]["Memory.used"] == 0.85
            assert data["Memory.used"]["Disk[0].io"] == 0.45

    @pytest.mark.asyncio
    async def test_get_anomalies(self, mock_cross_analyzer):
        """Test detecting anomalies in a column."""
        with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(return_value=mock_cross_analyzer)):
            response = client.get("/analysis/anomalies?column=CPU[0].usage&threshold=2.5&start=2023-01-01T10:00:00&end=2023-01-01T11:00:00")

            assert response.status_code == 200
            data = response.json()

            # Verify structure
            assert "anomalies" in data
            assert "data_points" in data

            # Verify anomalies array structure
            if data["anomalies"]:  # If there are anomalies
                assert "timestamp" in data["anomalies"][0]
                assert "value" in data["anomalies"][0]

            # Verify data points
            assert "timestamps" in data["data_points"]
            assert "values" in data["data_points"]
            assert "z_scores" in data["data_points"]

    @pytest.mark.asyncio
    async def test_event_correlation(self, mock_cross_analyzer):
        """Test correlating resource usage with events."""
        with patch("app.api.routes.analysis.get_cross_analyzer", new=AsyncMock(return_value=mock_cross_analyzer)):
            response = client.get(
                "/analysis/event_correlation?resource_column=CPU[0].usage&event_level=ERROR&window_minutes=5&start=2023-01-01T10:00:00&end=2023-01-01T11:00:00"
            )

            assert response.status_code == 200
            data = response.json()

            # Verify structure
            assert "resource_column" in data
            assert "window_minutes" in data
            assert "event_count" in data
            assert "events" in data
            assert "data_points" in data

            # Verify events filtering
            for event in data["events"]:
                assert event["level"] == "ERROR"

            # Verify data points
            assert "timestamps" in data["data_points"]
            assert "values" in data["data_points"]
