# 🚀 FastAPI Backend

A **headless REST service** that exposes log‑parser insights to automation, dashboards, or other microservices. It runs on the same `log_parser` library used by <PERSON><PERSON>.

---

## 🔧 Installation (Dev or Stand‑alone)

```bash
# From monorepo root
python -m venv .venv && source .venv/bin/activate

# Install backend extras + parser in editable mode
pip install -e ".[backend]" -e ./log_parser
```

*Requires Python ≥ 3.10.*

---

## ▶️ Running the Server

```bash
# Default: localhost:8000
uvicorn app.main:app --reload
```

* `--reload` enables hot‑reload during development.
* For production, drop `--reload` or switch to `uvicorn --workers N`.

### Environment Variables

| Variable                  | Required | Default                | Purpose                                              |
| ------------------------- | -------- | ---------------------- | ---------------------------------------------------- |
| `BASE_LOG_FOLDER`         | **Yes**  | `""` (empty)           | Root directory containing device logs to parse      |
| `ENV`                     | No       | `dev`                  | Environment type (`dev`, `test`, `prod`)             |
| `LOG_LEVEL`               | No       | `INFO`                 | Application log level (`DEBUG`, `INFO`, `WARNING`, `ERROR`) |
| `DEBUG`                   | No       | `false`                | Enable debug mode                                    |
| `DATA_PATH`               | No       | `""` (empty)           | Custom data path override                            |
| `RESOURCE_LOG_FILE`       | No       | `""` (empty)           | Specific resource log file path                      |
| `EVENT_LOG_FILE`          | No       | `""` (empty)           | Specific event log file path                         |
| `SAVE_INTERMEDIATE_DATA`  | No       | `false`                | Save intermediate parsing data for debugging         |
| `INTERMEDIATE_DATA_DIR`   | No       | `intermediate_data`    | Directory for intermediate data files                |

> **⚠️ Important:** `BASE_LOG_FOLDER` must be set for the API to parse and serve log data. Without it, endpoints will return empty results.

*Example usage:*

```bash
# For development
export BASE_LOG_FOLDER=/path/to/your/logs
export LOG_LEVEL=DEBUG
uvicorn app.main:app --reload

# Or run directly
BASE_LOG_FOLDER=/path/to/logs LOG_LEVEL=INFO uvicorn app.main:app
```

---

## 📑 API Documentation

After the server starts, open:

```
http://127.0.0.1:8000/docs  # Swagger UI
http://127.0.0.1:8000/redoc # ReDoc
```

The OpenAPI schema is generated at `/openapi.json`.

> **Tip for contributors:** Avoid hand‑editing endpoint tables. Link back to Swagger in other docs.

---

## 🗺️ Endpoint Overview

| Method | Path                              | Description                            |
| ------ | --------------------------------- | -------------------------------------- |
| `GET`  | `/resource-usage`                 | CPU load over time (pagination params) |
| `GET`  | `/events`                         | Event logs (query `start`, `end`)      |
| `GET`  | `/analysis/log_level_anomalies`   | Log‑level anomaly detection            |
| `GET`  | `/analysis/time_series_anomalies` | Time‑series anomaly detection          |

*Full parameter docs in Swagger.*

---

## 🧪 Running Tests

```bash
# Inside backend/ directory
autopep8 --in-place --recursive app  # optional style fix
pytest
```

Coverage reports are generated if `pytest-cov` is installed.

---

## 🔒 CORS & Security

* CORS is disabled by default; enable via `--cors` flag or env config if you plan to call from browsers outside same origin.
* Auth is currently **out‑of‑scope**; deploy behind your preferred gateway if needed.

---

## 🐳 Docker (Optional)

```bash
docker build -t log-backend .
docker run -p 8000:8000 log-backend
```

The provided `Dockerfile` (if present) uses Python slim base and copies only runtime deps.

---

## 📜 License

MIT – see root `LICENSE`.
