2025-02-03 16:36:52.291264    INFO: starting_up (control)
    hostname: GXB03431
    system: GridION X5 Mk1 (GRD-X5B003) GXB03431 running on ubuntu 20.04 (5.11.0-40-generic)
            Distribution:           24.11.8 (STABLE)
            MinKNOW Core:           6.2.6
            Bream:                  8.2.5
            Protocol configuration: 6.2.12
            <PERSON><PERSON> (client):         7.6.0+ad1db9c5a
            <PERSON><PERSON> (server):         7.6.7+de8939544

2025-02-03 16:36:52.300647 WARNING: partially_raised_file_limits (control_server)
    actual: 1024
    target: 4096
2025-02-03 16:36:52.302730    INFO: auth_guest_mode (rpc)
    value: local_only
2025-02-03 16:36:52.312815    INFO: flow_cell_position_instantiated (mgmt)
    device_id: P2S-02545-A
    hardware_type: P2_SOLO_USB
    os_identifier: 2a1d:0091@002:007
    simulated: false
2025-02-03 16:36:52.319226   ERROR: find_device_failed (mgmt)
    exception: Response from device was too short
2025-02-03 16:36:52.336034    INFO: manager_notified_new_auth_token (util)
    expires_in: 7484771424519368973ns
    token_type: internal
2025-02-03 16:36:52.336061    INFO: manager_notified_new_auth_token (util)
    expires_in: 89994992787742ns
    token_type: local
2025-02-03 16:36:52.364841    INFO: removable_device_disconnected (engine)
2025-02-03 16:36:59.322893    INFO: active_device_set (mgmt)
    identifier: 2a1d:0091@002:007
2025-02-03 16:36:59.491739 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 7176
2025-02-03 16:36:59.492273    INFO: device_discovered (engine)
    device_id: P2S-02545-A
2025-02-03 16:37:01.492749    INFO: device_without_flowcell_discovered (engine)
    device_id: P2S-02545-A
2025-02-03 22:56:49.266851    INFO: manager_notified_okta_public_keys_changed (util)
2025-02-04 16:36:47.329298    INFO: manager_notified_new_auth_token (util)
    expires_in: 89999999796015ns
    token_type: local
2025-02-05 16:36:47.329429    INFO: manager_notified_new_auth_token (util)
    expires_in: 89999999775330ns
    token_type: local
2025-02-05 19:35:07.714157    INFO: successfully_read_flow_cell_data (mgmt)
    attempt: 1
    flow_cell_data: V5{ minor_version = 0, wells_per_channel = 4, temperature_offset = 32767, flowcell_id = [ 50 41 59 38 33 36 38 34 00 00 00 00 00 00 00 00 ], product_code = [ 46 4c 4f 2d 50 52 4f 30 30 34 52 41 00 00 00 00 ] }
2025-02-05 19:35:07.714365    INFO: flowcell_discovered (engine)
    asic_id: 0004A30B010F3867
    asic_id_eeprom: 0004A30B010F3867
    flow_cell_id: PAY83684
2025-02-05 19:35:07.886395    INFO: firmware_component (mgmt)
    component_name: P2 USB-FW
    serial_number:
    version: 2.5.2
2025-02-05 19:35:07.886528    INFO: firmware_component (mgmt)
    component_name: P2 FPGA
    serial_number: 002884c44dd9e85c
    version: 2.4.6
2025-02-05 19:35:39.717822 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 19:35:39.720540    INFO: create_request_for_disabling_OS_standby (util)
2025-02-05 19:35:39.721038    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250205_1935_P2S-02545-A_PAY83684...
2025-02-05 19:35:39.722838    INFO: protocol_started (script)
    flowcell_id: PAY83684
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250205_1935_P2S-02545-A_PAY83684_4bd7f2ff
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250205_1935_P2S-02545-A_PAY83684_4bd7f2ff
    reported_output_reads_dir: /data/.
    run_id: 4bd7f2ff-2d2b-49a9-a205-df7658ed1638
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 19:35:39.723262    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-05 19:35:39.723302    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-05 19:35:54.770896    INFO: protocol_phase_control_started (rpc)
2025-02-05 19:35:54.771612    INFO: protocol_phase_changed (script)
    phase: PHASE_INITIALISING
2025-02-05 19:35:54.771663    INFO: base_device.wait_for_temperature (user_messages)
    target: 31.5
    timeout: 180
2025-02-05 19:38:55.062083    INFO: wait_for_temperature_complete (mgmt)
    elapsed_time: 180.289
    has_secondary_temp_limits: true
    primary_temperature_begin: 20.2498
    primary_temperature_end: 29.8469
    primary_temperature_tolerance: 0.1
    result: CancelledWaitingForTemperature
    secondary_temperature_begin: 30.1929
    secondary_temperature_end: 150
    target_primary_temperature: 31.5
    unstable_time: ************-02-05 19:38:55.066838    INFO: base_device.warn_to_reach_temperature (user_messages)
    timeout: 180
2025-02-05 19:38:55.255058    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-05 19:38:55.677764    INFO: data_acquisition_starting (engine)
    acquisition_run_id: 73405cbe2afe48fd420ba4dec9192e4d7ac0e045
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-05 19:38:55.845109    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-05 19:38:55.879603    INFO: start_processing_unblocks_called (mgmt)
2025-02-05 19:38:56.079689    INFO: data_acquisition_started (engine)
    acquisition_run_id: 73405cbe2afe48fd420ba4dec9192e4d7ac0e045
2025-02-05 19:38:59.907084 WARNING: p2_acquisition_finished_with_errors (device)
    asic_frame_counter_jumps_between_transfers: 0
    asic_frame_counter_jumps_within_transfer: 0
    failed_transfers: 16
    invalid_last_command_values: 0
    pb_corrupted_frames: [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]
    received_frames: 0
    sent_frame_counter_jumps_between_transfers: 0
    sent_frame_counter_jumps_within_transfer: 0
2025-02-05 19:38:59.907252    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 65535mA - 0mA (last: 0mA)
    asic_2_5v: 65535mA - 0mA (last: 0mA)
    ext_ntc_temp: -55°C - 150°C (last: 150°C)
    fpga_temp: 65535°C - 0°C (last: 0°C)
    internal_temp: 65535°C - 0°C (last: 0°C)
2025-02-05 19:38:59.907267    INFO: p2_acquisition_timing_stats (device)
    next_block: 9223372036854775807ns - -9223372036854775808ns
    notify_command: 9223372036854775807ns - -9223372036854775808ns
    submit_transfer: 5314ns - 31519ns (blamed for 15 delays)
    transfer_handled_late_count: 16
    transfer_processing: 13ns - 19489ns
    transpose: 9223372036854775807ns - -9223372036854775808ns
    update_saturation: 9223372036854775807ns - -9223372036854775808ns
    wait_for_transfer: 54ns - 2294ns
2025-02-05 19:38:59.930717   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:38:59.968734   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:38:59.989258    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e4b26f94-5a4b-447c-a2c0-c94b6fc8893f/metadata
    readers: file_writer@0
             BiasVoltageCol@4294967295
             DeviceTemperat@0
    size: 18060264
2025-02-05 19:38:59.989506    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e4b26f94-5a4b-447c-a2c0-c94b6fc8893f/commands
    readers: file_writer@0
             DeviceTemperat@0
    size: 4166664
2025-02-05 19:38:59.989658    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e4b26f94-5a4b-447c-a2c0-c94b6fc8893f/dynamic_analysis
    readers: file_writer@0
    size: 3074056
2025-02-05 19:38:59.989794    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e4b26f94-5a4b-447c-a2c0-c94b6fc8893f/channel_state_data_56128a1c-90a5-4f3f-b54a-cfa5a75094a3
    readers: file_writer@0
             ChannelStatesP@0
    size: 6186262
2025-02-05 19:38:59.989863    INFO: data_acquisition_finished (engine)
    acquisition_run_id: 73405cbe2afe48fd420ba4dec9192e4d7ac0e045
2025-02-05 19:38:59.989937    INFO: stop_processing_unblocks_called (mgmt)
2025-02-05 19:38:59.995616    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"INTERNAL_ERROR"}
2025-02-05 19:38:59.995746 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-05 19:39:00.006723   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:39:00.472156    INFO: protocol_phase_control_ended (rpc)
2025-02-05 19:39:00.651137   ERROR: script_crashed (script)
    exit_code: 1
    run_id: 4bd7f2ff-2d2b-49a9-a205-df7658ed1638
    script_output: /opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py:11: DeprecationWarning:
                   Pyarrow will become a required dependency of pandas in the next major release of pandas (pandas 3.0),
                   (to allow more performant data types, such as the Arrow string type, and better interoperability with other libraries)
                   but was not found to be installed on your system.
                   If this would cause problems for you,
                   please provide us feedback at https://github.com/pandas-dev/pandas/issues/54466

                     import pandas as pd
                   Traceback (most recent call last):
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 338, in <module>
                       @minknow.main
                        ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow/__init__.py", line 50, in main
                       fn(
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 342, in qc_script
                       main(config=args.config, environ=local.environ)
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 165, in main
                       calibrate(device, output_bulk=False, ping_data=True, purpose=device.get_exp_script_purpose())
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/calibrate_device.py", line 116, in calibrate
                       data = promethion_calibration.collect_data(device, config=config)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/promethion.py", line 55, in collect_data
                       data = collect_continuous_data_current_setup(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_continuous_data.py", line 135, in collect_continuous_data_current_setup
                       raw_data = collect_raw_data(device=device, collection_time_sec=collection_period, calibrated=calibrated)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_raw_data.py", line 36, in collect_raw_data
                       signal_data = get_signal(
                                     ^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/data.py", line 212, in get_signal
                       for msg in connection.data.get_signal_bytes(**kwargs):
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/_support.py", line 57, in __iter__
                       for m in self._message:
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 543, in __next__
                       return self._next()
                              ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 969, in _next
                       raise self
                   grpc._channel._MultiThreadedRendezvous: <_MultiThreadedRendezvous of RPC that terminated with:
                   	status = StatusCode.CANCELLED
                   	details = "Acquisition ended"
                   	debug_error_string = "UNKNOWN:Error received from peer ipv4:127.0.0.1:8000 {grpc_message:"Acquisition ended", grpc_status:1, created_time:"2025-02-05T19:38:58.39631606+00:00"}"
                   >
                   WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
                   E0000 00:00:1738784340.455796   34835 init.cc:229] grpc_wait_for_shutdown_with_timeout() timed out.

    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 19:39:02.044209 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 19:39:02.047196    INFO: protocol_finished_script_failure (script)
    error: ExitCode=1
    run_id: 4bd7f2ff-2d2b-49a9-a205-df7658ed1638
2025-02-05 19:39:02.047436    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["73405cbe2afe48fd420ba4dec9192e4d7ac0e045"],"monitor":"protocol_end","script_name":"checks/flowcell_check/platform_qc_RNA4:FLO-PRO...
2025-02-05 19:39:02.047946    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-05 19:39:02.051590   ERROR: failed_to_finish_transition (state_control)
    destination: Error
    observer: MantaControl
    operation_begin: 2025-02-05T19:38:59.942961
    operation_end: 2025-02-05T19:43:59.942961
    source: Running
    stage: transition
2025-02-05 19:39:02.052694   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 19:39:02.053312    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 19:39:02.251473   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 19:39:02.251598    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 19:39:02.451537   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 19:39:02.451682    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 19:54:46.955488 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2000
2025-02-05 19:54:46.958136    INFO: create_request_for_disabling_OS_standby (util)
2025-02-05 19:54:46.958359    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250205_1954_P2S-02545-A_PAY83684...
2025-02-05 19:54:46.960922    INFO: protocol_started (script)
    flowcell_id: PAY83684
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250205_1954_P2S-02545-A_PAY83684_583182a9
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250205_1954_P2S-02545-A_PAY83684_583182a9
    reported_output_reads_dir: /data/.
    run_id: 583182a9-5487-4ec4-964e-80ac5f955ed5
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 19:54:46.961439    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-05 19:54:46.961478    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-05 19:55:02.059597    INFO: protocol_phase_control_started (rpc)
2025-02-05 19:55:02.060078    INFO: protocol_phase_changed (script)
    phase: PHASE_INITIALISING
2025-02-05 19:55:02.060348    INFO: base_device.wait_for_temperature (user_messages)
    target: 31.5
    timeout: 180
2025-02-05 19:55:04.640725 WARNING: fixing_up_incomplete_acquisition (script)
    run_id: 73405cbe2afe48fd420ba4dec9192e4d7ac0e045
2025-02-05 19:58:02.348426    INFO: wait_for_temperature_complete (mgmt)
    elapsed_time: 180.287
    has_secondary_temp_limits: true
    primary_temperature_begin: 26.0732
    primary_temperature_end: 26.2199
    primary_temperature_tolerance: 0.1
    result: CancelledWaitingForTemperature
    secondary_temperature_begin: 150
    secondary_temperature_end: 150
    target_primary_temperature: 31.5
    unstable_time: 180.26
2025-02-05 19:58:02.352771    INFO: base_device.warn_to_reach_temperature (user_messages)
    timeout: 180
2025-02-05 19:58:02.496522    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-05 19:58:02.948591    INFO: data_acquisition_starting (engine)
    acquisition_run_id: b78406a4aa41aa44a756ae50d6ca326f831066d2
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-05 19:58:03.049081    INFO: start_processing_unblocks_called (mgmt)
2025-02-05 19:58:03.144985    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-05 19:58:03.249173    INFO: data_acquisition_started (engine)
    acquisition_run_id: b78406a4aa41aa44a756ae50d6ca326f831066d2
2025-02-05 19:58:03.354200   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:58:03.392275   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:58:03.430293   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:58:03.442387   ERROR: failed_to_finish_transition (state_control)
    destination: Running
    observer: MantaControl
    operation_begin: 2025-02-05T19:58:03.242265
    operation_end: 2025-02-05T19:59:42.428265
    source: Initialise
    stage: stable
2025-02-05 19:58:03.449339    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/3fff9523-ee5f-4540-87de-08a4ff6394a5/metadata
    readers: file_writer@0
             BiasVoltageCol@0
             DeviceTemperat@0
    size: 18060264
2025-02-05 19:58:03.449495    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/3fff9523-ee5f-4540-87de-08a4ff6394a5/commands
    readers: file_writer@0
             DeviceTemperat@0
             SignalDataPump@0
    size: 4166664
2025-02-05 19:58:03.449607    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/3fff9523-ee5f-4540-87de-08a4ff6394a5/dynamic_analysis
    readers: file_writer@0
    size: 3074056
2025-02-05 19:58:03.449714    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/3fff9523-ee5f-4540-87de-08a4ff6394a5/channel_state_data_e0bd44fb-1d5c-408f-b791-50203b14053f
    readers: file_writer@0
             ChannelStatesP@0
    size: 6186262
2025-02-05 19:58:03.449767    INFO: data_acquisition_finished (engine)
    acquisition_run_id: b78406a4aa41aa44a756ae50d6ca326f831066d2
2025-02-05 19:58:03.449829    INFO: stop_processing_unblocks_called (mgmt)
2025-02-05 19:58:03.453147    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"INTERNAL_ERROR"}
2025-02-05 19:58:03.453807 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-05 19:58:03.455042 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: SignalDataPump
2025-02-05 19:58:03.468398   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:58:03.506435   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 19:58:07.204587 WARNING: p2_acquisition_finished_with_errors (device)
    asic_frame_counter_jumps_between_transfers: 0
    asic_frame_counter_jumps_within_transfer: 0
    failed_transfers: 16
    invalid_last_command_values: 0
    pb_corrupted_frames: [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]
    received_frames: 0
    sent_frame_counter_jumps_between_transfers: 0
    sent_frame_counter_jumps_within_transfer: 0
2025-02-05 19:58:07.204727    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 65535mA - 0mA (last: 0mA)
    asic_2_5v: 65535mA - 0mA (last: 0mA)
    ext_ntc_temp: -55°C - 150°C (last: 150°C)
    fpga_temp: 65535°C - 0°C (last: 0°C)
    internal_temp: 65535°C - 0°C (last: 0°C)
2025-02-05 19:58:07.204739    INFO: p2_acquisition_timing_stats (device)
    next_block: 9223372036854775807ns - -9223372036854775808ns
    notify_command: 9223372036854775807ns - -9223372036854775808ns
    submit_transfer: 9223372036854775807ns - -9223372036854775808ns
    transfer_handled_late_count: 16
    transfer_processing: 13ns - 16955ns
    transpose: 9223372036854775807ns - -9223372036854775808ns
    update_saturation: 9223372036854775807ns - -9223372036854775808ns
    wait_for_transfer: 54ns - 405ns
2025-02-05 19:58:09.207361 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 19:58:09.210541   ERROR: failed_to_finish_transition (state_control)
    destination: Error
    observer: MantaControl
    operation_begin: 2025-02-05T19:58:03.443076
    operation_end: 2025-02-05T20:03:03.443076
    source: Running
    stage: transition
2025-02-05 19:58:09.210748   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 19:58:09.210857    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 19:58:09.210880    INFO: stop_protocol_requested (script)
    run_id: 583182a9-5487-4ec4-964e-80ac5f955ed5
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 19:58:09.215020    INFO: protocol_finished_internal_error (script)
    run_id: 583182a9-5487-4ec4-964e-80ac5f955ed5
2025-02-05 19:58:09.215091    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["b78406a4aa41aa44a756ae50d6ca326f831066d2"],"monitor":"protocol_end","script_name":"checks/flowcell_check/platform_qc_RNA4:FLO-PRO...
2025-02-05 19:58:09.215204    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-05 19:58:09.410589   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 19:58:09.410719    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 19:58:09.610596   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 19:58:09.610752    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 19:58:10.411251    INFO: rpc_error_occurred (rpc)
    details: {
                 "error_message": "Failed to start run: Error in observer 'MantaControl': Too many recent transfers failed: Transfer timed out",
                 "exit_code": -1
             }
    path: /minknow_api.acquisition.AcquisitionService/start
    status: UNKNOWN
    user_identity: ['']
