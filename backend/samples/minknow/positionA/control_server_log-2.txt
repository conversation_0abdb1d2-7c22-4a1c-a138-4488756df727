2025-02-05 20:28:49.384874    INFO: starting_up (control)
    hostname: GXB03431
    system: GridION X5 Mk1 (GRD-X5B003) GXB03431 running on ubuntu 20.04 (5.11.0-40-generic)
            Distribution:           24.11.8 (STABLE)
            MinKNOW Core:           6.2.6
            Bream:                  8.2.5
            Protocol configuration: 6.2.12
            <PERSON><PERSON> (client):         7.6.0+ad1db9c5a
            <PERSON><PERSON> (server):         7.6.7+de8939544

2025-02-05 20:28:49.398348 WARNING: partially_raised_file_limits (control_server)
    actual: 1024
    target: 4096
2025-02-05 20:28:49.400266    INFO: auth_guest_mode (rpc)
    value: local_only
2025-02-05 20:28:49.413475    INFO: flow_cell_position_instantiated (mgmt)
    device_id: P2S-02545-A
    hardware_type: P2_SOLO_USB
    os_identifier: 2a1d:0091@002:007
    simulated: false
2025-02-05 20:28:49.419850   ERROR: find_device_failed (mgmt)
    exception: Response from device was too short
2025-02-05 20:28:49.436868    INFO: manager_notified_new_auth_token (util)
    expires_in: 7484584707418508482ns
    token_type: internal
2025-02-05 20:28:49.436898    INFO: manager_notified_new_auth_token (util)
    expires_in: 89994976988804ns
    token_type: local
2025-02-05 20:28:49.465438    INFO: removable_device_disconnected (engine)
2025-02-05 20:28:56.423589    INFO: active_device_set (mgmt)
    identifier: 2a1d:0091@002:007
2025-02-05 20:28:56.589157    INFO: device_discovered (engine)
    device_id: P2S-02545-A
2025-02-05 20:28:56.918435    INFO: successfully_read_flow_cell_data (mgmt)
    attempt: 1
    flow_cell_data: V5{ minor_version = 0, wells_per_channel = 4, temperature_offset = 32767, flowcell_id = [ 50 41 59 38 33 36 38 34 00 00 00 00 00 00 00 00 ], product_code = [ 46 4c 4f 2d 50 52 4f 30 30 34 52 41 00 00 00 00 ] }
2025-02-05 20:28:56.918663 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 7503
2025-02-05 20:28:56.919103    INFO: flowcell_discovered (engine)
    asic_id: 0004A30B010F3867
    asic_id_eeprom: 0004A30B010F3867
    flow_cell_id: PAY83684
2025-02-05 20:28:57.089468    INFO: firmware_component (mgmt)
    component_name: P2 USB-FW
    serial_number:
    version: 2.5.2
2025-02-05 20:28:57.089584    INFO: firmware_component (mgmt)
    component_name: P2 FPGA
    serial_number: 002884c44dd9e85c
    version: 2.4.6
2025-02-05 20:32:30.280367 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2002
2025-02-05 20:32:30.285597    INFO: create_request_for_disabling_OS_standby (util)
2025-02-05 20:32:30.285771    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250205_2032_P2S-02545-A_PAY83684...
2025-02-05 20:32:30.287497    INFO: protocol_started (script)
    flowcell_id: PAY83684
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250205_2032_P2S-02545-A_PAY83684_c2ecc35e
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250205_2032_P2S-02545-A_PAY83684_c2ecc35e
    reported_output_reads_dir: /data/.
    run_id: c2ecc35e-fb30-4e57-b7e0-547497a2041a
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 20:32:30.287763    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-05 20:32:30.287793    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-05 20:32:45.407077    INFO: protocol_phase_control_started (rpc)
2025-02-05 20:32:45.407716    INFO: protocol_phase_changed (script)
    phase: PHASE_INITIALISING
2025-02-05 20:32:45.407708    INFO: base_device.wait_for_temperature (user_messages)
    target: 31.5
    timeout: 180
2025-02-05 20:35:45.694630    INFO: wait_for_temperature_complete (mgmt)
    elapsed_time: 180.285
    has_secondary_temp_limits: true
    primary_temperature_begin: 30.1421
    primary_temperature_end: 30.3816
    primary_temperature_tolerance: 0.1
    result: CancelledWaitingForTemperature
    secondary_temperature_begin: 28.5543
    secondary_temperature_end: 150
    target_primary_temperature: 31.5
    unstable_time: ************-02-05 20:35:45.699251    INFO: base_device.warn_to_reach_temperature (user_messages)
    timeout: 180
2025-02-05 20:35:45.813164    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-05 20:35:46.249726    INFO: data_acquisition_starting (engine)
    acquisition_run_id: d7346238fae69c402350b49fa50728b121e1080e
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-05 20:35:46.353551    INFO: start_processing_unblocks_called (mgmt)
2025-02-05 20:35:46.410978    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-05 20:35:46.552978    INFO: data_acquisition_started (engine)
    acquisition_run_id: d7346238fae69c402350b49fa50728b121e1080e
2025-02-05 20:35:50.473410 WARNING: p2_acquisition_finished_with_errors (device)
    asic_frame_counter_jumps_between_transfers: 0
    asic_frame_counter_jumps_within_transfer: 0
    failed_transfers: 16
    invalid_last_command_values: 0
    pb_corrupted_frames: [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]
    received_frames: 0
    sent_frame_counter_jumps_between_transfers: 0
    sent_frame_counter_jumps_within_transfer: 0
2025-02-05 20:35:50.473577    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 65535mA - 0mA (last: 0mA)
    asic_2_5v: 65535mA - 0mA (last: 0mA)
    ext_ntc_temp: -55°C - 150°C (last: 150°C)
    fpga_temp: 65535°C - 0°C (last: 0°C)
    internal_temp: 65535°C - 0°C (last: 0°C)
2025-02-05 20:35:50.473596    INFO: p2_acquisition_timing_stats (device)
    next_block: 9223372036854775807ns - -9223372036854775808ns
    notify_command: 9223372036854775807ns - -9223372036854775808ns
    submit_transfer: 6258ns - 853542ns (blamed for 15 delays)
    transfer_handled_late_count: 16
    transfer_processing: 15ns - 43992ns
    transpose: 9223372036854775807ns - -9223372036854775808ns
    update_saturation: 9223372036854775807ns - -9223372036854775808ns
    wait_for_transfer: 81ns - 25915ns
2025-02-05 20:35:50.479889   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 20:35:50.516569   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 20:35:50.553175    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/5c7e6f80-6773-4c0c-b64c-d03e1b436c0f/metadata
    readers: file_writer@0
             BiasVoltageCol@4294967295
             DeviceTemperat@0
    size: 18060264
2025-02-05 20:35:50.553359    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/5c7e6f80-6773-4c0c-b64c-d03e1b436c0f/commands
    readers: file_writer@0
             DeviceTemperat@0
    size: 4166664
2025-02-05 20:35:50.553509    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/5c7e6f80-6773-4c0c-b64c-d03e1b436c0f/dynamic_analysis
    readers: file_writer@0
    size: 3074056
2025-02-05 20:35:50.553612    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/5c7e6f80-6773-4c0c-b64c-d03e1b436c0f/channel_state_data_c53f91d0-2f04-416b-ae3a-37f6b2f9e92d
    readers: file_writer@0
             ChannelStatesP@0
    size: 6186262
2025-02-05 20:35:50.553662    INFO: data_acquisition_finished (engine)
    acquisition_run_id: d7346238fae69c402350b49fa50728b121e1080e
2025-02-05 20:35:50.553708    INFO: stop_processing_unblocks_called (mgmt)
2025-02-05 20:35:50.554699   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 20:35:50.559908    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"INTERNAL_ERROR"}
2025-02-05 20:35:50.560030 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-05 20:35:50.592686   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 20:35:51.061246    INFO: protocol_phase_control_ended (rpc)
2025-02-05 20:35:51.254247   ERROR: script_crashed (script)
    exit_code: 1
    run_id: c2ecc35e-fb30-4e57-b7e0-547497a2041a
    script_output: /opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py:11: DeprecationWarning:
                   Pyarrow will become a required dependency of pandas in the next major release of pandas (pandas 3.0),
                   (to allow more performant data types, such as the Arrow string type, and better interoperability with other libraries)
                   but was not found to be installed on your system.
                   If this would cause problems for you,
                   please provide us feedback at https://github.com/pandas-dev/pandas/issues/54466

                     import pandas as pd
                   Traceback (most recent call last):
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 338, in <module>
                       @minknow.main
                        ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow/__init__.py", line 50, in main
                       fn(
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 342, in qc_script
                       main(config=args.config, environ=local.environ)
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 165, in main
                       calibrate(device, output_bulk=False, ping_data=True, purpose=device.get_exp_script_purpose())
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/calibrate_device.py", line 116, in calibrate
                       data = promethion_calibration.collect_data(device, config=config)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/promethion.py", line 55, in collect_data
                       data = collect_continuous_data_current_setup(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_continuous_data.py", line 135, in collect_continuous_data_current_setup
                       raw_data = collect_raw_data(device=device, collection_time_sec=collection_period, calibrated=calibrated)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_raw_data.py", line 36, in collect_raw_data
                       signal_data = get_signal(
                                     ^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/data.py", line 212, in get_signal
                       for msg in connection.data.get_signal_bytes(**kwargs):
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/_support.py", line 57, in __iter__
                       for m in self._message:
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 543, in __next__
                       return self._next()
                              ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 969, in _next
                       raise self
                   grpc._channel._MultiThreadedRendezvous: <_MultiThreadedRendezvous of RPC that terminated with:
                   	status = StatusCode.CANCELLED
                   	details = "Acquisition ended"
                   	debug_error_string = "UNKNOWN:Error received from peer ipv4:127.0.0.1:8000 {grpc_message:"Acquisition ended", grpc_status:1, created_time:"2025-02-05T20:35:48.97628029+00:00"}"
                   >
                   WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
                   E0000 00:00:1738787751.049523    6962 init.cc:229] grpc_wait_for_shutdown_with_timeout() timed out.

    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 20:35:52.612465 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 20:35:52.615532    INFO: protocol_finished_script_failure (script)
    error: ExitCode=1
    run_id: c2ecc35e-fb30-4e57-b7e0-547497a2041a
2025-02-05 20:35:52.615855    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["d7346238fae69c402350b49fa50728b121e1080e"],"monitor":"protocol_end","script_name":"checks/flowcell_check/platform_qc_RNA4:FLO-PRO...
2025-02-05 20:35:52.616393    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-05 20:35:52.619113   ERROR: failed_to_finish_transition (state_control)
    destination: Error
    observer: MantaControl
    operation_begin: 2025-02-05T20:35:50.511255
    operation_end: 2025-02-05T20:40:50.511255
    source: Running
    stage: transition
2025-02-05 20:35:52.619346   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 20:35:52.619496    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 20:35:52.819509   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 20:35:52.819965    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 20:35:53.019590   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 20:35:53.020077    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 20:35:53.219720   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 20:35:53.220248    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:13:57.815333   ERROR: device_error (mgmt)
    exception: /builds/minknow/minknow-core/17867628/.conan/data/boost/1.78.0/nanopore/testing/package/be90984710d149573f5970f1c610249548726070/include/boost/outcome/detail/trait_std_error_code.hpp(96): Throw in function void boost::outcome_v2::policy::outcome_throw_as_system_error_with_payload(const std::error_code&)
               Dynamic exception type: boost::wrapexcept<std::system_error>
               std::exception::what: No such device (it may have been disconnected)

2025-02-05 21:13:57.900253    INFO: manager_notified_hw_state_change (util)
    new_identifier: 2a1d:0091@002:007
    new_state: unavailable
2025-02-05 21:13:58.066031    INFO: active_device_detached (mgmt)
    identifier: 2a1d:0091@002:007
2025-02-05 21:13:58.071650    INFO: removable_device_disconnected (engine)
2025-02-05 21:14:20.515540    INFO: manager_notified_hw_state_change (util)
    new_identifier: 2a1d:0091@002:008
    new_state: initialising
2025-02-05 21:14:22.678058    INFO: manager_notified_hw_state_change (util)
    new_identifier: 2a1d:0091@002:008
    new_state: ready
2025-02-05 21:14:22.715023   ERROR: find_device_failed (mgmt)
    exception: Response from device was too short
2025-02-05 21:14:29.718497    INFO: active_device_set (mgmt)
    identifier: 2a1d:0091@002:008
2025-02-05 21:14:29.883187    INFO: device_and_flowcell_discovered (engine)
    asic_id:
    asic_id_eeprom:
    device_id: P2S-02545-A
2025-02-05 21:14:30.211276    INFO: successfully_read_flow_cell_data (mgmt)
    attempt: 1
    flow_cell_data: V5{ minor_version = 0, wells_per_channel = 4, temperature_offset = 32767, flowcell_id = [ 50 41 59 38 33 36 38 34 00 00 00 00 00 00 00 00 ], product_code = [ 46 4c 4f 2d 50 52 4f 30 30 34 52 41 00 00 00 00 ] }
2025-02-05 21:14:30.211378 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 32139
2025-02-05 21:14:30.211578    INFO: firmware_component (mgmt)
    component_name: P2 USB-FW
    serial_number:
    version: 2.5.2
2025-02-05 21:14:30.211601    INFO: firmware_component (mgmt)
    component_name: P2 FPGA
    serial_number: 002884c44dd9e85c
    version: 2.4.6
2025-02-05 21:14:37.557706    INFO: manager_requested_exit (util)
    when: asap
2025-02-05 21:14:38.279683    INFO: exiting (control_server)
2025-02-05 21:14:38.563567    INFO: shutdown_complete (control)
