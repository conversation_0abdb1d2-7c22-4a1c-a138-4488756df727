2025-02-07 19:05:37.449068    INFO: starting_up (control)
    hostname: GXB03431
    system: GridION X5 Mk1 (GRD-X5B003) GXB03431 running on ubuntu 20.04 (5.11.0-40-generic)
            Distribution:           24.11.8 (STABLE)
            MinKNOW Core:           6.2.6
            Bream:                  8.2.5
            Protocol configuration: 6.2.12
            <PERSON><PERSON> (client):         7.6.0+ad1db9c5a
            <PERSON><PERSON> (server):         7.6.7+de8939544

2025-02-07 19:05:37.451371 WARNING: partially_raised_file_limits (control_server)
    actual: 1024
    target: 4096
2025-02-07 19:05:37.451496    INFO: auth_guest_mode (rpc)
    value: local_only
2025-02-07 19:05:37.458351    INFO: flow_cell_position_instantiated (mgmt)
    device_id: P2S-02545-A
    hardware_type: P2_SOLO_USB
    os_identifier: 2a1d:0091@002:006
    simulated: false
2025-02-07 19:05:37.464527    INFO: manager_notified_new_auth_token (util)
    expires_in: 7484416899390255520ns
    token_type: internal
2025-02-07 19:05:37.464550    INFO: manager_notified_new_auth_token (util)
    expires_in: 89910700002856ns
    token_type: local
2025-02-07 19:05:37.468920   ERROR: find_device_failed (mgmt)
    exception: Response from device was too short
2025-02-07 19:05:37.508786    INFO: removable_device_disconnected (engine)
2025-02-07 19:05:44.473479    INFO: active_device_set (mgmt)
    identifier: 2a1d:0091@002:006
2025-02-07 19:05:44.641679 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 7182
2025-02-07 19:05:44.641811    INFO: device_discovered (engine)
    device_id: P2S-02545-A
2025-02-07 19:05:46.642234    INFO: device_without_flowcell_discovered (engine)
    device_id: P2S-02545-A
2025-02-07 19:35:26.942013    INFO: successfully_read_flow_cell_data (mgmt)
    attempt: 1
    flow_cell_data: V5{ minor_version = 0, wells_per_channel = 4, temperature_offset = 32767, flowcell_id = [ 43 54 43 37 38 33 33 35 00 00 00 00 00 00 00 00 ], product_code = [ 46 4c 4f 2d 50 52 4f 31 31 34 4d 00 00 00 00 00 ] }
2025-02-07 19:35:26.942269    INFO: flowcell_discovered (engine)
    asic_id: 0004A30B0100ED5B
    asic_id_eeprom: 0004A30B0100ED5B
    flow_cell_id: CTC78335
2025-02-07 19:35:27.113344    INFO: firmware_component (mgmt)
    component_name: P2 USB-FW
    serial_number:
    version: 2.5.2
2025-02-07 19:35:27.113455    INFO: firmware_component (mgmt)
    component_name: P2 FPGA
    serial_number: 002884c44dd9e85c
    version: 2.4.6
2025-02-07 19:35:42.281660 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-07 19:35:42.285217    INFO: create_request_for_disabling_OS_standby (util)
2025-02-07 19:35:42.285370    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250207_1935_P2S-02545-A_CTC78335...
2025-02-07 19:35:42.287035    INFO: protocol_started (script)
    flowcell_id: CTC78335
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250207_1935_P2S-02545-A_CTC78335_4ab6af84
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250207_1935_P2S-02545-A_CTC78335_4ab6af84
    reported_output_reads_dir: /data/.
    run_id: 4ab6af84-c0bc-4f7a-a28d-74c8bff757cd
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/hardware_check/ctc:FLO-PRO002
    script_path: checks/hardware_check/ctc:FLO-PRO002
2025-02-07 19:35:42.287329    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-07 19:35:42.287429    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-07 19:35:57.384300    INFO: ctc_script.start (user_messages)
2025-02-07 19:35:57.494407    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-07 19:35:57.838628    INFO: data_acquisition_starting (engine)
    acquisition_run_id: 13184f21770d820d962d50bdc8f00fef167eec74
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-07 19:35:58.036992    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-07 19:35:58.040661    INFO: start_processing_unblocks_called (mgmt)
2025-02-07 19:35:58.241060    INFO: data_acquisition_started (engine)
    acquisition_run_id: 13184f21770d820d962d50bdc8f00fef167eec74
2025-02-07 19:36:04.239058    INFO: enforce_data_action (control)
    running_or_finishing: true
    user_requested_terminate_basecalling: true
2025-02-07 19:36:04.440466    INFO: acquisition_finished (device)
    received_frames: 26704
2025-02-07 19:36:04.440593    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 0mA - 0mA (last: 0mA)
    asic_2_5v: 0mA - 0mA (last: 0mA)
    ext_ntc_temp: 37.006996°C - 35.67955°C (last: 35.67955°C)
    fpga_temp: 511°C - 511°C (last: 511°C)
    internal_temp: 40°C - 41°C (last: 40°C)
2025-02-07 19:36:04.440651    INFO: p2_acquisition_timing_stats (device)
    next_block: 320312ns - 2717724ns (blamed for 2 delays)
    notify_command: 25ns - 431647ns
    submit_transfer: 5422ns - 110013ns
    transfer_handled_late_count: 2
    transfer_processing: 134609ns - 3523864ns
    transpose: 27597ns - 417467ns
    update_saturation: 104595ns - 2235495ns
    wait_for_transfer: 799ns - 3179509ns
2025-02-07 19:36:04.478632    INFO: data_acquisition_finishing (engine)
    acquisition_run_id: 13184f21770d820d962d50bdc8f00fef167eec74
2025-02-07 19:36:05.679518    INFO: data_acquisition_finished (engine)
    acquisition_run_id: 13184f21770d820d962d50bdc8f00fef167eec74
2025-02-07 19:36:05.679567    INFO: stop_processing_unblocks_called (mgmt)
2025-02-07 19:36:05.683876    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"USER_REQUESTED"}
2025-02-07 19:36:05.779706 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-07 19:36:06.079793    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-07 19:36:06.418370    INFO: data_acquisition_starting (engine)
    acquisition_run_id: be23a82596a323fc7974e3c97e045a906f0626ce
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-07 19:36:06.520046    INFO: start_processing_unblocks_called (mgmt)
2025-02-07 19:36:06.543785    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-07 19:36:06.719989    INFO: data_acquisition_started (engine)
    acquisition_run_id: be23a82596a323fc7974e3c97e045a906f0626ce
2025-02-07 19:36:12.741268    INFO: enforce_data_action (control)
    running_or_finishing: true
    user_requested_terminate_basecalling: false
2025-02-07 19:36:12.946610    INFO: acquisition_finished (device)
    received_frames: 26704
2025-02-07 19:36:12.946749    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 0mA - 0mA (last: 0mA)
    asic_2_5v: 0mA - 0mA (last: 0mA)
    ext_ntc_temp: 33.334026°C - 30.790998°C (last: 30.790998°C)
    fpga_temp: 511°C - 511°C (last: 511°C)
    internal_temp: 40°C - 41°C (last: 41°C)
2025-02-07 19:36:12.946826    INFO: p2_acquisition_timing_stats (device)
    next_block: 328939ns - 2342910ns (blamed for 2 delays)
    notify_command: 26ns - 443204ns
    submit_transfer: 6554ns - 104862ns
    transfer_handled_late_count: 2
    transfer_processing: 134415ns - 3151588ns
    transpose: 27763ns - 383077ns
    update_saturation: 104620ns - 842228ns
    wait_for_transfer: 1115ns - 3447900ns
2025-02-07 19:36:12.984057    INFO: data_acquisition_finishing (engine)
    acquisition_run_id: be23a82596a323fc7974e3c97e045a906f0626ce
2025-02-07 19:36:14.184975    INFO: data_acquisition_finished (engine)
    acquisition_run_id: be23a82596a323fc7974e3c97e045a906f0626ce
2025-02-07 19:36:14.185031    INFO: stop_processing_unblocks_called (mgmt)
2025-02-07 19:36:14.187751    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"USER_REQUESTED"}
2025-02-07 19:36:14.285095 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-07 19:36:14.585281    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-07 19:36:14.944473    INFO: data_acquisition_starting (engine)
    acquisition_run_id: 924113b0daf8b74c29fc6b1dc90e5869b7ef0b4b
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-07 19:36:15.046134    INFO: start_processing_unblocks_called (mgmt)
2025-02-07 19:36:15.051140    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-07 19:36:15.246453    INFO: data_acquisition_started (engine)
    acquisition_run_id: 924113b0daf8b74c29fc6b1dc90e5869b7ef0b4b
2025-02-07 19:36:21.249502    INFO: enforce_data_action (control)
    running_or_finishing: true
    user_requested_terminate_basecalling: false
2025-02-07 19:36:21.455615    INFO: acquisition_finished (device)
    received_frames: 26704
2025-02-07 19:36:21.455743    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 0mA - 0mA (last: 0mA)
    asic_2_5v: 0mA - 0mA (last: 0mA)
    ext_ntc_temp: 30.679344°C - 30.570915°C (last: 30.679344°C)
    fpga_temp: 511°C - 511°C (last: 511°C)
    internal_temp: 41°C - 42°C (last: 42°C)
2025-02-07 19:36:21.455799    INFO: p2_acquisition_timing_stats (device)
    next_block: 321182ns - 2292727ns (blamed for 1 delays)
    notify_command: 25ns - 418090ns
    submit_transfer: 5400ns - 118678ns
    transfer_handled_late_count: 1
    transfer_processing: 135341ns - 3094101ns
    transpose: 27286ns - 1904125ns
    update_saturation: 104632ns - 756602ns
    wait_for_transfer: 1386ns - 3186159ns
2025-02-07 19:36:21.493760    INFO: data_acquisition_finishing (engine)
    acquisition_run_id: 924113b0daf8b74c29fc6b1dc90e5869b7ef0b4b
2025-02-07 19:36:22.694404    INFO: data_acquisition_finished (engine)
    acquisition_run_id: 924113b0daf8b74c29fc6b1dc90e5869b7ef0b4b
2025-02-07 19:36:22.694412    INFO: stop_processing_unblocks_called (mgmt)
2025-02-07 19:36:22.696833    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"USER_REQUESTED"}
2025-02-07 19:36:22.794487 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-07 19:36:22.954733    INFO: sending_telemetry_message (ping)
    data: {"calibration_pixel_block_results":{"calibration_1":[true,true,true,true,true,true,true,true,true,true,true,true],"calibration_2":[true,true,true,t...
2025-02-07 19:36:22.990760    INFO: ctc_script.success (user_messages)
2025-02-07 19:36:24.189943    INFO: protocol_finished (script)
    run_id: 4ab6af84-c0bc-4f7a-a28d-74c8bff757cd
    script_path: checks/hardware_check/ctc:FLO-PRO002
2025-02-07 19:36:26.207327 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2016
2025-02-07 19:36:26.208532    INFO: protocol_finished_successfully (script)
    identity: ['MinKNOW']
    reason: Script Completed
    run_id: 4ab6af84-c0bc-4f7a-a28d-74c8bff757cd
2025-02-07 19:36:26.210600    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["13184f21770d820d962d50bdc8f00fef167eec74","be23a82596a323fc7974e3c97e045a906f0626ce","924113b0daf8b74c29fc6b1dc90e5869b7ef0b4b"],...
2025-02-07 19:36:26.210694    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-07 19:36:37.224368    INFO: flowcell_disconnected (engine)
2025-02-07 20:45:15.588180    INFO: successfully_read_flow_cell_data (mgmt)
    attempt: 1
    flow_cell_data: V5{ minor_version = 0, wells_per_channel = 4, temperature_offset = 32767, flowcell_id = [ 50 41 59 38 32 31 32 32 00 00 00 00 00 00 00 00 ], product_code = [ 46 4c 4f 2d 50 52 4f 30 30 34 52 41 00 00 00 00 ] }
2025-02-07 20:45:15.588255    INFO: flowcell_discovered (engine)
    asic_id: 0004A30B00FDFFFC
    asic_id_eeprom: 0004A30B00FDFFFC
    flow_cell_id: PAY82122
2025-02-07 20:45:15.750110    INFO: firmware_component (mgmt)
    component_name: P2 USB-FW
    serial_number:
    version: 2.5.2
2025-02-07 20:45:15.750199    INFO: firmware_component (mgmt)
    component_name: P2 FPGA
    serial_number: 002884c44dd9e85c
    version: 2.4.6
2025-02-07 20:45:25.766242 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2002
2025-02-07 20:45:25.769664    INFO: create_request_for_disabling_OS_standby (util)
2025-02-07 20:45:25.769899    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250207_2045_P2S-02545-A_PAY82122...
2025-02-07 20:45:25.772259    INFO: protocol_started (script)
    flowcell_id: PAY82122
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250207_2045_P2S-02545-A_PAY82122_45e1c65c
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250207_2045_P2S-02545-A_PAY82122_45e1c65c
    reported_output_reads_dir: /data/.
    run_id: 45e1c65c-73ee-4c1e-a879-86892c8881df
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-07 20:45:25.772616    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-07 20:45:25.772645    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-07 20:45:29.942340 WARNING: fixing_up_incomplete_acquisition (script)
    run_id: 9b0adbce2857a0790c099e4d5e53c3101c6241c4
2025-02-07 20:45:40.891259    INFO: protocol_phase_control_started (rpc)
2025-02-07 20:45:40.891664    INFO: protocol_phase_changed (script)
    phase: PHASE_INITIALISING
2025-02-07 20:45:40.892068    INFO: base_device.wait_for_temperature (user_messages)
    target: 31.5
    timeout: 180
2025-02-07 20:48:41.180790    INFO: wait_for_temperature_complete (mgmt)
    elapsed_time: 180.287
    has_secondary_temp_limits: true
    primary_temperature_begin: 17.5224
    primary_temperature_end: 29.2195
    primary_temperature_tolerance: 0.1
    result: CancelledWaitingForTemperature
    secondary_temperature_begin: 30.9634
    secondary_temperature_end: 150
    target_primary_temperature: 31.5
    unstable_time: ************-02-07 20:48:41.185437    INFO: base_device.warn_to_reach_temperature (user_messages)
    timeout: 180
2025-02-07 20:48:41.303747    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-07 20:48:41.741873    INFO: data_acquisition_starting (engine)
    acquisition_run_id: 25200f80c4ffba52ad4d14326dffc8456cdb8155
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-07 20:48:41.880978    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-07 20:48:41.943415    INFO: start_processing_unblocks_called (mgmt)
2025-02-07 20:48:42.143465    INFO: data_acquisition_started (engine)
    acquisition_run_id: 25200f80c4ffba52ad4d14326dffc8456cdb8155
2025-02-07 20:48:45.942995 WARNING: p2_acquisition_finished_with_errors (device)
    asic_frame_counter_jumps_between_transfers: 0
    asic_frame_counter_jumps_within_transfer: 0
    failed_transfers: 16
    invalid_last_command_values: 0
    pb_corrupted_frames: [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]
    received_frames: 0
    sent_frame_counter_jumps_between_transfers: 0
    sent_frame_counter_jumps_within_transfer: 0
2025-02-07 20:48:45.943159    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 65535mA - 0mA (last: 0mA)
    asic_2_5v: 65535mA - 0mA (last: 0mA)
    ext_ntc_temp: -55°C - 150°C (last: 150°C)
    fpga_temp: 65535°C - 0°C (last: 0°C)
    internal_temp: 65535°C - 0°C (last: 0°C)
2025-02-07 20:48:45.943172    INFO: p2_acquisition_timing_stats (device)
    next_block: 9223372036854775807ns - -9223372036854775808ns
    notify_command: 9223372036854775807ns - -9223372036854775808ns
    submit_transfer: 5384ns - 7487ns (blamed for 15 delays)
    transfer_handled_late_count: 16
    transfer_processing: 14ns - 25079ns
    transpose: 9223372036854775807ns - -9223372036854775808ns
    update_saturation: 9223372036854775807ns - -9223372036854775808ns
    wait_for_transfer: 59ns - 257ns
2025-02-07 20:48:45.977818   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-07 20:48:46.014256   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-07 20:48:46.052339   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-07 20:48:46.121970    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/a7cddf50-cb73-4986-a648-20858b83bdeb/metadata
    readers: file_writer@0
             BiasVoltageCol@4294967295
             DeviceTemperat@0
    size: 18060264
2025-02-07 20:48:46.122215    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/a7cddf50-cb73-4986-a648-20858b83bdeb/commands
    readers: file_writer@0
             DeviceTemperat@0
    size: 4166664
2025-02-07 20:48:46.122367    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/a7cddf50-cb73-4986-a648-20858b83bdeb/dynamic_analysis
    readers: file_writer@0
    size: 3074056
2025-02-07 20:48:46.122512    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/a7cddf50-cb73-4986-a648-20858b83bdeb/channel_state_data_aee2ada2-4977-4903-901d-13bc9cca426a
    readers: file_writer@0
             ChannelStatesP@0
    size: 6186262
2025-02-07 20:48:46.122602    INFO: data_acquisition_finished (engine)
    acquisition_run_id: 25200f80c4ffba52ad4d14326dffc8456cdb8155
2025-02-07 20:48:46.122672    INFO: stop_processing_unblocks_called (mgmt)
2025-02-07 20:48:46.126910    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"INTERNAL_ERROR"}
2025-02-07 20:48:46.127515 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-07 20:48:46.529025    INFO: protocol_phase_control_ended (rpc)
2025-02-07 20:48:46.702703   ERROR: script_crashed (script)
    exit_code: 1
    run_id: 45e1c65c-73ee-4c1e-a879-86892c8881df
    script_output: /opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py:11: DeprecationWarning:
                   Pyarrow will become a required dependency of pandas in the next major release of pandas (pandas 3.0),
                   (to allow more performant data types, such as the Arrow string type, and better interoperability with other libraries)
                   but was not found to be installed on your system.
                   If this would cause problems for you,
                   please provide us feedback at https://github.com/pandas-dev/pandas/issues/54466

                     import pandas as pd
                   Traceback (most recent call last):
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 338, in <module>
                       @minknow.main
                        ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow/__init__.py", line 50, in main
                       fn(
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 342, in qc_script
                       main(config=args.config, environ=local.environ)
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 165, in main
                       calibrate(device, output_bulk=False, ping_data=True, purpose=device.get_exp_script_purpose())
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/calibrate_device.py", line 116, in calibrate
                       data = promethion_calibration.collect_data(device, config=config)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/promethion.py", line 55, in collect_data
                       data = collect_continuous_data_current_setup(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_continuous_data.py", line 135, in collect_continuous_data_current_setup
                       raw_data = collect_raw_data(device=device, collection_time_sec=collection_period, calibrated=calibrated)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_raw_data.py", line 36, in collect_raw_data
                       signal_data = get_signal(
                                     ^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/data.py", line 212, in get_signal
                       for msg in connection.data.get_signal_bytes(**kwargs):
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/_support.py", line 57, in __iter__
                       for m in self._message:
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 543, in __next__
                       return self._next()
                              ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 969, in _next
                       raise self
                   grpc._channel._MultiThreadedRendezvous: <_MultiThreadedRendezvous of RPC that terminated with:
                   	status = StatusCode.CANCELLED
                   	details = "Acquisition ended"
                   	debug_error_string = "UNKNOWN:Error received from peer ipv4:127.0.0.1:8010 {created_time:"2025-02-07T20:48:44.457902983+00:00", grpc_status:1, grpc_message:"Acquisition ended"}"
                   >
                   WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
                   E0000 00:00:1738961326.519200   13249 init.cc:229] grpc_wait_for_shutdown_with_timeout() timed out.

    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-07 20:48:48.080958 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2002
2025-02-07 20:48:48.083927    INFO: protocol_finished_script_failure (script)
    error: ExitCode=1
    run_id: 45e1c65c-73ee-4c1e-a879-86892c8881df
2025-02-07 20:48:48.084164    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["25200f80c4ffba52ad4d14326dffc8456cdb8155"],"monitor":"protocol_end","script_name":"checks/flowcell_check/platform_qc_RNA4:FLO-PRO...
2025-02-07 20:48:48.084724    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-07 20:48:48.190668   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-07 20:48:48.190837    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-07 20:48:48.390685   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-07 20:48:48.390805    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-07 20:48:48.590928   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-07 20:48:48.591086    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-07 20:49:16.115982    INFO: flowcell_disconnected (engine)
