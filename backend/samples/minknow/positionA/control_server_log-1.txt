2025-02-05 21:14:38.627449    INFO: starting_up (control)
    hostname: GXB03431
    system: GridION X5 Mk1 (GRD-X5B003) GXB03431 running on ubuntu 20.04 (5.11.0-40-generic)
            Distribution:           24.11.8 (STABLE)
            MinKNOW Core:           6.2.6
            Bream:                  8.2.5
            Protocol configuration: 6.2.12
            <PERSON><PERSON> (client):         7.6.0+ad1db9c5a
            <PERSON><PERSON> (server):         7.6.7+de8939544

2025-02-05 21:14:38.630920 WARNING: partially_raised_file_limits (control_server)
    actual: 1024
    target: 4096
2025-02-05 21:14:38.631109    INFO: auth_guest_mode (rpc)
    value: local_only
2025-02-05 21:14:38.637982    INFO: flow_cell_position_instantiated (mgmt)
    device_id: P2S-02545-A
    hardware_type: P2_SOLO_USB
    os_identifier: 2a1d:0091@002:008
    simulated: false
2025-02-05 21:14:38.644701    INFO: manager_notified_new_auth_token (util)
    expires_in: 7484581958210085544ns
    token_type: internal
2025-02-05 21:14:38.644724    INFO: manager_notified_new_auth_token (util)
    expires_in: 87245769162675ns
    token_type: local
2025-02-05 21:14:38.688525    INFO: removable_device_disconnected (engine)
2025-02-05 21:14:40.640237    INFO: active_device_set (mgmt)
    identifier: 2a1d:0091@002:008
2025-02-05 21:14:40.804754    INFO: device_discovered (engine)
    device_id: P2S-02545-A
2025-02-05 21:14:41.132954    INFO: successfully_read_flow_cell_data (mgmt)
    attempt: 1
    flow_cell_data: V5{ minor_version = 0, wells_per_channel = 4, temperature_offset = 32767, flowcell_id = [ 50 41 59 38 33 36 38 34 00 00 00 00 00 00 00 00 ], product_code = [ 46 4c 4f 2d 50 52 4f 30 30 34 52 41 00 00 00 00 ] }
2025-02-05 21:14:41.133287 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2494
2025-02-05 21:14:41.134283    INFO: flowcell_discovered (engine)
    asic_id: 0004A30B010F3867
    asic_id_eeprom: 0004A30B010F3867
    flow_cell_id: PAY83684
2025-02-05 21:14:41.305189    INFO: firmware_component (mgmt)
    component_name: P2 USB-FW
    serial_number:
    version: 2.5.2
2025-02-05 21:14:41.305329    INFO: firmware_component (mgmt)
    component_name: P2 FPGA
    serial_number: 002884c44dd9e85c
    version: 2.4.6
2025-02-05 21:15:26.671270 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 21:15:26.673932    INFO: create_request_for_disabling_OS_standby (util)
2025-02-05 21:15:26.674247    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250205_2115_P2S-02545-A_PAY83684...
2025-02-05 21:15:26.676575    INFO: protocol_started (script)
    flowcell_id: PAY83684
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250205_2115_P2S-02545-A_PAY83684_bb27a051
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250205_2115_P2S-02545-A_PAY83684_bb27a051
    reported_output_reads_dir: /data/.
    run_id: bb27a051-6645-4be9-bcd5-bb94a85e3923
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 21:15:26.677029    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-05 21:15:26.677059    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-05 21:15:41.764874    INFO: protocol_phase_control_started (rpc)
2025-02-05 21:15:41.765499    INFO: base_device.wait_for_temperature (user_messages)
    target: 31.5
    timeout: 180
2025-02-05 21:15:41.765544    INFO: protocol_phase_changed (script)
    phase: PHASE_INITIALISING
2025-02-05 21:15:54.147621 WARNING: fixing_up_incomplete_acquisition (script)
    run_id: d7346238fae69c402350b49fa50728b121e1080e
2025-02-05 21:15:54.147783 WARNING: fixing_up_incomplete_acquisition (script)
    run_id: b78406a4aa41aa44a756ae50d6ca326f831066d2
2025-02-05 21:18:42.057665    INFO: wait_for_temperature_complete (mgmt)
    elapsed_time: 180.291
    has_secondary_temp_limits: true
    primary_temperature_begin: 30.0585
    primary_temperature_end: 30.3906
    primary_temperature_tolerance: 0.1
    result: CancelledWaitingForTemperature
    secondary_temperature_begin: 28.5688
    secondary_temperature_end: 150
    target_primary_temperature: 31.5
    unstable_time: ************-02-05 21:18:42.059039    INFO: base_device.warn_to_reach_temperature (user_messages)
    timeout: 180
2025-02-05 21:18:42.107024    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-05 21:18:42.440681    INFO: data_acquisition_starting (engine)
    acquisition_run_id: 997626b09f42d8c8d75aaa461cd99953ca828a32
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-05 21:18:42.541403    INFO: start_processing_unblocks_called (mgmt)
2025-02-05 21:18:42.611753    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-05 21:18:42.741652    INFO: data_acquisition_started (engine)
    acquisition_run_id: 997626b09f42d8c8d75aaa461cd99953ca828a32
2025-02-05 21:18:46.672356 WARNING: p2_acquisition_finished_with_errors (device)
    asic_frame_counter_jumps_between_transfers: 0
    asic_frame_counter_jumps_within_transfer: 0
    failed_transfers: 16
    invalid_last_command_values: 0
    pb_corrupted_frames: [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]
    received_frames: 0
    sent_frame_counter_jumps_between_transfers: 0
    sent_frame_counter_jumps_within_transfer: 0
2025-02-05 21:18:46.672522    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 65535mA - 0mA (last: 0mA)
    asic_2_5v: 65535mA - 0mA (last: 0mA)
    ext_ntc_temp: -55°C - 150°C (last: 150°C)
    fpga_temp: 65535°C - 0°C (last: 0°C)
    internal_temp: 65535°C - 0°C (last: 0°C)
2025-02-05 21:18:46.672536    INFO: p2_acquisition_timing_stats (device)
    next_block: 9223372036854775807ns - -9223372036854775808ns
    notify_command: 9223372036854775807ns - -9223372036854775808ns
    submit_transfer: 25859ns - 35372ns (blamed for 15 delays)
    transfer_handled_late_count: 16
    transfer_processing: 80ns - 217324ns
    transpose: 9223372036854775807ns - -9223372036854775808ns
    update_saturation: 9223372036854775807ns - -9223372036854775808ns
    wait_for_transfer: 356ns - 1756ns
2025-02-05 21:18:46.690291   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:18:46.728348   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:18:46.751968    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e87f25c0-a5d2-4d53-bbc7-4dd62ccb009f/metadata
    readers: file_writer@0
             BiasVoltageCol@4294967295
             DeviceTemperat@0
    size: 18060264
2025-02-05 21:18:46.752174    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e87f25c0-a5d2-4d53-bbc7-4dd62ccb009f/commands
    readers: file_writer@0
             DeviceTemperat@0
    size: 4166664
2025-02-05 21:18:46.752336    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e87f25c0-a5d2-4d53-bbc7-4dd62ccb009f/dynamic_analysis
    readers: file_writer@0
    size: 3074056
2025-02-05 21:18:46.752462    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/e87f25c0-a5d2-4d53-bbc7-4dd62ccb009f/channel_state_data_f569dfcd-1594-432d-a0a2-13130f1c4c21
    readers: file_writer@0
             ChannelStatesP@0
    size: 6186262
2025-02-05 21:18:46.752527    INFO: data_acquisition_finished (engine)
    acquisition_run_id: 997626b09f42d8c8d75aaa461cd99953ca828a32
2025-02-05 21:18:46.752592    INFO: stop_processing_unblocks_called (mgmt)
2025-02-05 21:18:46.758702    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"INTERNAL_ERROR"}
2025-02-05 21:18:46.759221 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-05 21:18:46.766350   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:18:46.804433   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:18:47.282759    INFO: protocol_phase_control_ended (rpc)
2025-02-05 21:18:47.629546   ERROR: script_crashed (script)
    exit_code: 1
    run_id: bb27a051-6645-4be9-bcd5-bb94a85e3923
    script_output: /opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py:11: DeprecationWarning:
                   Pyarrow will become a required dependency of pandas in the next major release of pandas (pandas 3.0),
                   (to allow more performant data types, such as the Arrow string type, and better interoperability with other libraries)
                   but was not found to be installed on your system.
                   If this would cause problems for you,
                   please provide us feedback at https://github.com/pandas-dev/pandas/issues/54466

                     import pandas as pd
                   Traceback (most recent call last):
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 338, in <module>
                       @minknow.main
                        ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow/__init__.py", line 50, in main
                       fn(
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 342, in qc_script
                       main(config=args.config, environ=local.environ)
                     File "/opt/ont/minknow/conf/package/shared/modular_qc/modular_qc_script.py", line 165, in main
                       calibrate(device, output_bulk=False, ping_data=True, purpose=device.get_exp_script_purpose())
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/calibrate_device.py", line 116, in calibrate
                       data = promethion_calibration.collect_data(device, config=config)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/calibration/promethion.py", line 55, in collect_data
                       data = collect_continuous_data_current_setup(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_continuous_data.py", line 135, in collect_continuous_data_current_setup
                       raw_data = collect_raw_data(device=device, collection_time_sec=collection_period, calibrated=calibrated)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/bream4/toolkit/procedure_components/data_extraction/collect_raw_data.py", line 36, in collect_raw_data
                       signal_data = get_signal(
                                     ^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/data.py", line 212, in get_signal
                       for msg in connection.data.get_signal_bytes(**kwargs):
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/minknow_api/_support.py", line 57, in __iter__
                       for m in self._message:
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 543, in __next__
                       return self._next()
                              ^^^^^^^^^^^^
                     File "/opt/ont/minknow/ont-python/lib/python3.12/site-packages/grpc/_channel.py", line 969, in _next
                       raise self
                   grpc._channel._MultiThreadedRendezvous: <_MultiThreadedRendezvous of RPC that terminated with:
                   	status = StatusCode.CANCELLED
                   	details = "Acquisition ended"
                   	debug_error_string = "UNKNOWN:Error received from peer ipv4:127.0.0.1:8000 {grpc_message:"Acquisition ended", grpc_status:1, created_time:"2025-02-05T21:18:45.209558335+00:00"}"
                   >
                   WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
                   E0000 00:00:1738790327.272611   16855 init.cc:229] grpc_wait_for_shutdown_with_timeout() timed out.

    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 21:18:48.812692 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 21:18:48.815666    INFO: protocol_finished_script_failure (script)
    error: ExitCode=1
    run_id: bb27a051-6645-4be9-bcd5-bb94a85e3923
2025-02-05 21:18:48.815905    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["997626b09f42d8c8d75aaa461cd99953ca828a32"],"monitor":"protocol_end","script_name":"checks/flowcell_check/platform_qc_RNA4:FLO-PRO...
2025-02-05 21:18:48.816166    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-05 21:18:48.817107   ERROR: failed_to_finish_transition (state_control)
    destination: Error
    observer: MantaControl
    operation_begin: 2025-02-05T21:18:46.710747
    operation_end: 2025-02-05T21:23:46.710747
    source: Running
    stage: transition
2025-02-05 21:18:48.817319   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 21:18:48.817462    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:18:49.017209   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 21:18:49.017406    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:18:49.217240   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 21:18:49.217393    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:18:49.417293   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 21:18:49.417439    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:29:28.043701 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2001
2025-02-05 21:29:28.046380    INFO: create_request_for_disabling_OS_standby (util)
2025-02-05 21:29:28.046736    INFO: sending_telemetry_message (ping)
    data: {"monitor":"protocol_start","mooneye_offload_enabled":false,"protocol_output_dir":"/data/./no_group/no_sample_id/20250205_2129_P2S-02545-A_PAY83684...
2025-02-05 21:29:28.049280    INFO: protocol_started (script)
    flowcell_id: PAY83684
    host_serial_number: GXB03431
    identity: ['']
    library_id:
    output_path: /data/./no_group/no_sample_id/20250205_2129_P2S-02545-A_PAY83684_bb2b9c1c
    output_reads_dir: /data/.
    position_id: P2S-02545-A
    protocol_group_id: no_group
    protocol_output_pattern: {protocol_group_id}/{sample_id}/{start_time}_{device_id}_{flow_cell_id}_{short_protocol_run_id}
    reported_output_path: /data/./no_group/no_sample_id/20250205_2129_P2S-02545-A_PAY83684_bb2b9c1c
    reported_output_reads_dir: /data/.
    run_id: bb2b9c1c-ee16-49d3-802c-3e72c034fb50
    sample_id_to_barcode: []
    script_args: -s conf/package/utility/protocol_selector.py start checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 21:29:28.049655    INFO: disk_space_info (rpc)
    filesystem_id: /
    space_remaining: 18
    unit: GB
2025-02-05 21:29:28.049694    INFO: disk_space_info_user (rpc)
    filesystem_id: /data
    space_remaining: 880
    unit: GB
2025-02-05 21:29:43.130936    INFO: protocol_phase_control_started (rpc)
2025-02-05 21:29:43.131233    INFO: protocol_phase_changed (script)
    phase: PHASE_INITIALISING
2025-02-05 21:29:43.131631    INFO: base_device.wait_for_temperature (user_messages)
    target: 31.5
    timeout: 180
2025-02-05 21:30:14.266938 WARNING: fixing_up_incomplete_acquisition (script)
    run_id: 997626b09f42d8c8d75aaa461cd99953ca828a32
2025-02-05 21:32:43.418910    INFO: wait_for_temperature_complete (mgmt)
    elapsed_time: 180.286
    has_secondary_temp_limits: true
    primary_temperature_begin: 26.4205
    primary_temperature_end: 26.3272
    primary_temperature_tolerance: 0.1
    result: CancelledWaitingForTemperature
    secondary_temperature_begin: 150
    secondary_temperature_end: 150
    target_primary_temperature: 31.5
    unstable_time: ************-02-05 21:32:43.423367    INFO: base_device.warn_to_reach_temperature (user_messages)
    timeout: 180
2025-02-05 21:32:43.582692    INFO: sending_telemetry_message (ping)
    data: {"bulk_enabled":false,"fastq_enabled":false,"monitor":"exp_start","multi_fast5_enabled":false,"pod5_enabled":false}
2025-02-05 21:32:43.998129    INFO: data_acquisition_starting (engine)
    acquisition_run_id: 9b0adbce2857a0790c099e4d5e53c3101c6241c4
    options: allow_file_output=false, enable_analysis=false, generate_reports=false, send_basecalling_metrics=false, send_sequencing_read_metrics=false, generate_final_summary=false
2025-02-05 21:32:44.198439    INFO: saturation_mode_changed (mgmt)
    general_threshold: 10
    software_enabled: true
    software_max_adc: 1900
    software_min_adc: -5
    unblock_threshold: 0
    user_general_threshold: 0
    user_threshold_enabled: false
    user_threshold_max_adc: -32768
    user_threshold_min_adc: 32767
    user_unblock_threshold: 0
2025-02-05 21:32:44.198625    INFO: start_processing_unblocks_called (mgmt)
2025-02-05 21:32:44.398698    INFO: data_acquisition_started (engine)
    acquisition_run_id: 9b0adbce2857a0790c099e4d5e53c3101c6241c4
2025-02-05 21:32:44.422586   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:32:44.460617   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:32:44.498532   ERROR: failed_to_finish_transition (state_control)
    destination: Running
    observer: MantaControl
    operation_begin: 2025-02-05T21:32:44.298514
    operation_end: 2025-02-05T21:34:23.485514
    source: Initialise
    stage: stable
2025-02-05 21:32:44.498615   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:32:44.536657   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:32:44.574759   ERROR: exception_during_observer_callback (state_control)
    callback_mode: loop
    cause: Dynamic exception type: std::system_error
           std::exception::what: Too many recent transfers failed: Transfer timed out

    observer: MantaControl
2025-02-05 21:32:44.598976    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/6c5c690a-8667-42e9-b789-88a1ac015e15/metadata
    readers: file_writer@0
             BiasVoltageCol@0
             DeviceTemperat@0
    size: 18060264
2025-02-05 21:32:44.599167    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/6c5c690a-8667-42e9-b789-88a1ac015e15/commands
    readers: file_writer@0
             DeviceTemperat@0
             SignalDataPump@0
    size: 4166664
2025-02-05 21:32:44.599311    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/6c5c690a-8667-42e9-b789-88a1ac015e15/dynamic_analysis
    readers: file_writer@0
    size: 3074056
2025-02-05 21:32:44.599451    INFO: hyperstream_state_dump (engine)
    newest_data_file: 0.file
    oldest_data_file: 0.file
    path: /data/intermediate/P2S-02545-A/6c5c690a-8667-42e9-b789-88a1ac015e15/channel_state_data_253af3c8-8dd6-4ae7-ba33-a59073272d7f
    readers: file_writer@0
             ChannelStatesP@0
    size: 6186262
2025-02-05 21:32:44.599521    INFO: data_acquisition_finished (engine)
    acquisition_run_id: 9b0adbce2857a0790c099e4d5e53c3101c6241c4
2025-02-05 21:32:44.599587    INFO: stop_processing_unblocks_called (mgmt)
2025-02-05 21:32:44.603042    INFO: sending_telemetry_message (ping)
    data: {"monitor":"exp_stop","stop_reason":"INTERNAL_ERROR"}
2025-02-05 21:32:44.603550 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: ChannelStatesPump
2025-02-05 21:32:44.604634 WARNING: rpc_pump_not_cleaned_up (rpc)
    pump_name: SignalDataPump
2025-02-05 21:32:48.256677 WARNING: p2_acquisition_finished_with_errors (device)
    asic_frame_counter_jumps_between_transfers: 0
    asic_frame_counter_jumps_within_transfer: 0
    failed_transfers: 16
    invalid_last_command_values: 0
    pb_corrupted_frames: [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]
    received_frames: 0
    sent_frame_counter_jumps_between_transfers: 0
    sent_frame_counter_jumps_within_transfer: 0
2025-02-05 21:32:48.256820    INFO: p2_acquisition_device_stats (device)
    asic_1_8v: 65535mA - 0mA (last: 0mA)
    asic_2_5v: 65535mA - 0mA (last: 0mA)
    ext_ntc_temp: -55°C - 150°C (last: 150°C)
    fpga_temp: 65535°C - 0°C (last: 0°C)
    internal_temp: 65535°C - 0°C (last: 0°C)
2025-02-05 21:32:48.256832    INFO: p2_acquisition_timing_stats (device)
    next_block: 9223372036854775807ns - -9223372036854775808ns
    notify_command: 9223372036854775807ns - -9223372036854775808ns
    submit_transfer: 9223372036854775807ns - -9223372036854775808ns
    transfer_handled_late_count: 16
    transfer_processing: 83ns - 67279ns
    transpose: 9223372036854775807ns - -9223372036854775808ns
    update_saturation: 9223372036854775807ns - -9223372036854775808ns
    wait_for_transfer: 329ns - 2275ns
2025-02-05 21:32:50.259639 WARNING: device_command_timed_out (mgmt)
    elapsed_time_ms: 2000
2025-02-05 21:32:50.261875   ERROR: failed_to_finish_transition (state_control)
    destination: Error
    observer: MantaControl
    operation_begin: 2025-02-05T21:32:44.498827
    operation_end: 2025-02-05T21:37:44.498827
    source: Running
    stage: transition
2025-02-05 21:32:50.262053   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 21:32:50.262176    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:32:50.262203    INFO: stop_protocol_requested (script)
    run_id: bb2b9c1c-ee16-49d3-802c-3e72c034fb50
    script_path: checks/flowcell_check/platform_qc_RNA4:FLO-PRO004RA
2025-02-05 21:32:50.267233    INFO: protocol_finished_internal_error (script)
    run_id: bb2b9c1c-ee16-49d3-802c-3e72c034fb50
2025-02-05 21:32:50.267309    INFO: sending_telemetry_message (ping)
    data: {"acquisitions":["9b0adbce2857a0790c099e4d5e53c3101c6241c4"],"monitor":"protocol_end","script_name":"checks/flowcell_check/platform_qc_RNA4:FLO-PRO...
2025-02-05 21:32:50.267446    INFO: clear_request_for_disabling_OS_standby (util)
2025-02-05 21:32:50.461974   ERROR: stopping_protocol_due_to_internal_error (control)
2025-02-05 21:32:50.462167    INFO: stopping_protocol (control)
    exit_reason: ProtocolEnded
2025-02-05 21:32:51.062589    INFO: rpc_error_occurred (rpc)
    details: {
                 "error_message": "Failed to start run: Error in observer 'MantaControl': Too many recent transfers failed: Transfer timed out",
                 "exit_code": -1
             }
    path: /minknow_api.acquisition.AcquisitionService/start
    status: UNKNOWN
    user_identity: ['']
2025-02-05 21:33:49.342391    INFO: flowcell_disconnected (engine)
2025-02-06 20:28:44.414490    INFO: manager_notified_new_auth_token (util)
    expires_in: 89999999770233ns
    token_type: local
2025-02-07 15:38:52.903326    INFO: manager_notified_okta_public_keys_changed (util)
2025-02-07 18:56:50.098806   ERROR: device_error (mgmt)
    exception: /builds/minknow/minknow-core/17867628/.conan/data/boost/1.78.0/nanopore/testing/package/be90984710d149573f5970f1c610249548726070/include/boost/outcome/detail/trait_std_error_code.hpp(96): Throw in function void boost::outcome_v2::policy::outcome_throw_as_system_error_with_payload(const std::error_code&)
               Dynamic exception type: boost::wrapexcept<std::system_error>
               std::exception::what: No such device (it may have been disconnected)

2025-02-07 18:56:50.764156    INFO: manager_notified_hw_state_change (util)
    new_identifier: 2a1d:0091@002:008
    new_state: unavailable
2025-02-07 18:56:50.764326    INFO: active_device_detached (mgmt)
    identifier: 2a1d:0091@002:008
2025-02-07 18:56:50.796700    INFO: removable_device_disconnected (engine)
2025-02-07 19:01:37.956492    INFO: manager_requested_exit (util)
    when: asap
2025-02-07 19:01:38.814964    INFO: exiting (control_server)
2025-02-07 19:01:40.776601    INFO: shutdown_complete (control)
