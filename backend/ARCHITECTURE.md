# Log Visualizer API Architecture

This document describes the architecture of the Log Visualizer API backend, explaining the design decisions and patterns used.

## Overview

The application follows a modular, layered architecture with the following key components:

1. **API Layer**
   - Routes/Endpoints
   - Request/Response Models
   - Dependencies
   - Error Handling

2. **Service Layer**
   - Business Logic
   - Data Processing

3. **Data Layer**
   - Log Analysis
   - Log Parsing

## Directory Structure

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py (slim entry point)
│   ├── config.py (configuration settings)
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── general.py
│   │   │   ├── resource.py
│   │   │   ├── minknow.py (to be implemented)
│   │   │   └── analysis.py (to be implemented)
│   │   ├── dependencies.py (shared endpoint dependencies)
│   │   └── errors.py (exception handlers)
│   ├── models/
│   │   ├── __init__.py
│   │   ├── log_entry.py (existing)
│   │   ├── request/ (API request models)
│   │   └── response/ (API response models)
│   ├── services/ (to be implemented)
│   ├── analysis/ (existing)
│   ├── parsers/ (existing)
│   └── utils/
│       ├── logging.py
│       └── time_utils.py (existing)
```

## Key Design Principles

1. **Separation of Concerns**
   - Each module has a single responsibility
   - Clear boundaries between layers
   - Reduced file sizes with focused components

2. **Dependency Injection**
   - FastAPI's dependency injection system for common operations
   - Centralized error handling
   - Reusable dependencies

3. **Standardized Error Handling**
   - Custom exceptions for domain-specific errors
   - Global exception handlers
   - Consistent error responses

4. **Structured Logging**
   - JSON-formatted logs
   - Consistent log format across the application
   - Performance monitoring

5. **Request/Response Models**
   - Clear API contracts with Pydantic models
   - Input validation
   - Self-documenting API

## API Endpoints

The API is organized into logical groups:

1. **General Endpoints**
   - Health check
   - API status
   - Log parsing

2. **Resource Endpoints**
   - Query resource logs
   - Get resource columns
   - Get specific resource column data

3. **Minknow Endpoints** (to be implemented)
   - Query Minknow logs
   - Get aggregated log data
   - Get positions

4. **Analysis Endpoints** (to be implemented)
   - Log summary
   - Error distribution
   - Timestamp ranges

## Error Handling

Custom exceptions are used to represent domain-specific errors:

- `AnalyzerNotFoundError`: When analyzer is not available
- `AnalyzerProcessingError`: For processing errors
- `InvalidTimestampError`: When timestamp validation fails

These are mapped to HTTP status codes through exception handlers registered with FastAPI.

## Logging

The application uses structured logging with a custom JSONFormatter that produces JSON logs for easier parsing and analysis. Logs include contextual information like:

- Timestamp
- Log level
- Module and function name
- Line number
- Custom context

## Future Improvements

1. **Service Layer Implementation**
   - Extract business logic from route handlers to services
   - Create a clean abstraction over the analyzer

2. **Caching**
   - Implement caching for frequently accessed data
   - Use Redis or in-memory caching

3. **API Versioning**
   - Implement versioning to maintain backward compatibility

4. **Authentication/Authorization**
   - Add security middleware
   - Implement API key authentication

5. **Background Tasks Management**
   - More sophisticated background task queue
   - Task status tracking
