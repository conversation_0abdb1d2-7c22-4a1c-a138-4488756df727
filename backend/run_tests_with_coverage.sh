#!/bin/bash
# Script to run tests with coverage and display results

# Set environment variables for testing
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}==== Running Tests with Coverage ====${NC}"

# Run pytest with coverage
python -m pytest --cov=app --cov-report=term --cov-report=html:coverage_html --cov-report=xml:coverage.xml

# Capture exit code
TEST_EXIT_CODE=$?

# Output results
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ All tests passed!${NC}"
else
    echo -e "${RED}✗ Some tests failed!${NC}"
fi

# Check if coverage directory exists
if [ -d "coverage_html" ]; then
    echo -e "${BLUE}==== Coverage Report ====${NC}"
    echo -e "${YELLOW}HTML Coverage Report:${NC} $(pwd)/coverage_html/index.html"
    echo -e "${YELLOW}XML Coverage Report:${NC} $(pwd)/coverage.xml"

    # Summarize coverage
    echo -e "${BLUE}==== Coverage Summary ====${NC}"
    COV_REPORT=$(python -c "
import xml.etree.ElementTree as ET
tree = ET.parse('coverage.xml')
root = tree.getroot()
line_rate = float(root.get('line-rate', '0')) * 100
print(f'Total coverage: {line_rate:.2f}%')
packages = root.findall('.//package')
for package in packages:
    name = package.get('name')
    line_rate = float(package.get('line-rate', '0')) * 100
    print(f'- {name}: {line_rate:.2f}%')
")
    echo "$COV_REPORT"
else
    echo -e "${RED}Coverage reports were not generated${NC}"
fi

exit $TEST_EXIT_CODE
