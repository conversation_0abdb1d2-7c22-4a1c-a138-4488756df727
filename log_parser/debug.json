(.venv) floy<PERSON>@MacBook-Pro-M1 dash_ui % BASE_LOG_FOLDER=../../examples/logs-P2I-00147 python src/app.py
{"timestamp": "2025-06-11T21:20:57.664021", "level": "WARNING", "message": "No resource log entries found", "module": "resource_analyzer", "function": "_create_dataframe", "line": 40}
{"timestamp": "2025-06-11T21:20:57.664904", "level": "WARNING", "message": "No minknow log entries found", "module": "structured_log_analyzer", "function": "_create_dataframe", "line": 41}
{"timestamp": "2025-06-11T21:20:57.665348", "level": "WARNING", "message": "No minknow log entries found", "module": "action_analyzer", "function": "_create_dataframe", "line": 89}
{"timestamp": "2025-06-11T21:20:57.667702", "level": "INFO", "message": "ENABLE_LOG_PARSER_DEBUG: Enabling log_parser performance tracking", "module": "app", "function": "<module>", "line": 31}
{"timestamp": "2025-06-11T21:20:57.667772", "level": "INFO", "message": "DEBUG_LOGGING_ENABLED: Debug logging enabled at level DEBUG", "module": "__init__", "function": "enable_debug_logging", "line": 45}
[2025-06-11 22:20:57] log_parser.utils.performance - INFO - PARSER_PERF_CONFIG: Set to normal verbosity
{"timestamp": "2025-06-11T21:20:57.667823", "level": "INFO", "message": "PARSER_PERF_CONFIG: Set to normal verbosity", "module": "performance", "function": "set_performance_verbosity", "line": 192}
{"timestamp": "2025-06-11T21:20:57.671361", "level": "INFO", "message": "PERF_START: Overall app startup", "module": "app", "function": "<module>", "line": 260}
{"timestamp": "2025-06-11T21:20:57.671397", "level": "INFO", "message": "Starting App...", "module": "app", "function": "<module>", "line": 261}
{"timestamp": "2025-06-11T21:20:57.671419", "level": "INFO", "message": "PERF_START: AnalyzerContainer creation", "module": "app", "function": "<module>", "line": 265}
{"timestamp": "2025-06-11T21:20:57.671453", "level": "INFO", "message": "PERF_END: AnalyzerContainer creation completed in 0.000s", "module": "app", "function": "<module>", "line": 268}
{"timestamp": "2025-06-11T21:20:57.671475", "level": "INFO", "message": "PERF_START: AnalyzerContainer.initialize()", "module": "app", "function": "<module>", "line": 272}
[2025-06-11 22:20:57] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_INIT_START: Initializing ResourceAnalyzer with 0 entries
{"timestamp": "2025-06-11T21:20:57.671554", "level": "DEBUG", "message": "BASE_ANALYZER_INIT_START: Initializing ResourceAnalyzer with 0 entries", "module": "base_analyzer", "function": "__init__", "line": 32}
{"timestamp": "2025-06-11T21:20:57.671589", "level": "WARNING", "message": "No resource log entries found", "module": "resource_analyzer", "function": "_create_dataframe", "line": 40}
[2025-06-11 22:20:57] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_INIT_END: ResourceAnalyzer initialized with DataFrame shape: (0, 0)
{"timestamp": "2025-06-11T21:20:57.671756", "level": "DEBUG", "message": "BASE_ANALYZER_INIT_END: ResourceAnalyzer initialized with DataFrame shape: (0, 0)", "module": "base_analyzer", "function": "__init__", "line": 35}
[2025-06-11 22:20:57] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_INIT_START: Initializing StructuredLogAnalyzer with 0 entries
{"timestamp": "2025-06-11T21:20:57.671792", "level": "DEBUG", "message": "BASE_ANALYZER_INIT_START: Initializing StructuredLogAnalyzer with 0 entries", "module": "base_analyzer", "function": "__init__", "line": 32}
{"timestamp": "2025-06-11T21:20:57.671816", "level": "WARNING", "message": "No minknow log entries found", "module": "structured_log_analyzer", "function": "_create_dataframe", "line": 41}
[2025-06-11 22:20:57] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_INIT_END: StructuredLogAnalyzer initialized with DataFrame shape: (0, 0)
{"timestamp": "2025-06-11T21:20:57.671923", "level": "DEBUG", "message": "BASE_ANALYZER_INIT_END: StructuredLogAnalyzer initialized with DataFrame shape: (0, 0)", "module": "base_analyzer", "function": "__init__", "line": 35}
[2025-06-11 22:20:57] log_parser.analysis.cross_analyzer - DEBUG - CROSS_ANALYZER_INIT_START: Initializing CrossAnalyzer
{"timestamp": "2025-06-11T21:20:57.671956", "level": "DEBUG", "message": "CROSS_ANALYZER_INIT_START: Initializing CrossAnalyzer", "module": "cross_analyzer", "function": "__init__", "line": 42}
[2025-06-11 22:20:57] log_parser.analysis.cross_analyzer - DEBUG - CROSS_ANALYZER_INIT_END: CrossAnalyzer initialization completed
{"timestamp": "2025-06-11T21:20:57.671982", "level": "DEBUG", "message": "CROSS_ANALYZER_INIT_END: CrossAnalyzer initialization completed", "module": "cross_analyzer", "function": "__init__", "line": 61}
[2025-06-11 22:20:57] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_INIT_START: Initializing ActionAnalyzer with 0 entries
{"timestamp": "2025-06-11T21:20:57.672181", "level": "DEBUG", "message": "BASE_ANALYZER_INIT_START: Initializing ActionAnalyzer with 0 entries", "module": "base_analyzer", "function": "__init__", "line": 32}
{"timestamp": "2025-06-11T21:20:57.672206", "level": "WARNING", "message": "No minknow log entries found", "module": "action_analyzer", "function": "_create_dataframe", "line": 89}
[2025-06-11 22:20:57] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_INIT_END: ActionAnalyzer initialized with DataFrame shape: (0, 0)
{"timestamp": "2025-06-11T21:20:57.672315", "level": "DEBUG", "message": "BASE_ANALYZER_INIT_END: ActionAnalyzer initialized with DataFrame shape: (0, 0)", "module": "base_analyzer", "function": "__init__", "line": 35}
{"timestamp": "2025-06-11T21:20:57.672338", "level": "INFO", "message": "Analyzer container initialized successfully with empty analyzers", "module": "container", "function": "initialize", "line": 68}
{"timestamp": "2025-06-11T21:20:57.672362", "level": "INFO", "message": "PERF_END: AnalyzerContainer.initialize() completed in 0.001s", "module": "app", "function": "<module>", "line": 275}
{"timestamp": "2025-06-11T21:20:57.672384", "level": "INFO", "message": "PERF_START: LogVisualizerApp creation", "module": "app", "function": "<module>", "line": 279}
{"timestamp": "2025-06-11T21:20:57.672405", "level": "INFO", "message": "PERF_START: LogVisualizerApp.__init__", "module": "app", "function": "__init__", "line": 130}
{"timestamp": "2025-06-11T21:20:57.672431", "level": "INFO", "message": "PERF_START: Dash app initialization", "module": "app", "function": "__init__", "line": 149}
{"timestamp": "2025-06-11T21:20:57.681141", "level": "INFO", "message": "PERF_END: Dash app initialization completed in 0.009s", "module": "app", "function": "__init__", "line": 156}
{"timestamp": "2025-06-11T21:20:57.681184", "level": "INFO", "message": "PERF_START: app_layout_setup (call #1)", "module": "app", "function": "wrapper", "line": 110}
{"timestamp": "2025-06-11T21:20:57.681210", "level": "INFO", "message": "PERF_START: Layout setup", "module": "app", "function": "_setup_layout", "line": 202}
{"timestamp": "2025-06-11T21:20:57.682810", "level": "INFO", "message": "PERF_END: Layout setup completed in 0.002s", "module": "app", "function": "_setup_layout", "line": 206}
{"timestamp": "2025-06-11T21:20:57.682847", "level": "INFO", "message": "PERF_END: app_layout_setup completed in 0.002s", "module": "app", "function": "wrapper", "line": 115}
{"timestamp": "2025-06-11T21:20:57.682870", "level": "INFO", "message": "PERF_START: app_callbacks_setup (call #1)", "module": "app", "function": "wrapper", "line": 110}
{"timestamp": "2025-06-11T21:20:57.682891", "level": "INFO", "message": "PERF_START: Callback registration", "module": "app", "function": "_setup_callbacks", "line": 211}
{"timestamp": "2025-06-11T21:20:57.683418", "level": "INFO", "message": "PERF_END: Callback registration completed in 0.001s", "module": "app", "function": "_setup_callbacks", "line": 218}
{"timestamp": "2025-06-11T21:20:57.683446", "level": "INFO", "message": "PERF_END: app_callbacks_setup completed in 0.001s", "module": "app", "function": "wrapper", "line": 115}
{"timestamp": "2025-06-11T21:20:57.683466", "level": "INFO", "message": "PERF_END: LogVisualizerApp.__init__ completed in 0.011s", "module": "app", "function": "__init__", "line": 162}
{"timestamp": "2025-06-11T21:20:57.683486", "level": "INFO", "message": "LOG_PARSER_PERF: Performance summary after app initialization", "module": "app", "function": "__init__", "line": 165}
{"timestamp": "2025-06-11T21:20:57.683506", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 65}
{"timestamp": "2025-06-11T21:20:57.683525", "level": "INFO", "message": "LOG PARSER PERFORMANCE SUMMARY", "module": "app", "function": "print_log_parser_performance", "line": 66}
{"timestamp": "2025-06-11T21:20:57.683544", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 67}
{"timestamp": "2025-06-11T21:20:57.683571", "level": "INFO", "message": "FUNCTION CALL COUNTS:", "module": "app", "function": "print_log_parser_performance", "line": 73}
{"timestamp": "2025-06-11T21:20:57.683590", "level": "INFO", "message": "------------------------------", "module": "app", "function": "print_log_parser_performance", "line": 74}
{"timestamp": "2025-06-11T21:20:57.683613", "level": "INFO", "message": "  register_parser....................      2 calls", "module": "app", "function": "print_log_parser_performance", "line": 76}
{"timestamp": "2025-06-11T21:20:57.683634", "level": "INFO", "message": "PERFORMANCE METRICS:", "module": "app", "function": "print_log_parser_performance", "line": 80}
{"timestamp": "2025-06-11T21:20:57.683655", "level": "INFO", "message": "--------------------------------------------------", "module": "app", "function": "print_log_parser_performance", "line": 81}
{"timestamp": "2025-06-11T21:20:57.683674", "level": "INFO", "message": "Function                  Calls    Avg Time   Avg Mem \u0394    Max Mem \u0394   ", "module": "app", "function": "print_log_parser_performance", "line": 82}
{"timestamp": "2025-06-11T21:20:57.683694", "level": "INFO", "message": "--------------------------------------------------", "module": "app", "function": "print_log_parser_performance", "line": 83}
{"timestamp": "2025-06-11T21:20:57.683716", "level": "INFO", "message": "register_parser           2        0.000      0.0+++++++++ 0.0+++++++++", "module": "app", "function": "print_log_parser_performance", "line": 87}
{"timestamp": "2025-06-11T21:20:57.683736", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 92}
{"timestamp": "2025-06-11T21:20:57.683754", "level": "INFO", "message": "Legend: Avg Time (seconds), Mem \u0394 (MB change)", "module": "app", "function": "print_log_parser_performance", "line": 93}
{"timestamp": "2025-06-11T21:20:57.683794", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 94}
{"timestamp": "2025-06-11T21:20:57.683817", "level": "INFO", "message": "PERF_END: LogVisualizerApp creation completed in 0.011s", "module": "app", "function": "<module>", "line": 282}
{"timestamp": "2025-06-11T21:20:57.683841", "level": "INFO", "message": "PERF_END: Overall app startup completed in 0.013s", "module": "app", "function": "<module>", "line": 285}
{"timestamp": "2025-06-11T21:20:57.683862", "level": "INFO", "message": "PERF_START: Background parsing thread creation", "module": "app", "function": "run", "line": 233}
{"timestamp": "2025-06-11T21:20:57.683979", "level": "INFO", "message": "Starting log parsing in background...", "module": "app", "function": "parse_logs_background", "line": 224}
{"timestamp": "2025-06-11T21:20:57.684002", "level": "INFO", "message": "PERF_START: log_parsing_check_and_parse (call #1)", "module": "app", "function": "wrapper", "line": 110}
{"timestamp": "2025-06-11T21:20:57.684026", "level": "INFO", "message": "Checking for logs in folder: ../../examples/logs-P2I-00147", "module": "app", "function": "_check_and_parse_logs", "line": 179}
{"timestamp": "2025-06-11T21:20:57.684051", "level": "INFO", "message": "Starting container log parsing...", "module": "app", "function": "_check_and_parse_logs", "line": 182}
{"timestamp": "2025-06-11T21:20:57.684203", "level": "INFO", "message": "PERF_END: Background parsing thread started in 0.000s", "module": "app", "function": "run", "line": 238}
{"timestamp": "2025-06-11T21:20:57.684330", "level": "INFO", "message": "PERF_START: Periodic performance summary thread started", "module": "app", "function": "run", "line": 251}
{"timestamp": "2025-06-11T21:20:57.684357", "level": "INFO", "message": "PERF_START: Dash app.run()", "module": "app", "function": "run", "line": 254}
Dash is running on http://127.0.0.1:8050/

 * Serving Flask app 'app'
 * Debug mode: on
{"timestamp": "2025-06-11T21:20:58.073452", "level": "INFO", "message": "Found cached logs: 43209 resource, 51890 minknow", "module": "container", "function": "_load_cached_logs", "line": 378}
{"timestamp": "2025-06-11T21:20:58.073585", "level": "INFO", "message": "PARSING_CACHE_HIT: Loaded cached parsed logs in c6fbbb708268", "module": "container", "function": "parse_logs", "line": 214}
[2025-06-11 22:20:58] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_UPDATE_START: Updating ResourceAnalyzer with 43209 entries
{"timestamp": "2025-06-11T21:20:58.074144", "level": "DEBUG", "message": "BASE_ANALYZER_UPDATE_START: Updating ResourceAnalyzer with 43209 entries", "module": "base_analyzer", "function": "update_data", "line": 45}
{"timestamp": "2025-06-11T21:20:58.084780", "level": "DEBUG", "message": "Found 69 unique columns in resource logs", "module": "resource_analyzer", "function": "_create_dataframe", "line": 51}
{"timestamp": "2025-06-11T21:20:58.085016", "level": "DEBUG", "message": "First 10 columns: ['Disk4FreeBytes', 'Temperature', 'Block0Name', 'Block3ReadBytesPerSecond', 'Block3Saturation', 'MemoryFreeMB', 'PowerDrawWatts', 'MemUsedBytes', 'Block7Saturation', 'Block4WriteBytesPerSecond']", "module": "resource_analyzer", "function": "_create_dataframe", "line": 52}
{"timestamp": "2025-06-11T21:20:58.085099", "level": "DEBUG", "message": "Found 0 unique block devices", "module": "resource_analyzer", "function": "_create_dataframe", "line": 74}
{"timestamp": "2025-06-11T21:20:58.393297", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:20:58.398835", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.006s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:20:58.399279", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:20:58.405535", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:20:58.405611", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.006s", "module": "data", "function": "wrapper", "line": 35}
[2025-06-11 22:20:58] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: (43209, 68)
{"timestamp": "2025-06-11T21:20:58.691410", "level": "DEBUG", "message": "BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: (43209, 68)", "module": "base_analyzer", "function": "update_data", "line": 48}
[2025-06-11 22:20:58] log_parser.utils.performance - INFO - PARSER_PERF_END: update_data completed in 0.617s - Memory: 0.0MB (Δ+0.0MB)
{"timestamp": "2025-06-11T21:20:58.691521", "level": "INFO", "message": "PARSER_PERF_END: update_data completed in 0.617s - Memory: 0.0MB (\u0394+0.0MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:20:58] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_UPDATE_START: Updating StructuredLogAnalyzer with 51890 entries
{"timestamp": "2025-06-11T21:20:58.691563", "level": "DEBUG", "message": "BASE_ANALYZER_UPDATE_START: Updating StructuredLogAnalyzer with 51890 entries", "module": "base_analyzer", "function": "update_data", "line": 45}
{"timestamp": "2025-06-11T21:20:58.703458", "level": "DEBUG", "message": "Found 298 unique keys in minknow logs", "module": "structured_log_analyzer", "function": "_create_dataframe", "line": 55}
{"timestamp": "2025-06-11T21:20:59.348823", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #2)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:20:59.348927", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:20:59.355747", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #2)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:20:59.362435", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:20:59.362479", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.007s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:00.334482", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #3)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:00.340903", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.006s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:00.348187", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #3)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:00.348274", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:21:00.348396", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:01.820815", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #4)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:01.820927", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:01.822316", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #4)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:01.823286", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:21:01.823339", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.001s", "module": "data", "function": "wrapper", "line": 35}
[2025-06-11 22:21:02] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: (51890, 305)
{"timestamp": "2025-06-11T21:21:02.302823", "level": "DEBUG", "message": "BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: (51890, 305)", "module": "base_analyzer", "function": "update_data", "line": 48}
[2025-06-11 22:21:02] log_parser.utils.performance - INFO - PARSER_PERF_END: update_data completed in 3.612s - Memory: 0.0MB (Δ+0.0MB)
{"timestamp": "2025-06-11T21:21:02.304462", "level": "INFO", "message": "PARSER_PERF_END: update_data completed in 3.612s - Memory: 0.0MB (\u0394+0.0MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:02] log_parser.analysis.cross_analyzer - DEBUG - CROSS_ANALYZER_INIT_START: Initializing CrossAnalyzer
{"timestamp": "2025-06-11T21:21:02.304612", "level": "DEBUG", "message": "CROSS_ANALYZER_INIT_START: Initializing CrossAnalyzer", "module": "cross_analyzer", "function": "__init__", "line": 42}
[2025-06-11 22:21:02] log_parser.analysis.cross_analyzer - DEBUG - CROSS_ANALYZER_INIT_END: CrossAnalyzer initialization completed
{"timestamp": "2025-06-11T21:21:02.304728", "level": "DEBUG", "message": "CROSS_ANALYZER_INIT_END: CrossAnalyzer initialization completed", "module": "cross_analyzer", "function": "__init__", "line": 61}
[2025-06-11 22:21:02] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_UPDATE_START: Updating ActionAnalyzer with 51890 entries
{"timestamp": "2025-06-11T21:21:02.304786", "level": "DEBUG", "message": "BASE_ANALYZER_UPDATE_START: Updating ActionAnalyzer with 51890 entries", "module": "base_analyzer", "function": "update_data", "line": 45}
{"timestamp": "2025-06-11T21:21:02.324279", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #5)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:02.324373", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:02.344175", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #5)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:02.344285", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:21:02.350636", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.006s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:03.312471", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #6)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:03.319234", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #6)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:03.338293", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.026s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:03.344491", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:21:03.357173", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.038s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:04.326662", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #7)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:04.326794", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:04.350681", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #7)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:04.356583", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:21:04.362030", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.011s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:05.300237", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #8)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:05.300651", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #8)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:05.301141", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
{"timestamp": "2025-06-11T21:21:05.301804", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.001s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:05.302903", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.003s", "module": "data", "function": "wrapper", "line": 35}
[2025-06-11 22:21:05] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: (51890, 301)
{"timestamp": "2025-06-11T21:21:05.897050", "level": "DEBUG", "message": "BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: (51890, 301)", "module": "base_analyzer", "function": "update_data", "line": 48}
[2025-06-11 22:21:05] log_parser.utils.performance - INFO - PARSER_PERF_END: update_data completed in 3.592s - Memory: 0.0MB (Δ+0.0MB)
{"timestamp": "2025-06-11T21:21:05.897141", "level": "INFO", "message": "PARSER_PERF_END: update_data completed in 3.592s - Memory: 0.0MB (\u0394+0.0MB)", "module": "performance", "function": "wrapper", "line": 117}
{"timestamp": "2025-06-11T21:21:05.897176", "level": "INFO", "message": "Analyzers updated successfully with new log data", "module": "container", "function": "update_analyzers", "line": 103}
{"timestamp": "2025-06-11T21:21:05.897251", "level": "INFO", "message": "Container log parsing completed successfully in 8.213s", "module": "app", "function": "_check_and_parse_logs", "line": 187}
{"timestamp": "2025-06-11T21:21:05.897274", "level": "INFO", "message": "LOG_PARSER_PERF: Performance summary after log parsing", "module": "app", "function": "_check_and_parse_logs", "line": 190}
{"timestamp": "2025-06-11T21:21:05.897308", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 65}
{"timestamp": "2025-06-11T21:21:05.897335", "level": "INFO", "message": "LOG PARSER PERFORMANCE SUMMARY", "module": "app", "function": "print_log_parser_performance", "line": 66}
{"timestamp": "2025-06-11T21:21:05.897712", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 67}
{"timestamp": "2025-06-11T21:21:05.897819", "level": "INFO", "message": "FUNCTION CALL COUNTS:", "module": "app", "function": "print_log_parser_performance", "line": 73}
{"timestamp": "2025-06-11T21:21:05.897862", "level": "INFO", "message": "------------------------------", "module": "app", "function": "print_log_parser_performance", "line": 74}
{"timestamp": "2025-06-11T21:21:05.897903", "level": "INFO", "message": "  clean_metadata_dict................     58 calls", "module": "app", "function": "print_log_parser_performance", "line": 76}
{"timestamp": "2025-06-11T21:21:05.897975", "level": "INFO", "message": "  update_data........................      3 calls", "module": "app", "function": "print_log_parser_performance", "line": 76}
{"timestamp": "2025-06-11T21:21:05.898022", "level": "INFO", "message": "  register_parser....................      2 calls", "module": "app", "function": "print_log_parser_performance", "line": 76}
{"timestamp": "2025-06-11T21:21:05.898099", "level": "INFO", "message": "PERFORMANCE METRICS:", "module": "app", "function": "print_log_parser_performance", "line": 80}
{"timestamp": "2025-06-11T21:21:05.898125", "level": "INFO", "message": "--------------------------------------------------", "module": "app", "function": "print_log_parser_performance", "line": 81}
{"timestamp": "2025-06-11T21:21:05.898147", "level": "INFO", "message": "Function                  Calls    Avg Time   Avg Mem \u0394    Max Mem \u0394   ", "module": "app", "function": "print_log_parser_performance", "line": 82}
{"timestamp": "2025-06-11T21:21:05.898168", "level": "INFO", "message": "--------------------------------------------------", "module": "app", "function": "print_log_parser_performance", "line": 83}
{"timestamp": "2025-06-11T21:21:05.898192", "level": "INFO", "message": "clean_metadata_dict       58       0.000      0.0+++++++++ 0.0+++++++++", "module": "app", "function": "print_log_parser_performance", "line": 87}
{"timestamp": "2025-06-11T21:21:05.898215", "level": "INFO", "message": "update_data               3        2.607      0.0+++++++++ 0.0+++++++++", "module": "app", "function": "print_log_parser_performance", "line": 87}
{"timestamp": "2025-06-11T21:21:05.898239", "level": "INFO", "message": "register_parser           2        0.000      0.0+++++++++ 0.0+++++++++", "module": "app", "function": "print_log_parser_performance", "line": 87}
{"timestamp": "2025-06-11T21:21:05.898275", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 92}
{"timestamp": "2025-06-11T21:21:05.898300", "level": "INFO", "message": "Legend: Avg Time (seconds), Mem \u0394 (MB change)", "module": "app", "function": "print_log_parser_performance", "line": 93}
{"timestamp": "2025-06-11T21:21:05.898324", "level": "INFO", "message": "============================================================", "module": "app", "function": "print_log_parser_performance", "line": 94}
{"timestamp": "2025-06-11T21:21:05.898361", "level": "INFO", "message": "PERF_END: log_parsing_check_and_parse completed in 8.214s", "module": "app", "function": "wrapper", "line": 115}
{"timestamp": "2025-06-11T21:21:05.898444", "level": "INFO", "message": "Background log parsing completed", "module": "app", "function": "parse_logs_background", "line": 227}
{"timestamp": "2025-06-11T21:21:06.297124", "level": "INFO", "message": "Parsing status updated: {'status': 'complete', 'start_time': datetime.datetime(2025, 6, 11, 21, 20, 57, 684070), 'end_time': datetime.datetime(2025, 6, 11, 21, 21, 5, 897217), 'error': None}", "module": "system", "function": "update_system_status", "line": 26}
{"timestamp": "2025-06-11T21:21:06.304629", "level": "INFO", "message": "CALLBACK_PERF_START: update_position_options (call #9)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.305561", "level": "DEBUG", "message": "Getting positions from DataFrame. Empty: False, Columns: Index(['log_level', 'log_event', 'library', 'process_name', 'file_name',\n       'folder_name', 'message', 'key_fastq_enabled',\n       'key_sequencing_summary_enabled', 'key_script_output',\n       ...\n       'key_identifier', 'key_software_min_adc',\n       'key_called_bases_failed_filters', 'key_produced_reads', 'key_seconds',\n       'key_has_invalid_pattern_subs', 'key_bytes_per_second',\n       'key_secondary_temperature_end', 'key_accepted_reads_selected',\n       'key_basecalled_fail_bases_split_simplex'],\n      dtype='object', length=305)", "module": "structured_log_analyzer", "function": "get_positions", "line": 107}
{"timestamp": "2025-06-11T21:21:06.307632", "level": "INFO", "message": "CALLBACK_PERF_START: update_date_pickers (call #9)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.307804", "level": "INFO", "message": "Date picker callback triggered", "module": "data", "function": "update_date_pickers", "line": 172}
[2025-06-11 22:21:06] log_parser.utils.performance - DEBUG - PARSER_PERF_START: get_time_range (call #1) - Memory: 1141.2MB
{"timestamp": "2025-06-11T21:21:06.308636", "level": "DEBUG", "message": "PARSER_PERF_START: get_time_range (call #1) - Memory: 1141.2MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:06] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_TIME_RANGE_START: Getting time range for StructuredLogAnalyzer (DataFrame shape: (51890, 305))
{"timestamp": "2025-06-11T21:21:06.310978", "level": "DEBUG", "message": "BASE_ANALYZER_TIME_RANGE_START: Getting time range for StructuredLogAnalyzer (DataFrame shape: (51890, 305))", "module": "base_analyzer", "function": "get_time_range", "line": 77}
[2025-06-11 22:21:06] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_TIME_RANGE_END: Time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090
{"timestamp": "2025-06-11T21:21:06.311424", "level": "DEBUG", "message": "BASE_ANALYZER_TIME_RANGE_END: Time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090", "module": "base_analyzer", "function": "get_time_range", "line": 101}
[2025-06-11 22:21:06] log_parser.utils.performance - INFO - PARSER_PERF_END: get_time_range completed in 0.004s - Memory: 1141.9MB (Δ+0.7MB)
{"timestamp": "2025-06-11T21:21:06.311669", "level": "INFO", "message": "PARSER_PERF_END: get_time_range completed in 0.004s - Memory: 1141.9MB (\u0394+0.7MB)", "module": "performance", "function": "wrapper", "line": 117}
{"timestamp": "2025-06-11T21:21:06.311849", "level": "INFO", "message": "Retrieved time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090", "module": "data", "function": "update_date_pickers", "line": 184}
{"timestamp": "2025-06-11T21:21:06.311924", "level": "INFO", "message": "CALLBACK_PERF_END: update_date_pickers completed in 0.004s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.313512", "level": "DEBUG", "message": "Found positions: ['', '1A', 'P2I-00147-A', 'P2I-00147-B']", "module": "structured_log_analyzer", "function": "get_positions", "line": 113}
{"timestamp": "2025-06-11T21:21:06.313655", "level": "INFO", "message": "CALLBACK_PERF_END: update_position_options completed in 0.009s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.325663", "level": "INFO", "message": "CALLBACK_PERF_START: update_log_scatter_graph (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.325982", "level": "INFO", "message": "Log scatter graph callback triggered", "module": "data", "function": "update_log_scatter_graph", "line": 926}
{"timestamp": "2025-06-11T21:21:06.326186", "level": "INFO", "message": "DATA_PERF_START: get_log_scatter_data (call #1)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.326294", "level": "INFO", "message": "DATA_PERF_END: get_log_scatter_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.327970", "level": "INFO", "message": "Using bucket frequency: 6h", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 888}
{"timestamp": "2025-06-11T21:21:06.329818", "level": "INFO", "message": "CALLBACK_PERF_START: update_time_range_slider (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.330421", "level": "DEBUG", "message": "Getting positions from DataFrame. Empty: False, Columns: Index(['log_level', 'log_event', 'library', 'process_name', 'file_name',\n       'folder_name', 'message', 'key_fastq_enabled',\n       'key_sequencing_summary_enabled', 'key_script_output',\n       ...\n       'key_identifier', 'key_software_min_adc',\n       'key_called_bases_failed_filters', 'key_produced_reads', 'key_seconds',\n       'key_has_invalid_pattern_subs', 'key_bytes_per_second',\n       'key_secondary_temperature_end', 'key_accepted_reads_selected',\n       'key_basecalled_fail_bases_split_simplex'],\n      dtype='object', length=305)", "module": "structured_log_analyzer", "function": "get_positions", "line": 107}
{"timestamp": "2025-06-11T21:21:06.336322", "level": "INFO", "message": "Time range slider callback triggered", "module": "data", "function": "update_time_range_slider", "line": 1075}
{"timestamp": "2025-06-11T21:21:06.337641", "level": "INFO", "message": "Updated time range slider: min=2025-02-27 15:05:36.857819, max=2025-03-21 20:24:27.317090, full range selected (1919930.4592709541s)", "module": "data", "function": "update_time_range_slider", "line": 1107}
{"timestamp": "2025-06-11T21:21:06.337774", "level": "INFO", "message": "CALLBACK_PERF_END: update_time_range_slider completed in 0.008s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.341084", "level": "DEBUG", "message": "Found positions: ['', '1A', 'P2I-00147-A', 'P2I-00147-B']", "module": "structured_log_analyzer", "function": "get_positions", "line": 113}
{"timestamp": "2025-06-11T21:21:06.341698", "level": "INFO", "message": "CALLBACK_PERF_START: update_log_scatter_graph (call #2)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.343173", "level": "INFO", "message": "CALLBACK_PERF_START: update_time_range_slider (call #2)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.343211", "level": "INFO", "message": "Found positions: ['', '1A', 'P2I-00147-A', 'P2I-00147-B']", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 892}
{"timestamp": "2025-06-11T21:21:06.343242", "level": "INFO", "message": "Log scatter graph callback triggered", "module": "data", "function": "update_log_scatter_graph", "line": 926}
[2025-06-11 22:21:06] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #1) - Memory: 1144.2MB
{"timestamp": "2025-06-11T21:21:06.343473", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #1) - Memory: 1144.2MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:06] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: []
{"timestamp": "2025-06-11T21:21:06.343556", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: []", "module": "base_analyzer", "function": "query", "line": 121}
{"timestamp": "2025-06-11T21:21:06.343626", "level": "INFO", "message": "Time range slider callback triggered", "module": "data", "function": "update_time_range_slider", "line": 1075}
{"timestamp": "2025-06-11T21:21:06.343751", "level": "INFO", "message": "DATA_PERF_START: get_log_scatter_data (call #2)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.343923", "level": "INFO", "message": "Updated time range slider: min=2025-02-27 15:05:36, max=2025-03-21 20:24:27, full range selected (1919931.0s)", "module": "data", "function": "update_time_range_slider", "line": 1107}
{"timestamp": "2025-06-11T21:21:06.343974", "level": "INFO", "message": "DATA_PERF_END: get_log_scatter_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.344068", "level": "INFO", "message": "CALLBACK_PERF_END: update_time_range_slider completed in 0.001s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.347067", "level": "INFO", "message": "Using bucket frequency: 6h", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 888}
{"timestamp": "2025-06-11T21:21:06.347704", "level": "DEBUG", "message": "Getting positions from DataFrame. Empty: False, Columns: Index(['log_level', 'log_event', 'library', 'process_name', 'file_name',\n       'folder_name', 'message', 'key_fastq_enabled',\n       'key_sequencing_summary_enabled', 'key_script_output',\n       ...\n       'key_identifier', 'key_software_min_adc',\n       'key_called_bases_failed_filters', 'key_produced_reads', 'key_seconds',\n       'key_has_invalid_pattern_subs', 'key_bytes_per_second',\n       'key_secondary_temperature_end', 'key_accepted_reads_selected',\n       'key_basecalled_fail_bases_split_simplex'],\n      dtype='object', length=305)", "module": "structured_log_analyzer", "function": "get_positions", "line": 107}
{"timestamp": "2025-06-11T21:21:06.349974", "level": "DEBUG", "message": "Found positions: ['', '1A', 'P2I-00147-A', 'P2I-00147-B']", "module": "structured_log_analyzer", "function": "get_positions", "line": 113}
{"timestamp": "2025-06-11T21:21:06.350062", "level": "INFO", "message": "Found positions: ['', '1A', 'P2I-00147-A', 'P2I-00147-B']", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 892}
[2025-06-11 22:21:06] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #2) - Memory: 1150.7MB
{"timestamp": "2025-06-11T21:21:06.350249", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #2) - Memory: 1150.7MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:06] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: []
{"timestamp": "2025-06-11T21:21:06.350589", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: []", "module": "base_analyzer", "function": "query", "line": 121}
{"timestamp": "2025-06-11T21:21:06.354411", "level": "INFO", "message": "CALLBACK_PERF_START: show_loading_on_slider_change (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.354710", "level": "INFO", "message": "Loading overlay callback triggered", "module": "data", "function": "show_loading_on_slider_change", "line": 125}
{"timestamp": "2025-06-11T21:21:06.354752", "level": "INFO", "message": "Showing loading overlays for data fetch", "module": "data", "function": "show_loading_on_slider_change", "line": 132}
{"timestamp": "2025-06-11T21:21:06.354784", "level": "INFO", "message": "CALLBACK_PERF_END: show_loading_on_slider_change completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.356133", "level": "INFO", "message": "CALLBACK_PERF_START: fetch_all_resource_data (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.356413", "level": "INFO", "message": "Data coordinator callback triggered", "module": "data", "function": "fetch_all_resource_data", "line": 208}
{"timestamp": "2025-06-11T21:21:06.356936", "level": "INFO", "message": "COORDINATOR_START: Fetching all resource data", "module": "data", "function": "fetch_all_resource_data", "line": 223}
{"timestamp": "2025-06-11T21:21:06.358713", "level": "INFO", "message": "DATA_PERF_START: get_memory_data (call #1)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.359253", "level": "INFO", "message": "CALLBACK_PERF_START: handle_slider_constraints_and_locking (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.359296", "level": "INFO", "message": "DATA_PERF_END: get_memory_data completed in 0.001s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.359471", "level": "INFO", "message": "CALLBACK_PERF_END: handle_slider_constraints_and_locking completed in 0.000s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.359850", "level": "INFO", "message": "DATA_PERF_START: get_cpu_load_data (call #1)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.360363", "level": "INFO", "message": "DATA_PERF_END: get_cpu_load_data completed in 0.001s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.360393", "level": "INFO", "message": "DATA_PERF_START: get_disk_capacity_data (call #1)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.360509", "level": "INFO", "message": "DATA_PERF_END: get_disk_capacity_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.361204", "level": "INFO", "message": "DATA_PERF_START: get_io_throughput_data (call #1)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.361266", "level": "INFO", "message": "DATA_PERF_END: get_io_throughput_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.361425", "level": "INFO", "message": "DATA_PERF_START: get_gpu_metrics_data (call #1)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.361515", "level": "INFO", "message": "DATA_PERF_END: get_gpu_metrics_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.361578", "level": "INFO", "message": "DATA_QUERY_START: resource_logs.query_logs (memory)", "module": "log_visualisation", "function": "get_memory_data", "line": 159}
{"timestamp": "2025-06-11T21:21:06.361631", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['MemTotalBytes', 'MemUsedBytes', 'SwapTotalBytes', 'SwapUsedBytes']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:06.367102", "level": "INFO", "message": "CALLBACK_PERF_START: show_loading_on_slider_change (call #2)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.367977", "level": "INFO", "message": "Loading overlay callback triggered", "module": "data", "function": "show_loading_on_slider_change", "line": 125}
{"timestamp": "2025-06-11T21:21:06.368169", "level": "INFO", "message": "Showing loading overlays for data fetch", "module": "data", "function": "show_loading_on_slider_change", "line": 132}
{"timestamp": "2025-06-11T21:21:06.368899", "level": "INFO", "message": "CALLBACK_PERF_END: show_loading_on_slider_change completed in 0.002s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:06.369437", "level": "INFO", "message": "CALLBACK_PERF_START: fetch_all_resource_data (call #2)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.369488", "level": "INFO", "message": "Data coordinator callback triggered", "module": "data", "function": "fetch_all_resource_data", "line": 208}
{"timestamp": "2025-06-11T21:21:06.369536", "level": "INFO", "message": "COORDINATOR_START: Fetching all resource data", "module": "data", "function": "fetch_all_resource_data", "line": 223}
{"timestamp": "2025-06-11T21:21:06.371094", "level": "INFO", "message": "DATA_PERF_START: get_memory_data (call #2)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.372014", "level": "INFO", "message": "DATA_PERF_END: get_memory_data completed in 0.001s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.373436", "level": "INFO", "message": "DATA_PERF_START: get_cpu_load_data (call #2)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.373518", "level": "INFO", "message": "DATA_PERF_END: get_cpu_load_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.373878", "level": "INFO", "message": "DATA_PERF_START: get_disk_capacity_data (call #2)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.374104", "level": "INFO", "message": "DATA_PERF_END: get_disk_capacity_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.374397", "level": "INFO", "message": "DATA_PERF_START: get_io_throughput_data (call #2)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.374431", "level": "INFO", "message": "DATA_PERF_END: get_io_throughput_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.374456", "level": "INFO", "message": "DATA_PERF_START: get_gpu_metrics_data (call #2)", "module": "log_visualisation", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:06.374479", "level": "INFO", "message": "DATA_PERF_END: get_gpu_metrics_data completed in 0.000s", "module": "log_visualisation", "function": "wrapper", "line": 47}
{"timestamp": "2025-06-11T21:21:06.374508", "level": "INFO", "message": "DATA_QUERY_START: resource_logs.query_logs (memory)", "module": "log_visualisation", "function": "get_memory_data", "line": 159}
{"timestamp": "2025-06-11T21:21:06.374839", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['MemTotalBytes', 'MemUsedBytes', 'SwapTotalBytes', 'SwapUsedBytes']", "module": "resource_logs", "function": "query_logs", "line": 21}
[2025-06-11 22:21:07] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:07.454996", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:07] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:07.548249", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:07] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:07.560036", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:07] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 51890/51890 rows
{"timestamp": "2025-06-11T21:21:07.591959", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 51890/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:07] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.249s - Memory: 1642.5MB (Δ+498.3MB)
{"timestamp": "2025-06-11T21:21:07.592121", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.249s - Memory: 1642.5MB (\u0394+498.3MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:07] log_parser.utils.performance - WARNING - PARSER_PERF_MEMORY: query significant memory change +498.3MB - Duration: 1.249s
{"timestamp": "2025-06-11T21:21:07.598271", "level": "WARNING", "message": "PARSER_PERF_MEMORY: query significant memory change +498.3MB - Duration: 1.249s", "module": "performance", "function": "wrapper", "line": 123}
[2025-06-11 22:21:07] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining
{"timestamp": "2025-06-11T21:21:07.693756", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:07] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 51888/51890 rows
{"timestamp": "2025-06-11T21:21:07.706835", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 51888/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:07] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.363s - Memory: 1645.8MB (Δ+495.1MB)
{"timestamp": "2025-06-11T21:21:07.713007", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.363s - Memory: 1645.8MB (\u0394+495.1MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:07] log_parser.utils.performance - WARNING - PARSER_PERF_MEMORY: query significant memory change +495.1MB - Duration: 1.363s
{"timestamp": "2025-06-11T21:21:07.726033", "level": "WARNING", "message": "PARSER_PERF_MEMORY: query significant memory change +495.1MB - Duration: 1.363s", "module": "performance", "function": "wrapper", "line": 123}
[2025-06-11 22:21:08] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #3) - Memory: 1715.0MB
{"timestamp": "2025-06-11T21:21:08.281298", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #3) - Memory: 1715.0MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:08] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: ['folder_name']
{"timestamp": "2025-06-11T21:21:08.287601", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: ['folder_name']", "module": "base_analyzer", "function": "query", "line": 121}
[2025-06-11 22:21:08] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #4) - Memory: 1715.0MB
{"timestamp": "2025-06-11T21:21:08.318061", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #4) - Memory: 1715.0MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:08] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: ['folder_name']
{"timestamp": "2025-06-11T21:21:08.324419", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: ['folder_name']", "module": "base_analyzer", "function": "query", "line": 121}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:09.592161", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:09.598538", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:09.709323", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 19/51890 rows remaining
{"timestamp": "2025-06-11T21:21:09.805997", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 19/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 201}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 19/51890 rows
{"timestamp": "2025-06-11T21:21:09.814917", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 19/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:09] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.551s - Memory: 1734.6MB (Δ+19.6MB)
{"timestamp": "2025-06-11T21:21:09.815129", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.551s - Memory: 1734.6MB (\u0394+19.6MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:09] log_parser.utils.performance - WARNING - PARSER_PERF_MEMORY: query significant memory change +19.6MB - Duration: 1.551s
{"timestamp": "2025-06-11T21:21:09.821364", "level": "WARNING", "message": "PARSER_PERF_MEMORY: query significant memory change +19.6MB - Duration: 1.551s", "module": "performance", "function": "wrapper", "line": 123}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining
{"timestamp": "2025-06-11T21:21:09.846426", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:09] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #5) - Memory: 1734.7MB
{"timestamp": "2025-06-11T21:21:09.848257", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #5) - Memory: 1734.7MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 19/51890 rows remaining
{"timestamp": "2025-06-11T21:21:09.884084", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 19/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 201}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: ['folder_name']
{"timestamp": "2025-06-11T21:21:09.896594", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: ['folder_name']", "module": "base_analyzer", "function": "query", "line": 121}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 19/51890 rows
{"timestamp": "2025-06-11T21:21:09.953180", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 19/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:09] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.656s - Memory: 1734.7MB (Δ+19.6MB)
{"timestamp": "2025-06-11T21:21:09.974222", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.656s - Memory: 1734.7MB (\u0394+19.6MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:09] log_parser.utils.performance - WARNING - PARSER_PERF_MEMORY: query significant memory change +19.6MB - Duration: 1.656s
{"timestamp": "2025-06-11T21:21:09.974271", "level": "WARNING", "message": "PARSER_PERF_MEMORY: query significant memory change +19.6MB - Duration: 1.656s", "module": "performance", "function": "wrapper", "line": 123}
[2025-06-11 22:21:09] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #6) - Memory: 1734.7MB
{"timestamp": "2025-06-11T21:21:09.975969", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #6) - Memory: 1734.7MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:09] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: ['folder_name']
{"timestamp": "2025-06-11T21:21:09.982293", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: ['folder_name']", "module": "base_analyzer", "function": "query", "line": 121}
{"timestamp": "2025-06-11T21:21:10.167634", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:10.187348", "level": "INFO", "message": "DATA_QUERY_END: resource_logs.query_logs (memory) completed in 3.813s", "module": "log_visualisation", "function": "get_memory_data", "line": 171}
{"timestamp": "2025-06-11T21:21:10.201021", "level": "INFO", "message": "DATA_TRANSFORM: memory data transformation completed in 0.001s", "module": "log_visualisation", "function": "get_memory_data", "line": 204}
{"timestamp": "2025-06-11T21:21:10.219599", "level": "INFO", "message": "Processed 4053 memory data points", "module": "log_visualisation", "function": "get_memory_data", "line": 205}
{"timestamp": "2025-06-11T21:21:10.228467", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:10.229043", "level": "INFO", "message": "COORDINATOR_FETCH: memory data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:10.230101", "level": "INFO", "message": "DATA_QUERY_END: resource_logs.query_logs (memory) completed in 3.869s", "module": "log_visualisation", "function": "get_memory_data", "line": 171}
{"timestamp": "2025-06-11T21:21:10.230270", "level": "INFO", "message": "DATA_QUERY_START: resource_logs.query_logs (CPU)", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 248}
{"timestamp": "2025-06-11T21:21:10.231718", "level": "INFO", "message": "DATA_TRANSFORM: memory data transformation completed in 0.001s", "module": "log_visualisation", "function": "get_memory_data", "line": 204}
{"timestamp": "2025-06-11T21:21:10.231873", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['Load1', 'Load5', 'Load15']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:10.232022", "level": "INFO", "message": "Processed 4053 memory data points", "module": "log_visualisation", "function": "get_memory_data", "line": 205}
{"timestamp": "2025-06-11T21:21:10.232799", "level": "INFO", "message": "COORDINATOR_FETCH: memory data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:10.233071", "level": "INFO", "message": "DATA_QUERY_START: resource_logs.query_logs (CPU)", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 248}
{"timestamp": "2025-06-11T21:21:10.233106", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['Load1', 'Load5', 'Load15']", "module": "resource_logs", "function": "query_logs", "line": 21}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:11.329775", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:11.399173", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:11.530208", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining
{"timestamp": "2025-06-11T21:21:11.577966", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 28076/51890 rows remaining
{"timestamp": "2025-06-11T21:21:11.617897", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 28076/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 201}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 28076/51890 rows
{"timestamp": "2025-06-11T21:21:11.662306", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 28076/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:11] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.821s - Memory: 1743.8MB (Δ+9.2MB)
{"timestamp": "2025-06-11T21:21:11.668862", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.821s - Memory: 1743.8MB (\u0394+9.2MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 28076/51890 rows remaining
{"timestamp": "2025-06-11T21:21:11.681263", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 28076/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 201}
[2025-06-11 22:21:11] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 28076/51890 rows
{"timestamp": "2025-06-11T21:21:11.694209", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 28076/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:11] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.718s - Memory: 1743.8MB (Δ+9.1MB)
{"timestamp": "2025-06-11T21:21:11.694318", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.718s - Memory: 1743.8MB (\u0394+9.1MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:12] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #7) - Memory: 1828.7MB
{"timestamp": "2025-06-11T21:21:12.044762", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #7) - Memory: 1828.7MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:12] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: ['folder_name']
{"timestamp": "2025-06-11T21:21:12.070122", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, filters: ['folder_name']", "module": "base_analyzer", "function": "query", "line": 121}
[2025-06-11 22:21:12] log_parser.utils.performance - DEBUG - PARSER_PERF_START: query (call #8) - Memory: 1699.9MB
{"timestamp": "2025-06-11T21:21:12.137776", "level": "DEBUG", "message": "PARSER_PERF_START: query (call #8) - Memory: 1699.9MB", "module": "performance", "function": "wrapper", "line": 65}
[2025-06-11 22:21:12] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: ['folder_name']
{"timestamp": "2025-06-11T21:21:12.137866", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_START: Querying StructuredLogAnalyzer - time range: 2025-02-27 15:05:36.857819 to 2025-03-21 20:24:27.317090, filters: ['folder_name']", "module": "base_analyzer", "function": "query", "line": 121}
{"timestamp": "2025-06-11T21:21:13.282666", "level": "INFO", "message": "Returning 4395 records (total=4395)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:13.283049", "level": "INFO", "message": "DATA_QUERY_END: resource_logs.query_logs (CPU) completed in 3.050s", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 259}
{"timestamp": "2025-06-11T21:21:13.283464", "level": "INFO", "message": "DATA_TRANSFORM: CPU load data transformation completed in 0.000s", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 284}
{"timestamp": "2025-06-11T21:21:13.283495", "level": "INFO", "message": "Processed 4395 CPU load data points", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 285}
{"timestamp": "2025-06-11T21:21:13.283648", "level": "INFO", "message": "COORDINATOR_FETCH: cpu data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:13.283677", "level": "INFO", "message": "Getting resource columns", "module": "resource_logs", "function": "get_columns", "line": 83}
{"timestamp": "2025-06-11T21:21:13.283978", "level": "INFO", "message": "Found 68 resource columns", "module": "resource_logs", "function": "get_columns", "line": 92}
{"timestamp": "2025-06-11T21:21:13.296676", "level": "INFO", "message": "DEBUG: Total available columns: 68", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 329}
{"timestamp": "2025-06-11T21:21:13.296747", "level": "INFO", "message": "DEBUG: Disk-related columns found: ['Disk4FreeBytes', 'Disk3Mountpoint', 'Disk5Mountpoint', 'Disk2FreeBytes', 'Disk3FreeBytes', 'Disk0TotalBytes', 'Disk1TotalBytes', 'Disk2TotalBytes', 'Disk2Mountpoint', 'Disk1Mountpoint', 'Disk5FreeBytes', 'Disk4TotalBytes', 'Disk0Mountpoint', 'Disk4Mountpoint', 'Disk5TotalBytes', 'Disk1FreeBytes', 'Disk3TotalBytes', 'Disk0FreeBytes']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 331}
{"timestamp": "2025-06-11T21:21:13.296828", "level": "INFO", "message": "DEBUG: Disk total columns: ['Disk0TotalBytes', 'Disk1TotalBytes', 'Disk2TotalBytes', 'Disk4TotalBytes', 'Disk5TotalBytes', 'Disk3TotalBytes']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 338}
{"timestamp": "2025-06-11T21:21:13.296867", "level": "INFO", "message": "DEBUG: Disk free columns: ['Disk4FreeBytes', 'Disk2FreeBytes', 'Disk3FreeBytes', 'Disk5FreeBytes', 'Disk1FreeBytes', 'Disk0FreeBytes']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 339}
{"timestamp": "2025-06-11T21:21:13.303090", "level": "INFO", "message": "DEBUG: Disk mount columns: ['Disk3Mountpoint', 'Disk5Mountpoint', 'Disk2Mountpoint', 'Disk1Mountpoint', 'Disk0Mountpoint', 'Disk4Mountpoint']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 340}
{"timestamp": "2025-06-11T21:21:13.303140", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['Disk0TotalBytes', 'Disk1TotalBytes', 'Disk2TotalBytes', 'Disk4TotalBytes', 'Disk5TotalBytes', 'Disk3TotalBytes', 'Disk4FreeBytes', 'Disk2FreeBytes', 'Disk3FreeBytes', 'Disk5FreeBytes', 'Disk1FreeBytes', 'Disk0FreeBytes', 'Disk3Mountpoint', 'Disk5Mountpoint', 'Disk2Mountpoint', 'Disk1Mountpoint', 'Disk0Mountpoint', 'Disk4Mountpoint']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:13.319329", "level": "INFO", "message": "Returning 4395 records (total=4395)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:13.319748", "level": "INFO", "message": "DATA_QUERY_END: resource_logs.query_logs (CPU) completed in 3.089s", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 259}
{"timestamp": "2025-06-11T21:21:13.320309", "level": "INFO", "message": "DATA_TRANSFORM: CPU load data transformation completed in 0.001s", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 284}
{"timestamp": "2025-06-11T21:21:13.320361", "level": "INFO", "message": "Processed 4395 CPU load data points", "module": "log_visualisation", "function": "get_cpu_load_data", "line": 285}
{"timestamp": "2025-06-11T21:21:13.320638", "level": "INFO", "message": "COORDINATOR_FETCH: cpu data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:13.320674", "level": "INFO", "message": "Getting resource columns", "module": "resource_logs", "function": "get_columns", "line": 83}
{"timestamp": "2025-06-11T21:21:13.321209", "level": "INFO", "message": "Found 68 resource columns", "module": "resource_logs", "function": "get_columns", "line": 92}
{"timestamp": "2025-06-11T21:21:13.321249", "level": "INFO", "message": "DEBUG: Total available columns: 68", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 329}
{"timestamp": "2025-06-11T21:21:13.321592", "level": "INFO", "message": "DEBUG: Disk-related columns found: ['Disk4FreeBytes', 'Disk3Mountpoint', 'Disk5Mountpoint', 'Disk2FreeBytes', 'Disk3FreeBytes', 'Disk0TotalBytes', 'Disk1TotalBytes', 'Disk2TotalBytes', 'Disk2Mountpoint', 'Disk1Mountpoint', 'Disk5FreeBytes', 'Disk4TotalBytes', 'Disk0Mountpoint', 'Disk4Mountpoint', 'Disk5TotalBytes', 'Disk1FreeBytes', 'Disk3TotalBytes', 'Disk0FreeBytes']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 331}
{"timestamp": "2025-06-11T21:21:13.321646", "level": "INFO", "message": "DEBUG: Disk total columns: ['Disk0TotalBytes', 'Disk1TotalBytes', 'Disk2TotalBytes', 'Disk4TotalBytes', 'Disk5TotalBytes', 'Disk3TotalBytes']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 338}
{"timestamp": "2025-06-11T21:21:13.321676", "level": "INFO", "message": "DEBUG: Disk free columns: ['Disk4FreeBytes', 'Disk2FreeBytes', 'Disk3FreeBytes', 'Disk5FreeBytes', 'Disk1FreeBytes', 'Disk0FreeBytes']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 339}
{"timestamp": "2025-06-11T21:21:13.321741", "level": "INFO", "message": "DEBUG: Disk mount columns: ['Disk3Mountpoint', 'Disk5Mountpoint', 'Disk2Mountpoint', 'Disk1Mountpoint', 'Disk0Mountpoint', 'Disk4Mountpoint']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 340}
{"timestamp": "2025-06-11T21:21:13.322155", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['Disk0TotalBytes', 'Disk1TotalBytes', 'Disk2TotalBytes', 'Disk4TotalBytes', 'Disk5TotalBytes', 'Disk3TotalBytes', 'Disk4FreeBytes', 'Disk2FreeBytes', 'Disk3FreeBytes', 'Disk5FreeBytes', 'Disk1FreeBytes', 'Disk0FreeBytes', 'Disk3Mountpoint', 'Disk5Mountpoint', 'Disk2Mountpoint', 'Disk1Mountpoint', 'Disk0Mountpoint', 'Disk4Mountpoint']", "module": "resource_logs", "function": "query_logs", "line": 21}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:13.543392", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:13.676689", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied start filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 180}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining
{"timestamp": "2025-06-11T21:21:13.683181", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51890/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 23169/51890 rows remaining
{"timestamp": "2025-06-11T21:21:13.759127", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 23169/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 201}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 23169/51890 rows
{"timestamp": "2025-06-11T21:21:13.800792", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 23169/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:13] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.663s - Memory: 1704.7MB (Δ+4.8MB)
{"timestamp": "2025-06-11T21:21:13.801084", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.663s - Memory: 1704.7MB (\u0394+4.8MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining
{"timestamp": "2025-06-11T21:21:13.824221", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied end filter, 51888/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 187}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 23167/51890 rows remaining
{"timestamp": "2025-06-11T21:21:13.963751", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_FILTER: Applied folder_name filter, 23167/51890 rows remaining", "module": "base_analyzer", "function": "query", "line": 201}
[2025-06-11 22:21:13] log_parser.analysis.base_analyzer - DEBUG - BASE_ANALYZER_QUERY_END: Query completed, returning 23167/51890 rows
{"timestamp": "2025-06-11T21:21:13.976190", "level": "DEBUG", "message": "BASE_ANALYZER_QUERY_END: Query completed, returning 23167/51890 rows", "module": "base_analyzer", "function": "query", "line": 205}
[2025-06-11 22:21:13] log_parser.utils.performance - INFO - PARSER_PERF_END: query completed in 1.938s - Memory: 1704.7MB (Δ-124.0MB)
{"timestamp": "2025-06-11T21:21:13.982563", "level": "INFO", "message": "PARSER_PERF_END: query completed in 1.938s - Memory: 1704.7MB (\u0394-124.0MB)", "module": "performance", "function": "wrapper", "line": 117}
[2025-06-11 22:21:13] log_parser.utils.performance - WARNING - PARSER_PERF_MEMORY: query significant memory change -124.0MB - Duration: 1.938s
{"timestamp": "2025-06-11T21:21:13.982623", "level": "WARNING", "message": "PARSER_PERF_MEMORY: query significant memory change -124.0MB - Duration: 1.938s", "module": "performance", "function": "wrapper", "line": 123}
{"timestamp": "2025-06-11T21:21:14.253768", "level": "INFO", "message": "Processed log scatter data:", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 948}
{"timestamp": "2025-06-11T21:21:14.272800", "level": "INFO", "message": "  - Timestamps: 90", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 949}
{"timestamp": "2025-06-11T21:21:14.274237", "level": "INFO", "message": "  - Positions: 4", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 950}
{"timestamp": "2025-06-11T21:21:14.280696", "level": "INFO", "message": "  - Warning points: 109", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 951}
{"timestamp": "2025-06-11T21:21:14.280728", "level": "INFO", "message": "  - Error points: 18", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 952}
{"timestamp": "2025-06-11T21:21:14.347024", "level": "INFO", "message": "Processed log scatter data:", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 948}
{"timestamp": "2025-06-11T21:21:14.372797", "level": "INFO", "message": "  - Timestamps: 90", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 949}
{"timestamp": "2025-06-11T21:21:14.378704", "level": "INFO", "message": "  - Positions: 4", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 950}
{"timestamp": "2025-06-11T21:21:14.378755", "level": "INFO", "message": "  - Warning points: 109", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 951}
{"timestamp": "2025-06-11T21:21:14.378893", "level": "INFO", "message": "  - Error points: 18", "module": "log_visualisation", "function": "get_log_scatter_data", "line": 952}
{"timestamp": "2025-06-11T21:21:17.507354", "level": "INFO", "message": "Updated log scatter graph with 109 warning points and 18 error points", "module": "data", "function": "update_log_scatter_graph", "line": 1055}
{"timestamp": "2025-06-11T21:21:17.509711", "level": "INFO", "message": "Updated log scatter graph with 109 warning points and 18 error points", "module": "data", "function": "update_log_scatter_graph", "line": 1055}
{"timestamp": "2025-06-11T21:21:17.510117", "level": "INFO", "message": "CALLBACK_PERF_END: update_log_scatter_graph completed in 11.168s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:17.510219", "level": "INFO", "message": "CALLBACK_PERF_END: update_log_scatter_graph completed in 11.185s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:27.475744", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:27.492406", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:27.493198", "level": "INFO", "message": "DEBUG: Found disk indices: [0, 1, 2, 3, 4, 5]", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 381}
{"timestamp": "2025-06-11T21:21:27.493953", "level": "INFO", "message": "DEBUG: Found disk indices: [0, 1, 2, 3, 4, 5]", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 381}
{"timestamp": "2025-06-11T21:21:27.494110", "level": "INFO", "message": "DEBUG: Mount point mapping: {0: '/run', 1: '/', 2: '/run/lock', 3: '/boot', 4: '/boot/efi', 5: '/data'}", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 403}
{"timestamp": "2025-06-11T21:21:27.494213", "level": "INFO", "message": "DEBUG: Mount point mapping: {0: '/run', 1: '/', 2: '/run/lock', 3: '/boot', 4: '/boot/efi', 5: '/data'}", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 403}
{"timestamp": "2025-06-11T21:21:27.494319", "level": "INFO", "message": "DEBUG: Small mounts (disk indices): {2, 4}", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 404}
{"timestamp": "2025-06-11T21:21:27.494409", "level": "INFO", "message": "DEBUG: Small mounts (disk indices): {2, 4}", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 404}
{"timestamp": "2025-06-11T21:21:27.509896", "level": "INFO", "message": "Processed 4053 disk capacity data points for 5 mount groups", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 448}
{"timestamp": "2025-06-11T21:21:27.509941", "level": "INFO", "message": "Mount points: ['/run', '/', '/boot', '/data', 'Other']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 449}
{"timestamp": "2025-06-11T21:21:27.510047", "level": "INFO", "message": "Small mounts grouped into 'Other': ['/run/lock', '/boot/efi']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 452}
{"timestamp": "2025-06-11T21:21:27.513222", "level": "INFO", "message": "Processed 4053 disk capacity data points for 5 mount groups", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 448}
{"timestamp": "2025-06-11T21:21:27.514616", "level": "INFO", "message": "COORDINATOR_FETCH: disk data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:27.514756", "level": "INFO", "message": "Mount points: ['/run', '/', '/boot', '/data', 'Other']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 449}
{"timestamp": "2025-06-11T21:21:27.514842", "level": "INFO", "message": "Getting resource columns", "module": "resource_logs", "function": "get_columns", "line": 83}
{"timestamp": "2025-06-11T21:21:27.514932", "level": "INFO", "message": "Small mounts grouped into 'Other': ['/run/lock', '/boot/efi']", "module": "log_visualisation", "function": "get_disk_capacity_data", "line": 452}
{"timestamp": "2025-06-11T21:21:27.515197", "level": "INFO", "message": "Found 68 resource columns", "module": "resource_logs", "function": "get_columns", "line": 92}
{"timestamp": "2025-06-11T21:21:27.516569", "level": "INFO", "message": "COORDINATOR_FETCH: disk data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:27.516636", "level": "INFO", "message": "DEBUG: Total available columns: 68", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 495}
{"timestamp": "2025-06-11T21:21:27.516674", "level": "INFO", "message": "Getting resource columns", "module": "resource_logs", "function": "get_columns", "line": 83}
{"timestamp": "2025-06-11T21:21:27.516771", "level": "INFO", "message": "DEBUG: Block-related columns found: ['Block0Name', 'Block3ReadBytesPerSecond', 'Block3Saturation', 'Block7Saturation', 'Block4WriteBytesPerSecond', 'Block5WriteBytesPerSecond', 'Block6Saturation', 'Block6WriteBytesPerSecond', 'Block1WriteBytesPerSecond', 'Block6Name', 'Block4ReadBytesPerSecond', 'Block3WriteBytesPerSecond', 'Block6ReadBytesPerSecond', 'Block1Saturation', 'Block5ReadBytesPerSecond', 'Block7WriteBytesPerSecond', 'Block5Name', 'Block1Name', 'Block0ReadBytesPerSecond', 'Block4Saturation', 'Block0Saturation', 'Block2Name', 'Block4Name', 'Block3Name', 'Block2Saturation', 'Block2WriteBytesPerSecond', 'Block5Saturation', 'Block2ReadBytesPerSecond', 'Block1ReadBytesPerSecond', 'Block0WriteBytesPerSecond', 'Block7Name', 'Block7ReadBytesPerSecond']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 497}
{"timestamp": "2025-06-11T21:21:27.516999", "level": "INFO", "message": "Found 68 resource columns", "module": "resource_logs", "function": "get_columns", "line": 92}
{"timestamp": "2025-06-11T21:21:27.517045", "level": "INFO", "message": "DEBUG: Block name columns: ['Block0Name', 'Block6Name', 'Block5Name', 'Block1Name', 'Block2Name', 'Block4Name', 'Block3Name', 'Block7Name']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 504}
{"timestamp": "2025-06-11T21:21:27.517098", "level": "INFO", "message": "DEBUG: Total available columns: 68", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 495}
{"timestamp": "2025-06-11T21:21:27.517131", "level": "INFO", "message": "DEBUG: Block read columns: ['Block3ReadBytesPerSecond', 'Block4ReadBytesPerSecond', 'Block6ReadBytesPerSecond', 'Block5ReadBytesPerSecond', 'Block0ReadBytesPerSecond', 'Block2ReadBytesPerSecond', 'Block1ReadBytesPerSecond', 'Block7ReadBytesPerSecond']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 505}
{"timestamp": "2025-06-11T21:21:27.517187", "level": "INFO", "message": "DEBUG: Block write columns: ['Block4WriteBytesPerSecond', 'Block5WriteBytesPerSecond', 'Block6WriteBytesPerSecond', 'Block1WriteBytesPerSecond', 'Block3WriteBytesPerSecond', 'Block7WriteBytesPerSecond', 'Block2WriteBytesPerSecond', 'Block0WriteBytesPerSecond']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 506}
{"timestamp": "2025-06-11T21:21:27.517269", "level": "INFO", "message": "DEBUG: Block-related columns found: ['Block0Name', 'Block3ReadBytesPerSecond', 'Block3Saturation', 'Block7Saturation', 'Block4WriteBytesPerSecond', 'Block5WriteBytesPerSecond', 'Block6Saturation', 'Block6WriteBytesPerSecond', 'Block1WriteBytesPerSecond', 'Block6Name', 'Block4ReadBytesPerSecond', 'Block3WriteBytesPerSecond', 'Block6ReadBytesPerSecond', 'Block1Saturation', 'Block5ReadBytesPerSecond', 'Block7WriteBytesPerSecond', 'Block5Name', 'Block1Name', 'Block0ReadBytesPerSecond', 'Block4Saturation', 'Block0Saturation', 'Block2Name', 'Block4Name', 'Block3Name', 'Block2Saturation', 'Block2WriteBytesPerSecond', 'Block5Saturation', 'Block2ReadBytesPerSecond', 'Block1ReadBytesPerSecond', 'Block0WriteBytesPerSecond', 'Block7Name', 'Block7ReadBytesPerSecond']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 497}
{"timestamp": "2025-06-11T21:21:27.517376", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['Block0Name', 'Block6Name', 'Block5Name', 'Block1Name', 'Block2Name', 'Block4Name', 'Block3Name', 'Block7Name', 'Block3ReadBytesPerSecond', 'Block4ReadBytesPerSecond', 'Block6ReadBytesPerSecond', 'Block5ReadBytesPerSecond', 'Block0ReadBytesPerSecond', 'Block2ReadBytesPerSecond', 'Block1ReadBytesPerSecond', 'Block7ReadBytesPerSecond', 'Block4WriteBytesPerSecond', 'Block5WriteBytesPerSecond', 'Block6WriteBytesPerSecond', 'Block1WriteBytesPerSecond', 'Block3WriteBytesPerSecond', 'Block7WriteBytesPerSecond', 'Block2WriteBytesPerSecond', 'Block0WriteBytesPerSecond']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:27.517476", "level": "INFO", "message": "DEBUG: Block name columns: ['Block0Name', 'Block6Name', 'Block5Name', 'Block1Name', 'Block2Name', 'Block4Name', 'Block3Name', 'Block7Name']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 504}
{"timestamp": "2025-06-11T21:21:27.517993", "level": "INFO", "message": "DEBUG: Block read columns: ['Block3ReadBytesPerSecond', 'Block4ReadBytesPerSecond', 'Block6ReadBytesPerSecond', 'Block5ReadBytesPerSecond', 'Block0ReadBytesPerSecond', 'Block2ReadBytesPerSecond', 'Block1ReadBytesPerSecond', 'Block7ReadBytesPerSecond']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 505}
{"timestamp": "2025-06-11T21:21:27.518052", "level": "INFO", "message": "DEBUG: Block write columns: ['Block4WriteBytesPerSecond', 'Block5WriteBytesPerSecond', 'Block6WriteBytesPerSecond', 'Block1WriteBytesPerSecond', 'Block3WriteBytesPerSecond', 'Block7WriteBytesPerSecond', 'Block2WriteBytesPerSecond', 'Block0WriteBytesPerSecond']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 506}
{"timestamp": "2025-06-11T21:21:27.518091", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['Block0Name', 'Block6Name', 'Block5Name', 'Block1Name', 'Block2Name', 'Block4Name', 'Block3Name', 'Block7Name', 'Block3ReadBytesPerSecond', 'Block4ReadBytesPerSecond', 'Block6ReadBytesPerSecond', 'Block5ReadBytesPerSecond', 'Block0ReadBytesPerSecond', 'Block2ReadBytesPerSecond', 'Block1ReadBytesPerSecond', 'Block7ReadBytesPerSecond', 'Block4WriteBytesPerSecond', 'Block5WriteBytesPerSecond', 'Block6WriteBytesPerSecond', 'Block1WriteBytesPerSecond', 'Block3WriteBytesPerSecond', 'Block7WriteBytesPerSecond', 'Block2WriteBytesPerSecond', 'Block0WriteBytesPerSecond']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:46.012039", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:46.014204", "level": "INFO", "message": "DEBUG: Found block indices: [0, 1, 2, 3, 4, 5, 6, 7]", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 547}
{"timestamp": "2025-06-11T21:21:46.026831", "level": "INFO", "message": "DEBUG: Block name mapping: {0: 'sda2', 1: 'sda3', 2: 'sda4', 3: 'sda5', 4: 'nvme0n1', 5: 'nvme0n1p1', 6: 'sda', 7: 'sda1'}", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 561}
{"timestamp": "2025-06-11T21:21:46.056303", "level": "INFO", "message": "Active devices: ['nvme0n1p1', 'sda', 'sda1', 'sda2', 'sda3', 'sda5']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 614}
{"timestamp": "2025-06-11T21:21:46.062751", "level": "INFO", "message": "Low-activity devices: ['nvme0n1', 'sda4']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 615}
{"timestamp": "2025-06-11T21:21:46.072724", "level": "INFO", "message": "Processed 4053 I/O throughput data points for 6 active devices", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 649}
{"timestamp": "2025-06-11T21:21:46.078741", "level": "INFO", "message": "Active devices: ['sda1', 'sda3', 'sda5', 'sda', 'sda2', 'nvme0n1p1']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 650}
{"timestamp": "2025-06-11T21:21:46.085099", "level": "INFO", "message": "Low-activity devices grouped into 'Other': ['nvme0n1', 'sda4']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 652}
{"timestamp": "2025-06-11T21:21:46.087825", "level": "INFO", "message": "COORDINATOR_FETCH: io data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:46.089601", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:46.089656", "level": "INFO", "message": "Getting resource columns", "module": "resource_logs", "function": "get_columns", "line": 83}
{"timestamp": "2025-06-11T21:21:46.090667", "level": "INFO", "message": "DEBUG: Found block indices: [0, 1, 2, 3, 4, 5, 6, 7]", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 547}
{"timestamp": "2025-06-11T21:21:46.090936", "level": "INFO", "message": "Found 68 resource columns", "module": "resource_logs", "function": "get_columns", "line": 92}
{"timestamp": "2025-06-11T21:21:46.091065", "level": "INFO", "message": "DEBUG: Block name mapping: {0: 'sda2', 1: 'sda3', 2: 'sda4', 3: 'sda5', 4: 'nvme0n1', 5: 'nvme0n1p1', 6: 'sda', 7: 'sda1'}", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 561}
{"timestamp": "2025-06-11T21:21:46.091134", "level": "INFO", "message": "DEBUG: Total available columns: 68", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 697}
{"timestamp": "2025-06-11T21:21:46.104052", "level": "INFO", "message": "DEBUG: GPU-related columns found: []", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 699}
{"timestamp": "2025-06-11T21:21:46.104100", "level": "INFO", "message": "DEBUG: Utilization columns: ['UtilizationPercent']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 717}
{"timestamp": "2025-06-11T21:21:46.104124", "level": "INFO", "message": "DEBUG: Temperature columns: ['Temperature']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 718}
{"timestamp": "2025-06-11T21:21:46.104144", "level": "INFO", "message": "DEBUG: Power columns: ['PowerDrawWatts']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 719}
{"timestamp": "2025-06-11T21:21:46.104166", "level": "INFO", "message": "DEBUG: Memory total columns: ['MemoryTotalMB']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 720}
{"timestamp": "2025-06-11T21:21:46.104187", "level": "INFO", "message": "DEBUG: Memory free columns: ['MemoryFreeMB']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 721}
{"timestamp": "2025-06-11T21:21:46.108329", "level": "INFO", "message": "Active devices: ['nvme0n1p1', 'sda', 'sda1', 'sda2', 'sda3', 'sda5']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 614}
{"timestamp": "2025-06-11T21:21:46.108356", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['UtilizationPercent', 'Temperature', 'PowerDrawWatts', 'MemoryTotalMB', 'MemoryFreeMB']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:46.108389", "level": "INFO", "message": "Low-activity devices: ['nvme0n1', 'sda4']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 615}
{"timestamp": "2025-06-11T21:21:46.112441", "level": "INFO", "message": "Processed 4053 I/O throughput data points for 6 active devices", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 649}
{"timestamp": "2025-06-11T21:21:46.112470", "level": "INFO", "message": "Active devices: ['sda1', 'sda3', 'sda5', 'sda', 'sda2', 'nvme0n1p1']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 650}
{"timestamp": "2025-06-11T21:21:46.112597", "level": "INFO", "message": "Low-activity devices grouped into 'Other': ['nvme0n1', 'sda4']", "module": "log_visualisation", "function": "get_io_throughput_data", "line": 652}
{"timestamp": "2025-06-11T21:21:46.115495", "level": "INFO", "message": "COORDINATOR_FETCH: io data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:46.115736", "level": "INFO", "message": "Getting resource columns", "module": "resource_logs", "function": "get_columns", "line": 83}
{"timestamp": "2025-06-11T21:21:46.115953", "level": "INFO", "message": "Found 68 resource columns", "module": "resource_logs", "function": "get_columns", "line": 92}
{"timestamp": "2025-06-11T21:21:46.115981", "level": "INFO", "message": "DEBUG: Total available columns: 68", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 697}
{"timestamp": "2025-06-11T21:21:46.116011", "level": "INFO", "message": "DEBUG: GPU-related columns found: []", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 699}
{"timestamp": "2025-06-11T21:21:46.116045", "level": "INFO", "message": "DEBUG: Utilization columns: ['UtilizationPercent']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 717}
{"timestamp": "2025-06-11T21:21:46.116443", "level": "INFO", "message": "DEBUG: Temperature columns: ['Temperature']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 718}
{"timestamp": "2025-06-11T21:21:46.116469", "level": "INFO", "message": "DEBUG: Power columns: ['PowerDrawWatts']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 719}
{"timestamp": "2025-06-11T21:21:46.116604", "level": "INFO", "message": "DEBUG: Memory total columns: ['MemoryTotalMB']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 720}
{"timestamp": "2025-06-11T21:21:46.116632", "level": "INFO", "message": "DEBUG: Memory free columns: ['MemoryFreeMB']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 721}
{"timestamp": "2025-06-11T21:21:46.116670", "level": "INFO", "message": "Querying resource logs: 2025-02-27 15:05:36 to 2025-03-21 20:24:27, columns=['UtilizationPercent', 'Temperature', 'PowerDrawWatts', 'MemoryTotalMB', 'MemoryFreeMB']", "module": "resource_logs", "function": "query_logs", "line": 21}
{"timestamp": "2025-06-11T21:21:50.050599", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:50.054606", "level": "INFO", "message": "Returning 4053 records (total=4053)", "module": "resource_logs", "function": "query_logs", "line": 70}
{"timestamp": "2025-06-11T21:21:50.055002", "level": "INFO", "message": "DEBUG: GPU mapping: {0: 'GPU'}", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 775}
{"timestamp": "2025-06-11T21:21:50.055341", "level": "INFO", "message": "DEBUG: GPU mapping: {0: 'GPU'}", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 775}
{"timestamp": "2025-06-11T21:21:50.057188", "level": "INFO", "message": "Processed 4053 GPU metrics data points for 1 GPUs", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 828}
{"timestamp": "2025-06-11T21:21:50.058917", "level": "INFO", "message": "Processed 4053 GPU metrics data points for 1 GPUs", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 828}
{"timestamp": "2025-06-11T21:21:50.058980", "level": "INFO", "message": "GPUs: ['GPU']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 829}
{"timestamp": "2025-06-11T21:21:50.059288", "level": "INFO", "message": "COORDINATOR_FETCH: gpu data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:50.059363", "level": "INFO", "message": "GPUs: ['GPU']", "module": "log_visualisation", "function": "get_gpu_metrics_data", "line": 829}
{"timestamp": "2025-06-11T21:21:50.059718", "level": "INFO", "message": "COORDINATOR_FETCH: gpu data completed", "module": "data", "function": "fetch_all_data", "line": 256}
{"timestamp": "2025-06-11T21:21:50.060193", "level": "INFO", "message": "COORDINATOR_END: All resource data fetched in 43.691s", "module": "data", "function": "fetch_all_resource_data", "line": 267}
{"timestamp": "2025-06-11T21:21:50.060246", "level": "INFO", "message": "COORDINATOR_CACHE: Cached data with 20607 total data points", "module": "data", "function": "fetch_all_resource_data", "line": 280}
{"timestamp": "2025-06-11T21:21:50.060292", "level": "INFO", "message": "CALLBACK_PERF_END: fetch_all_resource_data completed in 43.691s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:50.091831", "level": "INFO", "message": "COORDINATOR_END: All resource data fetched in 43.703s", "module": "data", "function": "fetch_all_resource_data", "line": 267}
{"timestamp": "2025-06-11T21:21:50.097389", "level": "INFO", "message": "COORDINATOR_CACHE: Cached data with 20607 total data points", "module": "data", "function": "fetch_all_resource_data", "line": 280}
{"timestamp": "2025-06-11T21:21:50.097461", "level": "INFO", "message": "CALLBACK_PERF_END: fetch_all_resource_data completed in 43.741s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:50.160062", "level": "INFO", "message": "CALLBACK_PERF_START: update_memory_graph (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:50.160275", "level": "INFO", "message": "Memory graph callback triggered", "module": "data", "function": "update_memory_graph", "line": 297}
{"timestamp": "2025-06-11T21:21:50.160631", "level": "INFO", "message": "CALLBACK_PLOTTING_START: memory_graph (from cache)", "module": "data", "function": "update_memory_graph", "line": 333}
{"timestamp": "2025-06-11T21:21:50.187602", "level": "INFO", "message": "CALLBACK_PERF_START: update_cpu_load_graph (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:50.187917", "level": "INFO", "message": "CPU load graph callback triggered", "module": "data", "function": "update_cpu_load_graph", "line": 428}
{"timestamp": "2025-06-11T21:21:50.187993", "level": "INFO", "message": "CALLBACK_PLOTTING_START: cpu_load_graph (from cache)", "module": "data", "function": "update_cpu_load_graph", "line": 463}
{"timestamp": "2025-06-11T21:21:50.208330", "level": "INFO", "message": "CALLBACK_PERF_START: update_disk_capacity_graph (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:50.208564", "level": "INFO", "message": "Disk capacity graph callback triggered", "module": "data", "function": "update_disk_capacity_graph", "line": 537}
{"timestamp": "2025-06-11T21:21:50.208600", "level": "INFO", "message": "CALLBACK_PLOTTING_START: disk_capacity_graph (from cache)", "module": "data", "function": "update_disk_capacity_graph", "line": 572}
{"timestamp": "2025-06-11T21:21:50.233149", "level": "INFO", "message": "CALLBACK_PERF_START: update_io_throughput_graph (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:50.233241", "level": "INFO", "message": "I/O throughput graph callback triggered", "module": "data", "function": "update_io_throughput_graph", "line": 645}
{"timestamp": "2025-06-11T21:21:50.233276", "level": "INFO", "message": "CALLBACK_PLOTTING_START: io_throughput_graph (from cache)", "module": "data", "function": "update_io_throughput_graph", "line": 680}
{"timestamp": "2025-06-11T21:21:50.253055", "level": "INFO", "message": "CALLBACK_PERF_START: update_gpu_metrics_graph (call #1)", "module": "data", "function": "wrapper", "line": 30}
{"timestamp": "2025-06-11T21:21:50.253158", "level": "INFO", "message": "GPU metrics graph callback triggered", "module": "data", "function": "update_gpu_metrics_graph", "line": 766}
{"timestamp": "2025-06-11T21:21:50.253191", "level": "INFO", "message": "CALLBACK_PLOTTING_START: gpu_metrics_graph (from cache)", "module": "data", "function": "update_gpu_metrics_graph", "line": 801}
{"timestamp": "2025-06-11T21:21:50.556245", "level": "INFO", "message": "CALLBACK_PLOTTING_END: memory_graph completed in 0.396s", "module": "data", "function": "update_memory_graph", "line": 410}
{"timestamp": "2025-06-11T21:21:50.559050", "level": "INFO", "message": "Updated CPU load graph with 4395 data points", "module": "data", "function": "update_cpu_load_graph", "line": 520}
{"timestamp": "2025-06-11T21:21:50.561556", "level": "INFO", "message": "Updated I/O throughput graph with 4053 data points", "module": "data", "function": "update_io_throughput_graph", "line": 749}
{"timestamp": "2025-06-11T21:21:50.564777", "level": "INFO", "message": "Updated disk capacity graph with 4053 data points", "module": "data", "function": "update_disk_capacity_graph", "line": 628}
{"timestamp": "2025-06-11T21:21:50.564849", "level": "INFO", "message": "Updated memory graph with 4053 data points", "module": "data", "function": "update_memory_graph", "line": 411}
{"timestamp": "2025-06-11T21:21:50.565320", "level": "INFO", "message": "CALLBACK_PERF_END: update_cpu_load_graph completed in 0.378s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:50.571701", "level": "INFO", "message": "CALLBACK_PERF_END: update_io_throughput_graph completed in 0.332s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:50.571760", "level": "INFO", "message": "CALLBACK_PERF_END: update_memory_graph completed in 0.412s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:50.578060", "level": "INFO", "message": "CALLBACK_PERF_END: update_disk_capacity_graph completed in 0.370s", "module": "data", "function": "wrapper", "line": 35}
{"timestamp": "2025-06-11T21:21:50.670968", "level": "INFO", "message": "Updated GPU metrics graph with 4053 data points", "module": "data", "function": "update_gpu_metrics_graph", "line": 908}
{"timestamp": "2025-06-11T21:21:50.671177", "level": "INFO", "message": "CALLBACK_PERF_END: update_gpu_metrics_graph completed in 0.418s", "module": "data", "function": "wrapper", "line": 35}
