from __future__ import annotations

import logging
import os
from abc import ABC, abstractmethod

from .models import LogEntry, QuarantineEntry
from .utils import _track_parser_performance

logger = logging.getLogger(__name__)


class BaseParser(ABC):
    """Abstract base class for all log parsers.
    Defines the interface that all concrete parser implementations must follow.
    """

    @property
    @abstractmethod
    def log_type(self) -> str:
        """Return the type of log this parser handles."""

    @abstractmethod
    def can_parse(self, file_path: str) -> bool:
        """Determine if this parser can parse the given file.

        Args:
            file_path: Path to the file to check

        Returns:
            True if this parser can parse the file, False otherwise
        """

    @abstractmethod
    def parse_file(self, file_path: str, **metadata) -> tuple[list[LogEntry], list[QuarantineEntry]]:
        """Parse a single file and return log entries and quarantine entries.

        Args:
            file_path: Path to the file to parse
            metadata: Additional metadata to attach to log entries

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries
        """

    @_track_parser_performance("parse_directory")
    def parse_directory(self, directory: str) -> tuple[list[LogEntry], list[QuarantineEntry]]:
        """Parse all applicable files in a directory.

        Args:
            directory: Directory containing log files

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries from all files
        """
        logger.debug("BASE_PARSER_DIR_START: Parsing directory %s with %s parser", directory, self.log_type)

        if not os.path.isdir(directory):
            logger.warning("BASE_PARSER_WARNING: Directory does not exist: %s", directory)
            return [], []

        all_entries = []
        all_quarantine = []
        files_found = 0
        files_parsed = 0
        files_skipped = 0

        # Walk through directory and parse all files this parser can handle
        for root, _, files in os.walk(directory):
            for filename in files:
                files_found += 1
                file_path = os.path.join(root, filename)

                if self.can_parse(file_path):
                    logger.debug("BASE_PARSER_DIR_PARSING: Processing file %s", file_path)

                    # Extract metadata from file path
                    relative_path = os.path.relpath(root, directory)
                    folder_name = relative_path if relative_path != "." else ""

                    metadata = {"file_name": filename, "folder_name": folder_name}

                    try:
                        entries, quarantine = self.parse_file(file_path, **metadata)
                        all_entries.extend(entries)
                        all_quarantine.extend(quarantine)
                        files_parsed += 1
                        logger.debug(
                            f"BASE_PARSER_DIR_SUCCESS: Parsed {len(entries)} entries "
                            f"and {len(quarantine)} quarantine entries from {file_path}"
                        )
                    except (OSError, UnicodeDecodeError, ValueError, TypeError):
                        logger.exception("BASE_PARSER_ERROR: Error parsing %s", file_path)
                else:
                    files_skipped += 1
                    logger.debug(f"BASE_PARSER_DIR_SKIP: Skipped file {file_path} (not parseable by {self.log_type} parser)")

        logger.debug(
            f"BASE_PARSER_DIR_END: Directory parsing complete - "
            f"Found: {files_found} files, Parsed: {files_parsed}, "
            f"Skipped: {files_skipped}, Total entries: {len(all_entries)}, "
            f"Total quarantine: {len(all_quarantine)}"
        )
        return all_entries, all_quarantine
