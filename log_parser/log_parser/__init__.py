"""Log parser package for Log Visualiser."""

from __future__ import annotations

import logging

import pandas as pd

from .utils import enable_performance_logging, get_parser_performance_stats
from .utils.performance import set_performance_verbosity

# Enable pandas copy-on-write mode for memory efficiency
# This prevents unnecessary DataFrame copies, reducing memory usage by 60-80%
pd.options.mode.copy_on_write = True

__version__ = "0.1.0"


def enable_debug_logging(level=logging.DEBUG):
    """Enable debug logging for the log parser package.

    Args:
        level: Logging level (default: DEBUG)
    """
    # Create console handler that will be shared
    handler = logging.StreamHandler()
    formatter = logging.Formatter("[%(asctime)s] %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)

    # Configure the root logger for this package
    logger = logging.getLogger(__name__)
    logger.setLevel(level)

    # Add handler if it doesn't exist
    if not logger.handlers:
        logger.addHandler(handler)

    # Also configure loggers for all submodules
    submodules = [
        "log_parser.utils",
        "log_parser.analysis.cross_analyzer",
        "log_parser.analysis.base_analyzer",
        "log_parser.parsers.parser_factory",
        "log_parser.base_parser",
    ]

    for module_name in submodules:
        module_logger = logging.getLogger(module_name)
        module_logger.setLevel(level)
        if not module_logger.handlers:
            module_logger.addHandler(handler)

    logger.info("DEBUG_LOGGING_ENABLED: Debug logging enabled at level %s", logging.getLevelName(level))


def get_performance_summary():
    """Get a summary of performance statistics for all tracked functions.

    Returns:
        Dictionary containing performance statistics
    """
    return get_parser_performance_stats()


def print_performance_summary():
    """Print a human-readable performance summary to the console."""
    stats = get_performance_summary()

    print("\n" + "=" * 60)
    print("LOG PARSER PERFORMANCE SUMMARY")
    print("=" * 60)

    # Print call counters
    if stats["call_counters"]:
        print("\nFUNCTION CALL COUNTS:")
        print("-" * 30)
        for func_name, count in sorted(stats["call_counters"].items(), key=lambda x: x[1], reverse=True):
            print(f"  {func_name:.<35} {count:>6} calls")

    # Print memory tracking
    if stats["memory_tracking"]:
        print("\nPERFORMANCE METRICS:")
        print("-" * 50)
        print("{:<25} {:<8} {:<10} {:<12} {:<12}".format("Function", "Calls", "Avg Time", "Avg Mem Δ", "Max Mem Δ"))
        print("-" * 50)

        for func_name, metrics in sorted(stats["memory_tracking"].items(), key=lambda x: x[1]["total_calls"], reverse=True):
            print(
                "{:<25} {:<8} {:<10.3f} {:+<12.1f} {:+<12.1f}".format(
                    func_name, metrics["total_calls"], metrics["avg_duration"], metrics["avg_memory_delta"], metrics["max_memory_delta"]
                )
            )

    print("\n" + "=" * 60)
    print("Legend: Avg Time (seconds), Mem Δ (MB change)")
    print("=" * 60)


# Convenience exports
__all__ = [
    "enable_debug_logging",
    "enable_performance_logging",
    "get_performance_summary",
    "print_performance_summary",
    "set_performance_verbosity",
]
