"""Data models for log parser components."""

from __future__ import annotations

from datetime import datetime

from pydantic import BaseModel, Field


class LogEntry(BaseModel):
    """Base class for all log entries."""

    timestamp: datetime


class ResourceLogEntry(LogEntry):
    """Model for Resource-System log entries."""

    data: dict
    file_name: str = ""
    folder_name: str = ""


class StructuredLogEntry(LogEntry):
    """Model for Structured log entries."""

    log_level: str = Field(..., description="Log level of the entry")
    log_event: str = Field(..., description="Log event of the entry")
    library: str = Field(..., description="Software Component Library of the entry")
    file_name: str = Field(..., description="Name of the log file containing the entry")
    folder_name: str = Field(..., description="Parent folder of the log file the entry came from")
    process_name: str = Field(..., description="Process name the entry came from")
    keys: dict = Field(..., description="Any additional meta data associated with the entry in key-value format")
    message: str = Field(..., description="The full log message")


class QuarantineEntry(BaseModel):
    """Model for quarantined log entries that couldn't be parsed."""

    file_name: str = Field(..., description="Name of the file containing the quarantined entry")
    line_number: int = Field(..., description="Line number in the file")
    reason: str = Field(..., description="Reason for quarantining the entry")
    content: str = Field(..., description="Content of the quarantined line or record")
    folder_name: str = Field("", description="Folder name (optional)")
    timestamp: datetime = Field(default_factory=datetime.now, description="Timestamp when the entry was quarantined")
