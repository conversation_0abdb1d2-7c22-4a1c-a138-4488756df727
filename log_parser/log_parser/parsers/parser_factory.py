from __future__ import annotations

import logging

from ..utils import _track_parser_performance
from .base_parser import BaseParser
from .resource_parser import ResourceParser
from .structured_log_parser import StructuredLogParser

logger = logging.getLogger(__name__)


class ParserFactory:
    """Factory class for creating and registering log parsers.
    Allows for dynamic registration of parsers and provides a clean way to create
    the appropriate parser for a given log type.
    """

    # Class variable to store registered parsers
    _parsers: dict[str, type[BaseParser]] = {}

    @classmethod
    @_track_parser_performance("register_parser")
    def register_parser(cls, parser_class: type[BaseParser]) -> None:
        """Register a parser class with the factory.

        Args:
            parser_class: The parser class to register
        """
        logger.debug(f"PARSER_FACTORY_REGISTER_START: Registering parser class {parser_class.__name__}")
        parser_instance = parser_class()
        cls._parsers[parser_instance.log_type] = parser_class
        logger.debug(f"PARSER_FACTORY_REGISTER_END: Registered parser for log type: {parser_instance.log_type}")

    @classmethod
    @_track_parser_performance("create_parser")
    def create_parser(cls, log_type: str) -> BaseParser:
        """Create a parser for the specified log type.

        Args:
            log_type: The type of log to create a parser for

        Returns:
            An instance of the appropriate parser

        Raises:
            ValueError: If no parser is registered for the given log type
        """
        logger.debug("PARSER_FACTORY_CREATE_START: Creating parser for log type: %s", log_type)

        if log_type not in cls._parsers:
            logger.error("PARSER_FACTORY_ERROR: No parser registered for log type: %s", log_type)
            raise ValueError(f"No parser registered for log type: {log_type}")

        parser = cls._parsers[log_type]()
        logger.debug(f"PARSER_FACTORY_CREATE_END: Created parser instance of type {parser.__class__.__name__}")
        return parser

    @classmethod
    def get_available_parsers(cls) -> list[str]:
        """Get a list of available parser types.

        Returns:
            List of registered parser types
        """
        available_parsers = list(cls._parsers.keys())
        logger.debug("PARSER_FACTORY_INFO: Available parsers: %s", available_parsers)
        return available_parsers

    @classmethod
    @_track_parser_performance("auto_detect_parser")
    def auto_detect_parser(cls, file_path: str) -> BaseParser | None:
        """Try to automatically detect and create the appropriate parser for a file.

        Args:
            file_path: Path to the file to detect a parser for

        Returns:
            An instance of the appropriate parser, or None if no parser can parse the file
        """
        logger.debug(f"PARSER_FACTORY_AUTO_DETECT_START: Auto-detecting parser for file: {file_path}")

        parsers_tested = 0
        for parser_class in cls._parsers.values():
            parsers_tested += 1
            parser = parser_class()
            logger.debug(f"PARSER_FACTORY_AUTO_DETECT_TEST: Testing parser {parser.log_type} ({parsers_tested}/{len(cls._parsers)})")

            if parser.can_parse(file_path):
                logger.debug(f"PARSER_FACTORY_AUTO_DETECT_SUCCESS: Auto-detected parser {parser.log_type} for file: {file_path}")
                return parser

        logger.debug(f"PARSER_FACTORY_AUTO_DETECT_FAIL: No parser found that can parse file: {file_path} (tested {parsers_tested} parsers)")
        return None


# Register parsers
logger.debug("PARSER_FACTORY_STARTUP: Registering default parsers")
ParserFactory.register_parser(ResourceParser)
ParserFactory.register_parser(StructuredLogParser)
logger.debug("PARSER_FACTORY_STARTUP: Default parsers registered successfully")
