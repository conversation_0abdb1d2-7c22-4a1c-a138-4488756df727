from __future__ import annotations

import json
import logging
import os
import re
from datetime import datetime
from typing import Any

from dateutil import parser as date_parser

from ..models import QuarantineEntry, StructuredLogEntry
from ..utils import clean_metadata_dict
from .base_parser import BaseParser

logger = logging.getLogger(__name__)


class StructuredLogParser(BaseParser):
    """Parser for Structured log files.
    Handles text-based log files from the Structured system.
    """

    # Regex patterns for parsing Structured logs
    _LOG_PATTERN = re.compile(
        r"(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}(?:\.\d+)?)\s+"  # timestamp group
        r"(\w+):\s+"  # log level group
        r"([\w.]+)\s+"  # log_event group (allows dots)
        r"\(([^)]+)\)"  # library group
    )

    # Pattern for libusb logs
    _LIBUSB_LOG_PATTERN = re.compile(
        r"(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}(?:\.\d+)?)\s+"  # timestamp group
        r"(\w+):\s+"  # log level group
        r"libusb:\s+"  # libusb prefix
        r"([^[]+)\s+"  # event/message group
        r"\[([^\]]+)\]"  # library group in brackets
    )

    # Pattern for bream logs
    _BREAM_LOG_PATTERN = re.compile(
        r"\[([^:]+):\s*(\d+)\]\s*-\s*"  # module:line group
        r"(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3})\s*-\s*"  # timestamp group
        r"(\w+)\s*-\s*"  # log level group
        r"(.*)"  # message group
    )

    # Pattern for extracting key-value pairs from log messages
    _KEY_VALUE_PATTERN = re.compile(r"(\w+)[=:]\s*([^,\n]+)(?:,\s*|$)")

    # Pattern for indented message lines that follow a log entry
    _MESSAGE_LINE_PATTERN = re.compile(r"^\s+([^:]+):\s+(.*)$")

    # Pattern for extracting key-value pairs from log messages
    _KEY_VALUE_PATTERN = re.compile(r"(\w+)=([^,]+)(?:,\s*|$)")

    # Pattern for key: value format
    _COLON_KEY_VALUE_PATTERN = re.compile(r"^([^:]+):\s*(.*)$")

    @property
    def log_type(self) -> str:
        return "structured"

    def can_parse(self, file_path: str) -> bool:
        """Check if this parser can parse the given file.

        Args:
            file_path: Path to the file to check

        Returns:
            True if this is a Structured log file, False otherwise
        """
        filename = os.path.basename(file_path)

        # Check if it's a bream log file
        if filename.startswith("bream-") and filename.endswith(".txt"):
            return False

        # Check if it's a Structured log file
        match = re.match(r"(.*)_log-(\d+)\.txt", filename)
        return match is not None and os.path.isfile(file_path)

    def parse_file(self, file_path: str, **metadata) -> tuple[list[StructuredLogEntry], list[QuarantineEntry]]:
        """Parse an Structured log file and return log entries and quarantine entries.

        Args:
            file_path: Path to the file to parse
            metadata: Additional metadata to attach to log entries

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries
        """
        logger.debug("Parsing Structured log file: %s", file_path)

        parsed_data = []
        quarantine = []

        # Extract metadata from filename
        filename = os.path.basename(file_path)
        process_match = re.match(r"(.*)_log-(\d+)\.txt", filename)

        if process_match:
            process_name = process_match.group(1)
        else:
            process_name = "unknown"

        # Clean the metadata
        cleaned_metadata = clean_metadata_dict(metadata)

        try:
            with open(file_path) as f:
                lines = f.readlines()
                total_lines = len(lines)
                line_idx = 0

                while line_idx < total_lines:
                    line = lines[line_idx].strip()
                    line_num = line_idx + 1

                    if not line:  # Skip empty lines
                        line_idx += 1
                        continue

                    # Try different log patterns
                    match = None
                    for pattern in [self._LOG_PATTERN, self._LIBUSB_LOG_PATTERN]:
                        match = pattern.match(line)
                        if match:
                            break

                    if match:
                        try:
                            # Extract groups based on which pattern matched
                            if pattern == self._LOG_PATTERN:
                                timestamp_str, log_level, log_event, library = match.groups()
                                message = line[match.end() :].strip()
                            else:  # LIBUSB pattern
                                timestamp_str, log_level, message, library = match.groups()
                                log_event = "libusb_event"

                            # Parse timestamp
                            try:
                                timestamp = self._parse_timestamp(timestamp_str)
                            except ValueError as e:
                                logger.warning("Invalid timestamp in %s, line %d: %s", file_path, line_num, timestamp_str)
                                error_entry = QuarantineEntry(
                                    file_name=cleaned_metadata.get("file_name", ""),
                                    line_number=line_num,
                                    reason="Invalid timestamp: %s" % str(e),
                                    content=line,
                                    folder_name=cleaned_metadata.get("folder_name", ""),
                                )
                                quarantine.append(error_entry)
                                line_idx += 1
                                continue

                            # Look for indented message lines that follow
                            next_line_idx = line_idx + 1
                            additional_lines = []

                            while (
                                next_line_idx < total_lines
                                and lines[next_line_idx].strip()
                                and self._MESSAGE_LINE_PATTERN.match(lines[next_line_idx])
                            ):
                                additional_lines.append(lines[next_line_idx].strip())
                                next_line_idx += 1

                            # If we found additional lines, update the message
                            if additional_lines:
                                message = message + "\n" + "\n".join(additional_lines)
                                line_idx = next_line_idx - 1

                            # Extract key-value pairs from message
                            keys = self._extract_key_value_pairs(message)

                            # Create log entry
                            entry = StructuredLogEntry(
                                timestamp=timestamp,
                                log_level=log_level,
                                log_event=log_event,
                                library=library,
                                file_name=cleaned_metadata.get("file_name", ""),
                                folder_name=cleaned_metadata.get("folder_name", ""),
                                process_name=process_name,
                                keys=keys,
                                message=message,
                            )
                            parsed_data.append(entry)

                        except (ValueError, TypeError, KeyError, AttributeError) as e:
                            logger.warning("Error parsing line %d in %s: %s", line_num, file_path, str(e))
                            error_entry = QuarantineEntry(
                                file_name=cleaned_metadata.get("file_name", ""),
                                line_number=line_num,
                                reason=str(e),
                                content=line,
                                folder_name=cleaned_metadata.get("folder_name", ""),
                            )
                            quarantine.append(error_entry)

                    else:
                        # Line doesn't match any known pattern
                        error_msg = self._get_pattern_error(line)
                        logger.debug("Line %d in %s doesn't match log pattern: %s", line_num, file_path, error_msg)
                        error_entry = QuarantineEntry(
                            file_name=cleaned_metadata.get("file_name", ""),
                            line_number=line_num,
                            reason="Invalid log format: %s" % error_msg,
                            content=line,
                            folder_name=cleaned_metadata.get("folder_name", ""),
                        )
                        quarantine.append(error_entry)

                    line_idx += 1

        except (OSError, UnicodeDecodeError):
            logger.exception("Error reading file %s", file_path)
            return [], []

        return parsed_data, quarantine

    def parse_directory(self, directory: str) -> tuple[list[StructuredLogEntry], list[QuarantineEntry]]:
        """Parse all applicable files in a directory.

        Args:
            directory: Directory containing log files

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries from all files
        """
        if not os.path.isdir(directory):
            logger.warning("Directory does not exist: %s", directory)
            return [], []

        all_entries = []
        all_quarantine = []

        # Walk through directory and parse all files this parser can handle
        for root, _, files in os.walk(directory):
            for filename in files:
                file_path = os.path.join(root, filename)

                if self.can_parse(file_path):
                    # Extract metadata from file path
                    relative_path = os.path.relpath(root, directory)
                    # If file is in the root directory, categorize as "minknow"
                    folder_name = relative_path if relative_path != "." else "minknow"

                    metadata = {"file_name": filename, "folder_name": folder_name}

                    try:
                        entries, quarantine = self.parse_file(file_path, **metadata)
                    except (OSError, UnicodeDecodeError, ValueError, TypeError):
                        logger.exception("Error parsing %s", file_path)
                    else:
                        all_entries.extend(entries)
                        all_quarantine.extend(quarantine)
                        logger.debug("Parsed %d entries and %d quarantine entries from %s", len(entries), len(quarantine), file_path)

        return all_entries, all_quarantine

    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse a timestamp string into a datetime object."""
        # Try different timestamp formats
        formats = [
            "%Y-%m-%d %H:%M:%S.%f",  # 2023-01-01 12:34:56.789
            "%Y-%m-%d %H:%M:%S",  # 2023-01-01 12:34:56
            "%Y-%m-%dT%H:%M:%S.%f",  # 2023-01-01T12:34:56.789
            "%Y-%m-%dT%H:%M:%S",  # 2023-01-01T12:34:56
        ]

        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue

        # If all formats fail, try dateutil parser as last resort
        try:
            return date_parser.parse(timestamp_str)
        except (ValueError, TypeError, AttributeError):
            # If that fails too, raise an error
            raise ValueError("Unable to parse timestamp: %s" % timestamp_str)

    def _extract_key_value_pairs(self, message: str) -> dict[str, Any]:
        """Extract key-value pairs from a log message."""
        result = {}

        # Try JSON format first (some messages are JSON)
        if message.strip().startswith("{") and message.strip().endswith("}"):
            try:
                return json.loads(message)
            except json.JSONDecodeError:
                pass

        # Check for newline-separated key: value pairs
        if "\n" in message:
            for line in message.split("\n"):
                line = line.strip()
                if not line:
                    continue

                # Try to match key: value format
                colon_match = self._COLON_KEY_VALUE_PATTERN.match(line)
                if colon_match:
                    key, value = colon_match.groups()
                    result[key.strip()] = self._convert_value(value)
                    continue

                # Fall back to key=value format for this line
                for match in self._KEY_VALUE_PATTERN.finditer(line):
                    key, value = match.groups()
                    result[key] = self._convert_value(value)
        else:
            # Fall back to regex-based extraction for key=value pairs
            for match in self._KEY_VALUE_PATTERN.finditer(message):
                key, value = match.groups()
                # Try to convert to appropriate type
                result[key] = self._convert_value(value)

        return result

    def _convert_value(self, value: str) -> Any:
        """Convert a string value to the appropriate type."""
        value = value.strip()

        # Check for boolean values
        if value.lower() in ("true", "yes"):
            return True
        if value.lower() in ("false", "no"):
            return False

        # Check for None values
        if value.lower() in ("none", "null"):
            return None

        # Try numeric conversion
        try:
            if "." in value:
                return float(value)
            return int(value)
        except ValueError:
            # Check if it's a file path (contains slashes)
            if "/" in value or "\\" in value:
                return value

            # Return as string if not numeric or file path
            return value

    def _get_pattern_error(self, line: str) -> str:
        """Analyze why a line failed to match the log pattern.

        Args:
            line: The line that failed to match

        Returns:
            A descriptive error message
        """
        # Check timestamp format
        timestamp_match = re.match(r"^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}(?:\.\d+)?", line)
        if not timestamp_match:
            return "Invalid timestamp format. Expected format: YYYY-MM-DD HH:MM:SS.microseconds or YYYY-MM-DDTHH:MM:SS.microseconds"

        # Check log level format
        after_timestamp = line[timestamp_match.end() :].strip()
        if not after_timestamp:
            return "Missing log level after timestamp"

        log_level_match = re.match(r"^(\w+):", after_timestamp)
        if not log_level_match:
            return "Invalid log level format. Expected format: LEVEL:"

        # Check if it's a libusb log
        after_level = after_timestamp[log_level_match.end() :].strip()
        if "libusb:" in after_level:
            # Check libusb format
            if not after_level.startswith("libusb:"):
                return "Invalid libusb format. Expected: libusb:"

            after_libusb = after_level[7:].strip()
            if not after_libusb:
                return "Missing event/message after libusb:"

            # Check for library in brackets
            if not after_libusb.endswith("]"):
                return "Library name must be enclosed in square brackets"

            if "[" not in after_libusb:
                return "Missing library name in square brackets"

            return "Invalid libusb format. Expected: libusb: event [library]"

        # Check standard format
        event_match = re.match(r"^([\w.]+)\s+\(", after_level)
        if not event_match:
            return "Invalid log event format. Expected format: EVENT (library). Event can contain dots."

        # Check library format
        after_event = after_level[event_match.end() :].strip()
        if not after_event:
            return "Missing library name in parentheses"

        if not after_event.endswith(")"):
            return "Library name must be enclosed in parentheses"

        return "Unknown format error"
