from __future__ import annotations

import logging
import os
from abc import ABC, abstractmethod

from ..models import LogEntry, QuarantineEntry

logger = logging.getLogger(__name__)


class BaseParser(ABC):
    """Abstract base class for all log parsers.
    Defines the interface that all concrete parser implementations must follow.
    """

    @property
    @abstractmethod
    def log_type(self) -> str:
        """Return the type of log this parser handles."""

    @abstractmethod
    def can_parse(self, file_path: str) -> bool:
        """Determine if this parser can parse the given file.

        Args:
            file_path: Path to the file to check

        Returns:
            True if this parser can parse the file, False otherwise
        """

    @abstractmethod
    def parse_file(self, file_path: str, **metadata) -> tuple[list[LogEntry], list[QuarantineEntry]]:
        """Parse a single file and return log entries and quarantine entries.

        Args:
            file_path: Path to the file to parse
            metadata: Additional metadata to attach to log entries

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries
        """

    def parse_directory(self, directory: str) -> tuple[list[LogEntry], list[QuarantineEntry]]:
        """Parse all applicable files in a directory.

        Args:
            directory: Directory containing log files

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries from all files
        """
        if not os.path.isdir(directory):
            logger.warning("Directory does not exist: %s", directory)
            return [], []

        all_entries = []
        all_quarantine = []

        # Walk through directory and parse all files this parser can handle
        for root, _, files in os.walk(directory):
            for filename in files:
                file_path = os.path.join(root, filename)

                if self.can_parse(file_path):
                    # Extract metadata from file path
                    relative_path = os.path.relpath(root, directory)
                    folder_name = relative_path if relative_path != "." else ""

                    metadata = {"file_name": filename, "folder_name": folder_name}

                    try:
                        entries, quarantine = self.parse_file(file_path, **metadata)
                    except Exception:
                        logger.exception("Error parsing %s", file_path)
                    else:
                        all_entries.extend(entries)
                        all_quarantine.extend(quarantine)
                        logger.debug(f"Parsed {len(entries)} entries and {len(quarantine)} quarantine entries from {file_path}")

        return all_entries, all_quarantine
