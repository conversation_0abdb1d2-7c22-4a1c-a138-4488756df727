from __future__ import annotations

import csv
import logging
import os
import re
from datetime import datetime
from typing import Any

from dateutil import parser as date_parser

from ..models import LogEntry, QuarantineEntry, ResourceLogEntry
from ..utils.metadata_utils import clean_metadata_dict
from .base_parser import BaseParser

logger = logging.getLogger(__name__)


class ResourceParser(BaseParser):
    """Parser for Resource-System log files.
    Handles CSV-formatted log files containing system resource metrics.
    """

    @property
    def log_type(self) -> str:
        return "resource"

    def can_parse(self, file_path: str) -> bool:
        """Check if this parser can parse the given file.

        Args:
            file_path: Path to the file to check

        Returns:
            True if this is a resource log file, False otherwise
        """
        filename = os.path.basename(file_path)
        return filename.startswith("resource-system") and filename.endswith(".log") and os.path.isfile(file_path)

    def parse_file(self, file_path: str, **metadata) -> tuple[list[LogEntry], list[QuarantineEntry]]:
        """Parse a Resource log file and return log entries and quarantine entries.

        Args:
            file_path: Path to the file to parse
            metadata: Additional metadata to attach to log entries

        Returns:
            Tuple containing a list of parsed log entries and a list of quarantine entries
        """
        logger.debug("Parsing Resource log file: %s", file_path)

        parsed_data = []
        quarantine = []

        # Clean the metadata
        cleaned_metadata = clean_metadata_dict(metadata)

        try:
            with open(file_path) as f:
                reader = csv.reader(f)
                headers = next(reader, [])

                if not headers:
                    logger.warning("Empty file: %s", file_path)
                    return [], []

                # Get expected column count from headers
                expected_column_count = len(headers)

                # Process each row
                for row_num, row in enumerate(reader, start=2):
                    try:
                        # Skip empty rows
                        if not row or all(cell.strip() == "" for cell in row):
                            continue

                        # Parse timestamp from first column
                        timestamp_str = row[0]
                        try:
                            timestamp = self._parse_timestamp(timestamp_str)
                        except ValueError as e:
                            logger.warning(f"Invalid timestamp in {file_path}, row {row_num}: {timestamp_str}")
                            error_entry = QuarantineEntry(
                                file_name=cleaned_metadata.get("file_name", ""),
                                line_number=row_num,
                                reason=f"Invalid timestamp: {e!s}",
                                content=",".join(row),
                                folder_name=cleaned_metadata.get("folder_name", ""),
                            )
                            quarantine.append(error_entry)
                            continue

                        # Handle rows with different column counts by padding with None
                        if len(row) != expected_column_count:
                            logger.debug(f"Row {row_num} in {file_path} has {len(row)} columns, expected {expected_column_count}")

                            # If fewer columns, add None values for missing columns
                            if len(row) < expected_column_count:
                                row = row + [None] * (expected_column_count - len(row))
                            # If more columns, truncate to expected size
                            elif len(row) > expected_column_count:
                                row = row[:expected_column_count]

                        # Create a dictionary of column values
                        data = {}
                        for i, header in enumerate(headers):
                            try:
                                value = self._parse_value(row[i], header) if i < len(row) else None
                                data[header] = value
                            except (ValueError, TypeError, AttributeError) as e:
                                logger.warning(f"Error parsing value for column {header} in row {row_num}: {e!s}")
                                data[header] = None

                        # Clean the data dictionary
                        cleaned_data = clean_metadata_dict(data)

                        # Create a normalized entry
                        entry = ResourceLogEntry(
                            timestamp=timestamp,
                            file_name=cleaned_metadata.get("file_name", ""),
                            folder_name=cleaned_metadata.get("folder_name", ""),
                            data=cleaned_data,
                        )

                        parsed_data.append(entry)

                    except (ValueError, TypeError, AttributeError, IndexError) as e:
                        logger.exception("Error parsing row %d in %s", row_num, file_path)
                        error_entry = QuarantineEntry(
                            file_name=cleaned_metadata.get("file_name", ""),
                            line_number=row_num,
                            reason=f"Parse error: {e!s}",
                            content=",".join(row),
                            folder_name=cleaned_metadata.get("folder_name", ""),
                        )
                        quarantine.append(error_entry)

        except (OSError, UnicodeDecodeError, csv.Error) as e:
            logger.exception("Error parsing Resource log file %s", file_path)
            error_entry = QuarantineEntry(
                file_name=cleaned_metadata.get("file_name", ""),
                line_number=0,
                reason=f"File error: {e!s}",
                content=f"File: {file_path}",
                folder_name=cleaned_metadata.get("folder_name", ""),
            )
            quarantine.append(error_entry)
        else:
            logger.info("Parsed %d entries from %s, %d errors", len(parsed_data), file_path, len(quarantine))

        return parsed_data, quarantine

    def _get_csv_reader(self, csvfile, sample_content: str) -> csv.reader:
        """Get a CSV reader with appropriate dialect detection and fallback.
        Uses multiple strategies to handle different resource log CSV formats.
        """
        # First try with csv.Sniffer to detect the dialect
        try:
            dialect = csv.Sniffer().sniff(sample_content)
            return csv.reader(csvfile, dialect)
        except (ValueError, TypeError, csv.Error) as e:
            logger.warning("Failed to determine CSV dialect, using default: %s", str(e))

            # If sniffer fails, try to determine the format from content inspection

            # Check if first line ends with newline and second line starts with timestamp format
            lines = sample_content.split("\n")
            if len(lines) >= 2:
                # Check if it's a standard CSV with a header row and data rows
                if self._is_valid_timestamp(lines[1].split(",")[0]):
                    logger.info("Detected standard resource log format with timestamp in first column")
                    return csv.reader(csvfile, csv.excel)  # Standard CSV with comma delimiter

            # If comma exists in the first line, assume comma-separated values
            if "," in sample_content.split("\n")[0]:
                logger.info("Using comma as delimiter based on content inspection")
                return csv.reader(csvfile, delimiter=",")

            # Last resort: use the default CSV reader with comma delimiter
            logger.info("Falling back to default CSV reader")
            return csv.reader(csvfile)

    def _is_valid_timestamp(self, timestamp_str: str) -> bool:
        """Check if a string could be a valid timestamp.
        Returns True if it matches expected timestamp format patterns.
        """
        if not timestamp_str:
            return False

        # First try regex patterns for common timestamp formats
        patterns = [
            r"\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}",  # 2023-01-01 12:34:56
            r"\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}\.\d+",  # 2023-01-01 12:34:56.789
            r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}",  # 2023-01-01T12:34:56
            r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+",  # 2023-01-01T12:34:56.789
        ]

        for pattern in patterns:
            if re.match(pattern, timestamp_str):
                return True

        # If regex fails, try date parser as a more flexible fallback
        try:
            # Attempt to parse using dateutil parser
            date_parser.parse(timestamp_str)
            return True
        except (ValueError, TypeError, AttributeError):
            pass

        return False

    def _normalize_header(self, header: str) -> str:
        """Normalize a header name by removing special characters and standardizing format."""
        # Strip whitespace and quotes
        normalized = header.strip().strip("\"'")

        # Replace spaces with underscores
        normalized = re.sub(r"\s+", "_", normalized)

        # Remove any other special characters
        normalized = re.sub(r"[^\w\[\]\.]+", "", normalized)

        return normalized

    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse a timestamp string into a datetime object."""
        # Try different timestamp formats
        formats = [
            "%Y-%m-%d %H:%M:%S.%f",  # 2023-01-01 12:34:56.789
            "%Y-%m-%d %H:%M:%S",  # 2023-01-01 12:34:56
            "%Y-%m-%dT%H:%M:%S.%f",  # 2023-01-01T12:34:56.789
            "%Y-%m-%dT%H:%M:%S",  # 2023-01-01T12:34:56
        ]

        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue

        # If all formats fail, try dateutil parser as last resort
        try:
            return date_parser.parse(timestamp_str)
        except (ValueError, TypeError, AttributeError):
            # If that fails too, raise an error
            raise ValueError(f"Unable to parse timestamp: {timestamp_str}")

    def _parse_value(self, value_str: str, header: str) -> Any:
        """Parse a string value based on the header type."""
        if value_str is None or not value_str or value_str.lower() in ["null", "none", "na", ""]:
            return None

        # Try to determine the type based on the header name
        if header.lower().endswith(("percentage", "percent", "ratio")):
            try:
                # Parse as float and ensure it's a percentage
                value = float(value_str.strip("%"))
                return value
            except (ValueError, TypeError):
                return value_str

        elif header.lower().endswith(("count", "number", "total", "size")):
            try:
                # Parse as integer
                return int(value_str)
            except (ValueError, TypeError):
                try:
                    # Try as float if int fails
                    return float(value_str)
                except (ValueError, TypeError):
                    return value_str

        elif header.lower().endswith(("rate", "speed", "bytespersecond")):
            try:
                # Parse as float
                return float(value_str)
            except (ValueError, TypeError):
                return value_str

        else:
            # Try numeric values first
            try:
                if isinstance(value_str, str) and "." in value_str:
                    return float(value_str)
                return int(value_str)
            except (ValueError, TypeError):
                # Return as string if not numeric
                return value_str
