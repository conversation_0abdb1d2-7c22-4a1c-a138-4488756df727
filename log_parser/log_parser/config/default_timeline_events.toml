[[timeline_event]]
name = "protocol"
start_event = "protocol_started"
end_event = "protocol_finished_successfully"
identifier_key = "run_id"
children = ["data_acquisition"]
markers = ["protocol_phase", "script_crashed"]

[[timeline_event]]
name = "data_acquisition"
start_event = "data_acquisition_starting"
end_event = "data_acquisition_finished"
identifier_key = "acquisition_run_id"
children = []

[[timeline_event]]
name = "control_server_lifecycle"
start_event = "starting_up"
end_event = "shutdown_complete"
identifier_key = ""  # Empty string means positional matching
process_filter = "control_server"
children = []

[[timeline_event]]
name = "basecaller_lifecycle"
start_event = "starting_up"
end_event = "shutdown_complete"
identifier_key = ""  # Empty string means positional matching
process_filter = "basecall_manager"
children = []

[[timeline_event]]
name = "mk_manager_lifecycle"
start_event = "mk_manager_starting"
end_event = "mk_manager_shut_down"
identifier_key = ""  # Empty string means positional matching
process_filter = "mk_manager_svc"
children = []

# Marker definitions
[[marker]]
name = "protocol_phase"
log_event = "protocol_phase_changed"
process_filter = "control_server"

[[marker]]
name = "script_crashed"
log_event = "script_crashed"
process_filter = "control_server"

[[marker]]
name = "script_restart"
log_event = "script_crashed_restarting"
process_filter = "control_server"

[[marker]]
name = "script_pause_device_error"
log_event = "protocol_paused_due_to_device_error"
process_filter = "control_server"

[[marker]]
name = "script_pause_fc_disconnect"
log_event = "protocol_paused_due_to_flow_cell_disconnection"
process_filter = "control_server"
