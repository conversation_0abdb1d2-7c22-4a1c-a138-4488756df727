"""Timestamp Utility Functions

This module provides standard utility functions for timestamp handling across the application.
It implements the project's timestamp standardization pattern:
- Storage Layer: Python datetime
- Analysis Layer: pandas Timestamp
- API Layer: ISO 8601 strings

All timestamp conversions should use these utility functions to ensure consistency.
"""

from __future__ import annotations

from datetime import datetime, timedelta


def round_down_to_minute(dt: datetime) -> datetime:
    """Round a datetime down to the nearest minute."""
    return dt.replace(second=0, microsecond=0)


def round_up_to_minute(dt: datetime) -> datetime:
    """Round a datetime up to the nearest minute."""
    if dt.second == 0 and dt.microsecond == 0:
        return dt
    return dt.replace(second=0, microsecond=0) + timedelta(minutes=1)


def to_pandas_timestamp(dt):
    """Convert Python datetime or string to pandas Timestamp for analysis.

    Args:
        dt: Python datetime, string timestamp, or None

    Returns:
        pandas Timestamp or None
    """
    import pandas as pd

    if dt is None:
        return None

    try:
        # Handle various input types
        if isinstance(dt, str):
            # Parse string timestamp
            return pd.to_datetime(dt)
        if isinstance(dt, pd.Timestamp):
            # Already a pandas timestamp
            return dt
        if hasattr(dt, "timestamp"):  # datetime, date objects
            # Convert datetime-like objects
            return pd.Timestamp(dt)
        # Try standard pandas conversion as fallback
        return pd.to_datetime(dt)
    except (ValueError, TypeError, AttributeError) as e:
        import logging

        logging.getLogger(__name__).warning(f"Failed to convert {type(dt)} to pandas Timestamp: {e}")
        return None


def to_python_datetime(ts):
    """Convert pandas Timestamp back to Python datetime.

    Args:
        ts: pandas Timestamp, Python datetime, or None

    Returns:
        Python datetime or None
    """
    if ts is None:
        return None
    if hasattr(ts, "to_pydatetime"):
        return ts.to_pydatetime()
    return ts  # Already a datetime


def format_timestamp_iso(dt):
    """Format timestamp as ISO 8601 string for API responses.

    Args:
        dt: pandas Timestamp, Python datetime, or None

    Returns:
        ISO 8601 formatted string or None
    """
    if dt is None:
        return None
    # Convert to Python datetime first if needed
    if hasattr(dt, "to_pydatetime"):
        dt = dt.to_pydatetime()
    return dt.isoformat()


def calculate_bucket_size(start_time: datetime, end_time: datetime, target_buckets: int = 75) -> int:
    """Calculate bucket size dynamically based on time range to achieve a target number of buckets.

    Args:
        start_time: Start of the time range
        end_time: End of the time range
        target_buckets: Target number of buckets (default: 75)

    Returns:
        Bucket size in seconds
    """
    MIN_BUCKET_SIZE = 10  # Minimum bucket size of 10 seconds

    # Calculate total time range in seconds
    total_seconds = (end_time - start_time).total_seconds()

    # Calculate ideal bucket size to achieve target number of buckets
    bucket_size = max(MIN_BUCKET_SIZE, int(total_seconds / target_buckets))

    # Round bucket size to nearest sensible interval
    if bucket_size < 60:  # Less than 1 minute
        # Round to nearest 10 seconds
        bucket_size = ((bucket_size + 5) // 10) * 10
    elif bucket_size < 300:  # Less than 5 minutes
        # Round to nearest minute
        bucket_size = ((bucket_size + 30) // 60) * 60
    elif bucket_size < 3600:  # Less than 1 hour
        # Round to nearest 5 minutes
        bucket_size = ((bucket_size + 150) // 300) * 300
    elif bucket_size < 86400:  # Less than 1 day
        # Round to nearest hour
        bucket_size = ((bucket_size + 1800) // 3600) * 3600
    else:
        # Round to nearest day
        bucket_size = ((bucket_size + 43200) // 86400) * 86400

    return bucket_size
