"""Performance tracking utilities for the log parser package."""

from __future__ import annotations

import functools
import logging
import os
import time
from datetime import datetime

import psutil

# Get the logger for performance tracking
logger = logging.getLogger(__name__)

# Create a specific logger for performance logs
perf_logger = logging.getLogger("log_parser.performance")

# Global performance tracking for parser functions
_parser_performance_counters = {}
_parser_memory_tracking = {}

# Functions to exclude from verbose logging (too noisy, called frequently)
_NOISY_FUNCTIONS = {"normalize_key", "clean_metadata_value", "clean_metadata_dict"}

# Functions that are important to track (frontend-facing, potentially slow)
_IMPORTANT_FUNCTIONS = {
    "get_log_summary",
    "get_column_availability",
    "get_error_distribution",
    "get_common_time_range",
    "query",
    "get_time_range",
    "cross_analyzer_initialize",
    "parse_directory",
    "auto_detect_parser",
}


def _track_parser_performance(function_name):
    """Performance tracking utility for parser functions."""

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            # Always collect stats, but be selective about logging
            is_noisy = function_name in _NOISY_FUNCTIONS
            is_important = function_name in _IMPORTANT_FUNCTIONS

            # Get memory usage before function execution (only for important functions)
            memory_before = 0
            if is_important:
                try:
                    process = psutil.Process(os.getpid())
                    memory_before = process.memory_info().rss / 1024 / 1024  # MB
                except (OSError, AttributeError, ImportError):
                    pass  # Don't fail if psutil fails

            # Increment call counter
            if function_name not in _parser_performance_counters:
                _parser_performance_counters[function_name] = 0
            _parser_performance_counters[function_name] += 1

            # Only log start for important functions
            if is_important:
                perf_logger.debug(
                    "PARSER_PERF_START: %s (call #%d) - Memory: %.1fMB",
                    function_name,
                    _parser_performance_counters[function_name],
                    memory_before,
                )

            try:
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time

                # Get memory usage after function execution (only for important functions)
                memory_after = memory_before
                memory_delta = 0
                if is_important:
                    try:
                        process = psutil.Process(os.getpid())
                        memory_after = process.memory_info().rss / 1024 / 1024  # MB
                        memory_delta = memory_after - memory_before
                    except (OSError, AttributeError, ImportError):
                        pass

                # Always track memory usage trends for statistics
                if function_name not in _parser_memory_tracking:
                    _parser_memory_tracking[function_name] = []
                _parser_memory_tracking[function_name].append(
                    {
                        "timestamp": datetime.now(),
                        "duration": elapsed,
                        "memory_before": memory_before,
                        "memory_after": memory_after,
                        "memory_delta": memory_delta,
                    }
                )

                # Keep only recent entries (last 100 calls) to prevent memory bloat
                if len(_parser_memory_tracking[function_name]) > 100:
                    _parser_memory_tracking[function_name] = _parser_memory_tracking[function_name][-100:]

                # Selective logging based on function importance and performance
                should_log_end = False

                if is_important:
                    # Always log important functions
                    should_log_end = True
                elif not is_noisy and elapsed > 0.1:
                    # Log slow functions (>100ms) even if not marked as important
                    should_log_end = True
                elif is_noisy:
                    # For noisy functions, only log significant batches or slow operations
                    call_count = _parser_performance_counters[function_name]
                    if call_count % 1000 == 0:  # Every 1000 calls instead of 100
                        perf_logger.debug(
                            "PARSER_PERF_BATCH: %s batch of 1000 calls - Avg: %.3fs, Total calls: %d", function_name, elapsed, call_count
                        )
                        should_log_end = False  # Don't double-log
                    elif elapsed > 0.01:  # Only log if >10ms for noisy functions
                        should_log_end = True

                if should_log_end:
                    if is_important or elapsed > 0.1:
                        perf_logger.debug(
                            "PARSER_PERF_END: %s completed in %.3fs - Memory: %.1fMB (Δ%+.1fMB)",
                            function_name,
                            elapsed,
                            memory_after,
                            memory_delta,
                        )
                    else:
                        perf_logger.debug("PARSER_PERF_END: %s completed in %.3fs", function_name, elapsed)

                # Special logging for significant memory changes
                if abs(memory_delta) > 10:  # >10MB change
                    perf_logger.debug(
                        "PARSER_PERF_MEMORY: %s significant memory change %+.1fMB - Duration: %.3fs",
                        function_name,
                        memory_delta,
                        elapsed,
                    )

                return result

            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
                elapsed = time.time() - start_time
                # Always log errors, regardless of function type
                perf_logger.debug("PARSER_PERF_ERROR: %s failed after %.3fs - Error: %s", function_name, elapsed, e)
                raise

        return wrapper

    return decorator


def _log_cache_operation(operation_type: str, cache_key: str, hit: bool | None = None, size: int | None = None):
    """Log cache operations for performance analysis (reduced verbosity)."""
    # Only log cache operations for important operations, not utility functions
    if any(important in cache_key for important in _IMPORTANT_FUNCTIONS):
        if hit is not None:
            status = "HIT" if hit else "MISS"
            logger.debug("PARSER_CACHE_%s: %s", status, cache_key)
        elif operation_type in ["set", "expire"] and size and size > 1024:  # Only log large cache sets
            logger.debug("PARSER_CACHE_%s: %s - Size: %d", operation_type.upper(), cache_key, size)


def get_parser_performance_stats():
    """Get current performance statistics for analysis."""
    return {
        "call_counters": _parser_performance_counters.copy(),
        "memory_tracking": {
            func: {
                "total_calls": len(entries),
                "avg_duration": (sum(e["duration"] for e in entries) / len(entries) if entries else 0),
                "avg_memory_delta": (sum(e["memory_delta"] for e in entries) / len(entries) if entries else 0),
                "max_memory_delta": max(e["memory_delta"] for e in entries) if entries else 0,
                "last_call": entries[-1]["timestamp"].isoformat() if entries else None,
            }
            for func, entries in _parser_memory_tracking.items()
        },
    }


def reset_performance_stats():
    """Reset all performance tracking statistics."""
    global _parser_performance_counters, _parser_memory_tracking
    _parser_performance_counters = {}
    _parser_memory_tracking = {}
    perf_logger.debug("PARSER_PERF_RESET: Performance statistics reset")


def enable_performance_logging(enable: bool = False):
    """Enable or disable performance logging for log_parser.

    Args:
        enable: Whether to enable performance logging
    """
    perf_logger = logging.getLogger("log_parser.performance")

    if enable:
        # Enable DEBUG level for performance logger only
        perf_logger.setLevel(logging.DEBUG)

        # Create console handler if it doesn't exist
        if not perf_logger.handlers:
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            console_handler.setFormatter(formatter)
            perf_logger.addHandler(console_handler)

        # Prevent propagation to parent logger to avoid duplicate logs
        perf_logger.propagate = False
        perf_logger.debug("PARSER_PERF_CONFIG: Performance logging enabled")
    else:
        # Disable DEBUG level for performance logger
        perf_logger.setLevel(logging.INFO)
        perf_logger.debug("PARSER_PERF_CONFIG: Performance logging disabled")


# Keep the old function for backward compatibility (deprecated)
def set_performance_verbosity(level: str):
    """DEPRECATED: Use enable_performance_logging() instead.
    Set performance logging verbosity.

    Args:
        level: 'minimal', 'normal', or 'verbose'
    """
    # Map old interface to new simplified interface
    if level in ["minimal", "normal", "verbose"]:
        enable_performance_logging(enable=True)
        perf_logger.debug("PARSER_PERF_CONFIG: Set to %s verbosity (deprecated interface)", level)
    else:
        enable_performance_logging(enable=False)
