"""Utility functions for the log parser."""

from __future__ import annotations

from .logging import logger, setup_logging
from .metadata_utils import clean_metadata_dict
from .performance import (
    _log_cache_operation,
    _track_parser_performance,
    enable_performance_logging,
    get_parser_performance_stats,
    reset_performance_stats,
)
from .serialization import convert_to_serializable, prepare_data_for_response
from .time_utils import round_down_to_minute, round_up_to_minute, to_pandas_timestamp

__all__ = [
    "_log_cache_operation",
    "_track_parser_performance",
    "clean_metadata_dict",
    "convert_to_serializable",
    "enable_performance_logging",
    "get_parser_performance_stats",
    "logger",
    "prepare_data_for_response",
    "reset_performance_stats",
    "round_down_to_minute",
    "round_up_to_minute",
    "setup_logging",
    "to_pandas_timestamp",
]
