from __future__ import annotations

import logging
import re
from typing import Any

import numpy as np

from .performance import _track_parser_performance

logger = logging.getLogger(__name__)


def normalize_key(key: str) -> str:
    """Normalize a key by:
    1. Removing quotes and backslashes
    2. Replacing spaces with underscores
    3. Removing any other special characters except underscores

    Args:
        key: The key to normalize

    Returns:
        Normalized key
    """
    # Remove quotes and backslashes
    key = key.replace('"', "").replace("\\", "")
    # Replace multiple spaces with a single underscore
    key = re.sub(r"\s+", "_", key)
    # Remove any other special characters except underscores
    key = re.sub(r"[^a-zA-Z0-9_]", "", key)
    return key


def clean_metadata_value(value: Any) -> Any:
    """Clean a metadata value by:
    1. Converting NaN/Inf/null values to None
    2. Removing unnecessary quotes from strings
    3. Converting numpy types to native Python types
    4. Handling lists and dictionaries recursively

    Args:
        value: The value to clean

    Returns:
        Cleaned value
    """
    # Handle numpy arrays first
    if isinstance(value, np.ndarray):
        return value.tolist()

    # Handle None/null values
    if value is None or (isinstance(value, str) and value.lower() in ("null", "none")):
        return None

    # Handle NaN/Inf values
    if isinstance(value, (float, np.floating)) and (np.isnan(value) or np.isinf(value)):
        return None

    # Handle numpy types
    if isinstance(value, (np.integer, np.floating, np.bool_)):
        return value.item()

    # Handle strings
    if isinstance(value, str):
        # Remove unnecessary quotes
        value = value.strip("\"'")
        # Convert string representations of null/NaN to None
        if value.lower() in ("null", "none", "nan"):
            return None
        return value

    # Handle lists
    if isinstance(value, (list, tuple)):
        cleaned_list = [clean_metadata_value(item) for item in value]
        # Filter out None values
        return [item for item in cleaned_list if item is not None]

    # Handle dictionaries
    if isinstance(value, dict):
        cleaned_dict = {}
        for k, v in value.items():
            cleaned_key = normalize_key(k)
            cleaned_value = clean_metadata_value(v)
            # Always add the key, even if the value is None
            cleaned_dict[cleaned_key] = cleaned_value
        return cleaned_dict

    return value


@_track_parser_performance("clean_metadata_dict")  # Keep this one as it's called less frequently
def clean_metadata_dict(metadata: dict[str, Any]) -> dict[str, Any]:
    """Clean a metadata dictionary by applying clean_metadata_value to all values
    and normalize_key to all keys.

    Args:
        metadata: The metadata dictionary to clean

    Returns:
        Cleaned metadata dictionary
    """
    cleaned_dict = {}
    for k, v in metadata.items():
        cleaned_key = normalize_key(k)
        cleaned_value = clean_metadata_value(v)
        if cleaned_value is not None:  # Only add non-None values
            cleaned_dict[cleaned_key] = cleaned_value
    return cleaned_dict
