"""Logging utilities and performance monitoring decorators."""

from __future__ import annotations

import json
import logging
import time
from collections.abc import Callable
from datetime import datetime
from functools import wraps
from typing import Any, TypeVar, cast


class JSONFormatter(logging.Formatter):
    """Format logs as <PERSON><PERSON><PERSON> for easier parsing."""

    def format(self, record: logging.LogRecord) -> str:
        """Format a log record as JSON string."""
        log_record = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add extra attributes if available
        if hasattr(record, "extra"):
            log_record.update(record.extra)

        # Add exception info if present
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_record)


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Set up structured logging.

    Args:
        log_level: The log level to use (DEBUG, INFO, WARNING, ERROR, CRITICAL)

    Returns:
        Logger: Configured logger instance
    """
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")

    # Configure the main app logger
    app_logger = logging.getLogger("app")
    app_logger.setLevel(numeric_level)

    # Configure the log_parser package and all its submodules
    log_parser_logger = logging.getLogger("log_parser")
    log_parser_logger.setLevel(numeric_level)

    # Clear any existing handlers
    for logger in [app_logger, log_parser_logger]:
        logger.handlers = []

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(JSONFormatter())
        logger.addHandler(console_handler)

        # File handler
        file_handler = logging.FileHandler("app.log")
        file_handler.setFormatter(JSONFormatter())
        logger.addHandler(file_handler)

    # Ensure all log_parser submodules inherit the log level
    logging.getLogger("log_parser").propagate = True

    return app_logger


# Type variable for callable return type
T = TypeVar("T")


def log_with_context(logger: logging.Logger, context: dict[str, Any]) -> Callable[[str, int], None]:
    """Add context to log messages.

    Args:
        logger: The logger instance
        context: Dictionary of context variables to add to all log messages

    Returns:
        Callable: Logging function with context
    """

    def _log(message: str, level: int = logging.INFO) -> None:
        extra = {"extra": context}
        logger.log(level, message, extra=extra)

    return _log


async def log_performance(func: Callable[..., T], logger: logging.Logger, context: dict[str, Any] | None = None) -> Callable[..., T]:
    """Decorate function for performance logging.

    Args:
        func: The function to decorate
        logger: The logger instance
        context: Optional context to add to the log

    Returns:
        Callable: Decorated function
    """

    @wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> T:
        ctx = context or {}
        ctx.update({"function": func.__name__})
        log = log_with_context(logger, ctx)

        log(f"Function {func.__name__} started")
        start_time = time.time()

        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            log(f"Function {func.__name__} completed in {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log(f"Function {func.__name__} failed after {duration:.2f}s: {e!s}", logging.ERROR)
            raise

    return cast("Callable[..., T]", wrapper)


# Create a default logger instance
logger = setup_logging()
