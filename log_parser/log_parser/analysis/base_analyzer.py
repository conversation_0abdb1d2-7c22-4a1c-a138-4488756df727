from __future__ import annotations

import hashlib
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import numpy as np
import pandas as pd

from ..models import LogEntry
from ..utils import _track_parser_performance
from ..utils.time_utils import format_timestamp_iso, to_pandas_timestamp, to_python_datetime

logger = logging.getLogger(__name__)


class BaseAnalyzer(ABC):
    """Abstract base class for all log analyzers.
    Defines the interface that all concrete analyzer implementations must follow.

    Timestamp handling follows these conventions:
    - Storage: Python datetime objects are used in models and for storage
    - Analysis: pandas Timestamp objects are used for all DataFrame operations
    - API: Python datetime objects are returned, which get converted to ISO strings at the API layer
    """

    def __init__(self, log_entries: list[LogEntry] | None = None):
        """Initialize with log entries and convert to appropriate format.

        Args:
            log_entries: List of log entries to analyze, or None for empty initialization
        """
        logger.debug(
            "BASE_ANALYZER_INIT_START: Initializing %s with %d entries", self.__class__.__name__, len(log_entries) if log_entries else 0
        )
        self.log_entries = log_entries or []
        self._df = self._create_dataframe(self.log_entries)

        # PHASE 2: Initialize hierarchical query cache
        self._query_cache = HierarchicalQueryCache(max_cached_results=12)

        logger.debug("BASE_ANALYZER_INIT_END: %s initialized with DataFrame shape: %s", self.__class__.__name__, self._df.shape)

    @_track_parser_performance("update_data")
    def update_data(self, log_entries: list[LogEntry]) -> None:
        """Update the analyzer with new log entries.

        Args:
            log_entries: List of new log entries to analyze
        """
        logger.debug("BASE_ANALYZER_UPDATE_START: Updating %s with %d entries", self.__class__.__name__, len(log_entries))
        self.log_entries = log_entries
        self._df = self._create_dataframe(log_entries)

        # PHASE 2: Clear query cache when data is updated
        if hasattr(self, "_query_cache"):
            self._query_cache.protected_entries.clear()
            self._query_cache.general_entries.clear()
            self._query_cache.cache_metadata.clear()
            self._query_cache.cached_results = self._query_cache._get_combined_cache_view()
            logger.debug("BASE_ANALYZER_UPDATE: Cleared hierarchical query cache (protected and general) due to data update")

        logger.debug("BASE_ANALYZER_UPDATE_END: Updated DataFrame shape: %s", self._df.shape)

    @property
    @abstractmethod
    def analyzer_type(self) -> str:
        """Return the type of analyzer."""

    @abstractmethod
    def _create_dataframe(self, log_entries: list[LogEntry]) -> pd.DataFrame:
        """Convert log entries to a DataFrame for analysis.

        Args:
            log_entries: List of log entries

        Returns:
            DataFrame with log entries
        """

    @_track_parser_performance("get_time_range")
    def get_time_range(self) -> tuple[datetime | None, datetime | None]:
        """Get the time range covered by the logs.

        Returns:
            Tuple of (start, end) timestamps, or (None, None) if no data
        """
        logger.debug(
            "BASE_ANALYZER_TIME_RANGE_START: Getting time range for %s (DataFrame shape: %s)", self.__class__.__name__, self._df.shape
        )

        if self._df.empty:
            logger.debug("BASE_ANALYZER_TIME_RANGE_END: DataFrame is empty, returning None")
            return None, None

        try:
            # Ensure index is datetime type
            if not pd.api.types.is_datetime64_any_dtype(self._df.index):
                logger.warning(
                    "BASE_ANALYZER_WARNING: Index is not a datetime type (%s), cannot determine time range", type(self._df.index)
                )
                return None, None

            # Get min and max timestamps from DataFrame index
            start = self._df.index.min()
            end = self._df.index.max()

            # Check for array-like objects - use more basic checks
            if isinstance(start, (list, np.ndarray)) or isinstance(end, (list, np.ndarray)):
                logger.warning("BASE_ANALYZER_WARNING: Min/max timestamps are array-like objects, cannot determine time range")
                return None, None

            # Always convert to Python datetime before returning
            result_start = to_python_datetime(start)
            result_end = to_python_datetime(end)
            logger.debug("BASE_ANALYZER_TIME_RANGE_END: Time range: %s to %s", result_start, result_end)
            return result_start, result_end
        except (ValueError, TypeError, AttributeError, pd.errors.ParserError):
            logger.exception("BASE_ANALYZER_ERROR: Error getting time range")
            return None, None

    @_track_parser_performance("query")
    def query(self, start: datetime | None = None, end: datetime | None = None, **filters) -> pd.DataFrame:
        """Query the dataset with time range and additional filters.

        PHASE 2: Now uses hierarchical caching for optimized drill-down patterns.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            filters: Additional column filters

        Returns:
            Filtered DataFrame
        """
        logger.debug(
            "BASE_ANALYZER_QUERY_START: Querying %s - time range: %s to %s, filters: %s",
            self.__class__.__name__,
            start,
            end,
            list(filters.keys()),
        )

        try:
            # Early return for empty DataFrame
            if self._df.empty:
                logger.debug("BASE_ANALYZER_QUERY_END: DataFrame is empty, returning empty result")
                return self._df.copy()

            # PHASE 2: Use hierarchical query cache for optimal performance
            result = self._query_cache.query(start, end, filters, self._df)

            original_size = len(self._df)
            result_size = len(result)
            logger.debug("BASE_ANALYZER_QUERY_END: Query completed, returning %d/%d rows", result_size, original_size)

            # Log cache statistics for monitoring
            cache_stats = self._query_cache.get_cache_stats()
            logger.debug(
                "BASE_ANALYZER_CACHE_STATS: %d cached queries, %.1fMB total cache size",
                cache_stats["cached_queries"],
                cache_stats["total_size_mb"],
            )

            return result

        except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
            logger.exception("BASE_ANALYZER_ERROR: Error in query method")
            return pd.DataFrame()

    def count_by_time(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        freq: str = "1Min",
        **filters,
    ) -> pd.Series:
        """Count log entries by time period.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            freq: Time frequency for binning ('1Min', '1h', etc.)
            filters: Additional column filters

        Returns:
            Series with counts by time period
        """
        df = self.query(start, end, **filters)
        if df.empty:
            return pd.Series()

        # Resample by time frequency and count
        return df.resample(freq).size()

    def aggregate(
        self,
        column: str,
        func: str = "mean",
        start: datetime | None = None,
        end: datetime | None = None,
        freq: str | None = None,
        **filters,
    ) -> pd.Series:
        """Aggregate a column by time period.

        Args:
            column: Column to aggregate
            func: Aggregation function ('mean', 'sum', 'min', 'max', etc.)
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            freq: Time frequency for binning ('1Min', '1h', etc.)
            filters: Additional column filters

        Returns:
            Series with aggregated values by time period, or a single value if freq is None
        """
        df = self.query(start, end, **filters)
        if df.empty or column not in df.columns:
            return pd.Series()

        # Apply aggregation
        if freq is not None:
            # Resample by time frequency and aggregate
            return df.resample(freq)[column].agg(func)
        # Aggregate to a single value
        return getattr(df[column], func)()

    def get_unique_values(
        self,
        column: str,
        start: datetime | None = None,
        end: datetime | None = None,
        **filters,
    ) -> list[Any]:
        """Get unique values for a column.

        Args:
            column: Column to get unique values for
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            filters: Additional column filters

        Returns:
            List of unique values
        """
        df = self.query(start, end, **filters)
        if df.empty or column not in df.columns:
            return []

        # Get unique values
        return df[column].unique().tolist()

    def to_dict(self) -> dict[str, Any]:
        """Convert analyzer metadata to a dictionary.

        Returns:
            Dictionary with analyzer metadata
        """
        start_time, end_time = self.get_time_range()

        # Convert timestamps to ISO format strings for JSON serialization
        start_time_iso = format_timestamp_iso(start_time)
        end_time_iso = format_timestamp_iso(end_time)

        return {
            "type": self.analyzer_type,
            "entry_count": len(self._df) if not self._df.empty else 0,
            "time_range": {"start": start_time_iso, "end": end_time_iso},
            "columns": list(self._df.columns) if not self._df.empty else [],
        }

    def get_frequency_distribution(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        freq: str = "1Min",
        **kwargs,
    ) -> pd.DataFrame:
        """Get a frequency distribution of events by time period.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            freq: Time frequency for binning ('1Min', '1h', etc.)
            **kwargs: Additional query filters

        Returns:
            DataFrame with frequency distribution
        """
        df = self.query(start, end, **kwargs)
        if df.empty:
            return pd.DataFrame()

        return df.resample(freq).size().reset_index(name="count")

    def get_timeline(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        freq: str = "1Min",
        **kwargs,
    ) -> dict[str, Any]:
        """Get a timeline of events for visualization.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            freq: Time frequency for binning ('1Min', '1h', etc.)
            **kwargs: Additional query filters

        Returns:
            Dictionary with timeline data
        """
        df = self.query(start, end, **kwargs)
        if df.empty:
            return {}

        timeline = {}
        for _, row in df.iterrows():
            timestamp = row.name.strftime("%Y-%m-%d %H:%M")
            if timestamp not in timeline:
                timeline[timestamp] = 0
            timeline[timestamp] += 1

        return timeline

    def get_cache_stats(self) -> dict[str, Any]:
        """Get hierarchical cache statistics for monitoring and debugging.

        PHASE 2: Provides visibility into cache performance for optimization.

        Returns:
            Dictionary with cache statistics
        """
        if hasattr(self, "_query_cache"):
            return self._query_cache.get_cache_stats()
        return {"cached_queries": 0, "total_size_mb": 0.0, "cache_keys": []}


class HierarchicalQueryCache:
    """PHASE 2: Hierarchical caching strategy for query results.

    Optimizes drill-down usage patterns:
    1. Full range queries → cached
    2. Sub-range queries → sliced from cached results (O(log n))
    3. Return to full range → instant cache hit
    """

    def __init__(self, max_cached_results: int = 12, protected_slots: int = 3):
        # Phase 2: Protected cache slots for high-value queries
        self.max_cached_results = max_cached_results
        self.protected_slots = protected_slots
        self.general_slots = max_cached_results - protected_slots

        # Separate storage for protected vs general entries
        self.protected_entries = {}  # {cache_key: DataFrame} - high-value queries
        self.general_entries = {}  # {cache_key: DataFrame} - normal queries

        # Combined metadata for both types
        self.cache_metadata = {}  # {cache_key: {'size': int, 'last_used': datetime, 'query_span': timedelta, 'is_protected': bool}}

        # Phase 3: Predictive caching system
        self.usage_patterns = []  # Track user navigation history
        self.common_ranges = {}  # Frequently accessed ranges
        self.prediction_confidence_threshold = 0.7  # Only pre-cache high-confidence predictions

        self.logger = logging.getLogger(__name__)

        # For backward compatibility
        self.cached_results = self._get_combined_cache_view()

    def _get_combined_cache_view(self) -> dict:
        """Return a combined view of protected and general caches for backward compatibility."""
        combined = {}
        combined.update(self.protected_entries)
        combined.update(self.general_entries)
        return combined

    def _should_protect_entry(self, start: datetime | None, end: datetime | None, df_source: pd.DataFrame) -> bool:
        """Determine if a query should be protected from eviction.

        Protected queries are:
        1. Full dataset queries (start=None and end=None)
        2. Queries covering >7 days of data
        3. Queries covering >50% of total dataset time range

        Args:
            start: Query start time
            end: Query end time
            df_source: Source DataFrame to determine total time range

        Returns:
            True if query should be protected, False otherwise
        """
        # Always protect full dataset queries
        if start is None and end is None:
            return True

        # Get total dataset time range
        if df_source.empty or not hasattr(df_source, "index"):
            return False

        try:
            dataset_start = df_source.index.min()
            dataset_end = df_source.index.max()
            total_span = dataset_end - dataset_start
        except (AttributeError, TypeError):
            return False

        # Protect queries covering >7 days
        if start is not None and end is not None:
            query_span = end - start
            if query_span.total_seconds() > (7 * 24 * 3600):  # 7 days in seconds
                return True

            # Protect queries covering >50% of dataset
            if total_span.total_seconds() > 0:
                coverage_ratio = query_span.total_seconds() / total_span.total_seconds()
                if coverage_ratio > 0.5:
                    return True

        return False

    def analyze_user_pattern(self, current_range: tuple[datetime | None, datetime | None], filters: dict[str, Any]) -> None:
        """Track user navigation patterns for predictive caching.

        Args:
            current_range: (start, end) tuple of current query range
            filters: Current query filters
        """
        from datetime import datetime

        pattern_entry = {
            "timestamp": datetime.now(),
            "start": current_range[0],
            "end": current_range[1],
            "filters": filters.copy(),
            "span_days": None,
        }

        # Calculate span in days for pattern analysis
        if current_range[0] is not None and current_range[1] is not None:
            span = current_range[1] - current_range[0]
            pattern_entry["span_days"] = span.total_seconds() / (24 * 3600)

        # Add to pattern history (keep last 50 patterns)
        self.usage_patterns.append(pattern_entry)
        if len(self.usage_patterns) > 50:
            self.usage_patterns = self.usage_patterns[-50:]

        # Update common ranges frequency
        range_key = self._generate_range_key(current_range[0], current_range[1])
        self.common_ranges[range_key] = self.common_ranges.get(range_key, 0) + 1

        self.logger.debug("PREDICTIVE_CACHE: Recorded pattern - range: %s, span: %.1f days", range_key[:8], pattern_entry["span_days"] or 0)

    def _generate_range_key(self, start: datetime | None, end: datetime | None) -> str:
        """Generate a key for range frequency tracking."""
        if start is None and end is None:
            return "full_dataset"
        elif start is None:
            return f"to_{end.isoformat()[:10]}"
        elif end is None:
            return f"from_{start.isoformat()[:10]}"
        else:
            return f"{start.isoformat()[:10]}_to_{end.isoformat()[:10]}"

    def predict_next_ranges(
        self, current_range: tuple[datetime | None, datetime | None], df_source: pd.DataFrame
    ) -> list[tuple[datetime | None, datetime | None]]:
        """Predict likely next ranges user will request based on patterns.

        Args:
            current_range: Current query range (start, end)
            df_source: Source DataFrame for range calculations

        Returns:
            List of predicted ranges with confidence >threshold
        """
        predictions = []
        start, end = current_range

        try:
            # Get dataset bounds for relative calculations
            if not df_source.empty and hasattr(df_source, "index"):
                dataset_start = df_source.index.min()
                dataset_end = df_source.index.max()
            else:
                return predictions

            # Pattern 1: Full dataset → common sub-ranges
            if start is None and end is None:
                predictions.extend(self._predict_from_full_dataset(dataset_start, dataset_end))

            # Pattern 2: Sub-range → parent range (zoom out)
            elif start is not None and end is not None:
                parent_ranges = self._predict_parent_ranges(start, end, dataset_start, dataset_end)
                predictions.extend(parent_ranges)

                # Pattern 3: Sub-range → sibling ranges (pan left/right)
                sibling_ranges = self._predict_sibling_ranges(start, end, dataset_start, dataset_end)
                predictions.extend(sibling_ranges)

            # Pattern 4: Based on historical common ranges
            historical_predictions = self._predict_from_history(current_range)
            predictions.extend(historical_predictions)

            # Remove duplicates and limit to high-confidence predictions
            unique_predictions = []
            seen_keys = set()

            for pred_range, confidence in predictions:
                range_key = self._generate_range_key(pred_range[0], pred_range[1])
                if range_key not in seen_keys and confidence >= self.prediction_confidence_threshold:
                    unique_predictions.append(pred_range)
                    seen_keys.add(range_key)

            self.logger.debug("PREDICTIVE_CACHE: Generated %d high-confidence predictions", len(unique_predictions))
            return unique_predictions[:3]  # Limit to top 3 predictions

        except Exception as e:
            self.logger.exception("PREDICTIVE_CACHE_ERROR: Error predicting ranges: %s", e)
            return []

    def _predict_from_full_dataset(
        self, dataset_start: datetime, dataset_end: datetime
    ) -> list[tuple[tuple[datetime | None, datetime | None], float]]:
        """Predict common drill-down ranges from full dataset."""
        predictions = []

        # Last month
        last_month_start = max(dataset_start, dataset_end - timedelta(days=30))
        predictions.append(((last_month_start, dataset_end), 0.8))

        # Last week
        last_week_start = max(dataset_start, dataset_end - timedelta(days=7))
        predictions.append(((last_week_start, dataset_end), 0.75))

        # Last 3 months
        three_months_start = max(dataset_start, dataset_end - timedelta(days=90))
        predictions.append(((three_months_start, dataset_end), 0.7))

        return predictions

    def _predict_parent_ranges(
        self, start: datetime, end: datetime, dataset_start: datetime, dataset_end: datetime
    ) -> list[tuple[tuple[datetime | None, datetime | None], float]]:
        """Predict parent ranges (zoom out scenarios)."""
        predictions = []
        current_span = end - start

        # 2x wider range centered on current
        expand_delta = current_span / 2
        wider_start = max(dataset_start, start - expand_delta)
        wider_end = min(dataset_end, end + expand_delta)
        predictions.append(((wider_start, wider_end), 0.75))

        # Full dataset (ultimate zoom out)
        predictions.append(((None, None), 0.8))

        return predictions

    def _predict_sibling_ranges(
        self, start: datetime, end: datetime, dataset_start: datetime, dataset_end: datetime
    ) -> list[tuple[tuple[datetime | None, datetime | None], float]]:
        """Predict sibling ranges (pan left/right scenarios)."""
        predictions = []
        span = end - start

        # Pan left (previous period)
        left_start = max(dataset_start, start - span)
        left_end = start
        if left_start < left_end:
            predictions.append(((left_start, left_end), 0.7))

        # Pan right (next period)
        right_start = end
        right_end = min(dataset_end, end + span)
        if right_start < right_end:
            predictions.append(((right_start, right_end), 0.7))

        return predictions

    def _predict_from_history(
        self, current_range: tuple[datetime | None, datetime | None]
    ) -> list[tuple[tuple[datetime | None, datetime | None], float]]:
        """Predict based on historical usage patterns."""
        predictions = []

        # Find most frequently used ranges
        sorted_ranges = sorted(self.common_ranges.items(), key=lambda x: x[1], reverse=True)

        for range_key, frequency in sorted_ranges[:3]:  # Top 3 most common
            if frequency >= 2:  # Used at least twice
                # Parse range key back to datetime tuple
                pred_range = self._parse_range_key(range_key)
                if pred_range != current_range:  # Don't predict current range
                    confidence = min(0.9, 0.5 + (frequency * 0.1))  # Scale by frequency
                    predictions.append((pred_range, confidence))

        return predictions

    def _parse_range_key(self, range_key: str) -> tuple[datetime | None, datetime | None]:
        """Parse range key back to datetime tuple."""
        from datetime import datetime

        if range_key == "full_dataset":
            return (None, None)
        elif range_key.startswith("to_"):
            date_str = range_key[3:]
            return (None, datetime.fromisoformat(date_str))
        elif range_key.startswith("from_"):
            date_str = range_key[5:]
            return (datetime.fromisoformat(date_str), None)
        elif "_to_" in range_key:
            start_str, end_str = range_key.split("_to_")
            return (datetime.fromisoformat(start_str), datetime.fromisoformat(end_str))
        else:
            return (None, None)

    def _trigger_predictive_caching(
        self, current_range: tuple[datetime | None, datetime | None], filters: dict[str, Any], df_source: pd.DataFrame
    ) -> None:
        """Trigger background predictive caching based on predicted user patterns.

        Args:
            current_range: Current query range that was just executed
            filters: Current query filters
            df_source: Source DataFrame for predictions
        """
        try:
            # Generate predictions for likely next ranges
            predicted_ranges = self.predict_next_ranges(current_range, df_source)

            if not predicted_ranges:
                return

            self.logger.debug("PREDICTIVE_CACHE: Triggering pre-caching for %d predicted ranges", len(predicted_ranges))

            # Pre-cache in background (limited to avoid overwhelming system)
            for pred_start, pred_end in predicted_ranges:
                # Check if not already cached
                pred_key = self._generate_cache_key(pred_start, pred_end, filters)
                if pred_key not in self.protected_entries and pred_key not in self.general_entries:

                    # Only pre-cache if we have room in general slots (don't evict existing)
                    if len(self.general_entries) < self.general_slots:
                        self._background_cache_range(pred_start, pred_end, filters, df_source)

        except Exception as e:
            self.logger.exception("PREDICTIVE_CACHE_ERROR: Error in predictive caching: %s", e)

    def _background_cache_range(
        self, start: datetime | None, end: datetime | None, filters: dict[str, Any], df_source: pd.DataFrame
    ) -> None:
        """Execute background caching for a predicted range.

        Args:
            start: Predicted range start
            end: Predicted range end
            filters: Query filters
            df_source: Source DataFrame
        """
        try:
            pred_key = self._generate_cache_key(start, end, filters)
            self.logger.debug("PREDICTIVE_CACHE: Background caching range %s", pred_key[:8])

            # Execute query for predicted range
            result = self._execute_full_query(start, end, filters, df_source)

            # Add to cache if not empty
            if not result.empty:
                self._add_to_cache(pred_key, result, start, end, filters, df_source)
                self.logger.debug(
                    "PREDICTIVE_CACHE: Successfully pre-cached range %s (%.1fMB)",
                    pred_key[:8],
                    result.memory_usage(deep=True).sum() / (1024 * 1024),
                )
            else:
                self.logger.debug("PREDICTIVE_CACHE: Skipped empty result for range %s", pred_key[:8])

        except Exception as e:
            self.logger.warning("PREDICTIVE_CACHE_ERROR: Failed to pre-cache range %s-%s: %s", start, end, e)

    def query(
        self,
        start: datetime | None,
        end: datetime | None,
        filters: dict[str, Any],
        df_source: pd.DataFrame,
    ) -> pd.DataFrame:
        """Query with hierarchical caching - check cache hierarchy before executing full query.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            filters: Additional column filters
            df_source: Source DataFrame to query against

        Returns:
            Filtered DataFrame
        """
        try:
            # Validate inputs to prevent strange errors
            if not isinstance(filters, dict):
                self.logger.warning("HIERARCHICAL_CACHE_WARNING: filters is not a dict: %s, using empty dict", type(filters))
                filters = {}

            # Generate cache key for this query
            query_key = self._generate_cache_key(start, end, filters)

            # 1. Check for exact cache match in both protected and general caches
            cached_result = None
            if query_key in self.protected_entries:
                cached_result = self.protected_entries[query_key]
                self.logger.debug("HIERARCHICAL_CACHE_HIT: Exact match in protected cache for query %s", query_key)
            elif query_key in self.general_entries:
                cached_result = self.general_entries[query_key]
                self.logger.debug("HIERARCHICAL_CACHE_HIT: Exact match in general cache for query %s", query_key)

            if cached_result is not None:
                self._update_last_used(query_key)

                # Phase 3: Track pattern even for cache hits to improve predictions
                self.analyze_user_pattern((start, end), filters)

                return cached_result.copy()

            # 2. Check if this query can be satisfied by slicing a cached result
            parent_result = self._find_parent_cache(start, end, filters)
            if parent_result:
                parent_df, parent_key = parent_result
                self.logger.debug("HIERARCHICAL_CACHE_SLICE: Slicing from cached parent %s", parent_key)
                try:
                    sliced_result = self._slice_cached_dataframe(parent_df, start, end, filters)

                    # Cache this sliced result for potential future use
                    self._add_to_cache(query_key, sliced_result, start, end, filters, df_source)
                    return sliced_result.copy()
                except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError) as slice_error:
                    self.logger.error(
                        "HIERARCHICAL_CACHE_SLICE_FAILED: Error slicing from parent %s: %s (type: %s)",
                        parent_key,
                        slice_error,
                        type(slice_error),
                    )
                    self.logger.error("HIERARCHICAL_CACHE_SLICE_FAILED: Falling back to full query execution")
                    # Fall through to full query execution

            # 3. No cache hit - execute full query and cache result
            self.logger.debug("HIERARCHICAL_CACHE_MISS: Executing full query for %s", query_key)
            result = self._execute_full_query(start, end, filters, df_source)
            self._add_to_cache(query_key, result, start, end, filters, df_source)

            # Phase 3: Track user pattern and trigger predictive caching
            self.analyze_user_pattern((start, end), filters)
            self._trigger_predictive_caching((start, end), filters, df_source)

            return result.copy()

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
            self.logger.exception("HIERARCHICAL_CACHE_ERROR: Critical error in cache query (type: %s)", type(e))
            self.logger.error("HIERARCHICAL_CACHE_ERROR: start=%s, end=%s, filters=%s", start, end, filters)
            # As a last resort, try direct execution without caching
            try:
                return self._execute_full_query(start, end, filters, df_source)
            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
                self.logger.exception("HIERARCHICAL_CACHE_FALLBACK_FAILED")
                raise  # Re-raise the fallback error

    def _generate_cache_key(self, start: datetime | None, end: datetime | None, filters: dict[str, Any]) -> str:
        """Generate unique cache key for query parameters."""
        # Create deterministic key from query parameters
        key_parts = []

        if start is not None:
            try:
                # Handle datetime objects properly
                if hasattr(start, "isoformat"):
                    key_parts.append("start_%s" % start.isoformat())
                else:
                    # For non-datetime objects, use string representation
                    key_parts.append("start_%s" % start)
            except (ValueError, TypeError, AttributeError):
                key_parts.append("start_invalid")

        if end is not None:
            try:
                # Handle datetime objects properly
                if hasattr(end, "isoformat"):
                    key_parts.append("end_%s" % end.isoformat())
                else:
                    # For non-datetime objects, use string representation
                    key_parts.append("end_%s" % end)
            except (ValueError, TypeError, AttributeError):
                key_parts.append("end_invalid")

        # Sort filters for consistent keys
        if filters:
            filter_items = sorted(filters.items())
            filter_str = "_".join(["%s_%s" % (k, v) for k, v in filter_items])
            key_parts.append("filters_%s" % filter_str)

        key_string = "_".join(key_parts) if key_parts else "no_filters"
        return hashlib.md5(key_string.encode(), usedforsecurity=False).hexdigest()[:12]

    def _find_parent_cache(self, start: datetime | None, end: datetime | None, filters: dict[str, Any]) -> tuple[pd.DataFrame, str] | None:
        """Find cached result that contains this time range and filters."""
        target_start = start
        target_end = end

        for cache_key, metadata in self.cache_metadata.items():
            cached_start = metadata.get("start")
            cached_end = metadata.get("end")
            cached_filters = metadata.get("filters", {})

            # Check if filters match (cached query must have same or subset of filters)
            if not self._filters_compatible(cached_filters, filters):
                continue

            # Check if time range is contained
            if self._time_range_contained(cached_start, cached_end, target_start, target_end):
                # Get DataFrame from appropriate cache
                if cache_key in self.protected_entries:
                    cached_df = self.protected_entries[cache_key]
                elif cache_key in self.general_entries:
                    cached_df = self.general_entries[cache_key]
                else:
                    continue  # Skip if not found in either cache
                return cached_df, cache_key

        return None

    def _filters_compatible(self, cached_filters: dict[str, Any], target_filters: dict[str, Any]) -> bool:
        """Check if cached filters are compatible with target filters."""
        # For now, require exact filter match for safety
        # Future enhancement: support filter subsetting
        return cached_filters == target_filters

    def _time_range_contained(
        self,
        cached_start: datetime | None,
        cached_end: datetime | None,
        target_start: datetime | None,
        target_end: datetime | None,
    ) -> bool:
        """Check if target time range is contained within cached time range."""
        # Handle None values (representing unbounded ranges)
        if cached_start is None and cached_end is None:
            # Cached range is full dataset - contains any target range
            return True
        if cached_start is None:
            # Cached range is [:cached_end] - target must end before cached_end
            return target_end is None or target_end <= cached_end
        if cached_end is None:
            # Cached range is [cached_start:] - target must start after cached_start
            return target_start is None or target_start >= cached_start
        # Both bounds defined - target must be within cached range
        target_start_ok = target_start is None or target_start >= cached_start
        target_end_ok = target_end is None or target_end <= cached_end
        return target_start_ok and target_end_ok

    def _slice_cached_dataframe(
        self,
        cached_df: pd.DataFrame,
        start: datetime | None,
        end: datetime | None,
        filters: dict[str, Any],
    ) -> pd.DataFrame:
        """Efficiently slice cached DataFrame using timestamp index."""
        try:
            result = cached_df

            # Apply time range slicing using pandas timestamp index (O(log n))
            if start is not None or end is not None:
                # Convert to pandas timestamps for consistent indexing
                pandas_start = to_pandas_timestamp(start) if start else None
                pandas_end = to_pandas_timestamp(end) if end else None

                self.logger.debug("HIERARCHICAL_CACHE_SLICE_DEBUG: start=%s (%s), end=%s (%s)", start, type(start), end, type(end))
                self.logger.debug(
                    "HIERARCHICAL_CACHE_SLICE_DEBUG: pandas_start=%s (%s), pandas_end=%s (%s)",
                    pandas_start,
                    type(pandas_start),
                    pandas_end,
                    type(pandas_end),
                )
                self.logger.debug(
                    "HIERARCHICAL_CACHE_SLICE_DEBUG: cached_df.index.dtype=%s, index_range=%s to %s",
                    cached_df.index.dtype,
                    cached_df.index.min(),
                    cached_df.index.max(),
                )

                # Use boolean indexing for more robust timestamp filtering
                # This handles cases where exact timestamps don't exist in the index
                if pandas_start is not None and pandas_end is not None:
                    mask = (result.index >= pandas_start) & (result.index <= pandas_end)
                    result = result[mask]
                elif pandas_start is not None:
                    mask = result.index >= pandas_start
                    result = result[mask]
                elif pandas_end is not None:
                    mask = result.index <= pandas_end
                    result = result[mask]

            # Apply additional filters (these should already match cached filters)
            for column, value in filters.items():
                if column in result.columns:
                    if isinstance(value, list):
                        result = result[result[column].isin(value)]
                    else:
                        result = result[result[column] == value]

            return result

        except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError) as e:
            self.logger.exception("HIERARCHICAL_CACHE_SLICE_ERROR: Error slicing cached DataFrame (type: %s)", type(e))
            self.logger.error("HIERARCHICAL_CACHE_SLICE_ERROR: start=%s, end=%s, filters=%s", start, end, filters)
            # Re-raise the original exception to maintain error propagation
            raise

    def _execute_full_query(
        self,
        start: datetime | None,
        end: datetime | None,
        filters: dict[str, Any],
        df_source: pd.DataFrame,
    ) -> pd.DataFrame:
        """Execute full query against source DataFrame (original query logic)."""
        # This replicates the original query logic from BaseAnalyzer.query()
        # to avoid circular dependency

        if df_source.empty:
            return df_source.copy()

        # Convert to pandas Timestamp for consistent comparisons
        pandas_start = to_pandas_timestamp(start) if start else None
        pandas_end = to_pandas_timestamp(end) if end else None

        # Ensure index is datetime type for time-based filtering
        if not pd.api.types.is_datetime64_any_dtype(df_source.index):
            try:
                temp_df = df_source.copy()
                temp_df.index = pd.to_datetime(temp_df.index)
                source_df = temp_df
            except (ValueError, TypeError):
                return pd.DataFrame(columns=df_source.columns)
        else:
            source_df = df_source

        # Apply time range filtering using efficient .loc[] slicing
        try:
            if pandas_start is not None and pandas_end is not None:
                result = source_df.loc[pandas_start:pandas_end].copy()
            elif pandas_start is not None:
                result = source_df.loc[pandas_start:].copy()
            elif pandas_end is not None:
                result = source_df.loc[:pandas_end].copy()
            else:
                result = source_df.copy()
        except (ValueError, TypeError, KeyError, pd.errors.ParserError):
            # Fallback to boolean indexing if .loc[] fails
            result = source_df.copy()
            if pandas_start is not None:
                result = result[result.index >= pandas_start]
            if pandas_end is not None:
                result = result[result.index <= pandas_end]

        # Apply column filters
        for column, value in filters.items():
            if column in result.columns:
                try:
                    if isinstance(value, list):
                        result = result[result[column].isin(value)]
                    else:
                        result = result[result[column] == value]
                except (ValueError, TypeError, KeyError) as e:
                    self.logger.warning("Error applying filter for column %s: %s", column, e)

        return result

    def _add_to_cache(
        self,
        query_key: str,
        result: pd.DataFrame,
        start: datetime | None,
        end: datetime | None,
        filters: dict[str, Any],
        df_source: pd.DataFrame,
    ) -> None:
        """Add result to cache with protected slot classification and intelligent eviction."""
        # Don't cache empty DataFrames
        if result.empty:
            self.logger.debug("HIERARCHICAL_CACHE_SKIP: Not caching empty DataFrame for query %s", query_key)
            return

        # Calculate query span for eviction prioritization
        if start is not None and end is not None:
            query_span = end - start
        else:
            # Use a large span for unbounded queries (full dataset)
            query_span = datetime.max - datetime.min

        # Determine if this query should be protected
        is_protected = self._should_protect_entry(start, end, df_source)

        # Check if we need to evict based on slot availability
        if is_protected:
            # Check protected slots capacity
            if len(self.protected_entries) >= self.protected_slots:
                self._evict_from_protected_slots()
        else:
            # Check general slots capacity
            if len(self.general_entries) >= self.general_slots:
                self._evict_from_general_slots()

        # Store in appropriate cache
        metadata = {
            "size": result.memory_usage(deep=True).sum(),
            "last_used": datetime.now(),
            "query_span": query_span,
            "start": start,
            "end": end,
            "filters": filters,
            "is_protected": is_protected,
        }

        if is_protected:
            self.protected_entries[query_key] = result
            cache_type = "protected"
        else:
            self.general_entries[query_key] = result
            cache_type = "general"

        self.cache_metadata[query_key] = metadata

        # Update backward compatibility view
        self.cached_results = self._get_combined_cache_view()

        cache_size_mb = result.memory_usage(deep=True).sum() / (1024 * 1024)
        self.logger.debug("HIERARCHICAL_CACHE_ADD: Cached query %s in %s cache (%.1fMB)", query_key, cache_type, cache_size_mb)

    def _evict_from_general_slots(self) -> None:
        """Evict least valuable entry from general cache slots."""
        if not self.general_entries:
            return

        # Score general entries for retention value
        scored_entries = []
        for cache_key in self.general_entries.keys():
            metadata = self.cache_metadata[cache_key]
            value_score = self._calculate_retention_score(metadata)
            scored_entries.append((cache_key, value_score))

        # Sort by score (ascending) and evict lowest value entry
        scored_entries.sort(key=lambda x: x[1])
        key_to_evict = scored_entries[0][0]

        self._remove_cache_entry(key_to_evict, "general")

    def _evict_from_protected_slots(self) -> None:
        """Evict least valuable entry from protected cache slots."""
        if not self.protected_entries:
            return

        # Score protected entries for retention value
        scored_entries = []
        for cache_key in self.protected_entries.keys():
            metadata = self.cache_metadata[cache_key]
            value_score = self._calculate_retention_score(metadata)
            scored_entries.append((cache_key, value_score))

        # Sort by score (ascending) and evict lowest value entry
        scored_entries.sort(key=lambda x: x[1])
        key_to_evict = scored_entries[0][0]

        self._remove_cache_entry(key_to_evict, "protected")

    def _calculate_retention_score(self, metadata: dict[str, Any]) -> float:
        """Calculate retention value score for a cache entry.

        Higher score = more valuable = keep longer

        Args:
            metadata: Cache entry metadata

        Returns:
            Retention score (higher = more valuable)
        """
        span_seconds = metadata["query_span"].total_seconds()
        last_used_minutes_ago = (datetime.now() - metadata["last_used"]).total_seconds() / 60

        # Normalize scores (0-1 range)
        span_score = min(span_seconds / (7 * 24 * 3600), 1.0)  # Max score for 1 week span
        recency_score = max(0, 1 - (last_used_minutes_ago / 60))  # Decay over 1 hour

        # Protected entries get bonus scoring
        protection_bonus = 2.0 if metadata.get("is_protected", False) else 1.0

        # Combined score (prioritize span over recency for hierarchical caching)
        value_score = ((0.7 * span_score) + (0.3 * recency_score)) * protection_bonus

        return value_score

    def _remove_cache_entry(self, cache_key: str, cache_type: str) -> None:
        """Remove entry from appropriate cache and update metadata."""
        if cache_key not in self.cache_metadata:
            return

        evicted_size_mb = self.cache_metadata[cache_key]["size"] / (1024 * 1024)
        self.logger.debug("HIERARCHICAL_CACHE_EVICT: Evicted %s from %s cache (%.1fMB)", cache_key, cache_type, evicted_size_mb)

        # Remove from appropriate cache
        if cache_type == "protected" and cache_key in self.protected_entries:
            del self.protected_entries[cache_key]
        elif cache_type == "general" and cache_key in self.general_entries:
            del self.general_entries[cache_key]

        # Remove metadata
        del self.cache_metadata[cache_key]

        # Update backward compatibility view
        self.cached_results = self._get_combined_cache_view()

    def _evict_least_valuable(self) -> None:
        """Legacy eviction method - now delegates to slot-specific eviction."""
        # Try to evict from general slots first
        if self.general_entries:
            self._evict_from_general_slots()
        elif self.protected_entries:
            self._evict_from_protected_slots()

    def _update_last_used(self, cache_key: str) -> None:
        """Update last used timestamp for cache entry."""
        if cache_key in self.cache_metadata:
            self.cache_metadata[cache_key]["last_used"] = datetime.now()

    def get_cache_stats(self) -> dict[str, Any]:
        """Get cache statistics for monitoring and debugging."""
        total_size = sum(metadata["size"] for metadata in self.cache_metadata.values())
        protected_size = sum(metadata["size"] for metadata in self.cache_metadata.values() if metadata.get("is_protected", False))
        general_size = total_size - protected_size

        return {
            "cached_queries": len(self.protected_entries) + len(self.general_entries),
            "protected_entries": len(self.protected_entries),
            "general_entries": len(self.general_entries),
            "protected_slots_used": f"{len(self.protected_entries)}/{self.protected_slots}",
            "general_slots_used": f"{len(self.general_entries)}/{self.general_slots}",
            "total_size_mb": total_size / (1024 * 1024),
            "protected_size_mb": protected_size / (1024 * 1024),
            "general_size_mb": general_size / (1024 * 1024),
            "cache_keys": list(self.protected_entries.keys()) + list(self.general_entries.keys()),
            "protected_keys": list(self.protected_entries.keys()),
            "general_keys": list(self.general_entries.keys()),
            # Phase 3: Predictive caching statistics
            "usage_patterns_tracked": len(self.usage_patterns),
            "common_ranges_learned": len(self.common_ranges),
            "prediction_threshold": self.prediction_confidence_threshold,
            "most_common_ranges": dict(sorted(self.common_ranges.items(), key=lambda x: x[1], reverse=True)[:5]),
        }
