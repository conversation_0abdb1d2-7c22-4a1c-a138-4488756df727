from __future__ import annotations

import logging
from datetime import datetime
from typing import Any

import pandas as pd

from ..models import LogEntry, StructuredLogEntry
from .anomaly_detection import AnomalyDetector
from .base_analyzer import BaseAnalyzer

logger = logging.getLogger(__name__)


class StructuredLogAnalyzer(BaseAnalyzer):
    """Analyzer for Minknow log data.
    Provides specialized methods for Minknow log analysis.
    """

    @property
    def analyzer_type(self) -> str:
        return "minknow"

    def _create_dataframe(self, log_entries: list[LogEntry]) -> pd.DataFrame:
        """Convert minknow log entries to a DataFrame.

        Args:
            log_entries: List of log entries

        Returns:
            DataFrame with log entries
        """
        # Filter for minknow log entries
        minknow_entries = [entry for entry in log_entries if isinstance(entry, StructuredLogEntry)]

        if not minknow_entries:
            logger.warning("No minknow log entries found")
            return pd.DataFrame()

        # Extract data from log entries
        records = []

        # Special handling for keys dictionary
        key_columns = set()

        # First pass: discover keys used in log entries
        for entry in minknow_entries:
            if entry.keys:
                key_columns.update(entry.keys.keys())

        logger.debug("Found %d unique keys in minknow logs", len(key_columns))

        # Second pass: create flattened records
        for entry in minknow_entries:
            record = {
                "timestamp": entry.timestamp,
                "log_level": entry.log_level,
                "log_event": entry.log_event,
                "library": entry.library,
                "process_name": entry.process_name,
                "file_name": entry.file_name,
                "folder_name": entry.folder_name,
                "message": entry.message,
            }

            # Add flattened keys
            if entry.keys:
                for key in key_columns:
                    column_name = f"key_{key}"
                    record[column_name] = entry.keys.get(key)

            records.append(record)

        # Create DataFrame
        df = pd.DataFrame(records)

        # Set timestamp as index
        if not df.empty and "timestamp" in df.columns:
            df.set_index("timestamp", inplace=True)
            df.sort_index(inplace=True)

        # PHASE 1.2: Convert string columns to categories for memory efficiency
        categorical_columns = ["folder_name", "log_level", "log_event", "process_name", "library"]
        for col in categorical_columns:
            if col in df.columns:
                df[col] = df[col].astype("category")
                logger.debug("Converted column '%s' to categorical dtype", col)

        return df

    # ===============================================================
    # Get basic information about the log data
    # ===============================================================
    def get_filters(self) -> list[str]:
        """Get filters for the log data.

        Returns:
            Dictionary of filters (column headers)
        """
        return list(self._df.columns) if not self._df.empty else []

    def get_positions(self) -> list[str]:
        """Get list of unique positions (folder names).

        Returns:
            List of position names
        """
        logger.debug(
            "Getting positions from DataFrame. Empty: %s, Columns: %s", self._df.empty, self._df.columns if not self._df.empty else []
        )
        if self._df.empty or "folder_name" not in self._df.columns:
            logger.debug("No positions found - DataFrame is empty or missing folder_name column")
            return []

        positions = sorted(self._df["folder_name"].unique().tolist())
        logger.debug("Found positions: %s", positions)
        return positions

    def get_log_levels(self) -> list[str]:
        """Get list of unique log levels.

        Returns:
            List of log levels
        """
        if self._df.empty or "log_level" not in self._df.columns:
            return []

        return sorted(self._df["log_level"].unique().tolist())

    # ===============================================================
    # Time ranges
    # ===============================================================
    def get_time_range(self, filter: str | None = None, value: str | None = None) -> tuple[datetime | None, datetime | None]:
        """Get the time range covered by the logs, optionally filtered by a specified column.

        Args:
            filter: Name of the column to filter logs
            value: Value to filter the specified column

        Returns:
            Tuple of (start, end) timestamps, or (None, None) if no data
        """
        if filter and value:
            if self._df.empty or filter not in self._df.columns:
                return None, None

            filtered_df = self._df[self._df[filter] == value]
            if filtered_df.empty:
                return None, None

            start = filtered_df.index.min()
            end = filtered_df.index.max()
            return start, end

        return super().get_time_range()

    def get_position_time_range(self, position: str) -> tuple[datetime | None, datetime | None]:
        """Get the time range for a specific position.

        Args:
            position: Position name

        Returns:
            Tuple of (start, end) timestamps, or (None, None) if no data
        """
        if self._df.empty or "folder_name" not in self._df.columns:
            return None, None

        position_df = self._df[self._df["folder_name"] == position]
        if position_df.empty:
            return None, None

        start = position_df.index.min()
        end = position_df.index.max()

        return start, end

    def get_log_events(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
    ) -> dict[str, int]:
        """Get counts of log events by type.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            position: Filter by position name

        Returns:
            Dictionary mapping log_event to count
        """
        filters = {}
        if position:
            filters["folder_name"] = position

        df = self.query(start, end, **filters)
        if df.empty or "log_event" not in df.columns:
            return {}

        # Count by log_event
        event_counts = df["log_event"].value_counts().to_dict()

        return event_counts

    def get_log_level_counts(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
    ) -> dict[str, int]:
        """Get counts of log levels.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            position: Filter by position name

        Returns:
            Dictionary mapping log_level to count
        """
        filters = {}
        if position:
            filters["folder_name"] = position

        df = self.query(start, end, **filters)
        if df.empty or "log_level" not in df.columns:
            return {}

        # Count by log_level
        level_counts = df["log_level"].value_counts().to_dict()

        return level_counts

    def get_event_timeline(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
        freq: str = "1Min",
        event_type: str | None = None,
    ) -> dict[str, Any]:
        """Get event timeline for visualization.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            position: Filter by position name
            freq: Time frequency for binning ('1Min', '1h', etc.)
            event_type: Filter by event type

        Returns:
            Dictionary with timeline data:
            - timestamps: List of timestamp strings
            - counts: List of event counts
            - event_types: Dictionary mapping event types to counts per timestamp
            - events: Dictionary mapping event types to counts per timestamp (legacy)
        """
        filters = {}
        if position:
            filters["folder_name"] = position
        if event_type:
            filters["log_event"] = event_type

        df = self.query(start, end, **filters)
        if df.empty:
            return {"timestamps": [], "counts": [], "event_types": {}, "events": {}}

        # Count events by time period
        period_counts = df.resample(freq).size()

        # Count events by type per time period
        event_type_counts = {}
        if "log_event" in df.columns:
            for event_type in df["log_event"].unique():
                event_df = df[df["log_event"] == event_type]
                event_counts = event_df.resample(freq).size()
                # Convert timestamps to strings
                event_type_counts[event_type] = event_counts.values.tolist()

        timestamps = [ts.strftime("%Y-%m-%dT%H:%M:%S") for ts in period_counts.index]

        return {
            "timestamps": timestamps,
            "counts": period_counts.values.tolist(),
            "event_types": event_type_counts,
            # Provide legacy "events" field for backward compatibility
            "events": event_type_counts,
        }

    def get_log_level_timeline(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
        freq: str = "1Min",
        log_level: str | None = None,
    ) -> dict[str, Any]:
        """Get log level timeline for visualization.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            position: Filter by position name
            freq: Time frequency for binning ('1Min', '1h', etc.)
            log_level: Filter by log level

        Returns:
            Dictionary with timeline data:
            - timestamps: List of timestamp strings
            - counts: List of log counts
            - log_levels: Dictionary mapping log levels to counts per timestamp
        """
        filters = {}
        if position:
            filters["folder_name"] = position
        if log_level:
            filters["log_level"] = log_level

        df = self.query(start, end, **filters)
        if df.empty:
            return {"timestamps": [], "counts": [], "log_levels": {}}

        # Count logs by time period
        period_counts = df.resample(freq).size()

        # Count logs by level per time period
        log_level_counts = {}
        if "log_level" in df.columns:
            for level in df["log_level"].unique():
                level_df = df[df["log_level"] == level]
                level_counts = level_df.resample(freq).size()
                # Convert timestamps to strings
                log_level_counts[level] = level_counts.values.tolist()

        timestamps = [ts.strftime("%Y-%m-%dT%H:%M:%S") for ts in period_counts.index]

        return {
            "timestamps": timestamps,
            "counts": period_counts.values.tolist(),
            "log_levels": log_level_counts,
        }

    def _df_to_dict_list(self, df: pd.DataFrame) -> list[dict[str, Any]]:
        """Convert DataFrame to list of dictionaries.

        Args:
            df: DataFrame to convert

        Returns:
            List of dictionaries
        """
        if df.empty:
            return []

        # Reset index to add timestamp as column
        df_reset = df.reset_index()

        # Convert to list of dictionaries
        records = df_reset.to_dict("records")

        # Convert timestamps to ISO strings
        for record in records:
            if "timestamp" in record and hasattr(record["timestamp"], "isoformat"):
                record["timestamp"] = record["timestamp"].isoformat()

        return records

    def detect_log_level_anomalies(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        error_threshold: float = 0.05,
        warning_threshold: float = 0.40,
        window_size: str = "10min",
        **filters,
    ) -> list[dict[str, Any]]:
        """Detect anomalies in log levels based on error and warning rate thresholds.

        Args:
            start: Start time for analysis
            end: End time for analysis
            error_threshold: Percentage threshold for error rate (0.0 to 1.0)
            warning_threshold: Percentage threshold for warning rate (0.0 to 1.0)
            window_size: Time window size for analysis (pandas frequency string)
            **filters: Additional filters to apply to the data

        Returns:
            List of dictionaries containing anomaly information
        """
        # Get filtered dataframe
        df = self.query(start=start, end=end, **filters)

        if df.empty:
            return []

        # Create anomaly detector
        detector = AnomalyDetector(window_size=window_size)

        # Detect anomalies
        return detector.detect_log_level_threshold_anomalies(df, error_threshold=error_threshold, warning_threshold=warning_threshold)

    def detect_time_series_anomalies(
        self,
        column: str,
        method: str = "zscore",
        start: datetime | None = None,
        end: datetime | None = None,
        window_size: str = "1h",
        rolling_window: int = 24,
        zscore_threshold: float = 3.0,
        iqr_factor: float = 1.5,
        control_chart_std: float = 3.0,
        min_logs_per_window: int = None,
        **filters,
    ) -> list[dict[str, Any]]:
        """Detect anomalies in time series data using various statistical methods.

        Args:
            column: Column to analyze (e.g., 'log_level', 'log_event')
            method: Anomaly detection method to use:
                   - 'zscore': Z-score based detection (default)
                   - 'iqr': Interquartile Range based detection
                   - 'control_chart': Control chart style detection
                   - 'persist': Persistence-based detection using ADTK
                   - 'level_shift': Level shift detection using ADTK
            start: Start time for analysis
            end: End time for analysis
            window_size: Time window size for resampling (pandas frequency string)
            rolling_window: Number of windows to use for rolling statistics
            zscore_threshold: Threshold for z-score method (default: 3.0)
            iqr_factor: Factor for IQR method (default: 1.5)
            control_chart_std: Number of standard deviations for control chart (default: 3.0)
            min_logs_per_window: Minimum number of logs required in a window
            **filters: Additional filters to apply to the data

        Returns:
            List of dictionaries containing anomaly information
        """
        # Get filtered dataframe
        df = self.query(start=start, end=end, **filters)

        if df.empty:
            return []

        # Create anomaly detector
        detector = AnomalyDetector(window_size=window_size)

        # Detect anomalies
        return detector.detect_time_series_anomalies(
            df,
            column=column,
            method=method,
            window_size=window_size,
            rolling_window=rolling_window,
            zscore_threshold=zscore_threshold,
            iqr_factor=iqr_factor,
            control_chart_std=control_chart_std,
            min_logs_per_window=min_logs_per_window,
        )
