from __future__ import annotations

import logging

from ..models import LogEntry
from .base_analyzer import BaseAnalyzer
from .resource_analyzer import ResourceAnalyzer
from .structured_log_analyzer import StructuredLogAnalyzer
from .timeline_analyzer import TimelineAnalyzer

logger = logging.getLogger(__name__)


class AnalyzerFactory:
    """Factory class for creating and managing log analyzers.
    Provides a clean way to create analyzers for different log types.
    """

    # Class variable to store registered analyzers
    _analyzers: dict[str, type[BaseAnalyzer]] = {}
    _custom_config_path: str | None = None  # Add config path storage

    @classmethod
    def register_analyzer(cls, analyzer_class: type[BaseAnalyzer]) -> None:
        """Register an analyzer class with the factory.

        Args:
            analyzer_class: The analyzer class to register
        """
        # Create a temporary instance to get the type
        temp_instance = analyzer_class([])
        analyzer_type = temp_instance.analyzer_type

        cls._analyzers[analyzer_type] = analyzer_class
        logger.debug("Registered analyzer for type: %s", analyzer_type)

    @classmethod
    def set_custom_timeline_config(cls, config_path: str | None) -> None:
        """Set custom timeline event configuration file path.

        Args:
            config_path: Path to custom timeline event configuration file
        """
        cls._custom_config_path = config_path

    @classmethod
    def create_analyzer(cls, analyzer_type: str, log_entries: list[LogEntry]) -> BaseAnalyzer:
        """Create an analyzer for the specified type.

        Args:
            analyzer_type: The type of analyzer to create
            log_entries: List of log entries to analyze

        Returns:
            An instance of the appropriate analyzer

        Raises:
            ValueError: If no analyzer is registered for the given type
        """
        if analyzer_type not in cls._analyzers:
            raise ValueError("No analyzer registered for type: %s" % analyzer_type)

        # Special handling for timeline analyzer to pass custom config
        if analyzer_type == "timeline":
            return TimelineAnalyzer(log_entries, cls._custom_config_path)

        return cls._analyzers[analyzer_type](log_entries)

    @classmethod
    def get_available_analyzers(cls) -> list[str]:
        """Get a list of available analyzer types.

        Returns:
            List of registered analyzer types
        """
        return list(cls._analyzers.keys())

    @classmethod
    def create_analyzers_for_logs(cls, log_entries: dict[str, list[LogEntry]]) -> dict[str, BaseAnalyzer]:
        """Create appropriate analyzers for each log type in the dictionary.

        Args:
            log_entries: Dictionary mapping log types to log entries

        Returns:
            Dictionary mapping log types to analyzer instances
        """
        analyzers = {}

        for log_type, entries in log_entries.items():
            if not entries:
                logger.warning("No log entries for type: %s", log_type)
                continue

            try:
                analyzer = cls.create_analyzer(log_type, entries)
                analyzers[log_type] = analyzer
                logger.debug("Created analyzer for type %s with %d entries", log_type, len(entries))
            except ValueError:
                logger.exception("Error creating analyzer for type %s", log_type)

        return analyzers


# Register analyzers
AnalyzerFactory.register_analyzer(ResourceAnalyzer)
AnalyzerFactory.register_analyzer(StructuredLogAnalyzer)
AnalyzerFactory.register_analyzer(TimelineAnalyzer)
