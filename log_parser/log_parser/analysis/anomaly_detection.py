from __future__ import annotations

from typing import Any

import pandas as pd
from adtk.data import validate_series
from adtk.detector import LevelShiftAD, PersistAD

from ..utils.logging import logger


class AnomalyDetector:
    """Detector for anomalies in structured logs.
    Provides multiple methods for anomaly detection.
    """

    def __init__(self, window_size: str = "1h", min_logs_per_window: int = None, rolling_window: int = 24):
        """Initialize the anomaly detector.

        Args:
            window_size: Time window size for analysis (pandas frequency string)
            min_logs_per_window: Minimum number of logs required in a window to be considered
                               If None, will be calculated based on window size
            rolling_window: Number of windows to use for rolling statistics
        """
        # Convert any 'T' to 'min' in the window size
        self.window_size = window_size.replace("T", "min")

        # Set minimum logs per window based on window size if not provided
        if min_logs_per_window is None:
            # Parse window size to get number of minutes
            window_minutes = int("".join(filter(str.isdigit, self.window_size)))
            # Require at least 5 logs per 10 minutes, scaled by window size
            self.min_logs_per_window = max(5, int(5 * window_minutes / 10))
        else:
            self.min_logs_per_window = min_logs_per_window

    def detect_log_level_threshold_anomalies(
        self,
        df: pd.DataFrame,
        error_threshold: float = 0.05,  # 5% default threshold for errors
        warning_threshold: float = 0.40,
    ) -> list[dict[str, Any]]:  # 40% default threshold for warnings
        """Detect anomalies in log levels based on error and warning rate thresholds.

        Args:
            df: DataFrame with log entries, must have 'log_level' column and datetime index
            error_threshold: Percentage threshold for error rate (0.0 to 1.0)
            warning_threshold: Percentage threshold for warning rate (0.0 to 1.0)

        Returns:
            List of dictionaries containing anomaly information:
            {
                'start': datetime,
                'end': datetime,
                'type': 'error' or 'warning',
                'rate': float,  # The actual rate that exceeded threshold
                'threshold': float  # The threshold that was exceeded
            }
        """
        if df.empty or "log_level" not in df.columns:
            return []

        # Resample by window size and calculate rates
        total_counts = df.resample(self.window_size).size()
        error_counts = df[df["log_level"] == "ERROR"].resample(self.window_size).size()
        warning_counts = df[df["log_level"] == "WARNING"].resample(self.window_size).size()

        # Calculate rates, handling division by zero
        error_rates = pd.Series(0.0, index=total_counts.index)
        warning_rates = pd.Series(0.0, index=total_counts.index)

        # Only calculate rates where we have logs
        mask = total_counts > 0
        error_rates[mask] = error_counts[mask] / total_counts[mask]
        warning_rates[mask] = warning_counts[mask] / total_counts[mask]

        # Filter out windows with too few logs to be statistically significant
        significant_windows = total_counts >= self.min_logs_per_window

        # Only consider rates in windows with enough logs
        error_rates = error_rates[significant_windows]
        warning_rates = warning_rates[significant_windows]

        anomalies = []

        # Detect error rate anomalies
        error_anomalies = self._detect_threshold_anomalies(error_rates, error_threshold, "error")
        anomalies.extend(error_anomalies)

        # Detect warning rate anomalies
        warning_anomalies = self._detect_threshold_anomalies(warning_rates, warning_threshold, "warning")
        anomalies.extend(warning_anomalies)

        # Sort anomalies by start time
        anomalies.sort(key=lambda x: x["start"])

        return anomalies

    def _detect_threshold_anomalies(self, series: pd.Series, threshold: float, anomaly_type: str) -> list[dict[str, Any]]:
        """Detect anomalies where values exceed the threshold.

        Args:
            series: Time series of rates
            threshold: Threshold to check against
            anomaly_type: Type of anomaly ('error' or 'warning')

        Returns:
            List of anomaly dictionaries
        """
        anomalies = []

        # Find windows where rate exceeds threshold
        anomaly_windows = series[series > threshold]

        if anomaly_windows.empty:
            return anomalies

        # Group consecutive windows into continuous periods
        current_window = None

        for timestamp, rate in anomaly_windows.items():
            if current_window is None:
                current_window = {
                    "start": timestamp,
                    "end": timestamp + pd.Timedelta(self.window_size),
                    "type": anomaly_type,
                    "rate": rate,
                    "threshold": threshold,
                }
            # Extend current window if consecutive
            elif timestamp <= current_window["end"]:
                current_window["end"] = timestamp + pd.Timedelta(self.window_size)
                current_window["rate"] = max(current_window["rate"], rate)
            else:
                # Start new window
                anomalies.append(current_window)
                current_window = {
                    "start": timestamp,
                    "end": timestamp + pd.Timedelta(self.window_size),
                    "type": anomaly_type,
                    "rate": rate,
                    "threshold": threshold,
                }

        # Add last window if exists
        if current_window is not None:
            anomalies.append(current_window)

        return anomalies

    def detect_time_series_anomalies(
        self,
        df: pd.DataFrame,
        column: str,
        method: str = "zscore",
        window_size: str = "1h",
        rolling_window: int = 24,
        zscore_threshold: float = 3.0,
        iqr_factor: float = 1.5,
        control_chart_std: float = 3.0,
        min_logs_per_window: int | None = None,
        level_shift_window: int = 10,
        min_data_coverage: float = 0.05,
    ) -> list[dict[str, Any]]:
        """Detect anomalies in time series data using various statistical methods.

        Args:
            df: DataFrame with log entries, must have datetime index
            column: Column to analyze (e.g., 'log_level', 'log_event', or a resource metric)
            method: Anomaly detection method to use:
                   - 'zscore': Z-score based detection (default)
                   - 'iqr': Interquartile Range based detection
                   - 'control_chart': Control chart style detection
                   - 'persist': Persistence-based detection using ADTK
                   - 'level_shift': Level shift detection using ADTK
            window_size: Time window size for resampling (pandas frequency string)
            rolling_window: Number of windows to use for rolling statistics
            zscore_threshold: Threshold for z-score method (default: 3.0)
            iqr_factor: Factor for IQR method (default: 1.5)
            control_chart_std: Number of standard deviations for control chart (default: 3.0)
            min_logs_per_window: Minimum number of logs required in a window
            level_shift_window: Window size for level shift detection (default: 10)
            min_data_coverage: Minimum percentage of non-null values required (0.0 to 1.0)

        Returns:
            List of dictionaries containing anomaly information
        """
        if df.empty or column not in df.columns:
            return []

        # Check data coverage
        non_null_count = df[column].count()
        total_count = len(df)
        coverage = non_null_count / total_count if total_count > 0 else 0

        if coverage < min_data_coverage:
            logger.warning("Column '%s' has insufficient data coverage: %.2f%% < %.2f%%", column, coverage * 100, min_data_coverage * 100)
            return []

        # Handle categorical data
        if df[column].dtype == "object" or df[column].dtype.name == "category":
            # For categorical data, we need to handle it differently based on the method
            if method in ["zscore", "iqr", "control_chart"]:
                # Convert categorical data to numeric using frequency encoding
                value_counts = df[column].value_counts(normalize=True)
                # Store original values for later use
                original_values = df[column].copy()
                df = df.copy()  # Create a copy to avoid modifying the original dataframe
                # Map categorical values to frequencies and ensure it's numeric
                df[column] = df[column].map(value_counts).astype(float)
                # Now treat it as numeric data
                if "timestamp" in df.columns:
                    series = df.resample(window_size, on="timestamp")[column].mean()
                    # Store all categories and their frequencies for each timestamp using original values
                    category_frequencies = original_values.groupby(pd.Grouper(key="timestamp", freq=window_size)).apply(
                        lambda x: x.value_counts(normalize=True).to_dict() if not x.empty else {}
                    )
                else:
                    series = df.resample(window_size)[column].mean()
                    # Store all categories and their frequencies for each timestamp using original values
                    category_frequencies = original_values.groupby(pd.Grouper(freq=window_size)).apply(
                        lambda x: x.value_counts(normalize=True).to_dict() if not x.empty else {}
                    )
                # Attach the category frequencies to the series for later use
                series.category_frequencies = category_frequencies
            else:
                # For other methods, use mode aggregation but convert result to numeric for anomaly detection
                try:
                    if "timestamp" in df.columns:
                        series = df.groupby(pd.Grouper(key="timestamp", freq=window_size))[column].agg(
                            lambda x: x.mode().iloc[0] if not x.empty and len(x.mode()) > 0 else None
                        )
                    else:
                        series = df.groupby(pd.Grouper(freq=window_size))[column].agg(
                            lambda x: x.mode().iloc[0] if not x.empty and len(x.mode()) > 0 else None
                        )
                    # Convert categorical values to numeric codes for anomaly detection algorithms
                    if not series.empty:
                        unique_values = series.dropna().unique()
                        value_to_code = {val: idx for idx, val in enumerate(unique_values)}
                        series = series.map(value_to_code).astype(float)
                except (IndexError, AttributeError):
                    logger.warning("Could not determine most frequent category for column '%s'", column)
                    return []
        # For numeric data, resample and calculate mean
        elif "timestamp" in df.columns:
            series = df.resample(window_size, on="timestamp")[column].mean()
        else:
            # Use index for resampling
            series = df.resample(window_size)[column].mean()

        # Filter out windows with too few logs
        if min_logs_per_window is not None:
            if "timestamp" in df.columns:
                log_counts = df.resample(window_size, on="timestamp").size()
            else:
                log_counts = df.resample(window_size).size()
            series = series[log_counts >= min_logs_per_window]

        # Fill NaN values
        series = series.ffill().bfill()

        # Check if series is empty after processing
        if series.empty:
            return []

        # Choose the appropriate detection method
        if method == "zscore":
            return self._detect_zscore_anomalies(series, zscore_threshold, rolling_window, column)
        if method == "iqr":
            return self._detect_iqr_anomalies(series, iqr_factor, rolling_window, column)
        if method == "control_chart":
            return self._detect_control_chart_anomalies(series, control_chart_std, rolling_window, column)
        if method == "persist":
            return self._detect_persist_anomalies(series, column)
        if method == "level_shift":
            return self._detect_level_shift_anomalies(series, level_shift_window, column)
        raise ValueError("Unknown anomaly detection method: %s" % method)

    def _detect_zscore_anomalies(
        self, series: pd.Series, threshold: float = 3.0, window: int = 24, column: str = None
    ) -> list[dict[str, Any]]:
        """Detect anomalies using z-score method.

        Args:
            series: Time series data
            threshold: Z-score threshold (default: 3.0)
            window: Rolling window size for mean and std calculation
            column: Name of the column being analyzed

        Returns:
            List of anomaly dictionaries
        """
        # Check if series is empty
        if series.empty:
            return []

        # Calculate rolling mean and standard deviation
        rolling_mean = series.rolling(window=window, min_periods=1).mean()
        rolling_std = series.rolling(window=window, min_periods=1).std()

        # Calculate z-scores
        z_scores = (series - rolling_mean) / rolling_std.replace(0, 1)  # Avoid division by zero

        # Find anomalies (absolute z-score > threshold)
        anomalies = []

        for timestamp, z_score in z_scores.items():
            if abs(z_score) > threshold:
                # Get all categories and their frequencies if this is categorical data
                category_frequencies = None
                if hasattr(series, "category_frequencies") and timestamp in series.category_frequencies:
                    category_frequencies = series.category_frequencies[timestamp]

                anomaly = {
                    "start": timestamp,
                    "end": timestamp + pd.Timedelta(self.window_size),
                    "type": "zscore",
                    "value": series[timestamp],
                    "threshold": threshold,
                    "column": column,
                    "details": {
                        "z_score": z_score,
                        "mean": rolling_mean[timestamp],
                        "std": rolling_std[timestamp],
                    },
                }

                # Add category frequencies if available
                if category_frequencies is not None:
                    anomaly["details"]["category_frequencies"] = category_frequencies

                anomalies.append(anomaly)

        return anomalies

    def _detect_iqr_anomalies(self, series: pd.Series, factor: float = 1.5, window: int = 24, column: str = None) -> list[dict[str, Any]]:
        """Detect anomalies using Interquartile Range (IQR) method.

        Args:
            series: Time series data
            factor: IQR factor (default: 1.5)
            window: Rolling window size for quartile calculation
            column: Name of the column being analyzed

        Returns:
            List of anomaly dictionaries
        """
        # Check if series is empty
        if series.empty:
            return []

        anomalies = []

        # Calculate rolling quartiles
        rolling_q1 = series.rolling(window=window, min_periods=1).quantile(0.25)
        rolling_q3 = series.rolling(window=window, min_periods=1).quantile(0.75)
        rolling_iqr = rolling_q3 - rolling_q1

        # Calculate lower and upper bounds
        lower_bound = rolling_q1 - factor * rolling_iqr
        upper_bound = rolling_q3 + factor * rolling_iqr

        # Find anomalies
        for timestamp, value in series.items():
            if value < lower_bound[timestamp] or value > upper_bound[timestamp]:
                # Get all categories and their frequencies if this is categorical data
                category_frequencies = None
                if hasattr(series, "category_frequencies") and timestamp in series.category_frequencies:
                    category_frequencies = series.category_frequencies[timestamp]

                anomaly = {
                    "start": timestamp,
                    "end": timestamp + pd.Timedelta(self.window_size),
                    "type": "iqr",
                    "value": value,
                    "threshold": factor,
                    "column": column,
                    "details": {
                        "q1": rolling_q1[timestamp],
                        "q3": rolling_q3[timestamp],
                        "iqr": rolling_iqr[timestamp],
                        "lower_bound": lower_bound[timestamp],
                        "upper_bound": upper_bound[timestamp],
                    },
                }

                # Add category frequencies if available
                if category_frequencies is not None:
                    anomaly["details"]["category_frequencies"] = category_frequencies

                anomalies.append(anomaly)

        return anomalies

    def _detect_control_chart_anomalies(
        self, series: pd.Series, std_factor: float = 3.0, window: int = 24, column: str = None
    ) -> list[dict[str, Any]]:
        """Detect anomalies using control chart style method.

        Args:
            series: Time series data
            std_factor: Number of standard deviations for control limits (default: 3.0)
            window: Rolling window size for mean and std calculation
            column: Name of the column being analyzed

        Returns:
            List of anomaly dictionaries
        """
        # Check if series is empty
        if series.empty:
            return []

        # Calculate rolling mean and standard deviation
        rolling_mean = series.rolling(window=window, min_periods=1).mean()
        rolling_std = series.rolling(window=window, min_periods=1).std()

        # Calculate control limits
        upper_limit = rolling_mean + std_factor * rolling_std
        lower_limit = rolling_mean - std_factor * rolling_std

        # Find anomalies
        anomalies = []

        for timestamp, value in series.items():
            if value > upper_limit[timestamp] or value < lower_limit[timestamp]:
                anomaly = {
                    "start": timestamp,
                    "end": timestamp + pd.Timedelta(self.window_size),
                    "type": "control_chart",
                    "value": value,
                    "threshold": std_factor,
                    "column": column,
                    "details": {
                        "mean": rolling_mean[timestamp],
                        "std": rolling_std[timestamp],
                        "upper_limit": upper_limit[timestamp],
                        "lower_limit": lower_limit[timestamp],
                    },
                }
                anomalies.append(anomaly)

        return anomalies

    def _detect_persist_anomalies(self, series: pd.Series, column: str = None) -> list[dict[str, Any]]:
        """Detect anomalies using persistence-based detection from ADTK.

        Args:
            series: Time series data
            column: Name of the column being analyzed

        Returns:
            List of anomaly dictionaries
        """
        # Check if series is empty
        if series.empty:
            return []

        # Validate series for ADTK
        series = validate_series(series)

        # Create detector
        detector = PersistAD()

        try:
            # Detect anomalies
            anomalies_idx = detector.fit_detect(series)

            # Convert boolean mask to list of anomalies
            anomalies = []

            # Find continuous anomaly periods
            current_anomaly = None

            for i, (timestamp, is_anomaly) in enumerate(zip(series.index, anomalies_idx, strict=False)):
                if is_anomaly:
                    if current_anomaly is None:
                        current_anomaly = {
                            "start": timestamp,
                            "end": timestamp + pd.Timedelta(self.window_size),
                            "type": "persist",
                            "value": series[timestamp],
                            "threshold": None,
                            "column": column,
                            "details": {"method": "Persistence-based detection"},
                        }
                    else:
                        current_anomaly["end"] = timestamp + pd.Timedelta(self.window_size)
                        current_anomaly["value"] = max(current_anomaly["value"], series[timestamp])
                elif current_anomaly is not None:
                    anomalies.append(current_anomaly)
                    current_anomaly = None

            # Add last anomaly if exists
            if current_anomaly is not None:
                anomalies.append(current_anomaly)

            return anomalies
        except (ValueError, TypeError, AttributeError, ImportError) as e:
            logger.warning("Persistence detection failed: %s", str(e))
            return []

    def _detect_level_shift_anomalies(self, series: pd.Series, window: int = 10, column: str = None) -> list[dict[str, Any]]:
        """Detect anomalies using level shift detection from ADTK.

        Args:
            series: Time series data
            window: Window size for level shift detection (default: 10)
            column: Name of the column being analyzed

        Returns:
            List of anomaly dictionaries
        """
        # Check if series is empty
        if series.empty:
            return []

        # Validate series for ADTK
        series = validate_series(series)

        try:
            # Create detector with window parameter
            detector = LevelShiftAD(window=window)

            # Detect anomalies
            anomalies_idx = detector.fit_detect(series)

            # Convert boolean mask to list of anomalies
            anomalies = []

            # Find continuous anomaly periods
            current_anomaly = None

            for i, (timestamp, is_anomaly) in enumerate(zip(series.index, anomalies_idx, strict=False)):
                if is_anomaly:
                    if current_anomaly is None:
                        # Calculate the mean before and after the shift
                        if i >= window:
                            before_mean = series.iloc[i - window : i].mean()
                        else:
                            before_mean = series.iloc[:i].mean() if i > 0 else series.iloc[0]

                        if i + window < len(series):
                            after_mean = series.iloc[i : i + window].mean()
                        else:
                            after_mean = series.iloc[i:].mean()

                        shift_magnitude = abs(after_mean - before_mean)

                        # Only create an anomaly if the shift is significant
                        if shift_magnitude > 5:  # Minimum shift magnitude threshold
                            current_anomaly = {
                                "start": timestamp,
                                "end": timestamp + pd.Timedelta(self.window_size),
                                "type": "level_shift",
                                "value": series[timestamp],
                                "threshold": None,
                                "column": column,
                                "details": {
                                    "method": "Level shift detection",
                                    "shift_magnitude": shift_magnitude,
                                    "before_mean": before_mean,
                                    "after_mean": after_mean,
                                },
                            }
                    else:
                        current_anomaly["end"] = timestamp + pd.Timedelta(self.window_size)
                        current_anomaly["value"] = max(current_anomaly["value"], series[timestamp])
                elif current_anomaly is not None:
                    anomalies.append(current_anomaly)
                    current_anomaly = None

            # Add last anomaly if exists
            if current_anomaly is not None:
                anomalies.append(current_anomaly)

            return anomalies
        except (ValueError, TypeError, AttributeError, ImportError) as e:
            logger.warning("Level shift detection failed: %s", str(e))
            return []
