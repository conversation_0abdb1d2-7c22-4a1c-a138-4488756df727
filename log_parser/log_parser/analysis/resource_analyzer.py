from __future__ import annotations

import logging
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd

from log_parser.models import LogEntry, ResourceLogEntry

from .anomaly_detection import AnomalyDetector
from .base_analyzer import BaseAnalyzer

logger = logging.getLogger(__name__)


class ResourceAnalyzer(BaseAnalyzer):
    """Analyzer for Resource-System log data.
    Provides specialized methods for system resource analysis.
    """

    @property
    def analyzer_type(self) -> str:
        return "resource"

    def _create_dataframe(self, log_entries: list[LogEntry]) -> pd.DataFrame:
        """Convert resource log entries to a DataFrame.

        Args:
            log_entries: List of log entries

        Returns:
            DataFrame with log entries
        """
        # Filter for resource log entries
        resource_entries = [entry for entry in log_entries if isinstance(entry, ResourceLogEntry)]

        if not resource_entries:
            logger.warning("No resource log entries found")
            return pd.DataFrame()

        # Extract data from log entries
        records = []

        # First pass: discover all possible columns across all entries
        all_columns = set()
        for entry in resource_entries:
            all_columns.update(entry.data.keys())

        logger.debug("Found %d unique columns in resource logs", len(all_columns))
        logger.debug("First 10 columns: %s", list(all_columns)[:10])

        # Special handling for block device data
        block_device_names = set()
        valid_block_columns = set()
        for column in all_columns:
            if isinstance(column, str) and column.startswith("Block["):
                try:
                    idx = int(column[column.index("[") + 1 : column.index("]")])
                    if ".Name" in column:
                        name_key = f"Block[{idx}].Name"
                        valid_block_columns.add(name_key)

                        for entry in resource_entries:
                            if entry.data.get(name_key):
                                block_device_names.add(entry.data[name_key])
                    else:
                        # Add all valid block device columns
                        valid_block_columns.add(column)
                except (ValueError, IndexError):
                    continue

        logger.debug("Found %d unique block devices", len(block_device_names))
        if block_device_names:
            logger.debug("Block devices: %s", block_device_names)

        # Second pass: create records with all columns
        for entry in resource_entries:
            # Filter out malformed block device columns
            record = {}
            for col in all_columns:
                if isinstance(col, str) and col.startswith("Block["):
                    # Only include block device columns if they're valid
                    if col in valid_block_columns:
                        record[col] = entry.data.get(col)
                else:
                    # Include all non-block device columns
                    record[col] = entry.data.get(col)

            record["timestamp"] = entry.timestamp

            # Add block device data as flattened columns
            for device_name in block_device_names:
                record[f"{device_name}_read"] = None
                record[f"{device_name}_write"] = None
                record[f"{device_name}_saturation"] = None

                for idx_key in record:
                    if isinstance(idx_key, str) and idx_key.startswith("Block[") and ".Name" in idx_key:
                        try:
                            idx = idx_key[idx_key.index("[") + 1 : idx_key.index("]")]
                            if record.get(idx_key) == device_name:
                                # Add device metrics
                                read_key = f"Block[{idx}].ReadBytesPerSecond"
                                write_key = f"Block[{idx}].WriteBytesPerSecond"
                                saturation_key = f"Block[{idx}].Saturation"

                                record[f"{device_name}_read"] = record.get(read_key)
                                record[f"{device_name}_write"] = record.get(write_key)
                                record[f"{device_name}_saturation"] = record.get(saturation_key)
                        except (ValueError, IndexError):
                            continue

            records.append(record)

        if not records:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index
        df = pd.DataFrame.from_records(records)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        # PHASE 1.2: Convert string columns to categories for memory efficiency
        categorical_columns = ["folder_name", "log_level", "log_event", "process_name", "library"]
        for col in categorical_columns:
            if col in df.columns:
                df[col] = df[col].astype("category")
                logger.debug("Converted column '%s' to categorical dtype", col)

        # Calculate column statistics
        if not df.empty:
            self._calculate_column_stats(df)

        return df

    def _calculate_column_stats(self, df: pd.DataFrame) -> None:
        """Calculate statistics for DataFrame columns.

        Args:
            df: DataFrame to calculate statistics for
        """
        self.column_stats = {}
        total_entries = len(df)  # Convert to Python int

        for col in df.columns:
            non_null_count = int(df[col].count())  # Convert to Python int
            first_seen = df[col].first_valid_index()
            last_seen = df[col].last_valid_index()

            # Safely convert timestamps to strings for JSON serialization
            first_seen_str = None
            if first_seen:
                if isinstance(first_seen, datetime):
                    first_seen_str = first_seen.isoformat()
                else:
                    first_seen_str = str(first_seen)

            last_seen_str = None
            if last_seen:
                if isinstance(last_seen, datetime):
                    last_seen_str = last_seen.isoformat()
                else:
                    last_seen_str = str(last_seen)

            # Calculate completeness as a Python float
            completeness = float((non_null_count / total_entries * 100) if total_entries > 0 else 0)

            self.column_stats[col] = {
                "completeness": completeness,
                "first_seen": first_seen_str,
                "last_seen": last_seen_str,
                "non_null_count": non_null_count,
                "total_entries": total_entries,
            }

    def get_resource_column(self, column: str, start: datetime | None = None, end: datetime | None = None) -> dict[str, Any]:
        """Get data for a specific resource column with timestamp and value pairs.

        Args:
            column: Name of the column to retrieve
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)

        Returns:
            Dictionary with timestamps and values
        """
        df = self.query(start, end)
        if df.empty or column not in df.columns:
            return {"timestamps": [], "values": []}

        # Filter out NaN values
        df_filtered = df[df[column].notna()]

        # Convert to lists for JSON serialization
        timestamps = [ts.isoformat() for ts in df_filtered.index]
        values = df_filtered[column].tolist()

        return {"timestamps": timestamps, "values": values}

    def query(self, start: datetime | None = None, end: datetime | None = None, **filters) -> pd.DataFrame:
        """Query the dataframe with filtering options.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)
            **filters: Additional filters to apply

        Returns:
            Filtered DataFrame
        """
        # PHASE 2: Use parent's cached query method for optimal performance
        return super().query(start=start, end=end, **filters)

    def get_system_overview(self, start: datetime | None = None, end: datetime | None = None) -> dict[str, Any]:
        """Get an overview of system resources for the specified time range.

        Args:
            start: Start timestamp (inclusive)
            end: End timestamp (inclusive)

        Returns:
            Dictionary with system overview metrics
        """
        try:
            # Validate and convert timestamps
            pandas_start = None
            pandas_end = None

            if start:
                if isinstance(start, (list, np.ndarray)) or (hasattr(start, "__iter__") and not isinstance(start, (str, bytes))):
                    logger.warning("Start timestamp is an array-like object, using None instead")
                else:
                    pandas_start = pd.to_datetime(start) if not isinstance(start, pd.Timestamp) else start

            if end:
                if isinstance(end, (list, np.ndarray)) or (hasattr(end, "__iter__") and not isinstance(end, (str, bytes))):
                    logger.warning("End timestamp is an array-like object, using None instead")
                else:
                    pandas_end = pd.to_datetime(end) if not isinstance(end, pd.Timestamp) else end

            # Query with validated timestamps
            df = self.query(start=pandas_start, end=pandas_end)

            if df.empty:
                return {}

            # Key metrics to include in overview
            metrics = {
                "cpu": {
                    "usage": self._aggregate_column(df, "CPU_Usage", "mean"),
                    "load_1min": self._aggregate_column(df, "CPU_LoadAvg_1min", "mean"),
                    "load_5min": self._aggregate_column(df, "CPU_LoadAvg_5min", "mean"),
                    "load_15min": self._aggregate_column(df, "CPU_LoadAvg_15min", "mean"),
                },
                "memory": {
                    "used_gb": None,  # Changed to match test expectations
                    "total_gb": None,  # Changed to match test expectations
                    "free": None,  # Keep for additional info
                    "usage_percent": self._aggregate_column(df, "Memory_UsedPercent", "mean"),
                },
                "disk": {"usage_percent": {}},
                "network": {"received_mbps": {}, "sent_mbps": {}},
            }

            # Handle memory values safely before division
            memory_total = self._aggregate_column(df, "Memory_Total", "mean")
            memory_used = self._aggregate_column(df, "Memory_Used", "mean")
            memory_free = self._aggregate_column(df, "Memory_Free", "mean")

            # Convert to GB to match test expectations
            if memory_total is not None and memory_total > 0:
                # Convert from bytes to GB (1 GB = 1073741824 bytes = 1024^3)
                metrics["memory"]["total_gb"] = memory_total / (1024**3)

            if memory_used is not None and memory_used > 0:
                # Convert from bytes to GB
                metrics["memory"]["used_gb"] = memory_used / (1024**3)

            if memory_free is not None and memory_free > 0:
                # Keep free memory in bytes
                metrics["memory"]["free"] = memory_free

            # Find disk usage columns
            disk_columns = [col for col in df.columns if col.endswith("_DiskUsedPercent")]
            for col in disk_columns:
                disk_name = col.replace("_DiskUsedPercent", "")
                metrics["disk"]["usage_percent"][disk_name] = self._aggregate_column(df, col, "mean")

            # Find network columns
            recv_columns = [col for col in df.columns if "_BytesRecv" in col or "_received" in col.lower()]
            sent_columns = [col for col in df.columns if "_BytesSent" in col or "_sent" in col.lower()]

            for col in recv_columns:
                if "Network" in col:
                    interface = col.split("_")[1]  # Assuming format like Network_eth0_BytesRecv
                    recv_value = self._aggregate_column(df, col, "mean")
                    if recv_value is not None and recv_value > 0:
                        metrics["network"]["received_mbps"][interface] = recv_value / (1024 * 1024 / 8)

            for col in sent_columns:
                if "Network" in col:
                    interface = col.split("_")[1]  # Assuming format like Network_eth0_BytesSent
                    sent_value = self._aggregate_column(df, col, "mean")
                    if sent_value is not None and sent_value > 0:
                        metrics["network"]["sent_mbps"][interface] = sent_value / (1024 * 1024 / 8)

            return metrics
        except Exception:
            logger.exception("Error in get_system_overview")
            return {}

    def get_column_stats(self) -> dict[str, dict[str, Any]]:
        """Get statistics for all columns in the resource data.

        Returns:
            Dictionary with column statistics
        """
        return self.column_stats if hasattr(self, "column_stats") else {}

    def detect_time_series_anomalies(
        self,
        column: str,
        method: str = "zscore",
        start: datetime | None = None,
        end: datetime | None = None,
        window_size: str = "1h",
        rolling_window: int = 24,
        zscore_threshold: float = 3.0,
        iqr_factor: float = 1.5,
        control_chart_std: float = 3.0,
        min_logs_per_window: int = None,
        **filters,
    ) -> list[dict[str, Any]]:
        """Detect anomalies in time series data using various statistical methods.

        Args:
            column: Column to analyze (e.g., 'cpu_percent', 'memory_percent')
            method: Anomaly detection method to use:
                   - 'zscore': Z-score based detection (default)
                   - 'iqr': Interquartile Range based detection
                   - 'control_chart': Control chart style detection
                   - 'persist': Persistence-based detection using ADTK
                   - 'level_shift': Level shift detection using ADTK
            start: Start time for analysis
            end: End time for analysis
            window_size: Time window size for resampling (pandas frequency string)
            rolling_window: Number of windows to use for rolling statistics
            zscore_threshold: Threshold for z-score method (default: 3.0)
            iqr_factor: Factor for IQR method (default: 1.5)
            control_chart_std: Number of standard deviations for control chart (default: 3.0)
            min_logs_per_window: Minimum number of logs required in a window
            **filters: Additional filters to apply to the data

        Returns:
            List of dictionaries containing anomaly information
        """
        # Get filtered dataframe
        df = self.query(start=start, end=end, **filters)

        if df.empty or column not in df.columns:
            return []

        # Create anomaly detector
        detector = AnomalyDetector(window_size=window_size)

        # Detect anomalies
        return detector.detect_time_series_anomalies(
            df,
            column=column,
            method=method,
            window_size=window_size,
            rolling_window=rolling_window,
            zscore_threshold=zscore_threshold,
            iqr_factor=iqr_factor,
            control_chart_std=control_chart_std,
            min_logs_per_window=min_logs_per_window,
        )

    def _aggregate_column(self, df: pd.DataFrame, column: str, func: str) -> Any:
        """Aggregate a column using the specified function.

        Args:
            df: DataFrame to aggregate
            column: Column to aggregate
            func: Aggregation function ('mean', 'sum', 'min', 'max', etc.)

        Returns:
            Aggregated value or None if column not found or all values are NaN
        """
        if column not in df.columns:
            return None

        # Check if the column has any non-null values
        if df[column].isna().all():
            return None

        try:
            result = getattr(df[column], func)()
            return None if pd.isna(result) else result
        except Exception as e:
            logger.warning("Error aggregating column %s with function %s: %s", column, func, str(e))
            return None
