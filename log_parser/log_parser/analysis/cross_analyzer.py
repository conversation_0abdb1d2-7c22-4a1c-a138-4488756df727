from __future__ import annotations

import logging
import threading
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import numpy as np
import pandas as pd

from ..utils import _log_cache_operation, _track_parser_performance
from ..utils.serialization import convert_to_serializable
from ..utils.time_utils import to_pandas_timestamp, to_python_datetime
from .resource_analyzer import ResourceAnalyzer
from .structured_log_analyzer import StructuredLogAnalyzer

logger = logging.getLogger(__name__)

# Global performance tracking for cross analyzer operations
_cross_analyzer_performance_counters = {}


class CrossAnalyzer:
    """Cross-log analyzer for analyzing correlations between different log types.
    This is not a BaseAnalyzer subclass because it works on multiple analyzers.

    Timestamp handling follows these conventions:
    - Storage: Python datetime objects are used in models and for storage
    - Analysis: pandas Timestamp objects are used for all DataFrame operations
    - API: Python datetime objects are returned, which get converted to ISO strings at the API layer
    """

    def __init__(self, resource_analyzer: ResourceAnalyzer, minknow_analyzer: StructuredLogAnalyzer):
        """Initialize with resource and minknow analyzers.

        Args:
            resource_analyzer: Analyzer for resource logs
            minknow_analyzer: Analyzer for minknow logs
        """
        logger.debug("CROSS_ANALYZER_INIT_START: Initializing CrossAnalyzer")

        self.resource_analyzer = resource_analyzer
        self.minknow_analyzer = minknow_analyzer

        # Cache storage
        self._cache = {}
        self._cache_timestamp = {}
        self._cache_ttl = 600  # Cache TTL in seconds (10 minutes)

        # Initialize common data
        self._common_time_range = None
        self._positions = None
        self._column_availability = {}
        self._log_summary = None

        # Meta information
        self.initialized = False

        logger.debug("CROSS_ANALYZER_INIT_END: CrossAnalyzer initialization completed")

    @_track_parser_performance("cross_analyzer_initialize")
    def initialize(self):
        """Pre-calculate and cache commonly used data.
        This method should be called during application startup.
        """
        logger.info("CROSS_ANALYZER_INITIALIZE: Starting initialization with pre-calculated data")
        start_time = time.time()

        # Get and cache common time range - this is fast and important
        self._common_time_range = self.get_common_time_range()
        logger.debug("CROSS_ANALYZER_CACHE_SET: common_time_range cached: %s", self._common_time_range)

        # Get and cache positions - this is fast
        self._positions = self.minknow_analyzer.get_positions()
        logger.debug("CROSS_ANALYZER_CACHE_SET: positions cached: %d positions", len(self._positions))

        # Pre-calculate column availability for common time range only
        # Position-specific calculations will be done on-demand
        common_start, common_end = self._common_time_range
        if common_start and common_end:
            try:
                pandas_start = to_pandas_timestamp(common_start)
                pandas_end = to_pandas_timestamp(common_end)

                if pandas_start and pandas_end:
                    self._column_availability["common"] = self._calculate_column_availability(pandas_start, pandas_end)
                    logger.debug("CROSS_ANALYZER_CACHE_SET: column_availability cached for common time range")
            except (ValueError, TypeError, AttributeError, KeyError):
                logger.exception("CROSS_ANALYZER_ERROR: Error calculating column availability for common time range")
                # Initialize with empty data to avoid future errors
                self._column_availability["common"] = {"resource": {}, "minknow": {}}

        # Just cache position time ranges without calculating column availability
        # This is much faster than calculating column availability for each position
        position_cache_count = 0
        for position in self._positions:
            pos_start, pos_end = self.minknow_analyzer.get_position_time_range(position)
            self._cache[f"position_time_range_{position}"] = (pos_start, pos_end)
            position_cache_count += 1
        logger.debug("CROSS_ANALYZER_CACHE_SET: position_time_ranges cached for %d positions", position_cache_count)

        # Only pre-calculate log summary - this is important for the dashboard
        self._log_summary = self._calculate_log_summary()
        logger.debug("CROSS_ANALYZER_CACHE_SET: log_summary cached")

        self.initialized = True
        end_time = time.time()
        logger.info("CROSS_ANALYZER_INITIALIZE: Initialization completed in %.2f seconds", end_time - start_time)

        # Flag to control background thread
        self._shutdown_flag = False

        # Start background thread for additional calculations if needed
        # This will continue loading after the server is ready to handle requests
        self._bg_thread = threading.Thread(target=self._background_initialize, daemon=True)
        self._bg_thread.start()
        logger.debug("CROSS_ANALYZER_BACKGROUND: Background initialization thread started")

    def shutdown(self):
        """Gracefully shut down the background initialization thread.
        Should be called during application shutdown.
        """
        logger.info("CROSS_ANALYZER_SHUTDOWN: Shutting down CrossAnalyzer...")
        self._shutdown_flag = True

        if hasattr(self, "_bg_thread") and self._bg_thread.is_alive():
            logger.debug("CROSS_ANALYZER_SHUTDOWN: Waiting for background initialization thread to terminate...")
            # Give the thread a short time to clean up
            self._bg_thread.join(timeout=2.0)
            if self._bg_thread.is_alive():
                logger.warning("CROSS_ANALYZER_SHUTDOWN: Background thread did not terminate gracefully")
            else:
                logger.debug("CROSS_ANALYZER_SHUTDOWN: Background thread terminated successfully")

    def _background_initialize(self):
        """Background thread to continue loading additional data after server startup.
        This prevents the server from stalling during initial requests.
        Checks shutdown flag periodically to allow clean shutdown.
        """
        try:
            logger.info("CROSS_ANALYZER_BACKGROUND_INITIALIZE: Starting background initialization of additional data")

            # Add some delay to let the server start up completely
            for _ in range(5):  # 5 seconds with checks every second
                if self._shutdown_flag:
                    logger.info("CROSS_ANALYZER_BACKGROUND_INITIALIZE: Background initialization cancelled during startup delay")
                    return
                time.sleep(1)

            # Pre-calculate error distribution for common time range - removed as part of refactoring (was unused dead code)

            # Limit the number of positions for which we pre-calculate data
            # to avoid excessive startup time
            max_positions_to_precalculate = min(3, len(self._positions))

            if max_positions_to_precalculate > 0 and not self._shutdown_flag:
                logger.info("CROSS_ANALYZER_BACKGROUND_INITIALIZE: Pre-calculating data for %d positions", max_positions_to_precalculate)

                for i, position in enumerate(self._positions[:max_positions_to_precalculate]):
                    # Check shutdown flag before each position
                    if self._shutdown_flag:
                        logger.info(
                            "CROSS_ANALYZER_BACKGROUND_INITIALIZE: Background initialization interrupted during position processing"
                        )
                        return

                    pos_start, pos_end = self._cache.get(f"position_time_range_{position}", (None, None))
                    if pos_start and pos_end:
                        try:
                            # Convert to pandas Timestamp and check type
                            pandas_start = to_pandas_timestamp(pos_start)
                            pandas_end = to_pandas_timestamp(pos_end)

                            # Skip if timestamps are arrays or invalid
                            if pandas_start is None or pandas_end is None:
                                logger.warning(
                                    "CROSS_ANALYZER_WARNING: Invalid timestamps for position %s, skipping pre-calculation", position
                                )
                                continue

                            if isinstance(pandas_start, (list, np.ndarray)) or isinstance(pandas_end, (list, np.ndarray)):
                                logger.warning(
                                    "CROSS_ANALYZER_WARNING: Array-like timestamps for position %s, skipping pre-calculation", position
                                )
                                continue

                            # We're using a simpler approach since we can't easily timeout pandas operations
                            # But we check shutdown flag before storing the result
                            result = self._calculate_column_availability(pandas_start, pandas_end)

                            if not self._shutdown_flag:  # Check again before storing
                                self._column_availability[f"position_{position}"] = result
                                logger.info("CROSS_ANALYZER_CACHE_SET: column_availability cached for position %s", position)
                            else:
                                logger.info(
                                    "CROSS_ANALYZER_BACKGROUND_INITIALIZE: Calculation completed "
                                    "but shutdown triggered, skipping cache storage"
                                )
                                return
                        except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
                            logger.exception("CROSS_ANALYZER_ERROR: Error calculating column availability for position %s", position)

            if not self._shutdown_flag:
                logger.info("CROSS_ANALYZER_BACKGROUND_INITIALIZE: Background initialization completed successfully")
            else:
                logger.info("CROSS_ANALYZER_BACKGROUND_INITIALIZE: Background initialization terminated due to shutdown request")

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("CROSS_ANALYZER_ERROR: Error in background initialization")
            # Continue serving requests even if background initialization fails
        finally:
            logger.info("CROSS_ANALYZER_BACKGROUND_INITIALIZE: Background thread exiting")

    def _get_cache(self, key: str) -> Any:
        """Get a value from the cache if available and not expired.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not available
        """
        if key in self._cache:
            # Check if cache is expired
            last_updated = self._cache_timestamp.get(key, 0)
            if time.time() - last_updated <= self._cache_ttl:
                _log_cache_operation("get", key, hit=True)
                return self._cache[key]
            # Cache expired, remove it
            _log_cache_operation("expire", key)
            del self._cache[key]
            del self._cache_timestamp[key]

        _log_cache_operation("get", key, hit=False)
        return None

    def _set_cache(self, key: str, value: Any):
        """Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
        """
        self._cache[key] = value
        self._cache_timestamp[key] = time.time()

        # Calculate cache size for logging
        cache_size = len(str(value)) if value is not None else 0
        _log_cache_operation("set", key, size=cache_size)

    def _calculate_column_availability(self, start: datetime, end: datetime) -> dict[str, dict[str, Any]]:
        """Calculate column availability without caching.

        Args:
            start: Start timestamp
            end: End timestamp

        Returns:
            Dictionary with column availability information
        """
        result = {"resource": {}, "minknow": {}}

        # Check shutdown flag if available
        if hasattr(self, "_shutdown_flag") and self._shutdown_flag:
            logger.info("CROSS_ANALYZER_WARNING: Column availability calculation interrupted by shutdown flag")
            return result

        # Optimize for performance by doing a single query for all columns
        # rather than querying for each column individually

        # Get resource column availability
        if hasattr(self.resource_analyzer, "_df") and not self.resource_analyzer._df.empty:
            try:
                # Ensure both timestamps are pandas Timestamps
                if not hasattr(start, "timestamp") or not isinstance(start, pd.Timestamp):
                    start = pd.to_datetime(start)
                if not hasattr(end, "timestamp") or not isinstance(end, pd.Timestamp):
                    end = pd.to_datetime(end)

                # Single query for the time range
                resource_df = self.resource_analyzer.query(start=start, end=end)

                if not resource_df.empty:
                    # Process in batches of columns to allow interruption between batches
                    resource_columns = resource_df.columns.tolist()
                    batch_size = 10  # Process 10 columns at a time

                    for i in range(0, len(resource_columns), batch_size):
                        # Check shutdown flag if available
                        if hasattr(self, "_shutdown_flag") and self._shutdown_flag:
                            logger.info("CROSS_ANALYZER_WARNING: Column availability calculation interrupted by shutdown flag")
                            return result

                        batch_columns = resource_columns[i : i + batch_size]
                        for col in batch_columns:
                            if col in resource_df.columns:
                                non_null_count = resource_df[col].count()
                                total_count = len(resource_df)

                                result["resource"][col] = {
                                    "available": bool(non_null_count > 0),
                                    "completeness": (float(non_null_count / total_count * 100) if total_count > 0 else 0),
                                    "count": int(non_null_count),
                                }
            except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
                logger.exception("CROSS_ANALYZER_ERROR: Error calculating resource column availability")
        else:
            logger.info("CROSS_ANALYZER_WARNING: No resource data available for column availability calculation")

        # Check shutdown flag if available
        if hasattr(self, "_shutdown_flag") and self._shutdown_flag:
            logger.info("CROSS_ANALYZER_WARNING: Column availability calculation interrupted by shutdown flag")
            return result

        # Get minknow column availability
        if hasattr(self.minknow_analyzer, "_df") and not self.minknow_analyzer._df.empty:
            try:
                # Ensure both timestamps are pandas Timestamps
                if not hasattr(start, "timestamp") or not isinstance(start, pd.Timestamp):
                    start = pd.to_datetime(start)
                if not hasattr(end, "timestamp") or not isinstance(end, pd.Timestamp):
                    end = pd.to_datetime(end)

                # Single query for the time range
                minknow_df = self.minknow_analyzer.query(start=start, end=end)

                if not minknow_df.empty:
                    # Process in batches of columns to allow interruption between batches
                    minknow_columns = minknow_df.columns.tolist()
                    batch_size = 10  # Process 10 columns at a time

                    for i in range(0, len(minknow_columns), batch_size):
                        # Check shutdown flag if available
                        if hasattr(self, "_shutdown_flag") and self._shutdown_flag:
                            logger.info("CROSS_ANALYZER_WARNING: Column availability calculation interrupted by shutdown flag")
                            return result

                        batch_columns = minknow_columns[i : i + batch_size]
                        for col in batch_columns:
                            if col in minknow_df.columns:
                                non_null_count = minknow_df[col].count()
                                total_count = len(minknow_df)

                                result["minknow"][col] = {
                                    "available": bool(non_null_count > 0),
                                    "completeness": (float(non_null_count / total_count * 100) if total_count > 0 else 0),
                                    "count": int(non_null_count),
                                }
            except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
                logger.exception("CROSS_ANALYZER_ERROR: Error calculating minknow column availability")
        else:
            logger.info("CROSS_ANALYZER_WARNING: No minknow data available for column availability calculation")

        return convert_to_serializable(result)

    def _calculate_log_summary(self) -> dict[str, Any]:
        """Calculate log summary without using cache.

        Returns:
            Dictionary with log summary data
        """
        try:
            common_start, common_end = self._common_time_range or self.get_common_time_range()

            if not common_start or not common_end:
                return {
                    "total_entries": 0,
                    "total_resource_entries": 0,
                    "counts_by_position": {},
                    "counts_by_level": {},
                    "errors_by_position": {},
                    "time_range": {"start": None, "end": None},
                }

            # Convert timestamps to pandas Timestamp objects for consistent handling
            pandas_start = to_pandas_timestamp(common_start)
            pandas_end = to_pandas_timestamp(common_end)

            # Check for invalid timestamp types
            if pandas_start is None or pandas_end is None:
                logger.warning("CROSS_ANALYZER_WARNING: Invalid timestamp range for log summary calculation")
                return {
                    "total_entries": 0,
                    "total_resource_entries": 0,
                    "counts_by_position": {},
                    "counts_by_level": {},
                    "errors_by_position": {},
                    "time_range": {"start": None, "end": None},
                }

            if isinstance(pandas_start, (list, np.ndarray)) or isinstance(pandas_end, (list, np.ndarray)):
                logger.warning("CROSS_ANALYZER_WARNING: Array-like timestamps for log summary calculation, cannot proceed")
                return {
                    "total_entries": 0,
                    "total_resource_entries": 0,
                    "counts_by_position": {},
                    "counts_by_level": {},
                    "errors_by_position": {},
                    "time_range": {"start": None, "end": None},
                }

            # Get position data
            positions = self._positions or self.minknow_analyzer.get_positions()
            position_counts = {}

            for pos in positions:
                # Get position-specific time range if needed
                pos_range = self._get_cache(f"position_time_range_{pos}")
                if not pos_range:
                    pos_range = self.minknow_analyzer.get_position_time_range(pos)
                    self._set_cache(f"position_time_range_{pos}", pos_range)

                pos_start, pos_end = pos_range

                if not pos_start or not pos_end:
                    continue

                # Ensure timestamp consistency
                try:
                    if isinstance(pos_start, str):
                        pos_start = pd.to_datetime(pos_start)
                    if isinstance(pos_end, str):
                        pos_end = pd.to_datetime(pos_end)

                    # Skip if timestamps are arrays or invalid
                    if isinstance(pos_start, (list, np.ndarray)) or isinstance(pos_end, (list, np.ndarray)):
                        logger.warning("CROSS_ANALYZER_WARNING: Array-like timestamps for position %s, skipping in log summary", pos)
                        continue

                    # Count log entries by level for this position
                    df = self.minknow_analyzer.query(start=pos_start, end=pos_end, folder_name=pos)

                    if not df.empty and "log_level" in df.columns:
                        level_counts = df["log_level"].value_counts().to_dict()

                        position_counts[pos] = {
                            "total": len(df),
                            "levels": level_counts,
                            "start_time": (pos_start.isoformat() if isinstance(pos_start, datetime) else str(pos_start)),
                            "end_time": (pos_end.isoformat() if isinstance(pos_end, datetime) else str(pos_end)),
                        }
                except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
                    logger.exception("CROSS_ANALYZER_ERROR: Error processing position %s for log summary", pos)
                    continue

            # Get resource metrics summary
            resource_summary = {}
            try:
                resource_summary = self.resource_analyzer.get_system_overview(start=common_start, end=common_end)
            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
                logger.exception("CROSS_ANALYZER_ERROR: Error getting resource overview for summary")

            # Get the actual count of resource entries within the time range
            total_resource_entries = 0
            try:
                resource_df = self.resource_analyzer.query(start=common_start, end=common_end)
                total_resource_entries = len(resource_df) if not resource_df.empty else 0
                logger.info("CROSS_ANALYZER_INFO: Found %d resource entries in full time range", total_resource_entries)
            except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
                logger.exception("CROSS_ANALYZER_ERROR: Error counting resource entries for summary")

            # Count total entries and entries by level
            total_minknow_entries = sum(p["total"] for p in position_counts.values())
            counts_by_position = {p: data["total"] for p, data in position_counts.items()}

            # Combine level counts across all positions
            counts_by_level = {}
            for pos_data in position_counts.values():
                for level, count in pos_data.get("levels", {}).items():
                    counts_by_level[level] = counts_by_level.get(level, 0) + count

            # Count errors by position
            errors_by_position = {}
            for pos, data in position_counts.items():
                error_count = data.get("levels", {}).get("ERROR", 0)
                if error_count > 0:
                    errors_by_position[pos] = error_count

            return {
                "total_entries": total_minknow_entries,
                "total_resource_entries": total_resource_entries,
                "counts_by_position": counts_by_position,
                "counts_by_level": counts_by_level,
                "errors_by_position": errors_by_position,
                "time_range": {
                    "start": common_start.isoformat() if common_start else None,
                    "end": common_end.isoformat() if common_end else None,
                },
                "positions": position_counts,
                "resource": resource_summary,
            }
        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
            logger.exception("CROSS_ANALYZER_ERROR: Error calculating log summary")
            return {
                "total_entries": 0,
                "total_resource_entries": 0,
                "counts_by_position": {},
                "counts_by_level": {},
                "errors_by_position": {},
                "time_range": {"start": None, "end": None},
                "error": str(e),
            }

    @_track_parser_performance("get_common_time_range")
    def get_common_time_range(self) -> tuple[datetime | None, datetime | None]:
        """Get the common time range across all log types.

        Returns:
            Tuple of (start, end) timestamps, or (None, None) if no overlap
        """
        # Return cached value if available
        if self._common_time_range:
            return self._common_time_range

        # Otherwise calculate
        try:
            resource_range = self.resource_analyzer.get_time_range()
            minknow_range = self.minknow_analyzer.get_time_range()

            # Handle cases where one analyzer has no data
            if not resource_range[0] or not resource_range[1]:
                logger.info("CROSS_ANALYZER_INFO: No resource log time range available, using minknow time range")
                self._common_time_range = minknow_range
                return minknow_range

            if not minknow_range[0] or not minknow_range[1]:
                logger.info("CROSS_ANALYZER_INFO: No minknow log time range available, using resource time range")
                self._common_time_range = resource_range
                return resource_range

            # Always convert to pandas Timestamp for consistent comparison
            try:
                res_start = to_pandas_timestamp(resource_range[0])
                res_end = to_pandas_timestamp(resource_range[1])
                min_start = to_pandas_timestamp(minknow_range[0])
                min_end = to_pandas_timestamp(minknow_range[1])

                # Safety check for numpy arrays or other non-comparable types
                if (
                    isinstance(res_start, (list, np.ndarray))
                    or isinstance(res_end, (list, np.ndarray))
                    or isinstance(min_start, (list, np.ndarray))
                    or isinstance(min_end, (list, np.ndarray))
                ):
                    logger.warning("CROSS_ANALYZER_WARNING: Invalid timestamp types detected in time range calculation")
                    # Try to use minknow time range as fallback
                    if not isinstance(min_start, (list, np.ndarray)) and not isinstance(min_end, (list, np.ndarray)):
                        logger.info("CROSS_ANALYZER_INFO: Using minknow time range as fallback")
                        self._common_time_range = minknow_range
                        return minknow_range
                    # If that fails too, use resource time range
                    if not isinstance(res_start, (list, np.ndarray)) and not isinstance(res_end, (list, np.ndarray)):
                        logger.info("CROSS_ANALYZER_INFO: Using resource time range as fallback")
                        self._common_time_range = resource_range
                        return resource_range
                    # If both fail, return None
                    logger.warning("CROSS_ANALYZER_WARNING: No valid time range available")
                    self._common_time_range = (None, None)
                    return (None, None)

                # Find the overlap using converted values
                if res_start is not None and min_start is not None:
                    start = max(res_start, min_start)
                else:
                    start = res_start if res_start is not None else min_start

                if res_end is not None and min_end is not None:
                    end = min(res_end, min_end)
                else:
                    end = res_end if res_end is not None else min_end

                if start is not None and end is not None and start > end:
                    logger.warning("CROSS_ANALYZER_WARNING: No common time range across log types")
                    start, end = None, None
            except (ValueError, TypeError, AttributeError, pd.errors.ParserError):
                logger.exception("CROSS_ANALYZER_ERROR: Error comparing timestamps in get_common_time_range")
                # Fallback to one of the ranges if possible
                if resource_range[0] and resource_range[1]:
                    logger.info("CROSS_ANALYZER_INFO: Using resource time range as fallback after error")
                    self._common_time_range = resource_range
                    return resource_range
                if minknow_range[0] and minknow_range[1]:
                    logger.info("CROSS_ANALYZER_INFO: Using minknow time range as fallback after error")
                    self._common_time_range = minknow_range
                    return minknow_range
                logger.warning("CROSS_ANALYZER_WARNING: No valid time range available after error")
                start, end = None, None

            # Convert back to Python datetime before returning
            result = (to_python_datetime(start), to_python_datetime(end))

        except (ValueError, TypeError, AttributeError, RuntimeError):
            logger.exception("CROSS_ANALYZER_ERROR: Unexpected error in get_common_time_range")
            result = (None, None)

        # Cache the result
        self._common_time_range = result

        return result

    @_track_parser_performance("get_column_availability")
    def get_column_availability(self, start: datetime, end: datetime) -> dict[str, dict[str, Any]]:
        """Get availability of columns in the given time range.

        Args:
            start: Start timestamp
            end: End timestamp

        Returns:
            Dictionary with column availability information
        """
        # Check if we're asking for the common time range
        common_start, common_end = self._common_time_range or self.get_common_time_range()

        # If common time range and we have a cache for it, use that
        if (common_start == start and common_end == end) and "common" in self._column_availability:
            return self._column_availability["common"]

        # Check if this is a position-specific time range
        positions = self._positions or self.minknow_analyzer.get_positions()
        for position in positions:
            pos_start, pos_end = self._get_cache(f"position_time_range_{position}") or (None, None)
            if pos_start == start and pos_end == end and f"position_{position}" in self._column_availability:
                return self._column_availability[f"position_{position}"]

        # If not found in cache, calculate it
        from ..utils.time_utils import to_pandas_timestamp

        # Ensure timestamps are properly converted
        try:
            pandas_start = to_pandas_timestamp(start)
            pandas_end = to_pandas_timestamp(end)

            if pandas_start is None or pandas_end is None:
                import logging

                logger = logging.getLogger(__name__)
                logger.warning("CROSS_ANALYZER_WARNING: Invalid timestamp range for column availability: start=%s, end=%s", start, end)
                return {"resource": {}, "minknow": {}}

            # Generate a cache key for this time range
            start_str = start.isoformat() if isinstance(start, datetime) else str(start)
            end_str = end.isoformat() if isinstance(end, datetime) else str(end)
            cache_key = f"column_availability_{start_str}_{end_str}"

            # Check if we have it in the general cache
            cached_result = self._get_cache(cache_key)
            if cached_result:
                return cached_result

            # Calculate if not in cache
            result = self._calculate_column_availability(pandas_start, pandas_end)

            # Cache the result
            self._set_cache(cache_key, result)

            return result
        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("CROSS_ANALYZER_ERROR: Error getting column availability")
            return {"resource": {}, "minknow": {}}

    @_track_parser_performance("get_log_summary")
    def get_log_summary(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
    ) -> dict[str, Any]:
        """Get summary statistics of the log data.

        Args:
            start: Start timestamp
            end: End timestamp
            position: Filter for specific position

        Returns:
            Dictionary with log summary
        """
        try:
            # If asking for the entire dataset with no filters, use the pre-calculated summary
            if (not start and not end and not position) and self._log_summary:
                return self._log_summary

            # If asking for a specific time range or position, create a cache key
            if start or end or position:
                start_str = start.isoformat() if isinstance(start, datetime) else "None"
                end_str = end.isoformat() if isinstance(end, datetime) else "None"
                position_str = position or "None"
                cache_key = f"log_summary_{start_str}_{end_str}_{position_str}"

                # Check if we have it cached
                cached_result = self._get_cache(cache_key)
                if cached_result:
                    return cached_result

            # Ensure we have timestamps
            if not start or not end:
                common_range = self._common_time_range or self.get_common_time_range()
                start = common_range[0] if not start else start
                end = common_range[1] if not end else end

            # If asking for the entire dataset with no position filter, and we still need to calculate
            if (not position) and self._common_time_range and start == self._common_time_range[0] and end == self._common_time_range[1]:
                # Calculate if not already calculated
                if not self._log_summary:
                    self._log_summary = self._calculate_log_summary()
                return self._log_summary

            # Check if we have valid timestamps
            if not start or not end:
                logger.warning("CROSS_ANALYZER_WARNING: No valid time range for log summary")
                return {
                    "total_entries": 0,
                    "total_resource_entries": 0,
                    "counts_by_position": {},
                    "counts_by_level": {},
                    "errors_by_position": {},
                    "time_range": {"start": None, "end": None},
                }

            # Convert timestamps to pandas Timestamp objects for consistent handling
            pandas_start = to_pandas_timestamp(start)
            pandas_end = to_pandas_timestamp(end)

            # Check for invalid timestamp types
            if pandas_start is None or pandas_end is None:
                logger.warning("CROSS_ANALYZER_WARNING: Invalid timestamp range for log summary")
                return {
                    "total_entries": 0,
                    "total_resource_entries": 0,
                    "counts_by_position": {},
                    "counts_by_level": {},
                    "errors_by_position": {},
                    "time_range": {"start": None, "end": None},
                }

            if isinstance(pandas_start, (list, np.ndarray)) or isinstance(pandas_end, (list, np.ndarray)):
                logger.warning("CROSS_ANALYZER_WARNING: Array-like timestamps for log summary, cannot proceed")
                return {
                    "total_entries": 0,
                    "total_resource_entries": 0,
                    "counts_by_position": {},
                    "counts_by_level": {},
                    "errors_by_position": {},
                    "time_range": {"start": None, "end": None},
                }

            # Get position data
            positions = [position] if position else (self._positions or self.minknow_analyzer.get_positions())
            position_counts = {}

            for pos in positions:
                # Skip positions that don't match the filter
                if position and pos != position:
                    continue

                # Get position-specific time range if needed
                if not start or not end:
                    pos_range = self._get_cache(f"position_time_range_{pos}")
                    if not pos_range:
                        pos_range = self.minknow_analyzer.get_position_time_range(pos)
                        self._set_cache(f"position_time_range_{pos}", pos_range)

                    pos_start, pos_end = pos_range
                else:
                    pos_start, pos_end = pandas_start, pandas_end

                if not pos_start or not pos_end:
                    continue

                # Ensure timestamp consistency
                if isinstance(pos_start, str):
                    pos_start = pd.to_datetime(pos_start)
                if isinstance(pos_end, str):
                    pos_end = pd.to_datetime(pos_end)

                # Check for array-like timestamps
                if isinstance(pos_start, (list, np.ndarray)) or isinstance(pos_end, (list, np.ndarray)):
                    logger.warning("CROSS_ANALYZER_WARNING: Array-like timestamps for position %s, skipping", pos)
                    continue

                # Count log entries by level for this position
                df = self.minknow_analyzer.query(start=pos_start, end=pos_end, folder_name=pos)

                if not df.empty and "log_level" in df.columns:
                    level_counts = df["log_level"].value_counts().to_dict()

                    position_counts[pos] = {
                        "total": len(df),
                        "levels": level_counts,
                        "start_time": (pos_start.isoformat() if isinstance(pos_start, datetime) else str(pos_start)),
                        "end_time": (pos_end.isoformat() if isinstance(pos_end, datetime) else str(pos_end)),
                    }

            # Get resource metrics summary and count of resource entries
            resource_summary = {}
            try:
                resource_summary = self.resource_analyzer.get_system_overview(start=start, end=end)
            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
                logger.exception("CROSS_ANALYZER_ERROR: Error getting resource overview")

            # Get the actual count of resource entries within the time range
            total_resource_entries = 0
            try:
                resource_df = self.resource_analyzer.query(start=start, end=end)
                total_resource_entries = len(resource_df) if not resource_df.empty else 0
                logger.info("CROSS_ANALYZER_INFO: Found %d resource entries in time range", total_resource_entries)
            except (ValueError, TypeError, AttributeError, KeyError, pd.errors.ParserError):
                logger.exception("CROSS_ANALYZER_ERROR: Error counting resource entries")

            # Calculate total entries and counts by level
            total_minknow_entries = sum(p["total"] for p in position_counts.values())
            counts_by_position = {p: data["total"] for p, data in position_counts.items()}

            # Combine level counts across all positions
            counts_by_level = {}
            for pos_data in position_counts.values():
                for level, count in pos_data.get("levels", {}).items():
                    counts_by_level[level] = counts_by_level.get(level, 0) + count

            # Count errors by position
            errors_by_position = {}
            for pos, data in position_counts.items():
                error_count = data.get("levels", {}).get("ERROR", 0)
                if error_count > 0:
                    errors_by_position[pos] = error_count

            result = {
                "total_entries": total_minknow_entries,
                "total_resource_entries": total_resource_entries,
                "counts_by_position": counts_by_position,
                "counts_by_level": counts_by_level,
                "errors_by_position": errors_by_position,
                "time_range": {
                    "start": start.isoformat() if start else None,
                    "end": end.isoformat() if end else None,
                },
                "position": position,
                "positions": position_counts,
                "resource": resource_summary,
            }

            # Cache the result if we have a specific time range or position
            if start or end or position:
                start_str = start.isoformat() if isinstance(start, datetime) else "None"
                end_str = end.isoformat() if isinstance(end, datetime) else "None"
                position_str = position or "None"
                cache_key = f"log_summary_{start_str}_{end_str}_{position_str}"
                self._set_cache(cache_key, result)

            return result

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
            logger.exception("CROSS_ANALYZER_ERROR: Error generating log summary")
            return {
                "total_entries": 0,
                "total_resource_entries": 0,
                "counts_by_position": {},
                "counts_by_level": {},
                "errors_by_position": {},
                "time_range": {"start": None, "end": None},
                "error": str(e),
            }
