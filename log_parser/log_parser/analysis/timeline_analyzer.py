from __future__ import annotations

import logging
import os
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any

import numpy as np
import pandas as pd
import toml

from ..models import LogEntry, StructuredLogEntry
from ..utils.metadata_utils import clean_metadata_dict
from .base_analyzer import BaseAnalyzer

logger = logging.getLogger(__name__)


@dataclass
class TimelineMarker:
    id: str  # Unique identifier for this specific marker instance
    name: str  # The type/name of the marker (e.g., "protocol_phase", "error_event")
    timestamp: datetime
    metadata: dict[str, Any]  # Store the metadata from the marker log event
    log_event: str  # The log event name that triggered this marker
    position_id: str  # The position this marker belongs to
    original_log_entry: Any  # Full original log entry


@dataclass
class TimelineEvent:
    id: str  # Unique identifier for this specific timeline event instance
    name: str  # The type/name of the timeline event (e.g., "protocol", "data_acquisition")
    start_identifier: str  # The start event identifier
    end_identifier: str  # The end event identifier
    start_time: datetime
    end_time: datetime | None  # None if no end event was found
    metadata: dict[str, Any]  # Store the metadata from both start and end events
    children: list[TimelineEvent]  # Nested timeline events that occurred within this time period
    markers: list[TimelineMarker]  # Markers that occurred within this timeline event
    position_id: str  # The position this timeline event belongs to
    start_event: Any  # Full start event
    end_event: Any | None  # None if no end event was found

    def is_complete(self) -> bool:
        """Check if this timeline event has both start and end events."""
        return self.end_event is not None and self.end_time is not None


class TimelineAnalyzer(BaseAnalyzer):
    """Analyzer for tracking and analyzing long-running timeline events in MinKNOW logs.
    Provides methods to identify, track and query nested timeline event relationships.
    """

    DEFAULT_CONFIG_PATH = Path(__file__).parent.parent / "config" / "default_timeline_events.toml"

    def __init__(self, log_entries: list[LogEntry], custom_config_path: str | None = None):
        """Initialize with log entries and optionally a custom config path.

        Args:
            log_entries: List of log entries to analyze
            custom_config_path: Optional path to custom timeline event configuration file
        """
        self.config = self._load_config(custom_config_path)
        self.position_timeline_events: dict[str, list[TimelineEvent]] = {}
        super().__init__(log_entries)

    @property
    def analyzer_type(self) -> str:
        return "timeline"

    def _load_config(self, custom_config_path: str | None = None) -> dict:
        """Load the TOML configuration file defining timeline event pairs"""
        # Always load default config first
        with open(self.DEFAULT_CONFIG_PATH) as f:
            config = toml.load(f)

        # If custom config provided, merge it with defaults
        if custom_config_path and os.path.exists(custom_config_path):
            try:
                with open(custom_config_path) as f:
                    custom_config = toml.load(f)
                # Merge custom timeline events with defaults
                config["timeline_event"].extend(custom_config.get("timeline_event", []))
            except (OSError, toml.TomlDecodeError, KeyError):
                logger.exception("Error loading custom timeline event config")

        return config

    def _create_dataframe(self, log_entries: list[LogEntry]) -> pd.DataFrame:
        """Convert log entries to a DataFrame and build timeline event trees.

        Args:
            log_entries: List of log entries

        Returns:
            DataFrame with log entries
        """
        # Filter for minknow log entries
        minknow_entries = [entry for entry in log_entries if isinstance(entry, StructuredLogEntry)]

        if not minknow_entries:
            logger.warning("No minknow log entries found")
            return pd.DataFrame()

        # Create DataFrame similar to MinknowAnalyzer
        records = []
        key_columns = set()

        # Discover keys used in log entries
        for entry in minknow_entries:
            if entry.keys:
                key_columns.update(entry.keys.keys())

        # Create flattened records
        for entry in minknow_entries:
            record = {
                "timestamp": entry.timestamp,
                "log_event": entry.log_event,
                "folder_name": entry.folder_name,
                "message": entry.message,
                "process_name": entry.process_name,
                "log_level": entry.log_level,
                "library": entry.library,
            }

            # Add flattened keys that actually exist in this entry
            if entry.keys:
                for key, value in entry.keys.items():
                    column_name = f"key_{key}"
                    record[column_name] = value

            records.append(record)

        df = pd.DataFrame(records)

        # Set timestamp as index
        if not df.empty and "timestamp" in df.columns:
            df.set_index("timestamp", inplace=True)
            df.sort_index(inplace=True)

        # PHASE 1.2: Convert string columns to categories for memory efficiency
        categorical_columns = ["folder_name", "log_level", "log_event", "process_name", "library"]
        for col in categorical_columns:
            if col in df.columns:
                df[col] = df[col].astype("category")
                logger.debug("Converted column '%s' to categorical dtype", col)

        # Build timeline event trees for each position
        self._build_timeline_event_trees(df, minknow_entries)

        return df

    def _build_timeline_event_trees(self, df: pd.DataFrame, minknow_entries: list) -> None:
        """Process the DataFrame to build timeline event trees for each position."""
        if df.empty:
            return

        # Create lookup map for original log entries for O(1) access
        entry_lookup = {}
        for entry in minknow_entries:
            key = (entry.timestamp, entry.log_event, entry.process_name)
            entry_lookup[key] = entry

        # Group by position
        for position in df["folder_name"].unique():
            pos_df = df[df["folder_name"] == position]

            # Ensure position DataFrame is sorted by timestamp for efficient range operations
            if not pos_df.index.is_monotonic_increasing:
                pos_df = pos_df.sort_index()

            # Process each timeline event type from config
            root_timeline_events = []
            for timeline_event_config in self.config["timeline_event"]:
                # Find all start events for this timeline event type
                start_events = pos_df[pos_df["log_event"] == timeline_event_config["start_event"]]

                # Filter by process name if specified in config
                if "process_filter" in timeline_event_config:
                    start_events = start_events[start_events["process_name"] == timeline_event_config["process_filter"]]

                for _, start_event in start_events.iterrows():
                    # Check if timeline event uses identifier-based or positional matching
                    uses_identifier = "identifier_key" in timeline_event_config and timeline_event_config["identifier_key"]

                    if uses_identifier:
                        # Identifier-based matching (original behavior)
                        identifier_key = f"key_{timeline_event_config['identifier_key']}"
                        if identifier_key not in start_event:
                            continue

                        identifier_value = start_event[identifier_key]
                        end_events = pos_df[
                            (pos_df["log_event"] == timeline_event_config["end_event"])
                            & (pos_df[identifier_key] == identifier_value)
                            & (pos_df.index > start_event.name)  # Ensure end event is after start
                        ]

                        # Create unique identifier for this timeline event
                        timeline_event_id = (
                            f"{position}_{timeline_event_config['name']}_{identifier_value}_{int(start_event.name.timestamp())}"
                        )
                    else:
                        # Positional matching - find first end event after start event
                        end_events = pos_df[
                            (pos_df["log_event"] == timeline_event_config["end_event"])
                            & (pos_df.index > start_event.name)  # Ensure end event is after start
                        ]

                        # Create unique identifier for this timeline event (without shared identifier)
                        timeline_event_id = f"{position}_{timeline_event_config['name']}_positional_{int(start_event.name.timestamp())}"

                    # Apply process filter to end events if specified
                    if "process_filter" in timeline_event_config:
                        end_events = end_events[end_events["process_name"] == timeline_event_config["process_filter"]]

                    # Collect metadata from start event and end event (if exists)
                    raw_metadata = {}
                    start_event_dict = start_event.to_dict()

                    # Extract key values from original structured log entry instead of DataFrame
                    # This avoids pandas NaN issues
                    start_key = (start_event.name, start_event.get("log_event"), start_event.get("process_name"))
                    original_start_entry = entry_lookup.get(start_key)

                    if original_start_entry and original_start_entry.keys:
                        for key, value in original_start_entry.keys.items():
                            raw_metadata[key] = value

                    # Add process information to metadata
                    raw_metadata["process_name"] = start_event.get("process_name")
                    raw_metadata["log_level"] = start_event.get("log_level")
                    raw_metadata["library"] = start_event.get("library")

                    end_event = None
                    end_time = None
                    if not end_events.empty:
                        end_event = end_events.iloc[0]
                        end_time = end_event.name

                        # Add end event metadata from original structured log entry
                        end_key = (end_event.name, end_event.get("log_event"), end_event.get("process_name"))
                        original_end_entry = entry_lookup.get(end_key)

                        if original_end_entry and original_end_entry.keys:
                            for key, value in original_end_entry.keys.items():
                                raw_metadata[key] = value

                        # Update process information from end event if available
                        if end_event.get("process_name"):
                            raw_metadata["end_process_name"] = end_event.get("process_name")
                        if end_event.get("log_level"):
                            raw_metadata["end_log_level"] = end_event.get("log_level")
                        if end_event.get("library"):
                            raw_metadata["end_library"] = end_event.get("library")

                    # Clean the metadata using our utility
                    cleaned_metadata = clean_metadata_dict(raw_metadata)

                    timeline_event = TimelineEvent(
                        id=timeline_event_id,
                        name=timeline_event_config["name"],
                        start_identifier=timeline_event_config["start_event"],
                        end_identifier=timeline_event_config["end_event"],
                        start_time=start_event.name,
                        end_time=end_time,
                        metadata=cleaned_metadata,
                        children=[],
                        markers=[],
                        position_id=position,
                        start_event=start_event,
                        end_event=end_event,
                    )

                    root_timeline_events.append(timeline_event)

            # Sort timeline events by start time
            root_timeline_events.sort(key=lambda x: x.start_time)

            # Build hierarchical relationships
            self._build_timeline_event_hierarchy(root_timeline_events)

            # Collect markers for timeline events
            self._collect_timeline_markers(root_timeline_events, pos_df, entry_lookup)

            # Store for this position
            self.position_timeline_events[position] = root_timeline_events

    def _build_timeline_event_hierarchy(self, timeline_events: list[TimelineEvent]) -> None:
        """Build parent-child relationships between timeline events based on their time ranges."""
        # Create a config lookup for faster access
        config_lookup = {tc["name"]: tc for tc in self.config["timeline_event"]}

        # Track events to remove by their IDs (to avoid modifying list while iterating)
        children_ids_to_remove = set()

        for potential_parent in timeline_events:
            parent_config = config_lookup.get(potential_parent.name)
            if not parent_config or not parent_config.get("children"):
                continue

            allowed_children = set(parent_config["children"])

            for potential_child in timeline_events:
                if (
                    potential_child == potential_parent
                    or potential_child.name not in allowed_children
                    or potential_child.id in children_ids_to_remove
                ):
                    continue

                # Check if child type is allowed and time range fits
                # Skip if either parent or child has no end time (incomplete events)
                if (
                    potential_child.start_time >= potential_parent.start_time
                    and potential_child.end_time is not None
                    and potential_parent.end_time is not None
                    and potential_child.end_time <= potential_parent.end_time
                ):
                    potential_parent.children.append(potential_child)
                    children_ids_to_remove.add(potential_child.id)

        # Remove children from root timeline events
        timeline_events[:] = [event for event in timeline_events if event.id not in children_ids_to_remove]

    def _collect_timeline_markers(self, timeline_events: list[TimelineEvent], pos_df: pd.DataFrame, entry_lookup: dict) -> None:
        """Collect markers for timeline events based on their configuration."""
        # Get marker configurations
        marker_configs = self.config.get("marker", [])
        if not marker_configs:
            return

        # Create a mapping of marker names to their configurations
        marker_config_map = {config["name"]: config for config in marker_configs}

        # Pre-filter and cache marker events by type for efficiency
        marker_events_cache = {}
        for marker_config in marker_configs:
            marker_events = pos_df[pos_df["log_event"] == marker_config["log_event"]]

            # Apply process filter if specified
            if "process_filter" in marker_config:
                marker_events = marker_events[marker_events["process_name"] == marker_config["process_filter"]]

            marker_events_cache[marker_config["name"]] = marker_events

        def collect_markers_for_event(timeline_event: TimelineEvent) -> None:
            """Recursively collect markers for a timeline event and its children."""
            # Get the timeline event configuration
            timeline_config = next((tc for tc in self.config["timeline_event"] if tc["name"] == timeline_event.name), None)

            if not timeline_config or "markers" not in timeline_config:
                return

            # Process each marker type this timeline event can contain
            for marker_name in timeline_config["markers"]:
                if marker_name not in marker_config_map or marker_name not in marker_events_cache:
                    continue

                marker_config = marker_config_map[marker_name]
                marker_events = marker_events_cache[marker_name]

                # Filter markers to those within this timeline event's time range using vectorized operations
                if timeline_event.end_time is None:
                    # For incomplete events, include all markers after start time
                    time_filtered_events = marker_events[marker_events.index >= timeline_event.start_time]
                else:
                    # For complete events, filter by both start and end time
                    time_filtered_events = marker_events[
                        (marker_events.index >= timeline_event.start_time) & (marker_events.index <= timeline_event.end_time)
                    ]

                # Process each marker event
                for _, marker_event in time_filtered_events.iterrows():
                    marker_time = marker_event.name

                    # Find the original log entry for metadata extraction using lookup
                    marker_key = (marker_time, marker_event.get("log_event"), marker_event.get("process_name"))
                    original_marker_entry = entry_lookup.get(marker_key)

                    if original_marker_entry:
                        # Extract metadata from original entry
                        marker_metadata = {}
                        if original_marker_entry.keys:
                            for key, value in original_marker_entry.keys.items():
                                marker_metadata[key] = value

                        # Add process information to metadata
                        marker_metadata["process_name"] = original_marker_entry.process_name
                        marker_metadata["log_level"] = original_marker_entry.log_level
                        marker_metadata["library"] = original_marker_entry.library

                        # Clean the metadata
                        cleaned_marker_metadata = clean_metadata_dict(marker_metadata)

                        # Create marker ID
                        marker_id = f"{timeline_event.position_id}_{marker_name}_{int(marker_time.timestamp())}"

                        # Create TimelineMarker
                        timeline_marker = TimelineMarker(
                            id=marker_id,
                            name=marker_name,
                            timestamp=marker_time,
                            metadata=cleaned_marker_metadata,
                            log_event=marker_config["log_event"],
                            position_id=timeline_event.position_id,
                            original_log_entry=original_marker_entry,
                        )

                        timeline_event.markers.append(timeline_marker)

            # Recursively collect markers for children
            for child in timeline_event.children:
                collect_markers_for_event(child)

        # Collect markers for all timeline events
        for timeline_event in timeline_events:
            collect_markers_for_event(timeline_event)

    def get_available_timeline_events(self) -> list[dict[str, Any]]:
        """Get all available timeline event types and their configurations.

        Returns:
            List of timeline event configurations
        """
        return self.config["timeline_event"]

    def get_timeline_event_hierarchy(self) -> dict[str, list[str]]:
        """Get the hierarchical relationships between timeline event types.

        Returns:
            Dictionary mapping timeline event names to lists of possible child timeline event names
        """
        return {timeline_event["name"]: timeline_event.get("children", []) for timeline_event in self.config["timeline_event"]}

    def get_timeline_event_metadata_keys(self, timeline_event_name: str) -> set[str]:
        """Get all possible metadata keys for a specific timeline event type.

        Args:
            timeline_event_name: Name of the timeline event type

        Returns:
            Set of metadata key names
        """
        metadata_keys = set()

        # Look through all positions and timeline events of this type
        for timeline_events in self.position_timeline_events.values():
            for timeline_event in self._find_timeline_events_by_type(timeline_events, timeline_event_name):
                metadata_keys.update(timeline_event.metadata.keys())

        return metadata_keys

    def get_timeline_event_by_id(self, timeline_event_id: str) -> TimelineEvent | None:
        """Find a timeline event by its unique identifier.

        Args:
            timeline_event_id: The unique identifier of the timeline event

        Returns:
            The TimelineEvent if found, None otherwise
        """

        def find_in_timeline_events(timeline_events: list[TimelineEvent]) -> TimelineEvent | None:
            for timeline_event in timeline_events:
                if timeline_event.id == timeline_event_id:
                    return timeline_event
                # Search in children
                child_result = find_in_timeline_events(timeline_event.children)
                if child_result:
                    return child_result
            return None

        # Search through all positions
        for timeline_events in self.position_timeline_events.values():
            result = find_in_timeline_events(timeline_events)
            if result:
                return result

        return None

    def get_available_markers(self) -> list[dict[str, Any]]:
        """Get all available marker types and their configurations.

        Returns:
            List of marker configurations
        """
        return self.config.get("marker", [])

    def query_markers(
        self,
        marker_type: str | None = None,
        timeline_event_id: str | None = None,
        position_id: str | None = None,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        metadata_filters: dict[str, Any] | None = None,
    ) -> list[TimelineMarker]:
        """Query markers based on multiple criteria.

        Args:
            marker_type: Filter by marker type name
            timeline_event_id: Filter by specific timeline event ID
            position_id: Filter by position
            start_time: Filter markers that occur after this time (inclusive)
            end_time: Filter markers that occur before this time (inclusive)
            metadata_filters: Dict of metadata key-value pairs to filter on

        Returns:
            List of matching markers
        """

        def collect_markers_from_event(timeline_event: TimelineEvent) -> list[TimelineMarker]:
            """Recursively collect markers from a timeline event and its children."""
            markers = []

            # Add markers from this timeline event
            for marker in timeline_event.markers:
                # Apply marker type filter
                if marker_type and marker.name != marker_type:
                    continue

                # Apply time range filters
                if start_time and marker.timestamp < start_time:
                    continue
                if end_time and marker.timestamp > end_time:
                    continue

                # Apply metadata filters
                if metadata_filters:
                    match = True
                    for key, value in metadata_filters.items():
                        if key not in marker.metadata or marker.metadata[key] != value:
                            match = False
                            break
                    if not match:
                        continue

                markers.append(marker)

            # Add markers from children
            for child in timeline_event.children:
                markers.extend(collect_markers_from_event(child))

            return markers

        # If timeline_event_id is specified, search only that timeline event
        if timeline_event_id:
            timeline_event = self.get_timeline_event_by_id(timeline_event_id)
            if timeline_event:
                return collect_markers_from_event(timeline_event)
            return []

        # Otherwise, search all timeline events
        result = []
        positions_to_search = [position_id] if position_id else self.position_timeline_events.keys()

        for pos in positions_to_search:
            if pos not in self.position_timeline_events:
                continue

            for timeline_event in self.position_timeline_events[pos]:
                result.extend(collect_markers_from_event(timeline_event))

        return result

    def get_markers_for_timeline_event(self, timeline_event_id: str) -> list[TimelineMarker]:
        """Get all markers for a specific timeline event.

        Args:
            timeline_event_id: The unique identifier of the timeline event

        Returns:
            List of markers for the timeline event
        """
        return self.query_markers(timeline_event_id=timeline_event_id)

    def query_timeline_events(
        self,
        timeline_event_type: str | None = None,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        position_id: str | None = None,
        metadata_filters: dict[str, Any] | None = None,
        include_children: bool = True,
    ) -> list[TimelineEvent]:
        """Query timeline events based on multiple criteria.

        Args:
            timeline_event_type: Filter by timeline event type name
            start_time: Filter timeline events that start after this time (inclusive)
            end_time: Filter timeline events that end before this time (inclusive)
            position_id: Filter by position
            metadata_filters: Dict of metadata key-value pairs to filter on
            include_children: Whether to include child timeline events in results

        Returns:
            List of matching timeline events
        """
        # Special case: for timeline_event_type, directly search for specific timeline events across all positions
        if timeline_event_type:
            all_matching = []
            for pos_timeline_events in self.position_timeline_events.values():
                for timeline_event in pos_timeline_events:
                    # Check root timeline event
                    if timeline_event.name == timeline_event_type:
                        all_matching.append(timeline_event)

                    # Check children recursively
                    def find_timeline_events_by_type(parent):
                        matches = []
                        for child in parent.children:
                            if child.name == timeline_event_type:
                                matches.append(child)
                            matches.extend(find_timeline_events_by_type(child))
                        return matches

                    all_matching.extend(find_timeline_events_by_type(timeline_event))

            # Apply other filters
            filtered = []
            for timeline_event in all_matching:
                if start_time and timeline_event.start_time < start_time:
                    continue
                if end_time and timeline_event.end_time is not None and timeline_event.end_time > end_time:
                    continue
                if position_id and timeline_event.position_id != position_id:
                    continue
                if metadata_filters:
                    match = True
                    for key, value in metadata_filters.items():
                        if key not in timeline_event.metadata or timeline_event.metadata[key] != value:
                            match = False
                            break
                    if not match:
                        continue
                filtered.append(timeline_event)

            return filtered

        # Filter by position first if specified
        positions_to_search = [position_id] if position_id else self.position_timeline_events.keys()
        result = []

        for pos in positions_to_search:
            if pos not in self.position_timeline_events:
                continue

            for timeline_event in self.position_timeline_events[pos]:
                # Check if timeline event is within time range
                if start_time and timeline_event.start_time < start_time:
                    continue
                if end_time and timeline_event.end_time is not None and timeline_event.end_time > end_time:
                    continue

                # Check metadata filters
                if metadata_filters:
                    match = True
                    for key, value in metadata_filters.items():
                        if key not in timeline_event.metadata or timeline_event.metadata[key] != value:
                            match = False
                            break
                    if not match:
                        continue

                # For time range queries:
                # 1. Add the parent timeline event
                result.append(timeline_event)

                # 2. If include_children, also add all children separately
                if include_children:

                    def collect_children(parent_timeline_event):
                        children_list = []
                        for child in parent_timeline_event.children:
                            # Only add children that also match the time range
                            if (not start_time or child.start_time >= start_time) and (
                                not end_time or child.end_time is None or child.end_time <= end_time
                            ):
                                children_list.append(child)
                                children_list.extend(collect_children(child))
                        return children_list

                    result.extend(collect_children(timeline_event))

        return result

    def get_timeline_event_timeline(
        self,
        position_id: str | None = None,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        timeline_event_types: list[str] | None = None,
    ) -> list[dict[str, Any]]:
        """Get a timeline of timeline events suitable for visualization.

        Args:
            position_id: Filter by position
            start_time: Start of time range
            end_time: End of time range
            timeline_event_types: List of timeline event types to include

        Returns:
            List of timeline events
        """
        timeline_events = self.query_timeline_events(start_time=start_time, end_time=end_time, position_id=position_id)

        def timeline_event_to_timeline_event(
            timeline_event: TimelineEvent, level: int = 0, parent_id: str | None = None
        ) -> list[dict[str, Any]]:
            events = []

            # Skip if we're filtering timeline event types and this one isn't included
            if timeline_event_types and timeline_event.name not in timeline_event_types:
                return events

            # Serialize markers
            markers_data = []
            for marker in timeline_event.markers:
                markers_data.append(
                    {
                        "id": marker.id,
                        "name": marker.name,
                        "timestamp": marker.timestamp.isoformat(),
                        "metadata": marker.metadata,
                        "log_event": marker.log_event,
                    }
                )

            events.append(
                {
                    "id": timeline_event.id,
                    "name": timeline_event.name,
                    "start": timeline_event.start_time.isoformat(),
                    "end": timeline_event.end_time.isoformat() if timeline_event.end_time is not None else None,
                    "duration": (
                        (timeline_event.end_time - timeline_event.start_time).total_seconds()
                        if timeline_event.end_time is not None
                        else None
                    ),
                    "level": level,
                    "parent_id": parent_id,
                    "metadata": timeline_event.metadata,
                    "is_complete": timeline_event.end_time is not None,
                    "markers": markers_data,
                }
            )

            # Add child events
            for child in timeline_event.children:
                events.extend(timeline_event_to_timeline_event(child, level + 1, timeline_event.id))

            return events

        timeline_events_list = []
        for timeline_event in timeline_events:
            timeline_events_list.extend(timeline_event_to_timeline_event(timeline_event))

        # Sort by start time
        timeline_events_list.sort(key=lambda x: x["start"])

        return timeline_events_list

    def get_timeline_event_duration_stats(
        self,
        timeline_event_type: str,
        position_id: str | None = None,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        group_by: str | None = None,
    ) -> dict[str, Any]:
        """Get detailed duration statistics for a timeline event type.

        Args:
            timeline_event_type: The type of timeline event to analyze
            position_id: Filter by position
            start_time: Start of time range
            end_time: End of time range
            group_by: Metadata key to group statistics by

        Returns:
            Dictionary containing statistics
        """
        timeline_events = self.query_timeline_events(
            timeline_event_type=timeline_event_type,
            position_id=position_id,
            start_time=start_time,
            end_time=end_time,
        )

        if not timeline_events:
            return {
                "count": 0,
                "mean_duration": 0,
                "median_duration": 0,
                "min_duration": 0,
                "max_duration": 0,
                "std_duration": 0,
                "grouped_stats": {},
            }

        durations = [
            (timeline_event.end_time - timeline_event.start_time).total_seconds()
            for timeline_event in timeline_events
            if timeline_event.end_time is not None
        ]

        stats = {
            "count": len(durations),
            "mean_duration": float(np.mean(durations)),
            "median_duration": float(np.median(durations)),
            "min_duration": float(np.min(durations)),
            "max_duration": float(np.max(durations)),
            "std_duration": float(np.std(durations)),
            "grouped_stats": {},
        }

        # Add grouped statistics if requested
        if group_by:
            grouped_stats = {}
            for timeline_event in timeline_events:
                group_value = timeline_event.metadata.get(group_by, "unknown")
                if group_value not in grouped_stats:
                    grouped_stats[group_value] = []
                if timeline_event.end_time is not None:
                    grouped_stats[group_value].append((timeline_event.end_time - timeline_event.start_time).total_seconds())

            # Calculate stats for each group
            stats["grouped_stats"] = {
                group: {
                    "count": len(durations),
                    "mean_duration": float(np.mean(durations)),
                    "median_duration": float(np.median(durations)),
                    "min_duration": float(np.min(durations)),
                    "max_duration": float(np.max(durations)),
                    "std_duration": float(np.std(durations)),
                }
                for group, durations in grouped_stats.items()
            }

        return stats

    def _find_timeline_events_by_type(self, timeline_events: list[TimelineEvent], timeline_event_type: str) -> list[TimelineEvent]:
        """Recursively find all timeline events of a specific type in a timeline event tree.

        Args:
            timeline_events: List of timeline events to search through
            timeline_event_type: Type of timeline events to find

        Returns:
            List of matching timeline events
        """
        matches = []
        for timeline_event in timeline_events:
            if timeline_event.name == timeline_event_type:
                matches.append(timeline_event)
            matches.extend(self._find_timeline_events_by_type(timeline_event.children, timeline_event_type))
        return matches

    def _timeline_event_to_dict(self, timeline_event: TimelineEvent) -> dict[str, Any]:
        """Convert a TimelineEvent object to a serializable dictionary.

        Args:
            timeline_event: The TimelineEvent object to convert

        Returns:
            Dictionary representation of the timeline event
        """
        # Clean start event
        start_event_dict = (
            timeline_event.start_event.to_dict() if hasattr(timeline_event.start_event, "to_dict") else timeline_event.start_event
        )
        cleaned_start_event = {}
        for k, v in start_event_dict.items():
            if v is not None and not (isinstance(v, float) and (v != v or v in (float("inf"), float("-inf")))):
                cleaned_start_event[k] = v

        # Clean end event (if exists)
        cleaned_end_event = {}
        if timeline_event.end_event is not None:
            end_event_dict = (
                timeline_event.end_event.to_dict() if hasattr(timeline_event.end_event, "to_dict") else timeline_event.end_event
            )
            for k, v in end_event_dict.items():
                if v is not None and not (isinstance(v, float) and (v != v or v in (float("inf"), float("-inf")))):
                    cleaned_end_event[k] = v

        # Serialize markers
        markers_data = []
        for marker in timeline_event.markers:
            markers_data.append(
                {
                    "id": marker.id,
                    "name": marker.name,
                    "timestamp": marker.timestamp.isoformat(),
                    "metadata": marker.metadata,
                    "log_event": marker.log_event,
                    "position_id": marker.position_id,
                }
            )

        return {
            "id": timeline_event.id,
            "name": timeline_event.name,
            "start_identifier": timeline_event.start_identifier,
            "end_identifier": timeline_event.end_identifier,
            "start_time": timeline_event.start_time.isoformat(),
            "end_time": timeline_event.end_time.isoformat() if timeline_event.end_time is not None else None,
            "metadata": timeline_event.metadata,
            "children": [self._timeline_event_to_dict(child) for child in timeline_event.children],
            "markers": markers_data,
            "position_id": timeline_event.position_id,
            "start_event": cleaned_start_event,
            "end_event": cleaned_end_event,
        }

    def get_timeline_event_tree(self) -> dict[str, list[dict[str, Any]]]:
        """Get the complete timeline event tree structure in a serializable format.

        Returns:
            Dictionary mapping position IDs to lists of timeline event trees
        """
        return {
            position: [self._timeline_event_to_dict(timeline_event) for timeline_event in timeline_events]
            for position, timeline_events in self.position_timeline_events.items()
        }

    def identify_timeline_events(self) -> int:
        """Identify and build all timeline events from the log entries.
        This method is called during initialization to process all log entries.

        Returns:
            Total number of timeline events identified
        """
        # Create DataFrame and build timeline event trees
        self._create_dataframe(self.log_entries)

        # Count total timeline events across all positions
        total_timeline_events = 0
        for timeline_events in self.position_timeline_events.values():
            total_timeline_events += len(timeline_events)

        return total_timeline_events
