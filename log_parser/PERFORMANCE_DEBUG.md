# Performance Debug Logging

This package now includes **intelligent performance tracking** that focuses on actual bottlenecks rather than flooding logs with noise from utility functions.

## Quick Start

### Enable Performance Tracking

```python
import log_parser
from log_parser.utils.performance import set_performance_verbosity

# Enable debug logging
log_parser.enable_debug_logging()

# Set verbosity level (choose one):
set_performance_verbosity('minimal')   # Only critical functions (recommended)
set_performance_verbosity('normal')    # Important functions + slow operations
set_performance_verbosity('verbose')   # All functions (very noisy)

# Your existing code...
# analyzer = CrossAnalyzer(resource_analyzer, structured_analyzer)
# result = analyzer.get_log_summary()
```

### View Performance Summary

```python
# Print performance summary (always available, regardless of verbosity)
log_parser.print_performance_summary()
```

## Verbosity Levels

### 🎯 **Minimal** (Recommended for Production Analysis)
- Only tracks: `get_log_summary`, `get_column_availability`, `get_error_distribution`, `cross_analyzer_initialize`
- **Purpose**: Identify frontend bottlenecks without noise
- **Use case**: Finding redundant API calls from frontend

### 📊 **Normal** (Default)
- Tracks important functions + any function taking >100ms
- Utility functions log only every 1000 calls or if >10ms
- **Purpose**: Balance between insight and noise
- **Use case**: General performance monitoring

### 🔍 **Verbose** (Debug Mode)
- Tracks all functions with full logging
- **Purpose**: Deep debugging of specific issues
- **Use case**: Investigating specific slow operations

## What Gets Tracked

### Always Collected (Statistics)
- Function call counts
- Execution timing
- Memory usage trends

### Selectively Logged (Based on Verbosity)
- **High-value functions**: Always logged if marked as important
- **Slow operations**: Functions taking >100ms logged regardless of importance
- **Noisy functions**: Only logged if slow (>10ms) or every 1000 calls
- **Memory changes**: Logged if >10MB change

## Log Message Formats

### Performance Tracking
- `PARSER_PERF_START: function_name (call #N) - Memory: X.XMB` (important functions only)
- `PARSER_PERF_END: function_name completed in X.XXXs - Memory: X.XMB (ΔX.XMB)` (important/slow functions)
- `PARSER_PERF_BATCH: function_name batch of 1000 calls - Avg: X.XXXs, Total calls: N` (noisy functions)
- `PARSER_PERF_MEMORY: function_name significant memory change +X.XMB` (large memory changes)

### Cache Operations
- `PARSER_CACHE_HIT/MISS: cache_key` (important operations only)
- `PARSER_CACHE_SET: cache_key - Size: X` (large cache sets only)

## Instrumented Functions

### 🎯 **Always Tracked** (Important Functions)
- `get_log_summary()` - Frontend log summary requests
- `get_column_availability()` - Column analysis (expensive)
- `get_error_distribution()` - Error analysis
- `get_common_time_range()` - Time range calculations
- `query()` - Data filtering operations
- `cross_analyzer_initialize()` - Analyzer initialization
- `parse_directory()` - File parsing operations

### 📊 **Conditionally Tracked** (Based on Performance)
- Any function taking >100ms (logged even if not marked important)
- Utility functions only if slow (>10ms) or in batches of 1000

### 🔇 **Minimally Tracked** (Noisy Functions)
- `normalize_key()` - Key normalization (called thousands of times)
- `clean_metadata_value()` - Value cleaning (recursive, frequent)
- `clean_metadata_dict()` - Dictionary processing

## Configuration in Dash UI

The dash_ui application automatically configures performance tracking:

```python
# In dash_ui/src/app.py
log_parser.enable_debug_logging()
set_performance_verbosity('normal')  # Change to 'minimal' for less noise
```

## Typical Usage Patterns

### Identifying Redundant Frontend Calls

**Before** (noisy logging):
```
normalize_key called 3000+ times...
clean_metadata_value called 5000+ times...
```

**After** (focused logging):
```
FUNCTION CALL COUNTS:
get_log_summary......................... 15 calls  ← Frontend calling too often!
get_column_availability................. 8 calls   ← Check if cacheable
```

### Identifying Slow Operations

```
PERFORMANCE METRICS:
Function                  Calls    Avg Time   Avg Mem Δ   Max Mem Δ
get_error_distribution    3        2.456      +15.2        +23.1     ← Slow!
get_column_availability   23       0.234      +2.1         +5.4      ← Called often
```

### Memory Issues

```
PARSER_PERF_MEMORY: get_error_distribution significant memory change +25.3MB - Duration: 3.234s
```

## Performance Tips

### For Frontend Integration
1. Look for high call counts on expensive functions → reduce redundant calls
2. Monitor cache hit/miss ratios → improve cache keys
3. Check for functions with high average times → optimization targets

### For Memory Optimization
1. Watch for functions with large positive memory deltas
2. Monitor memory trends over time
3. Look for memory that doesn't get freed (potential leaks)

## Example Output (Minimal Verbosity)

```bash
============================================================
LOG PARSER PERFORMANCE SUMMARY
============================================================

FUNCTION CALL COUNTS:
------------------------------
  get_log_summary......................... 12 calls
  get_column_availability................. 8 calls
  get_common_time_range................... 3 calls
  clean_metadata_dict..................... 45 calls

PERFORMANCE METRICS:
--------------------------------------------------
Function                  Calls    Avg Time   Avg Mem Δ   Max Mem Δ
--------------------------------------------------
get_log_summary           12       0.145      +2.1         +5.4
get_column_availability   8        0.234      +1.2         +3.2
get_common_time_range     3        0.023      +0.5         +0.7
clean_metadata_dict       45       0.002      +0.1         +0.3

============================================================
Legend: Avg Time (seconds), Mem Δ (MB change)
============================================================
```

This focused approach helps you identify **real performance bottlenecks** without getting lost in thousands of utility function calls!
