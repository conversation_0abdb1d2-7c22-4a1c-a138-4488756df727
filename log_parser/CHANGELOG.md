# Changelog

All notable changes to the log_parser library will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Comprehensive README documentation
- Basic usage examples
- API documentation requirements

## [0.1.0] - 2024-01-XX

### Added
- Initial release of log_parser library
- BaseParser abstract class and parser factory pattern
- StructuredLogParser for structured log entries
- ResourceParser for resource system logs
- BaseAnalyzer abstract class and analyzer factory
- StructuredLogAnalyzer for structured log analysis
- ResourceAnalyzer for resource log analysis
- CrossAnalyzer for multi-source correlation
- TimelineAnalyzer for workflow tracking
- AnomalyDetector for pattern anomaly detection
- Pydantic models for type-safe data structures
- Comprehensive test suite with >90% coverage
- Quarantine system for handling unparseable entries
- Time-series analysis capabilities
- Configurable action definitions via TOML
- Auto-detection of log formats
- Directory-based batch processing

### Dependencies
- pydantic >= 2.0.0
- python-dateutil >= 2.8.0
- numpy >= 1.20.0
- pandas >= 2.1.4
- scikit-learn >= 1.0.0
- statsmodels >= 0.13.0
- adtk >= 0.6.2
