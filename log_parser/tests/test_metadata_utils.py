from __future__ import annotations

import numpy as np

from log_parser.utils.metadata_utils import clean_metadata_dict, clean_metadata_value, normalize_key


def test_normalize_key():
    """Test key normalization."""
    # Test spaces
    assert normalize_key("key with spaces") == "key_with_spaces"
    assert normalize_key("multiple   spaces") == "multiple_spaces"

    # Test quotes and backslashes
    assert normalize_key('"quoted key"') == "quoted_key"
    assert normalize_key('\\"escaped quotes\\"') == "escaped_quotes"

    # Test special characters
    assert normalize_key("key-with-hyphens") == "keywithhyphens"
    assert normalize_key("key@#$%^&*()") == "key"

    # Test mixed cases
    assert normalize_key('"key with spaces and \\"quotes\\""') == "key_with_spaces_and_quotes"
    assert normalize_key("key-with spaces and@#$%^&*()") == "keywith_spaces_and"


def test_clean_metadata_value():
    """Test cleaning individual metadata values."""
    # Test None/null values
    assert clean_metadata_value(None) is None
    assert clean_metadata_value("null") is None
    assert clean_metadata_value("NULL") is None
    assert clean_metadata_value("None") is None

    # Test numpy types
    assert clean_metadata_value(np.int32(42)) == 42
    assert clean_metadata_value(np.float64(3.14)) == 3.14
    assert clean_metadata_value(np.array([1, 2, 3])) == [1, 2, 3]
    assert clean_metadata_value(np.bool_(True)) is True

    # Test NaN and Inf values
    assert clean_metadata_value(float("nan")) is None
    assert clean_metadata_value(float("inf")) is None
    assert clean_metadata_value(float("-inf")) is None
    assert clean_metadata_value("nan") is None
    assert clean_metadata_value("NaN") is None

    # Test lists
    assert clean_metadata_value([1, 2, 3]) == [1, 2, 3]
    assert clean_metadata_value([1, None, 3]) == [1, 3]  # None values filtered out
    assert clean_metadata_value([1, float("nan"), 3]) == [1, 3]  # NaN values filtered out
    assert clean_metadata_value([1, "null", 3]) == [1, 3]  # null string filtered out

    # Test dictionaries with complex keys
    test_dict = {
        "key with spaces": 42,
        '"quoted key"': 3.14,
        "key-with-hyphens": float("nan"),
        "key@#$%^&*()": None,
        '\\"escaped quotes\\"': [1, 2, 3],
        "null_value": "null",
        "nan_value": "nan",
    }
    cleaned_dict = clean_metadata_value(test_dict)
    assert cleaned_dict["key_with_spaces"] == 42
    assert cleaned_dict["quoted_key"] == 3.14
    assert cleaned_dict["keywithhyphens"] is None
    assert cleaned_dict["key"] is None
    assert cleaned_dict["escaped_quotes"] == [1, 2, 3]
    assert cleaned_dict["null_value"] is None
    assert cleaned_dict["nan_value"] is None

    # Test strings with quotes
    assert clean_metadata_value('"quoted string"') == "quoted string"
    assert clean_metadata_value("'quoted string'") == "quoted string"
    assert clean_metadata_value("unquoted string") == "unquoted string"
    assert clean_metadata_value('"partially"quoted"') == 'partially"quoted'


def test_clean_metadata_dict():
    """Test cleaning entire metadata dictionaries."""
    test_dict = {
        "key with spaces": 42,
        "float": 3.14,
        "nan": float("nan"),
        "inf": float("inf"),
        "none": None,
        "null": "null",
        "nan_str": "nan",
        "list": [1, float("nan"), 3],
        "dict": {
            "numpy_int": np.int32(42),
            "numpy_float": np.float64(3.14),
            "numpy_array": np.array([1, 2, 3]),
            "numpy_bool": np.bool_(True),
            "key with spaces": "value",
            "null_value": "null",
            "nan_value": "nan",
        },
        "string": '"quoted string"',
        '\\"escaped key\\"': "value",
    }

    cleaned_dict = clean_metadata_dict(test_dict)

    # Check that None/null/NaN values are removed from the top level
    assert "nan" not in cleaned_dict
    assert "inf" not in cleaned_dict
    assert "none" not in cleaned_dict
    assert "null" not in cleaned_dict
    assert "nan_str" not in cleaned_dict

    # Check remaining values
    assert cleaned_dict["key_with_spaces"] == 42
    assert cleaned_dict["float"] == 3.14
    assert cleaned_dict["list"] == [1, 3]  # NaN filtered out
    assert cleaned_dict["string"] == "quoted string"
    assert cleaned_dict["escaped_key"] == "value"

    # Check nested dictionary
    nested_dict = cleaned_dict["dict"]
    assert nested_dict["numpy_int"] == 42
    assert nested_dict["numpy_float"] == 3.14
    assert nested_dict["numpy_array"] == [1, 2, 3]
    assert nested_dict["numpy_bool"] is True
    assert nested_dict["key_with_spaces"] == "value"
    assert "null" not in nested_dict
    assert "nan" not in nested_dict
