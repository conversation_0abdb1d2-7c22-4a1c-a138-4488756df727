"""
Tests for the TimelineAnalyzer class.
"""

from datetime import datetime, timedelta

import pytest

from log_parser.analysis.timeline_analyzer import TimelineAnalyzer, TimelineEvent
from log_parser.models import StructuredLogEntry


class TestTimelineAnalyzer:
    """Tests for TimelineAnalyzer functionality."""

    def test_process_filtering(self):
        """Test that timeline events are filtered by process name."""
        base_time = datetime.now()
        mock_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Control server starting up",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="other_process",
                keys={"run_id": "123"},
                message="Other process starting up",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="shutdown_complete",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Control server shutdown complete",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="control_server_lifecycle")

        # Should find 1 event (only control_server events)
        assert len(timeline_events) == 1
        assert timeline_events[0].metadata.get("process_name") == "control_server"
        assert timeline_events[0].is_complete()

    def test_positional_matching(self):
        """Test that timeline events work without shared identifiers using positional matching."""
        base_time = datetime.now()
        mock_logs = [
            # Control server starts up (no run_id)
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={},  # No shared identifier
                message="Control server starting up",
            ),
            # Some other process starts up (should be ignored due to process filter)
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="other_process",
                keys={},
                message="Other process starting up",
            ),
            # Control server shuts down (no run_id)
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="shutdown_complete",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={},  # No shared identifier
                message="Control server shutdown complete",
            ),
            # Another control server starts up
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=3),
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={},
                message="Control server starting up again",
            ),
            # This shutdown should match the second startup
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=4),
                log_level="INFO",
                log_event="shutdown_complete",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={},
                message="Control server shutdown complete again",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="control_server_lifecycle")

        # Should find 2 complete timeline events (each startup matched with next shutdown)
        assert len(timeline_events) == 2

        # Both should be complete
        for event in timeline_events:
            assert event.is_complete(), f"Event {event.id} should be complete"

        # Both should be from control_server process
        for event in timeline_events:
            assert event.metadata.get("process_name") == "control_server"

        # Check that IDs contain 'positional' for positional matching
        for event in timeline_events:
            assert "positional" in event.id

    def test_incomplete_timeline_events(self):
        """Test that incomplete timeline events (missing end events) are handled correctly."""
        base_time = datetime.now()
        mock_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={},
                message="Control server starting up",
            ),
            # No shutdown event - this should create an incomplete timeline event
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="control_server_lifecycle")

        # Should find 1 incomplete event
        assert len(timeline_events) == 1
        assert not timeline_events[0].is_complete()
        assert timeline_events[0].end_time is None
        assert timeline_events[0].end_event is None

    def test_timeline_event_is_complete_method(self):
        """Test the is_complete method of TimelineEvent."""
        base_time = datetime.now()

        # Complete event
        complete_event = TimelineEvent(
            id="test_complete",
            name="test",
            start_identifier="start",
            end_identifier="end",
            start_time=base_time,
            end_time=base_time + timedelta(seconds=1),
            metadata={},
            children=[],
            markers=[],
            position_id="pos1",
            start_event={"test": "data"},
            end_event={"test": "data"},
        )
        assert complete_event.is_complete()

        # Incomplete event
        incomplete_event = TimelineEvent(
            id="test_incomplete",
            name="test",
            start_identifier="start",
            end_identifier="end",
            start_time=base_time,
            end_time=None,
            metadata={},
            children=[],
            markers=[],
            position_id="pos1",
            start_event={"test": "data"},
            end_event=None,
        )
        assert not incomplete_event.is_complete()

    def test_identifier_based_matching(self):
        """Test that identifier-based matching still works for events with shared identifiers."""
        base_time = datetime.now()
        mock_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "run_123"},
                message="Protocol started",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "run_456"},
                message="Another protocol started",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="protocol_finished_successfully",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "run_123"},
                message="Protocol finished",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="protocol")

        # Should find 2 events: 1 complete (run_123) and 1 incomplete (run_456)
        assert len(timeline_events) == 2

        # Find the complete and incomplete events
        complete_events = [e for e in timeline_events if e.is_complete()]
        incomplete_events = [e for e in timeline_events if not e.is_complete()]

        assert len(complete_events) == 1
        assert len(incomplete_events) == 1

        # The complete event should have run_123 identifier
        assert "run_123" in complete_events[0].id
        assert "run_456" in incomplete_events[0].id

    def test_positional_matching_metadata_collection(self):
        """Test that metadata is properly collected for positional matching events."""
        base_time = datetime.now()
        mock_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="starting_up",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"custom_key": "start_value", "session_id": "abc123"},
                message="Control server starting up",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="WARN",
                log_event="shutdown_complete",
                library="shutdown",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"custom_key": "end_value", "exit_code": "0"},
                message="Control server shutdown complete",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="control_server_lifecycle")

        # Should find 1 complete event
        assert len(timeline_events) == 1
        event = timeline_events[0]
        assert event.is_complete()

        # Check that metadata from both start and end events is collected
        metadata = event.metadata

        # From start event keys
        assert metadata.get("session_id") == "abc123"
        assert metadata.get("custom_key") == "end_value"  # End event overwrites start event

        # From end event keys
        assert metadata.get("exit_code") == "0"

        # Process information from start event
        assert metadata.get("process_name") == "control_server"
        assert metadata.get("log_level") == "INFO"
        assert metadata.get("library") == "control"

        # Process information from end event
        assert metadata.get("end_process_name") == "control_server"
        assert metadata.get("end_log_level") == "WARN"
        assert metadata.get("end_library") == "shutdown"

    def test_mk_manager_metadata_complete_vs_incomplete(self):
        """Test that start event metadata is preserved for both complete and incomplete mk_manager events."""
        base_time = datetime.now()

        # Test incomplete event (only start event)
        incomplete_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="mk_manager_starting",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"hostname": "test-host", "system": "test-system"},
                message="MK Manager starting",
            ),
        ]

        # Test complete event (start + end)
        complete_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="mk_manager_starting",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"hostname": "test-host", "system": "test-system"},
                message="MK Manager starting",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=5),
                log_level="INFO",
                log_event="mk_manager_shut_down",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"exit_status": "clean"},
                message="MK Manager shut down",
            ),
        ]

        # Test incomplete event
        incomplete_analyzer = TimelineAnalyzer(incomplete_logs)
        incomplete_events = incomplete_analyzer.query_timeline_events(timeline_event_type="mk_manager_lifecycle")

        assert len(incomplete_events) == 1
        incomplete_event = incomplete_events[0]
        assert not incomplete_event.is_complete()

        incomplete_metadata = incomplete_event.metadata

        # Test complete event
        complete_analyzer = TimelineAnalyzer(complete_logs)
        complete_events = complete_analyzer.query_timeline_events(timeline_event_type="mk_manager_lifecycle")

        assert len(complete_events) == 1
        complete_event = complete_events[0]
        assert complete_event.is_complete()

        complete_metadata = complete_event.metadata

        # Both should have the start event metadata
        assert incomplete_metadata.get("hostname") == "test-host", f"Incomplete event missing hostname: {incomplete_metadata}"
        assert incomplete_metadata.get("system") == "test-system", f"Incomplete event missing system: {incomplete_metadata}"

        assert complete_metadata.get("hostname") == "test-host", f"Complete event missing hostname: {complete_metadata}"
        assert complete_metadata.get("system") == "test-system", f"Complete event missing system: {complete_metadata}"

        # Complete event should also have end event metadata
        assert complete_metadata.get("exit_status") == "clean"

    def test_mk_manager_metadata_with_key_conflicts(self):
        """Test metadata collection when start and end events have conflicting keys."""
        base_time = datetime.now()

        # Create logs where end event has None values for some keys that start event has
        complete_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="mk_manager_starting",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"hostname": "test-host", "system": "test-system", "shared_key": "start_value"},
                message="MK Manager starting",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=5),
                log_level="INFO",
                log_event="mk_manager_shut_down",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"exit_status": "clean", "shared_key": "end_value"},  # Different value for shared_key
                message="MK Manager shut down",
            ),
        ]

        analyzer = TimelineAnalyzer(complete_logs)
        events = analyzer.query_timeline_events(timeline_event_type="mk_manager_lifecycle")

        assert len(events) == 1
        event = events[0]
        assert event.is_complete()

        metadata = event.metadata

        # Start event unique keys should be preserved
        assert metadata.get("hostname") == "test-host"
        assert metadata.get("system") == "test-system"

        # End event unique keys should be preserved
        assert metadata.get("exit_status") == "clean"

        # Conflicting key should have end event value (end event overwrites start)
        assert metadata.get("shared_key") == "end_value"

    def test_mk_manager_metadata_with_none_values(self):
        """Test metadata collection when some values get cleaned to None."""
        base_time = datetime.now()

        # Create logs where start event has values that might be cleaned to None
        complete_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="mk_manager_starting",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"hostname": "test-host", "system": "null", "empty_val": "", "nan_val": "NaN"},
                message="MK Manager starting",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=5),
                log_level="INFO",
                log_event="mk_manager_shut_down",
                library="mk_manager",
                file_name="test.log",
                folder_name="positionA",
                process_name="mk_manager_svc",
                keys={"exit_status": "clean"},
                message="MK Manager shut down",
            ),
        ]

        analyzer = TimelineAnalyzer(complete_logs)
        events = analyzer.query_timeline_events(timeline_event_type="mk_manager_lifecycle")

        assert len(events) == 1
        event = events[0]
        assert event.is_complete()

        metadata = event.metadata
        print(f"Metadata with None values: {metadata}")
        print(f"Metadata keys: {list(metadata.keys())}")

        # Values that should survive cleaning
        assert metadata.get("hostname") == "test-host"
        assert metadata.get("exit_status") == "clean"

        # Values that are cleaned to None should be removed entirely
        assert "system" not in metadata  # "null" gets cleaned to None, then removed
        assert "nan_val" not in metadata  # "NaN" gets cleaned to None, then removed

    def test_real_world_mk_manager_metadata(self):
        """Test metadata collection with real-world mk_manager example data."""
        base_time = datetime.now()

        # Create logs based on your real example with actual key names
        complete_logs = [
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="mk_manager_starting",
                library="mk_manager",
                file_name="mk_manager_svc_log-1.txt",
                folder_name="minknow",
                process_name="mk_manager_svc",
                keys={
                    "hostname": "P2i-00147",
                    "system": "PromethION 2 Integrated (PRO-INT002) P2I-00147 running on ubuntu 20.04 (5.15.0-119-generic)",
                    "Distribution": "unknown (MODIFIED)",
                    "MinKNOW Core": "6.2.6",
                    "Bream": "8.4.4",
                    "Protocol configuration": "6.2.12",
                    "Dorado (client)": "7.6.0+ad1db9c5a",
                    "Dorado (server)": "7.6.7+de8939544",
                },
                message="MK Manager starting",
            ),
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=25),
                log_level="INFO",
                log_event="mk_manager_shut_down",
                library="mk_manager",
                file_name="test.log",
                folder_name="minknow",
                process_name="mk_manager_svc",
                keys={},
                message="MK Manager shut down",
            ),
        ]

        analyzer = TimelineAnalyzer(complete_logs)
        events = analyzer.query_timeline_events(timeline_event_type="mk_manager_lifecycle")

        assert len(events) == 1
        event = events[0]
        assert event.is_complete()

        metadata = event.metadata
        print(f"Real-world metadata: {metadata}")

        # Debug: Check what the start event looks like
        start_event_dict = event.start_event.to_dict()
        print(f"Start event keys: {[k for k in start_event_dict.keys()]}")
        print(f"Start event key_ keys: {[k for k in start_event_dict.keys() if k.startswith('key_')]}")

        # These keys should now be preserved in the timeline metadata
        assert metadata.get("hostname") == "P2i-00147"
        assert metadata.get("system") == "PromethION 2 Integrated (PRO-INT002) P2I-00147 running on ubuntu 20.04 (5.15.0-119-generic)"

        # Keys with spaces and special characters should be normalized
        assert metadata.get("Distribution") == "unknown (MODIFIED)"
        assert metadata.get("MinKNOW_Core") == "6.2.6"  # "MinKNOW Core" → "MinKNOW_Core"
        assert metadata.get("Bream") == "8.4.4"
        assert metadata.get("Protocol_configuration") == "6.2.12"  # "Protocol configuration" → "Protocol_configuration"
        assert metadata.get("Dorado_client") == "7.6.0+ad1db9c5a"  # "Dorado (client)" → "Dorado_client"
        assert metadata.get("Dorado_server") == "7.6.7+de8939544"  # "Dorado (server)" → "Dorado_server"

        # Standard process metadata should also be present
        assert metadata.get("process_name") == "mk_manager_svc"
        assert metadata.get("log_level") == "INFO"
        assert metadata.get("library") == "mk_manager"

    def test_timeline_markers_basic(self):
        """Test basic timeline marker functionality."""
        base_time = datetime.now()
        mock_logs = [
            # Protocol starts
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol started",
            ),
            # Phase change marker
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_INITIALISING"},
                message="Phase changed to PHASE_INITIALISING",
            ),
            # Another phase change marker
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_RUNNING"},
                message="Phase changed to PHASE_RUNNING",
            ),
            # Protocol ends
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=3),
                log_level="INFO",
                log_event="protocol_finished_successfully",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol finished",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="protocol")

        # Should find 1 protocol event
        assert len(timeline_events) == 1
        protocol_event = timeline_events[0]
        assert protocol_event.is_complete()

        # Should have 2 markers
        assert len(protocol_event.markers) == 2

        # Check marker details
        markers = sorted(protocol_event.markers, key=lambda m: m.timestamp)

        # First marker
        assert markers[0].name == "protocol_phase"
        assert markers[0].log_event == "protocol_phase_changed"
        assert markers[0].metadata.get("phase") == "PHASE_INITIALISING"
        assert markers[0].metadata.get("process_name") == "control_server"
        assert markers[0].metadata.get("library") == "script"

        # Second marker
        assert markers[1].name == "protocol_phase"
        assert markers[1].log_event == "protocol_phase_changed"
        assert markers[1].metadata.get("phase") == "PHASE_RUNNING"

    def test_timeline_markers_query(self):
        """Test querying timeline markers."""
        base_time = datetime.now()
        mock_logs = [
            # Protocol starts
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol started",
            ),
            # Phase change marker
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_INITIALISING"},
                message="Phase changed to PHASE_INITIALISING",
            ),
            # Another phase change marker
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_RUNNING"},
                message="Phase changed to PHASE_RUNNING",
            ),
            # Protocol ends
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=3),
                log_level="INFO",
                log_event="protocol_finished_successfully",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol finished",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)

        # Query all markers
        all_markers = analyzer.query_markers()
        assert len(all_markers) == 2

        # Query by marker type
        phase_markers = analyzer.query_markers(marker_type="protocol_phase")
        assert len(phase_markers) == 2

        # Query by metadata filter
        init_markers = analyzer.query_markers(metadata_filters={"phase": "PHASE_INITIALISING"})
        assert len(init_markers) == 1
        assert init_markers[0].metadata.get("phase") == "PHASE_INITIALISING"

        # Query by time range
        early_markers = analyzer.query_markers(start_time=base_time, end_time=base_time + timedelta(seconds=1.5))
        assert len(early_markers) == 1
        assert early_markers[0].metadata.get("phase") == "PHASE_INITIALISING"

    def test_timeline_markers_serialization(self):
        """Test that markers are included in timeline event serialization."""
        base_time = datetime.now()
        mock_logs = [
            # Protocol starts
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol started",
            ),
            # Phase change marker
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_INITIALISING"},
                message="Phase changed to PHASE_INITIALISING",
            ),
            # Protocol ends
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="protocol_finished_successfully",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol finished",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)

        # Test timeline event serialization
        timeline_data = analyzer.get_timeline_event_timeline(position_id="positionA")
        assert len(timeline_data) == 1

        protocol_data = timeline_data[0]
        assert "markers" in protocol_data
        assert len(protocol_data["markers"]) == 1

        marker_data = protocol_data["markers"][0]
        assert marker_data["name"] == "protocol_phase"
        assert marker_data["log_event"] == "protocol_phase_changed"
        assert "timestamp" in marker_data
        assert marker_data["metadata"]["phase"] == "PHASE_INITIALISING"

        # Test tree serialization
        tree_data = analyzer.get_timeline_event_tree()
        assert "positionA" in tree_data
        assert len(tree_data["positionA"]) == 1

        protocol_tree = tree_data["positionA"][0]
        assert "markers" in protocol_tree
        assert len(protocol_tree["markers"]) == 1

    def test_markers_with_incomplete_timeline_events(self):
        """Test markers work with incomplete timeline events."""
        base_time = datetime.now()
        mock_logs = [
            # Protocol starts
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol started",
            ),
            # Phase change marker
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_INITIALISING"},
                message="Phase changed to PHASE_INITIALISING",
            ),
            # No protocol end event - incomplete timeline
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="protocol")

        # Should find 1 incomplete protocol event
        assert len(timeline_events) == 1
        protocol_event = timeline_events[0]
        assert not protocol_event.is_complete()

        # Should still have the marker
        assert len(protocol_event.markers) == 1
        assert protocol_event.markers[0].metadata.get("phase") == "PHASE_INITIALISING"

    def test_markers_process_filtering(self):
        """Test that markers respect process filtering."""
        base_time = datetime.now()
        mock_logs = [
            # Protocol starts
            StructuredLogEntry(
                timestamp=base_time,
                log_level="INFO",
                log_event="protocol_started",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol started",
            ),
            # Phase change from control_server (should be included)
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"phase": "PHASE_INITIALISING"},
                message="Phase changed to PHASE_INITIALISING",
            ),
            # Phase change from other_process (should be excluded)
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=1.5),
                log_level="INFO",
                log_event="protocol_phase_changed",
                library="script",
                file_name="test.log",
                folder_name="positionA",
                process_name="other_process",
                keys={"phase": "PHASE_RUNNING"},
                message="Phase changed to PHASE_RUNNING",
            ),
            # Protocol ends
            StructuredLogEntry(
                timestamp=base_time + timedelta(seconds=2),
                log_level="INFO",
                log_event="protocol_finished_successfully",
                library="control",
                file_name="test.log",
                folder_name="positionA",
                process_name="control_server",
                keys={"run_id": "123"},
                message="Protocol finished",
            ),
        ]

        analyzer = TimelineAnalyzer(mock_logs)
        timeline_events = analyzer.query_timeline_events(timeline_event_type="protocol")

        # Should find 1 protocol event
        assert len(timeline_events) == 1
        protocol_event = timeline_events[0]

        # Should have only 1 marker (from control_server, not other_process)
        assert len(protocol_event.markers) == 1
        assert protocol_event.markers[0].metadata.get("phase") == "PHASE_INITIALISING"
        assert protocol_event.markers[0].metadata.get("process_name") == "control_server"

    def test_get_available_markers(self):
        """Test getting available marker configurations."""
        analyzer = TimelineAnalyzer([])

        # Should have the protocol_phase marker from config
        markers = analyzer.get_available_markers()
        assert len(markers) >= 1

        # Check that protocol_phase marker is configured
        phase_marker = next((m for m in markers if m["name"] == "protocol_phase"), None)
        assert phase_marker is not None
        assert phase_marker["log_event"] == "protocol_phase_changed"
        assert phase_marker["process_filter"] == "control_server"
