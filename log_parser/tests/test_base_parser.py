from __future__ import annotations

import os
from datetime import datetime
from unittest.mock import mock_open, patch

import pytest

from log_parser.models import LogEntry
from log_parser.parsers.base_parser import BaseParser


class ConcreteParser(BaseParser):
    """Concrete implementation of BaseParser for testing."""

    @property
    def log_type(self) -> str:
        return "test"

    def can_parse(self, file_path: str) -> bool:
        """Check if a file can be parsed."""
        # Simple criteria: file must have .test extension
        return file_path.endswith(".test")

    def parse_file(self, file_path: str, **metadata) -> list:
        """Parse a file and return log entries."""
        entries = []

        # Simulate reading the file to make tests controllable
        with open(file_path) as f:
            for i, line in enumerate(f):
                # Create a log entry for each line
                entry = LogEntry(timestamp=datetime.now())
                entries.append(entry)

        return entries


class TestBaseParser:
    @pytest.fixture
    def parser(self):
        """Create a concrete parser instance."""
        return ConcreteParser()

    def test_log_type(self, parser):
        """Test the log type property."""
        assert parser.log_type == "test"

    def test_can_parse(self, parser):
        """Test the can_parse method."""
        assert parser.can_parse("file.test") is True
        assert parser.can_parse("file.txt") is False

    @patch("os.path.isdir")
    def test_parse_directory_non_existent(self, mock_isdir, parser):
        """Test parsing a non-existent directory."""
        mock_isdir.return_value = False

        entries, quarantine = parser.parse_directory("/non/existent/dir")
        assert entries == []
        assert quarantine == []
        mock_isdir.assert_called_once_with("/non/existent/dir")

    @patch("os.path.isdir")
    @patch("os.walk")
    def test_parse_directory_empty(self, mock_walk, mock_isdir, parser):
        """Test parsing an empty directory."""
        mock_isdir.return_value = True
        mock_walk.return_value = []

        entries, quarantine = parser.parse_directory("/empty/dir")
        assert entries == []
        assert quarantine == []
        mock_isdir.assert_called_once_with("/empty/dir")
        mock_walk.assert_called_once_with("/empty/dir")

    @patch("os.path.isdir")
    @patch("os.walk")
    @patch("builtins.open", new_callable=mock_open, read_data="line1\nline2\nline3")
    def test_parse_directory_with_files(self, mock_file, mock_walk, mock_isdir, parser):
        """Test parsing a directory with files."""
        mock_isdir.return_value = True
        mock_walk.return_value = [
            ("/root", ["subdir"], ["file1.test", "file2.txt"]),
            ("/root/subdir", [], ["file3.test", "file4.log"]),
        ]

        # Mock the can_parse and parse_file methods to control behavior
        with patch.object(parser, "parse_file", return_value=([LogEntry(timestamp=datetime.now())], [])) as mock_parse:
            entries, quarantine = parser.parse_directory("/test/dir")

            # Should only try to parse .test files
            assert mock_parse.call_count == 2

            # Check metadata passed to parse_file
            calls = mock_parse.call_args_list
            assert os.path.join("/root", "file1.test") in str(calls[0])
            assert os.path.join("/root/subdir", "file3.test") in str(calls[1])

            # Should have one entry per file
            assert len(entries) == 2
            assert len(quarantine) == 0

    @patch("os.path.isdir")
    @patch("os.walk")
    @patch.object(ConcreteParser, "parse_file")
    def test_parse_directory_error_handling(self, mock_parse, mock_walk, mock_isdir, parser):
        """Test error handling when parsing a directory."""
        mock_isdir.return_value = True
        mock_walk.return_value = [("/root", [], ["file1.test", "file2.test"])]

        # First file parses normally, second file raises exception
        mock_parse.side_effect = [
            ([LogEntry(timestamp=datetime.now())], []),
            Exception("Test parsing error"),
        ]

        entries, quarantine = parser.parse_directory("/test/dir")

        # Should have tried to parse both files
        assert mock_parse.call_count == 2

        # But only got entries from the first file
        assert len(entries) == 1
        assert len(quarantine) == 0

    @patch("builtins.open", new_callable=mock_open, read_data="line1\nline2\nline3")
    def test_parse_file_with_metadata(self, mock_file, parser):
        """Test parsing a file with metadata."""
        with patch.object(parser, "parse_file", wraps=parser.parse_file) as wrapped_parse:
            metadata = {"file_name": "test.log", "folder_name": "logs"}
            entries = parser.parse_file("test.test", **metadata)

            # Should have been called with the metadata
            wrapped_parse.assert_called_once_with("test.test", **metadata)

            # File should have been opened (mode defaults to "r")
            mock_file.assert_called_once_with("test.test")

            # Should have entries (one per line in the mock file)
            assert len(entries) == 3

    def test_abstract_methods(self):
        """Test that abstract methods must be implemented."""

        # Create a subclass that doesn't implement all abstract methods
        class IncompleteParser(BaseParser):
            @property
            def log_type(self) -> str:
                return "incomplete"

        # Attempting to instantiate should raise TypeError
        with pytest.raises(TypeError):
            IncompleteParser()
