from __future__ import annotations

from unittest.mock import mock_open, patch

import pytest

from log_parser.models import QuarantineEntry, StructuredLogEntry
from log_parser.parsers.structured_log_parser import StructuredLogParser


class TestStructuredLogParser:
    @pytest.fixture
    def parser(self):
        """Create a structured log parser instance."""
        return StructuredLogParser()

    def test_log_type(self, parser):
        """Test the log type property."""
        assert parser.log_type == "structured"

    def test_can_parse_valid_file(self, parser):
        """Test the can_parse method with valid files."""
        with patch("os.path.isfile", return_value=True):
            assert parser.can_parse("control_server_log-0.txt") is True
            assert parser.can_parse("sequencing_engine_log-1.txt") is True
            assert parser.can_parse("basecaller_log-2.txt") is True

    def test_can_parse_invalid_file(self, parser):
        """Test the can_parse method with invalid files."""
        with patch("os.path.isfile", return_value=True):
            assert parser.can_parse("bream-123.txt") is False
            assert parser.can_parse("file.txt") is False
            assert parser.can_parse("file.log") is False
            assert parser.can_parse("file") is False

    def test_parse_timestamp(self, parser):
        """Test the _parse_timestamp method."""
        # Test different timestamp formats
        timestamp = parser._parse_timestamp("2023-01-01 12:00:00.123456")
        assert timestamp.year == 2023
        assert timestamp.month == 1
        assert timestamp.day == 1
        assert timestamp.hour == 12
        assert timestamp.minute == 0
        assert timestamp.second == 0
        assert timestamp.microsecond == 123456

        timestamp = parser._parse_timestamp("2023-01-01T12:00:00")
        assert timestamp.year == 2023
        assert timestamp.hour == 12

        # Test invalid timestamp
        with pytest.raises(ValueError):
            parser._parse_timestamp("invalid timestamp")

    def test_extract_key_value_pairs(self, parser):
        """Test the _extract_key_value_pairs method."""
        # Test JSON format
        json_message = '{"key1": "value1", "key2": 123}'
        result = parser._extract_key_value_pairs(json_message)
        assert result == {"key1": "value1", "key2": 123}

        # Test key: value format
        message = """
        flowcell_id: PBC88974
        host_serial_number: P2I-00147
        identity: ['']
        library_id: 6_2_p2i_retest
        output_path: /data/path
        """
        result = parser._extract_key_value_pairs(message)
        assert result == {
            "flowcell_id": "PBC88974",
            "host_serial_number": "P2I-00147",
            "identity": "['']",
            "library_id": "6_2_p2i_retest",
            "output_path": "/data/path",
        }

        # Test empty message
        result = parser._extract_key_value_pairs("")
        assert result == {}

    def test_convert_value(self, parser):
        """Test the _convert_value method."""
        # Test boolean values
        assert parser._convert_value("true") is True
        assert parser._convert_value("yes") is True
        assert parser._convert_value("false") is False
        assert parser._convert_value("no") is False

        # Test None values
        assert parser._convert_value("none") is None
        assert parser._convert_value("null") is None

        # Test numeric values
        assert parser._convert_value("123") == 123
        assert parser._convert_value("123.45") == 123.45

        # Test string values
        assert parser._convert_value("hello") == "hello"
        assert parser._convert_value("123abc") == "123abc"

    @patch("builtins.open")
    @patch("os.path.isfile", return_value=True)
    def test_parse_file(self, mock_isfile, mock_open_func, parser):
        """Test parsing a log file."""
        # Create a sample log file content
        log_content = """2023-01-01 12:00:00.123456 INFO: protocol_started (script)
    flowcell_id: PBC88974
    host_serial_number: P2I-00147
    identity: ['']
    library_id: 6_2_p2i_retest
    output_path: /data/path
2023-01-01 12:01:00.123456 WARNING: device_error (device)
    error_code: ERR001
    error_message: Device not responding"""

        mock_file = mock_open(read_data=log_content)
        mock_open_func.return_value = mock_file()

        entries, quarantine = parser.parse_file(
            "control_server_log-0.txt",
            file_name="control_server_log-0.txt",
            folder_name="positionA",
        )

        # Check that we got the expected number of entries
        assert len(entries) == 2
        assert len(quarantine) == 0

        # Check first entry
        entry = entries[0]
        assert isinstance(entry, StructuredLogEntry)
        assert entry.timestamp.year == 2023
        assert entry.timestamp.month == 1
        assert entry.timestamp.day == 1
        assert entry.log_level == "INFO"
        assert entry.log_event == "protocol_started"
        assert entry.library == "script"
        assert entry.file_name == "control_server_log-0.txt"
        assert entry.folder_name == "positionA"
        assert entry.process_name == "control_server"
        assert entry.keys == {
            "flowcell_id": "PBC88974",
            "host_serial_number": "P2I-00147",
            "identity": "['']",
            "library_id": "6_2_p2i_retest",
            "output_path": "/data/path",
        }

        # Check second entry
        entry = entries[1]
        assert entry.log_level == "WARNING"
        assert entry.log_event == "device_error"
        assert entry.library == "device"
        assert entry.keys == {"error_code": "ERR001", "error_message": "Device not responding"}

    @patch("builtins.open")
    @patch("os.path.isfile", return_value=True)
    def test_parse_file_error_handling(self, mock_isfile, mock_open_func, parser):
        """Test error handling when parsing a file."""
        # Setup mock to raise an exception on reading
        mock_open_func.side_effect = OSError("Test error")

        entries, quarantine = parser.parse_file("control_server_log-0.txt")

        # Check that we have no entries and no quarantine entries
        assert len(entries) == 0
        assert len(quarantine) == 0

    @patch("builtins.open")
    @patch("os.path.isfile", return_value=True)
    def test_parse_file_empty(self, mock_isfile, mock_open_func, parser):
        """Test parsing an empty file."""
        # Empty file
        mock_file = mock_open(read_data="")
        mock_open_func.return_value = mock_file()

        entries, quarantine = parser.parse_file("control_server_log-0.txt")

        # Should have no entries and no quarantine entries
        assert len(entries) == 0
        assert len(quarantine) == 0

    @patch("builtins.open")
    @patch("os.path.isfile", return_value=True)
    def test_parse_file_invalid_format(self, mock_isfile, mock_open_func, parser):
        """Test parsing a file with invalid format."""
        # File with invalid log format
        log_content = """Invalid log format
Another invalid line"""

        mock_file = mock_open(read_data=log_content)
        mock_open_func.return_value = mock_file()

        entries, quarantine = parser.parse_file("control_server_log-0.txt")

        # Should have no entries and quarantine entries for invalid lines
        assert len(entries) == 0
        assert len(quarantine) == 2
        assert all(isinstance(q, QuarantineEntry) for q in quarantine)
