from __future__ import annotations

from datetime import datetime
from unittest.mock import mock_open, patch

import pytest

from log_parser.models import QuarantineEntry, ResourceLogEntry
from log_parser.parsers.resource_parser import ResourceParser
from log_parser.utils import clean_metadata_dict


class TestResourceParser:
    @pytest.fixture
    def parser(self):
        """Create a resource parser instance."""
        return ResourceParser()

    def test_log_type(self, parser):
        """Test the log type property."""
        assert parser.log_type == "resource"

    def test_can_parse_valid_file(self, parser):
        """Test the can_parse method with valid files."""
        # The actual implementation checks for resource-system*.log files
        with patch("os.path.isfile", return_value=True):
            assert parser.can_parse("resource-system.log") is True
            assert parser.can_parse("resource-system-1234.log") is True
            assert parser.can_parse("resource-system_special.log") is True

    def test_can_parse_invalid_file(self, parser):
        """Test the can_parse method with invalid files."""
        # Files that do not match the pattern should be rejected
        with patch("os.path.isfile", return_value=True):
            assert parser.can_parse("file.resource.csv") is False
            assert parser.can_parse("file.csv") is False
            assert parser.can_parse("file.txt") is False
            assert parser.can_parse("file.log") is False
            assert parser.can_parse("file") is False

    def test_normalize_header(self, parser):
        """Test the _normalize_header method."""
        assert parser._normalize_header("Time") == "Time"
        assert parser._normalize_header(" CPU Utilization ") == "CPU_Utilization"
        assert parser._normalize_header("Memory.Used") == "Memory.Used"
        assert parser._normalize_header("Block[0].Name") == "Block[0].Name"

    def test_is_valid_timestamp(self, parser):
        """Test the _is_valid_timestamp method."""
        assert parser._is_valid_timestamp("2023-01-01T12:00:00") is True
        assert parser._is_valid_timestamp("2023-01-01 12:00:00") is True
        assert parser._is_valid_timestamp("not a timestamp") is False
        assert parser._is_valid_timestamp("") is False

    @patch("builtins.open")
    @patch("os.path.isfile", return_value=True)
    def test_parse_file_error_handling(self, mock_isfile, mock_open_func, parser):
        """Test error handling when parsing a file."""
        # Setup mock to raise an exception on reading
        mock_open_func.side_effect = OSError("Test error")

        entries, quarantine = parser.parse_file("resource-system.log")

        # Check that we have no entries and one quarantine entry
        assert len(entries) == 0
        assert len(quarantine) == 1
        # Check that the quarantine entry contains the error information
        assert isinstance(quarantine[0], QuarantineEntry)
        assert "error" in quarantine[0].reason.lower()

    @patch("builtins.open")
    @patch("os.path.isfile", return_value=True)
    def test_parse_file_empty(self, mock_isfile, mock_open_func, parser):
        """Test parsing an empty file."""
        # Empty file
        mock_file = mock_open(read_data="")
        mock_open_func.return_value = mock_file()

        entries, quarantine = parser.parse_file("resource-system.log")

        # Should have no entries and no quarantine entries
        assert len(entries) == 0
        assert len(quarantine) == 0

    def test_parse_value(self, parser):
        """Test the _parse_value method."""
        # Test with numeric values
        assert parser._parse_value("123", "TestColumn") == 123
        assert parser._parse_value("123.45", "TestColumn") == 123.45

        # Test with string values
        assert parser._parse_value("text", "TestColumn") == "text"

        # Test with boolean-like string values (note: implementation does not convert to actual booleans)
        assert parser._parse_value("true", "TestColumn") == "true"
        assert parser._parse_value("false", "TestColumn") == "false"

        # Test with empty values
        assert parser._parse_value("", "TestColumn") is None
        assert parser._parse_value(None, "TestColumn") is None

        # Test with special characters
        assert parser._parse_value("Hello, World!", "TestColumn") == "Hello, World!"

    def test_parse_timestamp(self, parser):
        """Test the _parse_timestamp method."""
        # ISO format
        timestamp = parser._parse_timestamp("2023-01-01T12:00:00")
        assert timestamp.year == 2023
        assert timestamp.month == 1
        assert timestamp.day == 1
        assert timestamp.hour == 12
        assert timestamp.minute == 0
        assert timestamp.second == 0

        # Space format
        timestamp = parser._parse_timestamp("2023-01-01 12:00:00")
        assert timestamp.year == 2023
        assert timestamp.hour == 12

        # With milliseconds
        timestamp = parser._parse_timestamp("2023-01-01T12:00:00.123")
        assert timestamp.microsecond == 123000

    def test_resource_log_entry_model(self):
        """Test the ResourceLogEntry model."""
        # Create test data
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        data = {"CPU.Utilization": 50.5, "Memory.Used": 4000, "Memory.Total": 8000}

        # Create an entry
        entry = ResourceLogEntry(timestamp=timestamp, file_name="resource-system.log", folder_name="logs", data=data)

        # Clean the data before checking
        cleaned_data = clean_metadata_dict(data)

        # Verify entry fields
        assert entry.timestamp == timestamp
        assert entry.data["CPU.Utilization"] == 50.5
        assert entry.data["Memory.Used"] == 4000
        assert entry.data["Memory.Total"] == 8000
        assert entry.file_name == "resource-system.log"
        assert entry.folder_name == "logs"
