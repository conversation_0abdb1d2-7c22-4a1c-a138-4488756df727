"""
Tests for Phase 2: Hierarchical Query Cache functionality.

Tests the caching strategy that optimizes drill-down query patterns:
1. Full range queries → cached
2. Sub-range queries → sliced from cached results (O(log n))
3. Return to full range → instant cache hit
"""

from __future__ import annotations

from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pandas as pd
import pytest

from log_parser.analysis.base_analyzer import HierarchicalQueryCache


class TestHierarchicalQueryCache:
    """Test suite for hierarchical query caching functionality."""

    @pytest.fixture
    def sample_dataframe(self):
        """Create a sample DataFrame for testing with datetime index."""
        base_time = datetime(2024, 1, 1, 12, 0, 0)
        timestamps = [base_time + timedelta(minutes=i) for i in range(100)]

        df = pd.DataFrame(
            {
                "value": range(100),
                "folder_name": ["folder_1"] * 50 + ["folder_2"] * 50,
                "log_level": ["INFO"] * 80 + ["ERROR"] * 20,
            },
            index=pd.DatetimeIndex(timestamps),
        )

        return df

    @pytest.fixture
    def cache(self):
        """Create a HierarchicalQueryCache instance for testing."""
        return HierarchicalQueryCache(max_cached_results=3)

    @pytest.fixture
    def protected_cache(self):
        """Create a HierarchicalQueryCache with protected slots for Phase 2 testing."""
        return HierarchicalQueryCache(max_cached_results=6, protected_slots=2)

    def test_cache_initialization(self, cache):
        """Test that cache initializes correctly."""
        assert len(cache.cached_results) == 0
        assert len(cache.cache_metadata) == 0
        assert cache.max_cached_results == 3
        assert hasattr(cache, "logger")

    def test_cache_key_generation(self, cache):
        """Test cache key generation for various query parameters."""
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 13, 0, 0)

        # Test with no filters
        key1 = cache._generate_cache_key(start, end, {})
        assert isinstance(key1, str)
        assert len(key1) == 12  # MD5 hash truncated to 12 chars

        # Test with filters
        filters = {"folder_name": "folder_1", "log_level": "INFO"}
        key2 = cache._generate_cache_key(start, end, filters)
        assert key2 != key1  # Different filters should give different keys

        # Test consistency - same params should give same key
        key3 = cache._generate_cache_key(start, end, filters)
        assert key2 == key3

        # Test with None timestamps
        key4 = cache._generate_cache_key(None, None, {})
        assert isinstance(key4, str)

    def test_time_range_containment(self, cache):
        """Test time range containment logic."""
        base_time = datetime(2024, 1, 1, 12, 0, 0)

        # Full range (None, None) contains everything
        assert cache._time_range_contained(None, None, base_time, base_time + timedelta(hours=1))
        assert cache._time_range_contained(None, None, None, None)

        # Range [12:00, 14:00] contains [12:30, 13:30]
        cached_start = base_time
        cached_end = base_time + timedelta(hours=2)
        target_start = base_time + timedelta(minutes=30)
        target_end = base_time + timedelta(hours=1, minutes=30)
        assert cache._time_range_contained(cached_start, cached_end, target_start, target_end)

        # Range [12:00, 14:00] does NOT contain [11:00, 13:00]
        target_start_early = base_time - timedelta(hours=1)
        assert not cache._time_range_contained(cached_start, cached_end, target_start_early, target_end)

        # Range [12:00, 14:00] does NOT contain [13:00, 15:00]
        target_end_late = base_time + timedelta(hours=3)
        assert not cache._time_range_contained(cached_start, cached_end, target_start, target_end_late)

    def test_filters_compatibility(self, cache):
        """Test filter compatibility checking."""
        # Exact match should be compatible
        filters1 = {"folder_name": "folder_1", "log_level": "INFO"}
        filters2 = {"folder_name": "folder_1", "log_level": "INFO"}
        assert cache._filters_compatible(filters1, filters2)

        # Different filters should not be compatible
        filters3 = {"folder_name": "folder_2", "log_level": "INFO"}
        assert not cache._filters_compatible(filters1, filters3)

        # Empty filters
        assert cache._filters_compatible({}, {})
        assert not cache._filters_compatible({}, filters1)

    def test_full_query_execution(self, cache, sample_dataframe):
        """Test full query execution without caching."""
        start = datetime(2024, 1, 1, 12, 30, 0)
        end = datetime(2024, 1, 1, 12, 45, 0)
        filters = {"folder_name": "folder_1"}

        result = cache._execute_full_query(start, end, filters, sample_dataframe)

        # Should return filtered subset
        assert len(result) < len(sample_dataframe)
        assert all(result["folder_name"] == "folder_1")
        assert result.index.min() >= pd.Timestamp(start)
        assert result.index.max() <= pd.Timestamp(end)

    def test_dataframe_slicing(self, cache, sample_dataframe):
        """Test efficient DataFrame slicing from cached results."""
        start = datetime(2024, 1, 1, 12, 10, 0)
        end = datetime(2024, 1, 1, 12, 20, 0)
        filters = {"folder_name": "folder_1"}

        result = cache._slice_cached_dataframe(sample_dataframe, start, end, filters)

        # Should return filtered and time-sliced subset
        assert len(result) <= len(sample_dataframe)
        assert all(result["folder_name"] == "folder_1")
        assert result.index.min() >= pd.Timestamp(start)
        assert result.index.max() <= pd.Timestamp(end)

    def test_cache_miss_and_store(self, cache, sample_dataframe):
        """Test cache miss scenario - query and store result."""
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 13, 0, 0)
        filters = {"folder_name": "folder_1"}

        # First query should be a cache miss
        result = cache.query(start, end, filters, sample_dataframe)

        # Verify result is correct
        assert len(result) > 0
        assert all(result["folder_name"] == "folder_1")

        # Verify cache was populated
        assert len(cache.cached_results) == 1
        assert len(cache.cache_metadata) == 1

        # Verify cache metadata
        cache_key = list(cache.cache_metadata.keys())[0]
        metadata = cache.cache_metadata[cache_key]
        assert metadata["start"] == start
        assert metadata["end"] == end
        assert metadata["filters"] == filters

    def test_cache_hit_exact_match(self, cache, sample_dataframe):
        """Test cache hit scenario - exact query match."""
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 13, 0, 0)
        filters = {"folder_name": "folder_1"}

        # First query - cache miss
        result1 = cache.query(start, end, filters, sample_dataframe)

        # Second identical query - should be cache hit
        with patch.object(cache, "_execute_full_query") as mock_full_query:
            result2 = cache.query(start, end, filters, sample_dataframe)

            # Full query should not be called
            mock_full_query.assert_not_called()

            # Results should be identical
            pd.testing.assert_frame_equal(result1, result2)

    def test_cache_hierarchical_slicing(self, cache, sample_dataframe):
        """Test hierarchical caching - sub-range query from cached full range."""
        # Full range query
        full_start = datetime(2024, 1, 1, 12, 0, 0)
        full_end = datetime(2024, 1, 1, 13, 0, 0)
        filters = {"folder_name": "folder_1"}

        # Cache full range
        full_result = cache.query(full_start, full_end, filters, sample_dataframe)

        # Sub-range query - should slice from cached result
        sub_start = datetime(2024, 1, 1, 12, 15, 0)
        sub_end = datetime(2024, 1, 1, 12, 45, 0)

        with patch.object(cache, "_execute_full_query") as mock_full_query:
            sub_result = cache.query(sub_start, sub_end, filters, sample_dataframe)

            # Full query should not be called (sliced from cache)
            mock_full_query.assert_not_called()

            # Sub-result should be subset of full result
            assert len(sub_result) <= len(full_result)
            assert sub_result.index.min() >= pd.Timestamp(sub_start)
            assert sub_result.index.max() <= pd.Timestamp(sub_end)

    def test_cache_eviction(self, cache, sample_dataframe):
        """Test cache eviction when cache is full."""
        # Fill cache to capacity
        base_time = datetime(2024, 1, 1, 12, 0, 0)

        for i in range(4):  # One more than max_cached_results
            start = base_time + timedelta(hours=i)
            end = start + timedelta(minutes=30)
            filters = {"folder_name": "folder_1"}

            cache.query(start, end, filters, sample_dataframe)

        # Cache should not exceed max size
        assert len(cache.cached_results) <= cache.max_cached_results
        assert len(cache.cache_metadata) <= cache.max_cached_results

    def test_cache_stats(self, cache, sample_dataframe):
        """Test cache statistics reporting."""
        # Initially empty
        stats = cache.get_cache_stats()
        assert stats["cached_queries"] == 0
        assert stats["total_size_mb"] == 0.0
        assert stats["cache_keys"] == []

        # After caching some queries
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 13, 0, 0)
        filters = {"folder_name": "folder_1"}

        cache.query(start, end, filters, sample_dataframe)

        stats = cache.get_cache_stats()
        assert stats["cached_queries"] == 1
        assert stats["total_size_mb"] > 0.0
        assert len(stats["cache_keys"]) == 1

    def test_empty_dataframe_handling(self, cache):
        """Test handling of empty DataFrames."""
        empty_df = pd.DataFrame()
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 13, 0, 0)
        filters = {}

        result = cache.query(start, end, filters, empty_df)
        assert result.empty
        assert len(cache.cached_results) == 0  # Should not cache empty results

    def test_filter_edge_cases(self, cache, sample_dataframe):
        """Test edge cases with filters."""
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 13, 0, 0)

        # List filter
        filters = {"folder_name": ["folder_1", "folder_2"]}
        result = cache.query(start, end, filters, sample_dataframe)
        assert len(result) > 0

        # Non-existent column filter
        filters = {"non_existent_column": "value"}
        result = cache.query(start, end, filters, sample_dataframe)
        # Should handle gracefully without error
        assert isinstance(result, pd.DataFrame)


class TestBaseAnalyzerIntegration:
    """Test hierarchical caching integration with BaseAnalyzer."""

    @pytest.fixture
    def mock_analyzer(self):
        """Create a mock BaseAnalyzer for testing."""
        from log_parser.analysis.base_analyzer import BaseAnalyzer

        # Create a concrete implementation for testing
        class TestAnalyzer(BaseAnalyzer):
            @property
            def analyzer_type(self) -> str:
                return "test"

            def _create_dataframe(self, log_entries):
                base_time = datetime(2024, 1, 1, 12, 0, 0)
                timestamps = [base_time + timedelta(minutes=i) for i in range(len(log_entries))]

                return pd.DataFrame(
                    {"value": range(len(log_entries)), "folder_name": "folder_1"},
                    index=pd.DatetimeIndex(timestamps),
                )

        # Create with sample log entries
        log_entries = [Mock() for _ in range(10)]
        return TestAnalyzer(log_entries)

    def test_analyzer_cache_initialization(self, mock_analyzer):
        """Test that BaseAnalyzer initializes with hierarchical cache."""
        assert hasattr(mock_analyzer, "_query_cache")
        assert isinstance(mock_analyzer._query_cache, HierarchicalQueryCache)

    def test_analyzer_query_uses_cache(self, mock_analyzer):
        """Test that BaseAnalyzer.query() uses hierarchical cache."""
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 12, 5, 0)

        # Mock the cache query method
        with patch.object(mock_analyzer._query_cache, "query") as mock_cache_query:
            mock_cache_query.return_value = pd.DataFrame()

            mock_analyzer.query(start, end)

            # Verify cache.query was called
            mock_cache_query.assert_called_once()

    def test_analyzer_cache_stats_method(self, mock_analyzer):
        """Test that BaseAnalyzer exposes cache statistics."""
        stats = mock_analyzer.get_cache_stats()

        assert isinstance(stats, dict)
        assert "cached_queries" in stats
        assert "total_size_mb" in stats
        assert "cache_keys" in stats

    def test_analyzer_data_update_clears_cache(self, mock_analyzer):
        """Test that updating analyzer data clears the cache."""
        # Populate cache
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 1, 12, 5, 0)
        mock_analyzer.query(start, end)

        initial_cache_size = len(mock_analyzer._query_cache.cached_results)

        # Update data
        new_log_entries = [Mock() for _ in range(5)]
        mock_analyzer.update_data(new_log_entries)

        # Cache should be cleared
        assert len(mock_analyzer._query_cache.cached_results) == 0
        assert len(mock_analyzer._query_cache.cache_metadata) == 0


class TestProtectedCacheSlots:
    """Test suite for Phase 2: Protected Cache Slots functionality."""

    @pytest.fixture
    def sample_dataframe(self):
        """Create a sample DataFrame for testing with datetime index."""
        base_time = datetime(2024, 1, 1, 12, 0, 0)
        timestamps = [base_time + timedelta(minutes=i) for i in range(1000)]  # Larger dataset

        df = pd.DataFrame(
            {
                "value": range(1000),
                "folder_name": ["folder_1"] * 500 + ["folder_2"] * 500,
                "log_level": ["INFO"] * 800 + ["ERROR"] * 200,
            },
            index=pd.DatetimeIndex(timestamps),
        )
        return df

    @pytest.fixture
    def protected_cache(self):
        """Create a cache with protected slots."""
        return HierarchicalQueryCache(max_cached_results=6, protected_slots=2)

    def test_protected_cache_initialization(self, protected_cache):
        """Test protected cache initializes with correct slot allocation."""
        assert protected_cache.max_cached_results == 6
        assert protected_cache.protected_slots == 2
        assert protected_cache.general_slots == 4
        assert len(protected_cache.protected_entries) == 0
        assert len(protected_cache.general_entries) == 0

    def test_query_classification_full_dataset(self, protected_cache, sample_dataframe):
        """Test that full dataset queries are classified as protected."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            # Full dataset query (None, None) should be protected
            result = protected_cache.query(None, None, {}, sample_dataframe)

            assert len(protected_cache.protected_entries) == 1
            assert len(protected_cache.general_entries) == 0

            # Check cache metadata indicates protection
            cache_key = list(protected_cache.cache_metadata.keys())[0]
            assert protected_cache.cache_metadata[cache_key]["is_protected"] is True
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold

    def test_query_classification_large_timespan(self, protected_cache, sample_dataframe):
        """Test that queries >7 days are classified as protected."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            start = datetime(2024, 1, 1, 12, 0, 0)
            end = datetime(2024, 1, 10, 12, 0, 0)  # 9 days

            result = protected_cache.query(start, end, {}, sample_dataframe)

            assert len(protected_cache.protected_entries) == 1
            assert len(protected_cache.general_entries) == 0
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold

    def test_query_classification_high_coverage(self, protected_cache, sample_dataframe):
        """Test that queries covering >50% of dataset are protected."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            # Query covering ~60% of the dataset should be protected
            dataset_start = sample_dataframe.index.min()
            dataset_end = sample_dataframe.index.max()
            span = dataset_end - dataset_start

            start = dataset_start + span * 0.1  # Start 10% in
            end = dataset_end - span * 0.1  # End 10% before end (80% coverage)

            result = protected_cache.query(start, end, {}, sample_dataframe)

            assert len(protected_cache.protected_entries) == 1
            assert len(protected_cache.general_entries) == 0
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold

    def test_query_classification_small_range(self, protected_cache, sample_dataframe):
        """Test that small ranges are classified as general."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            start = datetime(2024, 1, 1, 12, 0, 0)
            end = datetime(2024, 1, 1, 12, 30, 0)  # 30 minutes

            result = protected_cache.query(start, end, {}, sample_dataframe)

            assert len(protected_cache.protected_entries) == 0
            assert len(protected_cache.general_entries) == 1

            # Check cache metadata indicates general
            cache_key = list(protected_cache.cache_metadata.keys())[0]
            assert protected_cache.cache_metadata[cache_key]["is_protected"] is False
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold

    def test_protected_slot_eviction_priority(self, protected_cache, sample_dataframe):
        """Test that general slots are evicted before protected slots."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            # Fill general slots first
            for i in range(4):  # Fill all 4 general slots
                start = datetime(2024, 1, 1, 12, i * 10, 0)
                end = datetime(2024, 1, 1, 12, i * 10 + 5, 0)
                protected_cache.query(start, end, {}, sample_dataframe)

            assert len(protected_cache.general_entries) == 4
            assert len(protected_cache.protected_entries) == 0

            # Add one protected entry
            protected_cache.query(None, None, {}, sample_dataframe)  # Full dataset
            assert len(protected_cache.protected_entries) == 1
            assert len(protected_cache.general_entries) == 4

            # Add another general entry - should evict oldest general
            start = datetime(2024, 1, 1, 13, 0, 0)
            end = datetime(2024, 1, 1, 13, 5, 0)
            protected_cache.query(start, end, {}, sample_dataframe)

            # Should still have 4 general and 1 protected
            assert len(protected_cache.general_entries) == 4
            assert len(protected_cache.protected_entries) == 1
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold

    def test_protected_slot_overflow_eviction(self, protected_cache, sample_dataframe):
        """Test eviction when protected slots are full."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            # Fill both protected slots
            protected_cache.query(None, None, {}, sample_dataframe)  # Full dataset

            # Add another protected query (>7 days)
            start = datetime(2024, 1, 1, 12, 0, 0)
            end = datetime(2024, 1, 10, 12, 0, 0)
            protected_cache.query(start, end, {}, sample_dataframe)

            assert len(protected_cache.protected_entries) == 2

            # Add third protected query - should evict least valuable protected
            start2 = datetime(2024, 1, 1, 12, 0, 0)
            end2 = datetime(2024, 1, 15, 12, 0, 0)  # 14 days
            protected_cache.query(start2, end2, {}, sample_dataframe)

            assert len(protected_cache.protected_entries) == 2  # Should still be 2
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold

    def test_cache_stats_include_protection_info(self, protected_cache, sample_dataframe):
        """Test that cache stats include protected slot information."""
        # Disable predictive caching for predictable test behavior
        original_threshold = protected_cache.prediction_confidence_threshold
        protected_cache.prediction_confidence_threshold = 1.0  # Disable predictions

        try:
            # Add mixed entries
            protected_cache.query(None, None, {}, sample_dataframe)  # Protected

            start = datetime(2024, 1, 1, 12, 0, 0)
            end = datetime(2024, 1, 1, 12, 30, 0)
            protected_cache.query(start, end, {}, sample_dataframe)  # General

            stats = protected_cache.get_cache_stats()

            assert "protected_entries" in stats
            assert "general_entries" in stats
            assert "protected_slots_used" in stats
            assert "general_slots_used" in stats
            assert "protected_size_mb" in stats
            assert "general_size_mb" in stats

            assert stats["protected_entries"] == 1
            assert stats["general_entries"] == 1
            assert stats["protected_slots_used"] == "1/2"
            assert stats["general_slots_used"] == "1/4"
        finally:
            # Restore original threshold
            protected_cache.prediction_confidence_threshold = original_threshold


class TestPredictiveCaching:
    """Test suite for Phase 3: Smart Cache Key Strategy with Predictive Caching."""

    @pytest.fixture
    def sample_dataframe(self):
        """Create a sample DataFrame with longer time range for prediction testing."""
        base_time = datetime(2024, 1, 1, 0, 0, 0)
        timestamps = [base_time + timedelta(hours=i) for i in range(24 * 90)]  # 90 days

        df = pd.DataFrame(
            {
                "value": range(len(timestamps)),
                "folder_name": ["folder_1"] * (len(timestamps) // 2) + ["folder_2"] * (len(timestamps) // 2),
            },
            index=pd.DatetimeIndex(timestamps),
        )
        return df

    @pytest.fixture
    def predictive_cache(self):
        """Create a cache for predictive testing."""
        return HierarchicalQueryCache(max_cached_results=12, protected_slots=3)

    def test_pattern_tracking_initialization(self, predictive_cache):
        """Test that predictive caching components initialize correctly."""
        assert hasattr(predictive_cache, "usage_patterns")
        assert hasattr(predictive_cache, "common_ranges")
        assert hasattr(predictive_cache, "prediction_confidence_threshold")
        assert predictive_cache.prediction_confidence_threshold == 0.7
        assert len(predictive_cache.usage_patterns) == 0
        assert len(predictive_cache.common_ranges) == 0

    def test_pattern_recording(self, predictive_cache, sample_dataframe):
        """Test that user patterns are recorded correctly."""
        start = datetime(2024, 1, 1, 12, 0, 0)
        end = datetime(2024, 1, 2, 12, 0, 0)

        # Execute query to trigger pattern recording
        result = predictive_cache.query(start, end, {}, sample_dataframe)

        assert len(predictive_cache.usage_patterns) == 1
        assert len(predictive_cache.common_ranges) == 1

        pattern = predictive_cache.usage_patterns[0]
        assert pattern["start"] == start
        assert pattern["end"] == end
        assert pattern["span_days"] == 1.0

    def test_range_key_generation(self, predictive_cache):
        """Test range key generation for pattern tracking."""
        # Full dataset
        key1 = predictive_cache._generate_range_key(None, None)
        assert key1 == "full_dataset"

        # Bounded range
        start = datetime(2024, 1, 1)
        end = datetime(2024, 1, 2)
        key2 = predictive_cache._generate_range_key(start, end)
        assert key2 == "2024-01-01_to_2024-01-02"

        # Unbounded start
        key3 = predictive_cache._generate_range_key(None, end)
        assert key3 == "to_2024-01-02"

        # Unbounded end
        key4 = predictive_cache._generate_range_key(start, None)
        assert key4 == "from_2024-01-01"

    def test_range_key_parsing(self, predictive_cache):
        """Test parsing range keys back to datetime tuples."""
        # Full dataset
        result1 = predictive_cache._parse_range_key("full_dataset")
        assert result1 == (None, None)

        # Bounded range
        result2 = predictive_cache._parse_range_key("2024-01-01_to_2024-01-02")
        assert result2 == (datetime(2024, 1, 1), datetime(2024, 1, 2))

        # Unbounded start
        result3 = predictive_cache._parse_range_key("to_2024-01-02")
        assert result3 == (None, datetime(2024, 1, 2))

        # Unbounded end
        result4 = predictive_cache._parse_range_key("from_2024-01-01")
        assert result4 == (datetime(2024, 1, 1), None)

    def test_full_dataset_predictions(self, predictive_cache, sample_dataframe):
        """Test predictions from full dataset query."""
        # Query full dataset to trigger pattern analysis
        result = predictive_cache.query(None, None, {}, sample_dataframe)

        # Get predictions
        predictions = predictive_cache.predict_next_ranges((None, None), sample_dataframe)

        assert len(predictions) > 0

        # Should predict common drill-down ranges (last month, week, etc.)
        dataset_end = sample_dataframe.index.max()
        found_monthly = False
        found_weekly = False

        for pred_start, pred_end in predictions:
            if pred_end == dataset_end and pred_start is not None:
                span = dataset_end - pred_start
                if 25 <= span.days <= 35:  # ~1 month
                    found_monthly = True
                elif 5 <= span.days <= 10:  # ~1 week
                    found_weekly = True

        assert found_monthly or found_weekly

    def test_subrange_parent_predictions(self, predictive_cache, sample_dataframe):
        """Test predictions for parent ranges (zoom out)."""
        # Query a sub-range
        start = datetime(2024, 1, 15, 0, 0, 0)
        end = datetime(2024, 1, 16, 0, 0, 0)

        result = predictive_cache.query(start, end, {}, sample_dataframe)

        # Get predictions
        predictions = predictive_cache.predict_next_ranges((start, end), sample_dataframe)

        assert len(predictions) > 0

        # Should predict full dataset and wider ranges
        found_full_dataset = False
        found_wider_range = False

        for pred_start, pred_end in predictions:
            if pred_start is None and pred_end is None:
                found_full_dataset = True
            elif pred_start is not None and pred_end is not None:
                if pred_start < start and pred_end > end:
                    found_wider_range = True

        assert found_full_dataset

    def test_sibling_range_predictions(self, predictive_cache, sample_dataframe):
        """Test predictions for sibling ranges (pan left/right)."""
        # Query a sub-range in the middle of dataset
        start = datetime(2024, 2, 1, 0, 0, 0)
        end = datetime(2024, 2, 2, 0, 0, 0)
        span = end - start

        result = predictive_cache.query(start, end, {}, sample_dataframe)

        # Get predictions
        predictions = predictive_cache.predict_next_ranges((start, end), sample_dataframe)

        assert len(predictions) > 0

        # Should predict adjacent periods
        found_left_pan = False
        found_right_pan = False

        for pred_start, pred_end in predictions:
            if pred_start is not None and pred_end is not None:
                pred_span = pred_end - pred_start
                if pred_span == span:  # Same span
                    if pred_end == start:  # Left adjacent
                        found_left_pan = True
                    elif pred_start == end:  # Right adjacent
                        found_right_pan = True

        # Should find at least one pan direction
        assert found_left_pan or found_right_pan

    def test_historical_pattern_predictions(self, predictive_cache, sample_dataframe):
        """Test predictions based on historical usage patterns."""
        # Create a frequently used range by querying it multiple times
        start = datetime(2024, 1, 10, 0, 0, 0)
        end = datetime(2024, 1, 11, 0, 0, 0)

        # Query the same range multiple times to build history
        for _ in range(3):
            predictive_cache.query(start, end, {}, sample_dataframe)

        # Query a different range to trigger predictions
        start2 = datetime(2024, 1, 5, 0, 0, 0)
        end2 = datetime(2024, 1, 6, 0, 0, 0)
        result = predictive_cache.query(start2, end2, {}, sample_dataframe)

        # Get predictions from historical patterns
        historical_predictions = predictive_cache._predict_from_history((start2, end2))

        # Should include the frequently used range
        found_frequent_range = False
        for pred_range, confidence in historical_predictions:
            if pred_range == (start, end):
                found_frequent_range = True
                assert confidence > predictive_cache.prediction_confidence_threshold

        assert found_frequent_range

    def test_background_precaching_trigger(self, predictive_cache, sample_dataframe):
        """Test that background pre-caching is triggered correctly."""
        # Mock the background caching to verify it's called
        with patch.object(predictive_cache, "_background_cache_range") as mock_bg_cache:
            # Query to trigger pattern analysis and prediction
            result = predictive_cache.query(None, None, {}, sample_dataframe)

            # Should have triggered background caching calls
            assert mock_bg_cache.call_count >= 0  # May be 0 if no room in general slots

    def test_predictive_cache_stats(self, predictive_cache, sample_dataframe):
        """Test that cache stats include predictive caching information."""
        # Clear any existing patterns to ensure clean test
        predictive_cache.usage_patterns.clear()
        predictive_cache.common_ranges.clear()

        # Generate some patterns - use distinctly different queries
        start1 = datetime(2024, 1, 1, 12, 0, 0)
        end1 = datetime(2024, 1, 2, 12, 0, 0)
        predictive_cache.query(start1, end1, {}, sample_dataframe)

        # Use a completely different time range that won't be predicted from the first
        start2 = datetime(2024, 3, 1, 12, 0, 0)
        end2 = datetime(2024, 3, 2, 12, 0, 0)
        predictive_cache.query(start2, end2, {}, sample_dataframe)

        stats = predictive_cache.get_cache_stats()

        # Should include predictive caching stats
        assert "usage_patterns_tracked" in stats
        assert "common_ranges_learned" in stats
        assert "prediction_threshold" in stats
        assert "most_common_ranges" in stats

        # Should have tracked at least the patterns we queried
        assert stats["usage_patterns_tracked"] >= 1
        assert stats["common_ranges_learned"] >= 1
        assert stats["prediction_threshold"] == 0.7
        assert isinstance(stats["most_common_ranges"], dict)

    def test_pattern_history_limit(self, predictive_cache, sample_dataframe):
        """Test that pattern history is limited to prevent memory growth."""
        # Clear existing patterns and disable caching to force pattern tracking
        predictive_cache.usage_patterns.clear()
        predictive_cache.common_ranges.clear()

        # Directly call analyze_user_pattern to test the limit without cache interference
        for i in range(60):
            start = datetime(2024, 1, 1, 0, 0, 0) + timedelta(days=i)
            end = start + timedelta(hours=1)
            predictive_cache.analyze_user_pattern((start, end), {})

        # Should be limited to 50 patterns
        assert len(predictive_cache.usage_patterns) == 50
