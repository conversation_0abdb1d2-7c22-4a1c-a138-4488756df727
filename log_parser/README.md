# Log Parser Library

A comprehensive Python library for parsing, analyzing, and extracting insights from various types of log files. This library is designed to handle structured logs, resource system logs, and provides advanced analysis capabilities including anomaly detection and cross-log correlation.

## 🚀 Features

### Core Parsing Capabilities
- **Multi-format Support**: Parse structured logs and resource system logs
- **Auto-detection**: Automatically detect log format and choose appropriate parser
- **Factory Pattern**: Extensible parser architecture for adding new log formats
- **Quarantine System**: Handle unparseable entries gracefully with detailed error tracking
- **Batch Processing**: Parse entire directories with recursive file discovery

### Advanced Analysis
- **Time-series Analysis**: Analyze log patterns over time with configurable time windows
- **Anomaly Detection**: Identify unusual patterns in log data using statistical methods
- **Action Tracking**: Track multi-step processes and workflows across log entries
- **Cross-log Analysis**: Correlate events across multiple log sources
- **Resource Monitoring**: Analyze system resource usage patterns

### Data Models
- **Pydantic Integration**: Type-safe data models with validation
- **Structured Entries**: Rich metadata extraction from log entries
- **Quarantine Tracking**: Detailed tracking of unparseable content
- **Flexible Schema**: Support for custom log formats and metadata

## 📦 Installation

```bash
pip install log_parser
```

### Dependencies
- Python 3.8+
- pydantic >= 2.0.0
- python-dateutil >= 2.8.0
- numpy >= 1.20.0
- pandas >= 2.1.4
- scikit-learn >= 1.0.0
- statsmodels >= 0.13.0
- adtk >= 0.6.2

## 🔧 Quick Start

### Basic Usage

```python
from log_parser.parsers import ParserFactory
from log_parser.analysis import AnalyzerFactory

# Auto-detect and parse a single file
parser = ParserFactory.auto_detect_parser("path/to/logfile.log")
if parser:
    log_entries, quarantine_entries = parser.parse_file("path/to/logfile.log")
    print(f"Parsed {len(log_entries)} entries, {len(quarantine_entries)} quarantined")

# Parse an entire directory
log_entries, quarantine_entries = parser.parse_directory("path/to/logs/")

# Create analyzer for structured logs
analyzer = AnalyzerFactory.create_analyzer("structured", log_entries)

# Get time range of logs
start_time, end_time = analyzer.get_time_range()
print(f"Logs span from {start_time} to {end_time}")

# Count entries by time period
hourly_counts = analyzer.count_by_time(freq='1h')
print(hourly_counts.head())
```

### Parser Types

#### Structured Log Parser
Handles structured log entries with defined fields like log level, event type, library, etc.

```python
from log_parser.parsers import StructuredLogParser

parser = StructuredLogParser()
entries, quarantine = parser.parse_file("structured.log")

# Access structured fields
for entry in entries[:5]:
    print(f"{entry.timestamp}: {entry.log_level} - {entry.log_event}")
    print(f"Library: {entry.library}, Process: {entry.process_name}")
```

#### Resource Parser
Parses resource system logs containing performance metrics and system data.

```python
from log_parser.parsers import ResourceParser

parser = ResourceParser()
entries, quarantine = parser.parse_file("resources.log")

# Access resource data
for entry in entries[:5]:
    print(f"{entry.timestamp}: {entry.data}")
```

## 📊 Analysis Capabilities

### Time-series Analysis

```python
from log_parser.analysis import StructuredLogAnalyzer
from datetime import datetime, timedelta

analyzer = StructuredLogAnalyzer(log_entries)

# Query specific time range
end_time = datetime.now()
start_time = end_time - timedelta(hours=24)
recent_logs = analyzer.query(start=start_time, end=end_time)

# Get frequency distribution
freq_dist = analyzer.get_frequency_distribution(freq='1h')
print(freq_dist)

# Filter by log level
error_logs = analyzer.query(log_level='ERROR')
print(f"Found {len(error_logs)} error entries")
```

### Anomaly Detection

```python
from log_parser.analysis import AnomalyDetector

# Initialize with log entries
detector = AnomalyDetector(log_entries)

# Detect anomalies in log frequency
anomalies = detector.detect_frequency_anomalies(freq='1h', sensitivity=0.95)
print(f"Detected {len(anomalies)} anomalous time periods")

# Detect anomalies in specific metrics
if hasattr(detector, 'detect_metric_anomalies'):
    metric_anomalies = detector.detect_metric_anomalies('response_time')
```

### Action Analysis

```python
from log_parser.analysis import TimelineAnalyzer

# Create analyzer instance
analyzer = TimelineAnalyzer(log_entries)

# Load action definitions
analyzer.load_action_config("config/default_actions.toml")

# Find completed actions
completed_actions = analyzer.get_completed_actions()
for action in completed_actions:
    print(f"Action: {action['name']}, Duration: {action['duration']}")

# Find incomplete actions
incomplete_actions = analyzer.get_incomplete_actions()
print(f"Found {len(incomplete_actions)} incomplete actions")
```

### Cross-log Analysis

```python
from log_parser.analysis import CrossAnalyzer

# Analyze relationships between different log sources
structured_analyzer = StructuredLogAnalyzer(structured_entries)
resource_analyzer = ResourceAnalyzer(resource_entries)

cross_analyzer = CrossAnalyzer()
cross_analyzer.add_analyzer("structured", structured_analyzer)
cross_analyzer.add_analyzer("resource", resource_analyzer)

# Find correlations between different log types
correlations = cross_analyzer.find_correlations()
print("Cross-log correlations:", correlations)
```

## 🏗️ Architecture

### Parser Architecture
- **BaseParser**: Abstract base class defining the parser interface
- **ParserFactory**: Factory for creating and registering parsers
- **Auto-detection**: Intelligent format detection based on file content

### Analyzer Architecture
- **BaseAnalyzer**: Abstract base class for all analyzers
- **AnalyzerFactory**: Factory for creating analyzers
- **Specialized Analyzers**: Purpose-built analyzers for different log types

### Data Models
- **LogEntry**: Base class for all log entries
- **StructuredLogEntry**: Rich metadata for structured logs
- **ResourceLogEntry**: System resource data
- **QuarantineEntry**: Tracks unparseable content with error details

## 🧪 Testing

The library includes comprehensive tests covering:
- Parser functionality and edge cases
- Analyzer accuracy and performance
- Anomaly detection algorithms
- Cross-log correlation
- Error handling and quarantine system

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=log_parser --cov-report=term-missing
```

## 🔧 Configuration

### Custom Actions
Define custom multi-step processes in TOML format:

```toml
[[action]]
name = "user_login_flow"
start_event = "login_attempt"
end_event = "login_success"
identifier_key = "session_id"
children = ["authentication", "authorization"]

[[action]]
name = "authentication"
start_event = "auth_started"
end_event = "auth_completed"
identifier_key = "auth_token"
children = []
```

### Parser Configuration
Extend the library with custom parsers:

```python
from log_parser.parsers import BaseParser, ParserFactory

class CustomLogParser(BaseParser):
    @property
    def log_type(self) -> str:
        return "custom"

    def can_parse(self, file_path: str) -> bool:
        # Implementation for format detection
        pass

    def parse_file(self, file_path: str, **metadata):
        # Implementation for parsing
        pass

# Register custom parser
ParserFactory.register_parser(CustomLogParser)
```

## 📈 Performance

- **Efficient Processing**: Pandas-based analysis for high performance
- **Memory Management**: Streaming processing for large log files
- **Parallel Processing**: Multi-threaded parsing for directory processing
- **Caching**: Intelligent caching of analysis results

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and ensure:
- Tests pass for all changes
- New features include appropriate tests
- Documentation is updated for new functionality

## 📝 License

This project is part of the Log Visualiser suite and follows the same licensing terms.

## 🔗 Related Projects

- **Backend API**: REST API for log analysis services
- **Console Interface**: Command-line tools for log processing
- **Tern**: Terminal-based log visualization
- **Log Visualiser**: Complete log analysis and visualization platform
