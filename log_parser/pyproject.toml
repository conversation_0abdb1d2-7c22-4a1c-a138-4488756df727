[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "log_parser"
version = "0.1.0"
authors = [
  { name = "Log Visualiser Team" },
]
description = "A comprehensive Python library for parsing, analyzing, and extracting insights from various types of log files"
readme = "README.md"
license = { text = "MIT" }
keywords = ["logging", "log-analysis", "parsing", "anomaly-detection", "time-series"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "Topic :: System :: Logging",
    "Topic :: System :: Monitoring",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.10"
dependencies = [
    "pydantic>=2.0.0",
    "python-dateutil>=2.8.0",
    "numpy>=1.20.0",
    "pandas>=2.1.4",
    "scikit-learn>=1.0.0",
    "statsmodels>=0.13.0",
    "adtk>=0.6.2"
]  # Added required dependencies

[project.optional-dependencies]
test = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
]
dev = [
    "black>=23.11.0",
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "pre-commit>=3.5.0",
    "bandit[toml]>=1.7.5",
    "pydocstyle>=6.3.0",
    "isort>=5.12.0",
    "pylint>=3.0.0",
    "safety>=2.3.0",
]

[project.urls]
"Homepage" = "https://github.com/yourusername/log_visualiser"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=log_parser --cov-report=term-missing"

[tool.ruff]
target-version = "py310"
line-length = 140
extend-exclude = [
    ".git",
    ".venv",
    "venv",
    "dist",
    "build",
    "*.egg-info",
    "tests",
    "*/tests",
    "**/tests/**",
    "archive",
    "*/archive",
    "**/archive/**",
]

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "C",    # mccabe
    "I",    # isort
    "N",    # pep8-naming
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "YTT",  # flake8-2020
    "ANN",  # flake8-annotations
    "S",    # flake8-bandit
    "BLE",  # flake8-blind-except
    "FBT",  # flake8-boolean-trap
    "B",    # flake8-bugbear
    "A",    # flake8-builtins
    "COM",  # flake8-commas
    "C4",   # flake8-comprehensions
    "DTZ",  # flake8-datetimez
    "T10",  # flake8-debugger
    "EM",   # flake8-errmsg
    "EXE",  # flake8-executable
    "FA",   # flake8-future-annotations
    "ISC",  # flake8-implicit-str-concat
    "ICN",  # flake8-import-conventions
    "G",    # flake8-logging-format
    "INP",  # flake8-no-pep420
    "PIE",  # flake8-pie
    "T20",  # flake8-print
    "PYI",  # flake8-pyi
    "PT",   # flake8-pytest-style
    "Q",    # flake8-quotes
    "RSE",  # flake8-raise
    "RET",  # flake8-return
    "SLF",  # flake8-self
    "SIM",  # flake8-simplify
    "TID",  # flake8-tidy-imports
    "TCH",  # flake8-type-checking
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib
    "ERA",  # eradicate
    "PD",   # pandas-vet
    "PGH",  # pygrep-hooks
    "PL",   # pylint
    "TRY",  # tryceratops
    "FLY",  # flynt
    "NPY",  # numpy-specific
    "PERF", # perflint
    "RUF",  # ruff-specific
]
ignore = [
    "D100",    # Missing docstring in public module
    "D104",    # Missing docstring in public package
    "D107",    # Missing docstring in __init__
    "COM812",  # Trailing comma missing (conflicts with formatter)
    "ISC001",  # Implicitly concatenated string literals (conflicts with formatter)
    "FBT003",  # Boolean positional value in function call
    "PLR0913", # Too many arguments in function definition
    "D203",    # 1 blank line required before class docstring (conflicts with D211)
    "D213",    # Multi-line docstring summary should start at the second line (conflicts with D212)
    "TID252",  # Relative imports from parent modules are banned
]

[tool.ruff.lint.isort]
known-first-party = ["log_parser"]
required-imports = ["from __future__ import annotations"]

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true
ignore-fully-untyped = true

[tool.ruff.lint.flake8-bandit]
check-typed-exception = true

[tool.ruff.lint.per-file-ignores]
"tests/**/*" = ["S101", "PLR2004", "ANN", "D"]  # Allow assert, magic values, missing annotations, missing docstrings in tests
"examples/**/*" = ["D", "ANN", "T20", "EXE001", "PLR0915", "BLE001", "DTZ005"]  # Allow missing docstrings, annotations, prints, executables, long functions, blind exceptions, naive datetime in examples
"**/conftest.py" = ["D", "ANN"]  # Allow missing docstrings and annotations in conftest

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "adtk.*",
    "sklearn.*",
    "statsmodels.*",
    "numpy.*",
    "pandas.*",
]
ignore_missing_imports = true

[tool.black]
line-length = 140
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101"]  # Skip assert_used test

[tool.isort]
profile = "black"
line_length = 140
known_first_party = ["log_parser"]
