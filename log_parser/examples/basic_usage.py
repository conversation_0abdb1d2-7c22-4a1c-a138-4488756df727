#!/usr/bin/env python3
"""
Basic usage example for the log_parser library.

This script demonstrates how to:
1. Parse log files
2. Analyze log data
3. Detect anomalies
4. Generate reports
"""

from __future__ import annotations

import sys
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

# Add the log_parser to the path for local development
sys.path.insert(0, str(Path(__file__).parent.parent))

import logging

from log_parser.analysis import AnalyzerFactory, AnomalyDetector
from log_parser.parsers import ParserFactory

# Set up logging for the example
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def main():
    """Demonstrate basic log parsing and analysis."""

    logger.info("🔍 Log Parser Library - Basic Usage Example")
    logger.info("=" * 50)

    # Example 1: Auto-detect and parse a log file
    logger.info("\n1. Auto-detecting log format...")
    log_file = "sample_logs/structured.log"  # Replace with actual log file

    parser = ParserFactory.auto_detect_parser(log_file)
    if parser:
        logger.info("✅ Detected parser: %s", parser.log_type)

        # Parse the file
        log_entries, quarantine_entries = parser.parse_file(log_file)
        logger.info("📊 Parsed %d entries", len(log_entries))
        logger.info("⚠️  Quarantined %d entries", len(quarantine_entries))

        if log_entries:
            # Example 2: Analyze the parsed data
            logger.info("\n2. Analyzing log data...")
            analyzer = AnalyzerFactory.create_analyzer(parser.log_type, log_entries)

            # Get time range
            start_time, end_time = analyzer.get_time_range()
            if start_time and end_time:
                logger.info("📅 Time range: %s to %s", start_time, end_time)
                duration = end_time - start_time
                logger.info("⏱️  Duration: %s", duration)

            # Get frequency distribution
            logger.info("\n3. Getting frequency distribution...")
            freq_dist = analyzer.get_frequency_distribution(freq="1h")
            logger.info("📈 Hourly log frequency (top 5):")
            logger.info("\n%s", freq_dist.head())

            # Example 3: Anomaly detection
            logger.info("\n4. Detecting anomalies...")
            detector = AnomalyDetector(log_entries)

            try:
                anomalies = detector.detect_frequency_anomalies(freq="1h", sensitivity=0.95)
                logger.info("🚨 Detected %d anomalous time periods", len(anomalies))

                if anomalies:
                    logger.info("   Anomalous periods:")
                    for anomaly in anomalies[:3]:  # Show first 3
                        logger.info("   - %s", anomaly)

            except (ValueError, TypeError, AttributeError, ImportError) as e:
                logger.warning("⚠️  Anomaly detection error: %s", e)

            # Example 4: Query specific data
            logger.info("\n5. Querying specific data...")

            # Query last 24 hours
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)

            recent_data = analyzer.query(start=start_time, end=end_time)
            logger.info("📋 Found %d entries in last 24 hours", len(recent_data))

            # Query by specific criteria (if structured logs)
            if hasattr(analyzer, "query") and parser.log_type == "structured":
                try:
                    error_logs = analyzer.query(log_level="ERROR")
                    logger.info("🔴 Found %d ERROR entries", len(error_logs))

                    warning_logs = analyzer.query(log_level="WARNING")
                    logger.info("🟡 Found %d WARNING entries", len(warning_logs))

                except (ValueError, TypeError, AttributeError, KeyError) as e:
                    logger.warning("⚠️  Query error: %s", e)

        else:
            logger.error("❌ No log entries found to analyze")

    else:
        logger.error("❌ Could not detect parser for %s", log_file)
        logger.info("   Available parsers: %s", ParserFactory.get_available_parsers())

    logger.info("\n✅ Example completed!")


if __name__ == "__main__":
    main()
