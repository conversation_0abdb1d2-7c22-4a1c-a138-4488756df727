---
description:
globs:
alwaysApply: false
---
# Dash Application Development – Cursor Rules

## Framework Usage

- **Dash Core Components (DCC):** Always use Dash Core Components for core UI elements. These are the built-in interactive components (e.g. graphs, dropdowns, sliders, inputs) that <PERSON> provides out-of-the-box ([Dash Core Components | Dash for Python Documentation | Plotly](https://dash.plotly.com/dash-core-components#:~:text=Dash%20ships%20with%20supercharged%20components,for%20interactive%20user%20interfaces)). Utilizing DCC ensures stability and consistency, as these components are maintained as part of Dash itself. Ensure the latest version of the package is referenced.

- **Dash Mantine Components (DMC):** Use Dash Mantine Components 1.2.0 for layout and styling needs. DMC is an extensive library (90+ components) based on the Mantine React toolkit, offering pre-designed, responsive UI elements ([dash-mantine-components · docs](https://www.dash-mantine-components.com)). Rely on DMC for containers, grids, buttons, typography, etc., to create a modern look and feel without writing custom CSS.

- **Prefer Prebuilt Over Custom:** When building the interface, prefer using existing DMC (or DCC) components before attempting to create a new custom component. In most cases, a suitable component or combination of components already exists. Only implement custom Dash components if absolutely necessary (e.g. no existing component meets a specific requirement). This approach saves development time and keeps the app consistent with the Dash ecosystem.

## Internal Business Logic

- **Use `log_parser` Package:** All business logic related to log analysis or parsing should reside in and be accessed through the `log_parser` package. Do not implement standalone parsing or analysis functions in the Dash app code if the functionality is already provided by `log_parser`. This ensures a single source of truth for parsing logic.

- **Leverage Analyser Classes:** The `log_parser` package contains `Analyser` classes (inheriting from `BaseAnalyser`) that encapsulate various analysis operations. Always utilize these existing analysers for any computations or data processing. For example, if there is an `XYZAnalyser` in `log_parser` that provides a certain analysis, use it instead of writing a new analysis function in the app.

- **Avoid Duplicating Logic:** Do not create new implementations of classes or methods that already exist in `log_parser`. If additional functionality is needed, extend the relevant class or add a method to the `log_parser` package (following its contribution guidelines) rather than duplicating code. Reusing and extending `log_parser` ensures consistency in how logs are parsed and analyzed across the project.

## Coding Standards

- **PEP8 Compliance:** Follow PEP8 style guidelines for all Python code. This includes using clear naming conventions, proper indentation (4 spaces), appropriate line length, and other formatting rules. Adhering to PEP8 makes the code more readable and consistent across the team, as “readability counts” ([PEP 8 – Style Guide for Python Code | peps.python.org](https://peps.python.org/pep-0008/#:~:text=One%20of%20Guido%E2%80%99s%20key%20insights,PEP%2020%20says%2C%20%E2%80%9CReadability%20counts%E2%80%9D)). Automated linters (like `flake8` or `pylint`) can help enforce this.

- **Clean & Readable Code:** Write code with clarity in mind. Use descriptive variable and function names, break up complex logic into smaller functions, and avoid overly complicated or deeply nested constructs. Keep functions and modules focused on a single purpose where possible. This improves maintainability and makes it easier for others to understand the codebase.

- **Documentation and Comments:** Ensure the code is well-documented. Write docstrings for all public functions, classes, and modules explaining their purpose and usage. Include inline comments for any non-obvious logic. Well-documented code helps new contributors quickly grasp the functionality and intent of the code, and it serves as live documentation for the team.

## Responsiveness

- **Responsive Design:** Design the application’s layout to be responsive across different devices and screen sizes. A responsive Dash app should render well on small mobile screens, tablets, and large desktop monitors alike, adjusting content layout accordingly ([Responsive design - Learn web development | MDN](https://developer.mozilla.org/en-US/docs/Learn_web_development/Core/CSS_layout/Responsive_Design#:~:text=Responsive%20design%20,resolutions%20while%20ensuring%20good%20usability)). Plan the UI layout with a mobile-first mindset, ensuring essential information and controls are accessible on smaller screens.

- **Dash Mantine Responsive Features:** Leverage the responsive features of Dash Mantine Components to achieve fluid layouts. Use components like `dmc.Grid` and `dmc.Col` (columns) with breakpoint props (`xs`, `sm`, `md`, `lg`, `xl`) to create grids that automatically adjust at different screen widths (e.g. stacking columns on mobile). Components such as `dmc.SimpleGrid` provide simple responsive grids with equal-width columns for quick layouts. Additionally, consider using `dmc.MediaQuery` or similar utilities if needed to show/hide or style content based on viewport size. By using these features, the app can dynamically reflow content for optimal display on any device.

- **Testing for Responsiveness:** During development, test the application on multiple screen sizes or use browser developer tools to simulate different devices. Ensure that no component overflows or breaks the layout on smaller screens. Dash Mantine Components are built to be responsive, but verifying the layout manually helps catch any issues early. Aim for a UI that is not just functionally responsive but also user-friendly on touch devices (adequate spacing, readable text, etc.).

## Testing

- **Unit Tests with Pytest:** Write unit tests for all newly developed functionalities. Use the `pytest` framework to create test functions that cover the logic of callbacks, utility functions, and any critical components of the app. Each bug fix or new feature should include corresponding tests to prevent regressions. The goal is to build a reliable test suite that developers can run to quickly catch errors.

- **Coverage Goals:** Aim for a test coverage in the range of 70–80% of the code ([Test Coverage Tutorial: Comprehensive Guide With Best Practices](https://www.lambdatest.com/learning-hub/test-coverage#:~:text=The%20optimal%20level%20of%20test,is%20generally%20considered%20good%20practice)). This level of coverage is generally considered good practice to ensure that most of the code paths are exercised by tests, without being impractical. Focus on covering core business logic and any complex branching logic with tests. It's understood that some code (like UI layout definitions or simple getters/setters) might not need direct testing, but critical logic should be well-tested.

- **Mock External Dependencies:** When writing tests, prefer mocking out external dependencies or side effects. For instance, if a function interacts with a file system, database, or external service, use `unittest.mock` or `pytest` fixtures to simulate that interaction. This ensures tests run in isolation and are not flaky. By mocking anything that depends on persistent or external state, the tests remain deterministic ([mocking - Should I mock all the dependencies when unit testing? - Stack Overflow](https://stackoverflow.com/questions/23643643/should-i-mock-all-the-dependencies-when-unit-testing#:~:text=31)). For example, if `log_parser.Analyser` classes read from files, provide a fake input or use monkeypatching to avoid real file I/O in tests.

- **Test Isolation:** Each test should be independent of others. Use fixtures to set up and tear down any state needed for a test. Avoid relying on the results of one test in another. This allows tests to be run in any order and still pass, and it simplifies debugging when a test fails. Also, clean up any temporary resources (files, database entries, etc.) that a test creates.

## General Behavior

- **Consult Official Documentation:** When guidance is needed, always refer to official documentation rather than unofficial sources. For Dash components and usage, use the Dash official docs (Dash Core Components documentation ([Dash Core Components | Dash for Python Documentation | Plotly](https://dash.plotly.com/dash-core-components#:~:text=Dash%20ships%20with%20supercharged%20components,for%20interactive%20user%20interfaces)) is a primary resource). For Dash Mantine Components, refer to the official Dash Mantine documentation ([dash-mantine-components · PyPI](https://pypi.org/project/dash-mantine-components/#:~:text=Documentation)) which provides usage examples and API references for every component. Using official docs ensures that you follow recommended practices and have up-to-date information on the libraries.

- **Stick to Approved Libraries:** Avoid using unrelated third-party UI or backend libraries unless explicitly approved by the project maintainers. The tech stack is standardized on Dash (for UI) and the given Python backend (including `log_parser` for logic). Introducing a different UI library (like an alternative component library or a separate CSS framework) or a different server framework could cause conflicts and maintenance challenges. Always discuss with the team before adding any new dependency to ensure it aligns with project guidelines.

- **Input Validation:** Always validate user inputs and assume malicious or malformed data could be received. Whether the input comes from dash core components (like `dcc.Input` or form fields) or external sources, check that it meets expected formats and ranges before processing. This might include checking string lengths, numeric bounds, or file content. Never trust user input blindly ([How to handle dash prefixed filenames | LabEx](https://labex.io/tutorials/cybersecurity-how-to-handle-dash-prefixed-filenames-419797#:~:text=,Implement%20comprehensive%20error%20checking)) – validating it helps prevent errors and security issues.

- **Exception Handling:** Handle exceptions gracefully throughout the application. Wrap critical sections of code (such as file parsing, data analysis, or callbacks) in try/except blocks and provide meaningful error handling. For example, if a log file fails to parse, catch the exception and perhaps log an error message or inform the user via the UI (rather than letting the entire app crash). Graceful exception handling makes the app more robust and user-friendly, as it can recover from or at least report errors in a controlled way. Always implement comprehensive error checking and defensive programming practices ([How to handle dash prefixed filenames | LabEx](https://labex.io/tutorials/cybersecurity-how-to-handle-dash-prefixed-filenames-419797#:~:text=,Implement%20comprehensive%20error%20checking)).

- **Overall Consistency:** Ensure that all the above rules are followed consistently across the project. In cases where project-specific conventions conflict with general guidelines, the project-specific rules (as outlined in this document) take precedence. Regular code reviews should be conducted so that framework usage, business logic implementation, coding style, responsiveness, testing, and general best practices remain aligned with these rules. By adhering to these guidelines, the team can maintain a high-quality codebase and deliver a reliable, maintainable Dash application.
