"""
Tests for memory data pipeline migration.

This module tests the migration of get_memory_data() from the original implementation
to the pipeline pattern, ensuring output consistency and performance characteristics.
"""

import asyncio

# Add tern module to path
import sys
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio

tern_path = Path(__file__).parent.parent / "tern"
sys.path.insert(0, str(tern_path))

# Configure pytest for async tests
pytest_plugins = ("pytest_asyncio",)

from tern.core.cache import VisualizationDataCache
from tern.core.memory_pipeline import MemoryDataPipeline
from tern.core.pipeline import DataSources
from tern.core.pipeline.transformations import BaseTransformations


class MockResourceAnalyzer:
    """Mock resource analyzer for testing."""

    def __init__(self, sample_data=None):
        self.sample_data = sample_data if sample_data is not None else self._generate_sample_data()
        self.call_count = 0

    def _generate_sample_data(self):
        """Generate realistic sample data for testing."""
        # Use a wide time range to cover test scenarios
        base_time = datetime.now() - timedelta(days=5)  # Start 5 days ago
        data = []

        # Generate data over a 10-day period to cover various test time ranges
        for i in range(200):  # More data points
            record = {
                "timestamp": base_time + timedelta(hours=i),  # Hourly data points
                "MemTotalBytes": 8589934592,  # 8GB
                "MemUsedBytes": 4294967296 + (i * 52428800),  # 4GB + variation
                "SwapTotalBytes": 2147483648,  # 2GB
                "SwapUsedBytes": i * 10485760,  # Increasing swap usage
            }
            data.append(record)

        return data

    def get_resource_data_for_columns(self, columns, start_time, end_time, limit=5000, offset=0):
        """Mock resource data retrieval."""
        self.call_count += 1

        # Filter data by time range and columns
        filtered_data = []
        for record in self.sample_data[offset : offset + limit]:
            if start_time <= record["timestamp"] <= end_time:
                # Only include requested columns
                filtered_record = {"timestamp": record["timestamp"]}
                for col in columns:
                    if col in record:
                        filtered_record[col] = record[col]
                filtered_data.append(filtered_record)

        return filtered_data

    def get_column_stats(self):
        """Mock column stats."""
        return {
            "MemTotalBytes": {"count": len(self.sample_data), "mean": 8589934592},
            "MemUsedBytes": {"count": len(self.sample_data), "mean": 4294967296},
            "SwapTotalBytes": {"count": len(self.sample_data), "mean": 2147483648},
            "SwapUsedBytes": {"count": len(self.sample_data), "mean": 1073741824},
        }

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        self.call_count += 1

        # If no sample data, return empty DataFrame
        if not self.sample_data:
            return pd.DataFrame()

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start is None or end is None or (start <= record["timestamp"] <= end):
                filtered_data.append(record)

        if not filtered_data:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(filtered_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        return df


class TestMemoryPipelineMigration:
    """Test memory data pipeline migration."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_resource_analyzer = MockResourceAnalyzer()
        self.mock_cross_analyzer = Mock()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        # Create DataSources and Cache instances for pipeline testing
        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )
        self.cache = VisualizationDataCache(max_cached_results=10)

        # Test time range
        self.start_time = datetime.now() - timedelta(hours=2)
        self.end_time = datetime.now()

    @pytest.mark.asyncio
    async def test_pipeline_output_format(self):
        """Test that pipeline output matches expected format."""
        result = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Check output structure
        assert isinstance(result, dict)
        assert "timestamps" in result
        assert "mem_total_gb" in result
        assert "mem_used_gb" in result
        assert "swap_total_gb" in result
        assert "swap_used_gb" in result

        # Check data types
        assert isinstance(result["timestamps"], list)
        assert isinstance(result["mem_total_gb"], list)
        assert isinstance(result["mem_used_gb"], list)
        assert isinstance(result["swap_total_gb"], list)
        assert isinstance(result["swap_used_gb"], list)

        # Check data consistency
        if result["timestamps"]:
            timestamp_count = len(result["timestamps"])
            assert len(result["mem_total_gb"]) == timestamp_count
            assert len(result["mem_used_gb"]) == timestamp_count
            assert len(result["swap_total_gb"]) == timestamp_count
            assert len(result["swap_used_gb"]) == timestamp_count

    @pytest.mark.asyncio
    async def test_unit_conversion(self):
        """Test that bytes are correctly converted to GB."""
        result = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Check that values are in GB range (should be < 100 for our test data)
            for mem_total in result["mem_total_gb"]:
                assert 0 <= mem_total <= 100, f"mem_total_gb out of expected range: {mem_total}"

            for mem_used in result["mem_used_gb"]:
                assert 0 <= mem_used <= 100, f"mem_used_gb out of expected range: {mem_used}"

            # Memory total should be approximately 8GB for our test data
            if result["mem_total_gb"]:
                assert 7 <= result["mem_total_gb"][0] <= 9, "Memory total should be ~8GB"

    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test handling of empty data."""
        # Create analyzer with no data
        empty_analyzer = MockResourceAnalyzer(sample_data=[])
        empty_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=empty_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        result = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=empty_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure
        assert result["timestamps"] == []
        assert result["mem_total_gb"] == []
        assert result["mem_used_gb"] == []
        assert result["swap_total_gb"] == []
        assert result["swap_used_gb"] == []

    @pytest.mark.asyncio
    async def test_cache_behavior(self):
        """Test that caching works correctly."""
        # First call - should cache result
        result1 = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_first = self.mock_resource_analyzer.call_count

        # Second call with same parameters - should use cache
        result2 = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_second = self.mock_resource_analyzer.call_count

        # Should not have made additional analyzer calls
        assert call_count_after_second == call_count_after_first

        # Results should be identical
        assert result1 == result2

    @pytest.mark.asyncio
    async def test_different_time_ranges(self):
        """Test that different time ranges produce different cache keys."""
        # Reset call count to ensure clean test
        self.mock_resource_analyzer.call_count = 0

        # First call
        result1 = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_first = self.mock_resource_analyzer.call_count

        # Second call with significantly different time range (cache can't slice)
        different_start = self.start_time + timedelta(days=1)  # Make it very different
        different_end = self.end_time + timedelta(days=1)
        result2 = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=different_start, end_time=different_end
        )
        call_count_after_second = self.mock_resource_analyzer.call_count

        # Should have made additional analyzer call
        assert call_count_after_second > call_count_after_first

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in pipeline."""
        # Mock analyzer to raise exception on query method (which is actually used)
        self.mock_resource_analyzer.query = Mock(side_effect=Exception("Test error"))

        result = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure on error (the pipeline handles errors gracefully)
        assert result["timestamps"] == []
        assert result["mem_total_gb"] == []
        assert result["mem_used_gb"] == []
        assert result["swap_total_gb"] == []
        assert result["swap_used_gb"] == []

    def test_pipeline_construction(self):
        """Test pipeline construction and configuration."""
        pipeline = MemoryDataPipeline(
            cache=VisualizationDataCache(max_cached_results=10),
            method_name="get_memory_data",
            start_time=self.start_time,
            end_time=self.end_time,
        )

        # Check pipeline properties
        assert pipeline.method_name == "get_memory_data"
        assert pipeline.cache_params["start_time"] == self.start_time
        assert pipeline.cache_params["end_time"] == self.end_time

        # Test data sources injection
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        assert pipeline.data_sources == data_sources

    def test_pipeline_step_configuration(self):
        """Test pipeline step configuration."""
        pipeline = MemoryDataPipeline()
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        pipeline.build_memory_pipeline(self.start_time, end_time=self.end_time)

        # Check that steps were added
        assert len(pipeline.steps) == 3
        assert pipeline.steps[0].name == "fetch_resource_metrics"
        assert pipeline.steps[1].name == "convert_units"
        assert pipeline.steps[2].name == "format_time_series"

    def test_transformation_steps(self):
        """Test individual transformation steps."""
        # Test unit conversion
        test_data = {
            "records": [
                {"timestamp": "2023-01-01T00:00:00", "MemTotalBytes": 1073741824, "MemUsedBytes": 0},  # 1 GB
                {"timestamp": "2023-01-01T00:01:00", "MemTotalBytes": 0, "MemUsedBytes": 2147483648},  # 2 GB
            ]
        }

        converted = BaseTransformations.transform_convert_units(test_data, "bytes", "gb")

        assert len(converted["records"]) == 2
        assert converted["records"][0]["MemTotalBytes"] == 1.0
        assert converted["records"][1]["MemUsedBytes"] == 2.0

        # Test time series formatting
        formatted = BaseTransformations.format_time_series(converted)

        assert "timestamps" in formatted
        assert "MemTotalBytes" in formatted
        assert "MemUsedBytes" in formatted
        assert len(formatted["timestamps"]) == 2

    @pytest.mark.asyncio
    async def test_real_data_integration(self):
        """Test with real sample data if available."""
        sample_data_path = Path("/Users/<USER>/Workspace/examples/logs-P2I-00147")

        if not sample_data_path.exists():
            pytest.skip("Real sample data not available")

        # Load real data
        csv_file = sample_data_path / "ont-resource-logger" / "resource-system.log"
        if not csv_file.exists():
            pytest.skip("Sample CSV file not found")

        real_data = []
        try:
            with open(csv_file, "r") as f:
                lines = f.readlines()

            # Parse first 50 lines for testing
            for line in lines[1:51]:
                parts = line.strip().split(",")
                if len(parts) >= 5:
                    try:
                        record = {
                            "timestamp": datetime.fromisoformat(parts[0].replace("Z", "+00:00")),
                            "MemTotalBytes": int(float(parts[1])) if parts[1] else 0,
                            "MemUsedBytes": int(float(parts[3])) if parts[3] else 0,
                            "SwapTotalBytes": int(float(parts[4])) if parts[4] else 0,
                            "SwapUsedBytes": int(float(parts[5])) if parts[5] else 0,
                        }
                        real_data.append(record)
                    except (ValueError, IndexError):
                        continue
        except FileNotFoundError:
            pytest.skip("Could not read sample data file")

        if not real_data:
            pytest.skip("No valid real data found")

        # Create analyzer with real data
        real_analyzer = MockResourceAnalyzer(sample_data=real_data)
        real_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=real_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        # Test with real data
        start_time = real_data[0]["timestamp"]
        end_time = real_data[-1]["timestamp"]

        result = await MemoryDataPipeline.get_memory_data(cache=self.cache, sources=real_sources, start_time=start_time, end_time=end_time)

        # Validate real data results
        assert len(result["timestamps"]) > 0
        assert len(result["mem_total_gb"]) == len(result["timestamps"])

        # Memory values should be reasonable (allow 0 for test data that might have empty values)
        for mem_total in result["mem_total_gb"]:
            assert 0 <= mem_total < 1000, f"Unreasonable memory total: {mem_total}GB"


class TestPipelinePerformance:
    """Performance tests for pipeline migration."""

    def setup_method(self):
        """Set up performance test fixtures."""
        # Create larger dataset for performance testing
        large_data = []
        base_time = datetime.now() - timedelta(hours=25)  # Start 25 hours ago to cover test ranges

        for i in range(1000):  # 1000 data points
            record = {
                "timestamp": base_time + timedelta(seconds=i * 60),
                "MemTotalBytes": 8589934592 + (i * 1048576),
                "MemUsedBytes": 4294967296 + (i * 524288),
                "SwapTotalBytes": 2147483648,
                "SwapUsedBytes": i * 10485760,
            }
            large_data.append(record)

        self.mock_resource_analyzer = MockResourceAnalyzer(sample_data=large_data)
        self.mock_cross_analyzer = Mock()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        # Create DataSources and Cache instances for pipeline testing
        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )
        self.cache = VisualizationDataCache(max_cached_results=10)

    @pytest.mark.asyncio
    async def test_large_dataset_performance(self):
        """Test performance with large dataset."""
        start_time = datetime.now() - timedelta(hours=24)
        end_time = datetime.now()

        import time

        start = time.perf_counter()
        result = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=start_time, end_time=end_time
        )
        end = time.perf_counter()

        execution_time = end - start

        # Should complete within reasonable time (< 1 second for 1000 records)
        assert execution_time < 1.0, f"Performance regression: {execution_time:.3f}s"

        # Should return expected number of records
        assert len(result["timestamps"]) > 0
        print(f"Pipeline processed {len(result['timestamps'])} records in {execution_time:.3f}s")

    @pytest.mark.asyncio
    async def test_cache_performance(self):
        """Test cache performance characteristics."""
        start_time = datetime.now() - timedelta(hours=1)
        end_time = datetime.now()

        # First call (cache miss)
        import time

        start = time.perf_counter()
        result1 = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=start_time, end_time=end_time
        )
        first_call_time = time.perf_counter() - start

        # Second call (cache hit)
        start = time.perf_counter()
        result2 = await MemoryDataPipeline.get_memory_data(
            cache=self.cache, sources=self.data_sources, start_time=start_time, end_time=end_time
        )
        second_call_time = time.perf_counter() - start

        # Cache hit should be faster (or at least not slower)
        # Note: At microsecond levels, timing can be noisy, so we use a lenient check
        assert second_call_time <= first_call_time * 2.0, f"Cache performance issue: {second_call_time:.4f}s vs {first_call_time:.4f}s"

        # Results should be identical
        assert result1 == result2

        print(f"Cache miss: {first_call_time:.3f}s, Cache hit: {second_call_time:.3f}s")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
