"""
Tests for LogScatterDataPipeline.

This module tests the specialized pipeline for log scatter data transformation.
"""

from datetime import datetime, timedelta
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio
from tern.core.log_scatter_pipeline import LogScatterDataPipeline
from tern.core.pipeline import DataSources


class MockCache:
    """Mock cache for testing."""

    def __init__(self):
        self.cache = {}

    def get_cached_transformation(self, method_name, start_time, end_time):
        """Mock the VisualizationDataCache interface."""
        # Return None for cache miss
        return None

    def add_to_cache(self, query_key, result, method_name, start_time, end_time):
        """Mock the VisualizationDataCache interface."""
        self.cache[query_key] = result

    def _generate_cache_key(self, method_name, start_time, end_time):
        """Mock cache key generation."""
        return f"{method_name}_{start_time}_{end_time}"


class MockMinknowAnalyzer:
    """Mock MinKnow analyzer for testing."""

    def get_positions(self):
        return ["position1", "position2"]

    def get_log_level_timeline(self, start, end, position, freq):
        """Mock log level timeline data."""
        return {"timestamps": [datetime.now(), datetime.now() + timedelta(minutes=15)], "log_levels": {"WARNING": [5, 3], "ERROR": [2, 1]}}


class MockCrossAnalyzer:
    """Mock CrossAnalyzer for testing."""

    def __init__(self):
        self.minknow_analyzer = MockMinknowAnalyzer()


class TestLogScatterDataPipeline:
    """Test LogScatterDataPipeline functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_cache = MockCache()
        self.mock_cross_analyzer = MockCrossAnalyzer()
        self.mock_resource_analyzer = Mock()
        self.mock_minknow_analyzer = MockMinknowAnalyzer()
        self.mock_timeline_analyzer = Mock()

        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        self.start_time = datetime.now()
        self.end_time = self.start_time + timedelta(hours=1)

    def test_pipeline_construction(self):
        """Test pipeline construction."""
        pipeline = LogScatterDataPipeline(
            cache=self.mock_cache, method_name="get_log_scatter_data", start_time=self.start_time, end_time=self.end_time
        )

        assert pipeline.cache == self.mock_cache
        assert pipeline.method_name == "get_log_scatter_data"
        assert pipeline.cache_params["start_time"] == self.start_time
        assert pipeline.cache_params["end_time"] == self.end_time

    @pytest.mark.asyncio
    async def test_bucket_frequency_calculation(self):
        """Test bucket frequency calculation based on time range."""
        pipeline = LogScatterDataPipeline()

        # Test 1 day range (should use 15Min)
        start = datetime.now()
        end = start + timedelta(days=1)

        result = await pipeline._calculate_bucket_frequency(None, start, end)
        assert result["bucket_freq"] == "15Min"

        # Test 1 week range (should use 1h)
        end = start + timedelta(days=7)
        result = await pipeline._calculate_bucket_frequency(None, start, end)
        assert result["bucket_freq"] == "1h"

        # Test 1 month range (should use 6h)
        end = start + timedelta(days=30)
        result = await pipeline._calculate_bucket_frequency(None, start, end)
        assert result["bucket_freq"] == "6h"

    @pytest.mark.asyncio
    async def test_pipeline_execution(self):
        """Test full pipeline execution."""
        pipeline = LogScatterDataPipeline(
            cache=self.mock_cache, method_name="get_log_scatter_data", start_time=self.start_time, end_time=self.end_time
        )

        pipeline.inject_sources(self.data_sources)

        # Build and execute pipeline
        result = await pipeline.build_log_scatter_pipeline(self.start_time, self.end_time).execute()

        # Verify result structure
        assert "timestamps" in result
        assert "positions" in result
        assert "scatter_data" in result
        assert "WARNING" in result["scatter_data"]
        assert "ERROR" in result["scatter_data"]

        # Verify positions
        assert len(result["positions"]) == 2
        assert "position1" in result["positions"]
        assert "position2" in result["positions"]

        # Verify scatter data structure
        warning_data = result["scatter_data"]["WARNING"]
        assert "x" in warning_data
        assert "y" in warning_data
        assert "sizes" in warning_data

        error_data = result["scatter_data"]["ERROR"]
        assert "x" in error_data
        assert "y" in error_data
        assert "sizes" in error_data

    @pytest.mark.asyncio
    async def test_static_method_implementation(self):
        """Test the static method implementation."""
        result = await LogScatterDataPipeline.get_log_scatter_data(self.mock_cache, self.data_sources, self.start_time, self.end_time)

        # Verify result structure
        assert "timestamps" in result
        assert "positions" in result
        assert "scatter_data" in result
        assert "WARNING" in result["scatter_data"]
        assert "ERROR" in result["scatter_data"]

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in pipeline."""
        # Mock the minknow analyzer to raise an error
        mock_minknow_analyzer = Mock()
        mock_minknow_analyzer.get_positions.side_effect = Exception("Test error")

        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        # Should return empty structure on error
        result = await LogScatterDataPipeline.get_log_scatter_data(self.mock_cache, data_sources, self.start_time, self.end_time)

        # Should return empty data structure
        assert result["timestamps"] == []
        assert result["positions"] == []
        assert len(result["scatter_data"]["WARNING"]["x"]) == 0
        assert len(result["scatter_data"]["ERROR"]["x"]) == 0


# Add asyncio import for the test
import asyncio
