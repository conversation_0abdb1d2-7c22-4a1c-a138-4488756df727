"""Tests for the progress tracking system."""

import threading
import time
from unittest.mock import Mock, patch

import pytest
from tern.core.container import Ana<PERSON><PERSON><PERSON>ontainer
from tern.core.parsing_controller import <PERSON><PERSON><PERSON><PERSON>roller
from tern.core.progress_calculator import ProgressCalculator
from tern.core.progress_tracker import ProgressTracker
from tern.mocks.backend_mocks import MockFileDialogService, MockParsingController, MockProgressTracker
from tern.services.file_dialog import FileDialogService


class TestProgressCalculator:
    """Test the progress calculation engine."""

    def test_initialization(self):
        """Test progress calculator initialization."""
        calculator = ProgressCalculator()
        assert calculator._start_time is None
        assert calculator._current_stage == ""
        assert calculator._stage_progress == 0.0

    def test_start_parsing(self):
        """Test starting parsing progress tracking."""
        calculator = ProgressCalculator()
        calculator.start_parsing()

        assert calculator._start_time is not None
        assert calculator._stage_start_time is not None
        assert calculator._current_stage == ""
        assert calculator._stage_progress == 0.0

    def test_set_stage(self):
        """Test setting parsing stage."""
        calculator = ProgressCalculator()
        calculator.start_parsing()
        calculator.set_stage("🎣 Casting nets for logs", 10)

        assert calculator._current_stage == "🎣 Casting nets for logs"
        assert calculator._stage_total_files == 10
        assert calculator._stage_files_processed == 0

    def test_update_file_progress(self):
        """Test updating file-based progress."""
        calculator = ProgressCalculator()
        calculator.start_parsing()
        calculator.set_stage("🐠 Diving for MinKnow logs", 5)
        calculator.update_file_progress(3, "test.log")

        assert calculator._stage_files_processed == 3
        assert calculator._current_file == "test.log"
        assert calculator._stage_progress == 60.0  # 3/5 * 100

    def test_calculate_overall_progress(self):
        """Test overall progress calculation."""
        calculator = ProgressCalculator()
        calculator.start_parsing()

        # Stage 1: 5% weight
        calculator.set_stage("🎣 Casting nets for logs", 2)
        calculator.update_file_progress(1, "file1.log")
        progress = calculator.calculate_overall_progress()
        assert progress > 0
        assert progress <= 5

        # Stage 2: 10% weight
        calculator.set_stage("🐟 Teaching analyzers to fish", 1)
        progress = calculator.calculate_overall_progress()
        assert progress >= 5  # Allow for exactly 5
        assert progress <= 15

    def test_create_progress_update(self):
        """Test creating progress update."""
        calculator = ProgressCalculator()
        calculator.start_parsing()

        # Set total files discovered (this would normally be done by discover_parseable_files)
        calculator._total_files_discovered = 5

        calculator.set_stage("🎣 Casting nets for logs", 2)
        calculator.update_file_progress(1, "test.log")

        update = calculator.create_progress_update()
        assert update.stage == "🎣 Casting nets for logs"
        assert update.current_file == "test.log"
        assert update.files_processed == 1  # Cumulative files processed
        assert update.total_files == 5  # Total files discovered
        assert update.elapsed_time > 0

    def test_discover_parseable_files(self):
        """Test file discovery functionality."""
        calculator = ProgressCalculator()

        # Test with non-existent directory
        counts = calculator.discover_parseable_files("/non/existent/path")
        assert counts["resource"] == 0
        assert counts["minknow"] == 0
        assert counts["total"] == 0

    def test_format_time(self):
        """Test time formatting."""
        calculator = ProgressCalculator()

        assert calculator.format_time(30.5) == "30.5s"
        assert calculator.format_time(90) == "1m 30s"
        assert calculator.format_time(3661) == "1h 1m"


class TestProgressTracker:
    """Test the progress tracker."""

    def test_initialization(self):
        """Test progress tracker initialization."""
        tracker = ProgressTracker()
        status = tracker.get_status()
        assert status.status == "idle"
        assert status.progress == 0

    def test_update_progress(self):
        """Test progress updates."""
        tracker = ProgressTracker()
        tracker.set_parsing_start()

        from tern.core.interfaces import ProgressUpdate

        update = ProgressUpdate(
            stage="🎣 Casting nets for logs", progress=25, current_file="test.log", files_processed=1, total_files=4, elapsed_time=1.5
        )

        tracker.update_progress(update)
        status = tracker.get_status()
        assert status.stage == "🎣 Casting nets for logs"
        assert status.progress == 25
        assert status.current_file == "test.log"

    def test_set_error(self):
        """Test error state setting."""
        tracker = ProgressTracker()
        tracker.set_error("Test error", "parsing")

        status = tracker.get_status()
        assert status.status == "error"
        assert status.stage == "parsing"
        assert status.error["message"] == "Test error"

    def test_set_parsing_complete(self):
        """Test parsing completion."""
        tracker = ProgressTracker()
        tracker.set_parsing_start()
        tracker.set_parsing_complete()

        status = tracker.get_status()
        assert status.status == "complete"
        assert status.progress == 100

    def test_reset_status(self):
        """Test status reset."""
        tracker = ProgressTracker()
        tracker.set_parsing_start()
        tracker.reset_status()

        status = tracker.get_status()
        assert status.status == "idle"
        assert status.progress == 0


class TestMockProgressTracker:
    """Test the mock progress tracker."""

    def test_simulation(self):
        """Test mock parsing simulation."""
        tracker = MockProgressTracker()
        tracker.start_simulation()

        # Wait a bit for simulation to start
        time.sleep(0.1)

        status = tracker.get_status()
        assert status.status == "parsing"
        assert status.progress > 0

        # Stop simulation
        tracker.stop_simulation()
        time.sleep(0.5)  # Wait longer for simulation to complete

        status = tracker.get_status()
        # The status might still be "parsing" if the simulation thread hasn't finished yet
        # Let's check that it's either "parsing" or "complete"
        assert status.status in ["parsing", "complete"]


class TestFileDialogService:
    """Test the file dialog service."""

    def test_validation(self):
        """Test path validation."""
        service = FileDialogService()

        # Test empty path
        result = service.validate_path("")
        assert not result["valid"]
        assert "empty" in result["errors"][0].lower()

        # Test non-existent path
        result = service.validate_path("/non/existent/path")
        assert not result["valid"]
        assert "exist" in result["errors"][0].lower()

    def test_system_info(self):
        """Test system information."""
        service = FileDialogService()
        info = service.get_system_info()

        assert "system" in info
        assert "has_webview" in info
        assert "home_directory" in info


class TestMockFileDialogService:
    """Test the mock file dialog service."""

    def test_mock_selection(self):
        """Test mock directory selection."""
        service = MockFileDialogService("/test/path")
        result = service.select_directory()
        assert result == "/test/path"

    def test_mock_validation(self):
        """Test mock validation."""
        service = MockFileDialogService()
        result = service.validate_path("any/path")
        assert result["valid"]
        assert result["has_logs"]


class TestParsingController:
    """Test the parsing controller."""

    def test_initialization(self):
        """Test parsing controller initialization."""
        container = Mock()
        controller = ParsingController(container)
        assert not controller.is_parsing()

    def test_cancellation_request(self):
        """Test cancellation request."""
        container = Mock()
        controller = ParsingController(container)

        # Should return False when not parsing
        assert not controller.cancel_parsing()

        # Test cancellation flag
        assert not controller.is_cancellation_requested()


class TestMockParsingController:
    """Test the mock parsing controller."""

    def test_mock_parsing(self):
        """Test mock parsing operations."""
        controller = MockParsingController()

        # Test successful parsing
        assert controller.start_parsing("/test/path")
        assert controller.is_parsing()

        # Test cancellation
        assert controller.cancel_parsing()
        assert not controller.is_parsing()

    def test_mock_failure(self):
        """Test mock parsing failure."""
        controller = MockParsingController()
        controller.set_mock_success(False)

        assert not controller.start_parsing("/test/path")


class TestIntegration:
    """Integration tests for the progress tracking system."""

    def test_progress_calculation_integration(self):
        """Test integration between calculator and tracker."""
        calculator = ProgressCalculator()
        tracker = ProgressTracker()

        calculator.start_parsing()
        calculator.set_stage("🎣 Casting nets for logs", 2)
        calculator.update_file_progress(1, "test.log")

        update = calculator.create_progress_update()
        tracker.update_progress(update)

        status = tracker.get_status()
        assert status.stage == "🎣 Casting nets for logs"
        assert status.current_file == "test.log"

    def test_thread_safety(self):
        """Test thread safety of progress tracking."""
        tracker = ProgressTracker()
        tracker.set_parsing_start()

        def update_progress():
            for i in range(10):
                from tern.core.interfaces import ProgressUpdate

                update = ProgressUpdate(
                    stage=f"Stage {i}", progress=i * 10, current_file=f"file_{i}.log", files_processed=i, total_files=10, elapsed_time=i
                )
                tracker.update_progress(update)
                time.sleep(0.01)

        # Start multiple threads
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=update_progress)
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Should not crash and should have valid state
        status = tracker.get_status()
        assert status.status in ["parsing", "complete", "error"]
        assert 0 <= status.progress <= 100
