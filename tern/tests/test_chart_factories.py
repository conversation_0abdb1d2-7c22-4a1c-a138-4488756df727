"""Tests for chart factory components."""

from __future__ import annotations

import dash_mantine_components as dmc
import pytest
from dash import dcc
from tern.components.charts import (
    CHART_PLOTLY_CONFIGS,
    CHART_TYPE_CONFIGS,
    DEFAULT_LAYOUT,
    DEFAULT_PLOTLY_CONFIG,
    SIMPLIFIED_PLOTLY_CONFIG,
    chart_card,
    chart_card_pattern_matching,
    create_chart_card_layout,
    create_chart_pattern_matching,
    get_chart_config,
    get_chart_mode,
    get_plotly_config,
    register_chart_callback,
    register_pattern_matching_callbacks,
)
from tern.components.charts.base import _normalize_input_list
from tern.components.charts.callbacks import _generate_chart_figure, _get_empty_chart_figure
from tern.components.charts.configs import merge_configs


class TestChartConfigs:
    """Test chart configuration functionality."""

    def test_default_plotly_config_structure(self):
        """Test that DEFAULT_PLOTLY_CONFIG has required keys."""
        # Bandit: ignore assert warnings in test files
        assert "displaylogo" in DEFAULT_PLOTLY_CONFIG  # nosec B101
        assert "modeBarButtonsToRemove" in DEFAULT_PLOTLY_CONFIG  # nosec B101
        assert "scrollZoom" in DEFAULT_PLOTLY_CONFIG  # nosec B101
        assert DEFAULT_PLOTLY_CONFIG["displaylogo"] is False  # nosec B101

    def test_default_layout_structure(self):
        """Test that DEFAULT_LAYOUT has required keys."""
        assert "showlegend" in DEFAULT_LAYOUT  # nosec B101
        assert "legend" in DEFAULT_LAYOUT  # nosec B101
        assert "hovermode" in DEFAULT_LAYOUT  # nosec B101
        assert "margin" in DEFAULT_LAYOUT  # nosec B101

    def test_chart_type_configs_exist(self):
        """Test that chart type configs are properly defined."""
        assert "cpu_load" in CHART_TYPE_CONFIGS  # nosec B101
        assert "disk_capacity" in CHART_TYPE_CONFIGS  # nosec B101
        assert "gpu_metrics" in CHART_TYPE_CONFIGS  # nosec B101
        assert "log_scatter" in CHART_TYPE_CONFIGS  # nosec B101

    def test_get_chart_config(self):
        """Test get_chart_config function."""
        cpu_config = get_chart_config("cpu_load")
        assert "yaxis" in cpu_config  # nosec B101
        assert cpu_config["yaxis"]["title"] == "Load Average"  # nosec B101

        # Test unknown chart type returns base config
        unknown_config = get_chart_config("unknown_type")
        assert "showlegend" in unknown_config  # nosec B101

    def test_merge_configs(self):
        """Test config merging functionality."""
        base = {"a": 1, "b": 2}
        overrides = {"b": 3, "c": 4}
        merged = merge_configs(base, overrides)

        assert merged["a"] == 1  # nosec B101
        assert merged["b"] == 3  # nosec B101
        assert merged["c"] == 4  # nosec B101

        # Test with None overrides
        merged_none = merge_configs(base, None)
        assert merged_none == base  # nosec B101

    def test_simplified_plotly_config(self):
        """Test simplified plotly configuration structure."""
        assert "displayModeBar" in SIMPLIFIED_PLOTLY_CONFIG  # nosec B101
        assert SIMPLIFIED_PLOTLY_CONFIG["displayModeBar"] is False  # nosec B101
        assert "displaylogo" in SIMPLIFIED_PLOTLY_CONFIG  # nosec B101

    def test_chart_plotly_configs(self):
        """Test chart-specific Plotly configurations."""
        assert "log_scatter" in CHART_PLOTLY_CONFIGS  # nosec B101
        assert CHART_PLOTLY_CONFIGS["log_scatter"] == SIMPLIFIED_PLOTLY_CONFIG  # nosec B101

    def test_get_plotly_config(self):
        """Test get_plotly_config function."""
        # Test log_scatter gets simplified config
        log_scatter_config = get_plotly_config("log_scatter")
        assert log_scatter_config["displayModeBar"] is False  # nosec B101

        # Test other charts get default config
        cpu_config = get_plotly_config("cpu_load")
        assert "modeBarButtonsToRemove" in cpu_config  # nosec B101
        assert "displayModeBar" not in cpu_config  # nosec B101

        # Test unknown chart type gets default config
        unknown_config = get_plotly_config("unknown_type")
        assert "modeBarButtonsToRemove" in unknown_config  # nosec B101

    def test_get_chart_mode(self):
        """Test get_chart_mode function."""
        assert get_chart_mode("cpu_load") == "lines"  # nosec B101
        assert get_chart_mode("disk_capacity") == "stacked_area"  # nosec B101
        assert get_chart_mode("gpu_metrics") == "dual_axis"  # nosec B101
        assert get_chart_mode("log_scatter") == "scatter"  # nosec B101
        assert get_chart_mode("unknown_type") == "lines"  # nosec B101  # default


class TestChartBase:
    """Test base chart functionality."""

    def test_normalize_input_list(self):
        """Test input ID normalization."""
        # Test string input
        result = _normalize_input_list("single-id")
        assert result == ["single-id"]  # nosec B101

        # Test list input
        result = _normalize_input_list(["id1", "id2", "id3"])
        assert result == ["id1", "id2", "id3"]  # nosec B101

        # Test list with None values
        result = _normalize_input_list(["id1", None, "", "id2"])
        assert result == ["id1", "id2"]  # nosec B101

        # Test empty cases
        result = _normalize_input_list([])
        assert result == []  # nosec B101

    def test_create_chart_card_layout(self):
        """Test chart card layout creation."""
        layout = create_chart_card_layout(
            graph_id="test-graph", loading_id="test-loading", title="Test Chart", height=300, description="Test description"
        )

        # Should return a DMC Stack
        assert isinstance(layout, dmc.Stack)  # nosec B101
        assert layout.gap == "md"  # nosec B101
        assert len(layout.children) == 2  # nosec B101

        # Check the structure
        group = layout.children[0]
        assert isinstance(group, dmc.Group)  # nosec B101

        paper = layout.children[1]
        assert isinstance(paper, dmc.Paper)  # nosec B101
        assert paper.className == "graph-container"  # nosec B101

    def test_create_chart_card_layout_with_plotly_config(self):
        """Test chart card layout creation with custom Plotly config."""
        custom_config = {"displayModeBar": False}
        layout = create_chart_card_layout(
            graph_id="test-graph", loading_id="test-loading", title="Test Chart", height=300, plotly_config=custom_config
        )

        # Should return a DMC Stack
        assert isinstance(layout, dmc.Stack)  # nosec B101

        # Get the graph component
        paper = layout.children[1]
        graph = paper.children[1]
        assert isinstance(graph, dcc.Graph)  # nosec B101
        assert graph.config == custom_config  # nosec B101

    def test_create_chart_card_layout_no_description(self):
        """Test chart card layout creation without description."""
        layout = create_chart_card_layout(graph_id="test-graph", loading_id="test-loading", title="Test Chart", height=300)

        # Should filter out None description
        assert isinstance(layout, dmc.Stack)  # nosec B101
        group = layout.children[0]
        # Group should only have title, not description
        assert len(group.children) == 2  # nosec B101

    def test_chart_card_basic(self):
        """Test basic chart card creation."""
        card = chart_card(
            card_id="test-chart",
            input_ids=["input1", "input2"],
            title="Test Chart",
            chart_type="cpu_load",
            height=400,
            description="Test description",
        )

        # Should return a DMC Stack
        assert isinstance(card, dmc.Stack)  # nosec B101
        assert card.gap == "md"  # nosec B101

    def test_chart_card_single_input(self):
        """Test chart card with single input ID."""
        card = chart_card(card_id="test-chart", input_ids="single-input", title="Test Chart")

        assert isinstance(card, dmc.Stack)  # nosec B101

    def test_chart_card_defaults(self):
        """Test chart card with default parameters."""
        card = chart_card(card_id="test-chart", input_ids=["input1"])

        assert isinstance(card, dmc.Stack)  # nosec B101
        # Should use default height of 400px
        paper = card.children[1]  # Paper is second child
        graph = paper.children[1]  # Graph is second child of paper
        assert isinstance(graph, dcc.Graph)  # nosec B101
        assert graph.style["height"] == "400px"  # nosec B101

    def test_chart_card_with_log_scatter_config(self):
        """Test chart card uses simplified config for log scatter."""
        card = chart_card(card_id="test-log-scatter", input_ids=["input1"], title="Test Log Scatter", chart_type="log_scatter")

        assert isinstance(card, dmc.Stack)  # nosec B101
        # Get the graph component
        paper = card.children[1]
        graph = paper.children[1]
        assert isinstance(graph, dcc.Graph)  # nosec B101
        # Should use simplified config (no mode bar)
        assert graph.config.get("displayModeBar") is False  # nosec B101

    def test_chart_card_with_standard_config(self):
        """Test chart card uses standard config for other chart types."""
        card = chart_card(card_id="test-cpu", input_ids=["input1"], title="Test CPU Chart", chart_type="cpu_load")

        assert isinstance(card, dmc.Stack)  # nosec B101
        # Get the graph component
        paper = card.children[1]
        graph = paper.children[1]
        assert isinstance(graph, dcc.Graph)  # nosec B101
        # Should use standard config (has mode bar buttons to remove)
        assert "modeBarButtonsToRemove" in graph.config  # nosec B101
        assert "displayModeBar" not in graph.config  # nosec B101


class TestChartCallbacks:
    """Test chart callback functionality."""

    def test_register_chart_callback_function_exists(self):
        """Test that register_chart_callback function is callable."""
        # Should be able to call the function
        assert callable(register_chart_callback)  # nosec B101

    def test_generate_chart_figure_empty_data(self):
        """Test chart figure generation with empty data."""
        figure = _generate_chart_figure("cpu_load", None, [])

        assert "data" in figure  # nosec B101
        assert "layout" in figure  # nosec B101
        assert figure["data"] == []  # nosec B101

    def test_generate_chart_figure_with_sample_data(self):
        """Test chart figure generation with sample data."""
        sample_data = {
            "data": {
                "cpu": {
                    "timestamps": ["2023-01-01T00:00:00", "2023-01-01T00:01:00"],
                    "load1": [1.2, 1.5],
                    "load5": [1.1, 1.3],
                    "load15": [1.0, 1.2],
                }
            }
        }

        figure = _generate_chart_figure("cpu_load", sample_data, [])

        assert "data" in figure  # nosec B101
        assert "layout" in figure  # nosec B101
        assert len(figure["data"]) == 3  # nosec B101  # Three traces (1min, 5min, 15min)

        # Check trace structure
        trace = figure["data"][0]
        assert "x" in trace  # nosec B101
        assert "y" in trace  # nosec B101
        assert "name" in trace  # nosec B101
        assert trace["name"] == "Load (1 min)"  # nosec B101

    def test_get_empty_chart_figure(self):
        """Test empty chart figure generation."""
        figure = _get_empty_chart_figure("cpu_load")

        assert "data" in figure  # nosec B101
        assert "layout" in figure  # nosec B101
        assert figure["data"] == []  # nosec B101
        assert "yaxis" in figure["layout"]  # nosec B101

    def test_generate_different_chart_types(self):
        """Test chart generation for different chart types."""
        sample_data = {
            "data": {
                "disk": {"timestamps": ["2023-01-01T00:00:00"], "mount_points": {"/": [75.5], "/home": [82.1]}},
                "memory": {"timestamps": ["2023-01-01T00:00:00"], "mem_used_gb": [8.2], "swap_used_gb": [1.1], "mem_total_gb": [16.0]},
            }
        }

        # Test disk capacity
        disk_figure = _generate_chart_figure("disk_capacity", sample_data, [])
        assert len(disk_figure["data"]) == 2  # nosec B101  # Two mount points
        assert disk_figure["data"][0]["name"] == "/"  # nosec B101

        # Test memory
        memory_figure = _generate_chart_figure("memory_details", sample_data, [])
        assert len(memory_figure["data"]) == 3  # nosec B101  # RAM used, Swap used, Total memory
        assert memory_figure["data"][0]["name"] == "RAM used"  # nosec B101

    def test_chart_card_registers_callback(self):
        """Test that chart_card automatically registers callback."""
        # This test verifies that the callback registration doesn't cause errors
        # The actual callback testing would require a Dash app context
        try:
            card = chart_card(card_id="test-callback-chart", input_ids=["test-input"], title="Test Callback Chart", chart_type="cpu_load")
            # If we get here without errors, callback registration worked
            assert isinstance(card, dmc.Stack)  # nosec B101
        except Exception as e:
            # Callback registration should not fail during component creation
            assert False, f"Callback registration failed: {e}"  # nosec B101


class TestChartIntegration:
    """Test integration between components."""

    def test_configs_import_in_base(self):
        """Test that base module can import configs correctly."""
        from tern.components.charts.base import get_plotly_config
        from tern.components.charts.configs import DEFAULT_PLOTLY_CONFIG as config_config

        # Should be able to get default config
        base_config = get_plotly_config("unknown_type")  # Returns default config
        assert "displaylogo" in base_config  # nosec B101
        assert "displaylogo" in config_config  # nosec B101

    def test_module_imports(self):
        """Test that all modules can be imported correctly."""
        # Test main package imports
        from tern.components.charts import CHART_TYPE_CONFIGS, DEFAULT_LAYOUT, DEFAULT_PLOTLY_CONFIG, chart_card, create_chart_card_layout

        # Test individual module imports
        from tern.components.charts.base import chart_card as base_chart_card
        from tern.components.charts.configs import DEFAULT_PLOTLY_CONFIG as config_plotly

        # Should be the same functions/objects
        assert chart_card is base_chart_card  # nosec B101
        assert DEFAULT_PLOTLY_CONFIG is config_plotly  # nosec B101

    def test_callback_imports(self):
        """Test that callback functions can be imported correctly."""
        from tern.components.charts import register_chart_callback
        from tern.components.charts.callbacks import register_chart_callback as callback_func

        # Should be the same function
        assert register_chart_callback is callback_func  # nosec B101


class TestPatternMatchingCharts:
    """Test pattern-matching chart functionality."""

    def test_create_chart_pattern_matching(self):
        """Test pattern-matching chart creation."""
        chart = create_chart_pattern_matching(
            chart_type="cpu_load", card_id="test-cpu", title="Test CPU Chart", description="Test pattern-matching chart"
        )

        # Should return a DMC Stack
        assert isinstance(chart, dmc.Stack)  # nosec B101

    def test_chart_card_pattern_matching(self):
        """Test pattern-matching chart card creation."""
        card = chart_card_pattern_matching(card_id="test-pattern", chart_type="memory_details", title="Test Pattern Chart")

        # Should return a DMC Stack
        assert isinstance(card, dmc.Stack)  # nosec B101

    def test_pattern_matching_chart_factories(self):
        """Test that all pattern-matching factory functions exist and work."""
        from tern.components.charts.factories import (
            cpu_load_chart_pattern_matching,
            disk_capacity_chart_pattern_matching,
            gpu_metrics_chart_pattern_matching,
            io_throughput_chart_pattern_matching,
            memory_chart_pattern_matching,
        )

        # Test CPU chart
        cpu_chart = cpu_load_chart_pattern_matching("test-cpu")
        assert isinstance(cpu_chart, dmc.Stack)  # nosec B101

        # Test memory chart
        memory_chart = memory_chart_pattern_matching("test-memory")
        assert isinstance(memory_chart, dmc.Stack)  # nosec B101

        # Test disk chart
        disk_chart = disk_capacity_chart_pattern_matching("test-disk")
        assert isinstance(disk_chart, dmc.Stack)  # nosec B101

        # Test I/O chart
        io_chart = io_throughput_chart_pattern_matching("test-io")
        assert isinstance(io_chart, dmc.Stack)  # nosec B101

        # Test GPU chart
        gpu_chart = gpu_metrics_chart_pattern_matching("test-gpu")
        assert isinstance(gpu_chart, dmc.Stack)  # nosec B101

    def test_register_pattern_matching_callbacks_function_exists(self):
        """Test that pattern-matching callback registration function exists."""
        assert callable(register_pattern_matching_callbacks)  # nosec B101

    def test_pattern_matching_component_ids(self):
        """Test that pattern-matching charts create proper component IDs."""
        # This is a conceptual test - we can't easily inspect the actual component IDs
        # without a full Dash app context, but we can verify the function executes
        chart = create_chart_pattern_matching(
            chart_type="cpu_load",
            card_id="test-id",
        )

        # Should create chart without errors
        assert isinstance(chart, dmc.Stack)  # nosec B101


class TestPatternMatchingPerformance:
    """Test performance-related aspects of pattern-matching."""

    def test_pattern_matching_reduces_callback_count(self):
        """Test that pattern-matching approach reduces callback overhead.

        This is more of a conceptual test to document the performance benefits.
        """
        # Individual callback approach: Each chart = 1 callback
        individual_callbacks = 6  # cpu, memory, disk, io, gpu, (log scatter uses different pattern)

        # Pattern-matching approach: All charts = 1 callback
        pattern_matching_callbacks = 1

        # Calculate reduction
        reduction_percentage = ((individual_callbacks - pattern_matching_callbacks) / individual_callbacks) * 100

        # Should be 83% reduction (5/6 = 83.33%)
        assert reduction_percentage > 80  # nosec B101
        assert reduction_percentage < 85  # nosec B101

    def test_chart_factory_api_consistency(self):
        """Test that pattern-matching factories maintain API consistency."""
        # Both approaches should accept the same basic parameters
        individual_chart = chart_card(card_id="individual-test", input_ids=["test-input"], chart_type="cpu_load", title="Test Chart")

        pattern_chart = chart_card_pattern_matching(card_id="pattern-test", chart_type="cpu_load", title="Test Chart")

        # Both should return DMC Stack components
        assert isinstance(individual_chart, dmc.Stack)  # nosec B101
        assert isinstance(pattern_chart, dmc.Stack)  # nosec B101


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
