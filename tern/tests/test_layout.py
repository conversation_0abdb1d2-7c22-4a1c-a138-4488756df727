from __future__ import annotations

import dash_mantine_components as dmc
import pytest
from dash import Dash
from dash_mantine_components import MantineProvider
from tern.layout.main_layout import create_main_layout


def create_test_app():
    """Create a minimal Dash app instance for testing purposes."""
    test_app = Dash(__name__, external_stylesheets=[dmc.styles.ALL])
    test_app.layout = create_main_layout()
    return test_app


@pytest.fixture
def app():
    """Create a test app instance for testing."""
    return create_test_app()


def test_app_initialization(app):
    """Test that the app is properly initialized with the correct type."""
    assert isinstance(app, Dash)


def test_layout_structure(app):
    """Test the basic structure of the app layout."""
    # Check that the root component is a MantineProvider
    assert isinstance(app.layout, MantineProvider)

    # Get the children of the MantineProvider
    children = app.layout.children

    # Since we're using the new AppShell layout, update the test to match the actual structure
    # The layout has: Store components, and AppShell
    assert len(children) >= 3  # Store components + AppShell

    # Find the AppShell component
    appshell = None
    for child in children:
        if hasattr(child, "id") and getattr(child, "id", None) == "appshell":
            appshell = child
            break

    assert appshell is not None
    assert hasattr(appshell, "children")


def test_header_content(app):
    """Test the header contains the expected elements."""
    # Get the AppShell from the layout
    children = app.layout.children
    appshell = None
    for child in children:
        if hasattr(child, "id") and getattr(child, "id", None) == "appshell":
            appshell = child
            break

    assert appshell is not None

    # The AppShell contains header, navbar, and main content
    # We need to check the structure matches what's created by the layout functions
    assert hasattr(appshell, "children")
    # The test should verify the basic structure exists
    assert len(appshell.children) >= 2  # At least header and main content
