"""
Tests for CPU load data pipeline migration.

This module tests the migration of get_cpu_load_data() from the original implementation
to the pipeline pattern, ensuring output consistency and performance characteristics.
"""

import asyncio

# Add tern module to path
import sys
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio

tern_path = Path(__file__).parent.parent / "tern"
sys.path.insert(0, str(tern_path))

# Configure pytest for async tests
pytest_plugins = ("pytest_asyncio",)

from tern.core.cache import VisualizationDataCache
from tern.core.cpu_load_pipeline import CpuLoadDataPipeline
from tern.core.pipeline import DataSources


class MockResourceAnalyzer:
    """Mock resource analyzer for testing."""

    def __init__(self, sample_data=None):
        self.sample_data = sample_data if sample_data is not None else self._generate_sample_data()
        self.call_count = 0

    def _generate_sample_data(self):
        """Generate realistic CPU load sample data for testing."""
        base_time = datetime.now() - timedelta(hours=2)
        data = []

        for i in range(100):  # 100 data points
            record = {
                "timestamp": base_time + timedelta(minutes=i),
                "Load1": 0.5 + (i * 0.01),  # Gradually increasing load
                "Load5": 0.4 + (i * 0.008),
                "Load15": 0.3 + (i * 0.006),
            }
            data.append(record)

        return data

    def get_column_stats(self):
        """Mock column stats."""
        return {
            "Load1": {"count": len(self.sample_data), "mean": 1.0},
            "Load5": {"count": len(self.sample_data), "mean": 0.8},
            "Load15": {"count": len(self.sample_data), "mean": 0.6},
        }

    def get_resource_column(self, column, start_time, end_time):
        """Mock get_resource_column method like the real ResourceAnalyzer."""
        self.call_count += 1

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start_time is None or end_time is None or (start_time <= record["timestamp"] <= end_time):
                if column in record:
                    filtered_data.append(record)

        if not filtered_data:
            return {"timestamps": [], "values": []}

        # Extract timestamps and values for the requested column
        timestamps = [record["timestamp"].isoformat() for record in filtered_data]
        values = [record.get(column, 0) for record in filtered_data]

        return {"timestamps": timestamps, "values": values}

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        self.call_count += 1

        # If no sample data, return empty DataFrame
        if not self.sample_data:
            return pd.DataFrame()

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start is None or end is None or (start <= record["timestamp"] <= end):
                filtered_data.append(record)

        if not filtered_data:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(filtered_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        return df


class TestCpuLoadPipelineMigration:
    """Test CPU load data pipeline migration."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_resource_analyzer = MockResourceAnalyzer()
        self.mock_cross_analyzer = Mock()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        # Create DataSources and Cache instances for pipeline testing
        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )
        self.cache = VisualizationDataCache(max_cached_results=10)

        # Test time range
        self.start_time = datetime.now() - timedelta(hours=1)
        self.end_time = datetime.now()

    @pytest.mark.asyncio
    async def test_pipeline_output_format(self):
        """Test that pipeline output matches expected format."""
        result = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Check output structure
        assert isinstance(result, dict)
        assert "timestamps" in result
        assert "load1" in result
        assert "load5" in result
        assert "load15" in result

        # Check data types
        assert isinstance(result["timestamps"], list)
        assert isinstance(result["load1"], list)
        assert isinstance(result["load5"], list)
        assert isinstance(result["load15"], list)

        # Check data consistency
        if result["timestamps"]:
            timestamp_count = len(result["timestamps"])
            assert len(result["load1"]) == timestamp_count
            assert len(result["load5"]) == timestamp_count
            assert len(result["load15"]) == timestamp_count

    @pytest.mark.asyncio
    async def test_load_values_reasonable(self):
        """Test that load values are in reasonable ranges."""
        result = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Check that load values are reasonable (typically 0-10 for most systems)
            for load1 in result["load1"]:
                assert 0 <= load1 <= 20, f"load1 out of expected range: {load1}"

            for load5 in result["load5"]:
                assert 0 <= load5 <= 20, f"load5 out of expected range: {load5}"

            for load15 in result["load15"]:
                assert 0 <= load15 <= 20, f"load15 out of expected range: {load15}"

    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test handling of empty data."""
        # Create analyzer with no data
        empty_analyzer = MockResourceAnalyzer(sample_data=[])
        empty_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=empty_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        result = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=empty_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure
        assert result["timestamps"] == []
        assert result["load1"] == []
        assert result["load5"] == []
        assert result["load15"] == []

    @pytest.mark.asyncio
    async def test_cache_behavior(self):
        """Test that caching works correctly."""
        # First call - should cache result
        result1 = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_first = self.mock_resource_analyzer.call_count

        # Second call with same parameters - should use cache
        result2 = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_second = self.mock_resource_analyzer.call_count

        # Should not have made additional analyzer calls
        assert call_count_after_second == call_count_after_first

        # Results should be identical
        assert result1 == result2

    @pytest.mark.asyncio
    async def test_different_time_ranges(self):
        """Test that different time ranges produce different results."""
        # Reset call count to ensure clean test
        self.mock_resource_analyzer.call_count = 0

        # First call
        result1 = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_first = self.mock_resource_analyzer.call_count

        # Second call with different time range
        different_start = self.start_time + timedelta(hours=1)
        different_end = self.end_time + timedelta(hours=1)
        result2 = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=different_start, end_time=different_end
        )
        call_count_after_second = self.mock_resource_analyzer.call_count

        # Should have made additional analyzer call
        assert call_count_after_second > call_count_after_first

    def test_pipeline_construction(self):
        """Test pipeline construction and configuration."""
        pipeline = CpuLoadDataPipeline(
            cache=VisualizationDataCache(max_cached_results=10),
            method_name="get_cpu_load_data",
            start_time=self.start_time,
            end_time=self.end_time,
        )

        # Check pipeline properties
        assert pipeline.method_name == "get_cpu_load_data"
        assert pipeline.cache_params["start_time"] == self.start_time
        assert pipeline.cache_params["end_time"] == self.end_time

        # Test data sources injection
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        assert pipeline.data_sources == data_sources

    def test_pipeline_step_configuration(self):
        """Test pipeline step configuration."""
        pipeline = CpuLoadDataPipeline()
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        pipeline.build_cpu_load_pipeline(self.start_time, end_time=self.end_time)

        # Check that steps were added
        assert len(pipeline.steps) == 2
        assert pipeline.steps[0].name == "fetch_resource_metrics"
        assert pipeline.steps[1].name == "process_cpu_data"

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in pipeline."""
        # Mock analyzer to raise exception on get_resource_column method
        self.mock_resource_analyzer.get_resource_column = Mock(side_effect=Exception("Test error"))

        result = await CpuLoadDataPipeline.get_cpu_load_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure on error
        assert result["timestamps"] == []
        assert result["load1"] == []
        assert result["load5"] == []
        assert result["load15"] == []


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
