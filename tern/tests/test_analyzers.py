from __future__ import annotations

from datetime import datetime

import pandas as pd
import pytest

from log_parser.analysis.analyzer_factory import AnalyzerFactory
from log_parser.analysis.cross_analyzer import CrossAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.analysis.timeline_analyzer import TimelineAnalyzer
from log_parser.models import LogEntry


@pytest.fixture
def sample_logs():
    """Create sample log data for testing."""
    return {
        "resource": [
            LogEntry(
                timestamp=datetime.now(),
                level="INFO",
                message="Resource log message",
                resource={"type": "CPU", "usage": 80},
            )
        ],
        "minknow": [
            LogEntry(
                timestamp=datetime.now(),
                level="INFO",
                message="MinKNOW log message",
                action="start",
            )
        ],
    }


@pytest.fixture
def analyzers(sample_logs):
    """Create analyzer instances with sample data."""
    # Create analyzers using factory
    resource_analyzer = AnalyzerFactory.create_analyzer("resource", sample_logs["resource"])
    minknow_analyzer = AnalyzerFactory.create_analyzer("minknow", sample_logs["minknow"])
    cross_analyzer = CrossAnalyzer(resource_analyzer=resource_analyzer, minknow_analyzer=minknow_analyzer)
    timeline_analyzer = AnalyzerFactory.create_analyzer("timeline", sample_logs["minknow"])

    return {
        "resource": resource_analyzer,
        "minknow": minknow_analyzer,
        "cross": cross_analyzer,
        "timeline": timeline_analyzer,
    }


def test_resource_analyzer(analyzers):
    """Test ResourceAnalyzer functionality."""
    analyzer = analyzers["resource"]
    assert isinstance(analyzer, ResourceAnalyzer)
    # Test actual methods that exist
    assert analyzer.analyzer_type == "resource"
    time_range = analyzer.get_time_range()
    assert isinstance(time_range, tuple)
    assert len(time_range) == 2


def test_minknow_analyzer(analyzers):
    """Test StructuredLogAnalyzer functionality."""
    analyzer = analyzers["minknow"]
    assert isinstance(analyzer, StructuredLogAnalyzer)
    # Test actual methods that exist
    assert analyzer.analyzer_type == "minknow"
    time_range = analyzer.get_time_range()
    assert isinstance(time_range, tuple)
    assert len(time_range) == 2


def test_cross_analyzer(analyzers):
    """Test CrossAnalyzer functionality."""
    analyzer = analyzers["cross"]
    assert isinstance(analyzer, CrossAnalyzer)
    # Test actual methods that exist
    time_range = analyzer.get_common_time_range()
    assert isinstance(time_range, tuple)
    assert len(time_range) == 2


def test_timeline_analyzer(analyzers):
    """Test TimelineAnalyzer functionality."""
    analyzer = analyzers["timeline"]
    assert isinstance(analyzer, TimelineAnalyzer)
    # Test actual methods that exist
    assert analyzer.analyzer_type == "timeline"
    time_range = analyzer.get_time_range()
    assert isinstance(time_range, tuple)
    assert len(time_range) == 2


def test_analyzer_error_handling(analyzers):
    """Test error handling in analyzers."""
    analyzer = analyzers["resource"]
    # Test with empty dataframe
    analyzer._df = pd.DataFrame()
    time_range = analyzer.get_time_range()
    assert time_range == (None, None)
