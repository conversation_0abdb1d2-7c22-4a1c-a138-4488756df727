"""
Tests for I/O throughput data pipeline migration.

This module tests the migration of get_io_throughput_data() from the original implementation
to the pipeline pattern, ensuring output consistency and performance characteristics.
"""

import asyncio

# Add tern module to path
import sys
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio

tern_path = Path(__file__).parent.parent / "tern"
sys.path.insert(0, str(tern_path))

# Configure pytest for async tests
pytest_plugins = ("pytest_asyncio",)

from tern.core.cache import VisualizationDataCache
from tern.core.io_throughput_pipeline import IoThroughputDataPipeline
from tern.core.pipeline import DataSources


class MockResourceAnalyzer:
    """Mock resource analyzer for testing I/O throughput data."""

    def __init__(self, sample_data=None):
        self.sample_data = sample_data if sample_data is not None else self._generate_sample_data()
        self.call_count = 0

    def _generate_sample_data(self):
        """Generate realistic I/O throughput sample data for testing."""
        base_time = datetime.now() - timedelta(hours=2)
        data = []

        for i in range(50):  # 50 data points
            record = {
                "timestamp": base_time + timedelta(minutes=i * 2),
                # Block 0 - High activity device (main disk)
                "Block0Name": "/dev/sda",
                "Block0ReadBytesPerSecond": (10 + i) * 1024 * 1024,  # 10+ MB/s read
                "Block0WriteBytesPerSecond": (5 + i // 2) * 1024 * 1024,  # 5+ MB/s write
                # Block 1 - Medium activity device
                "Block1Name": "/dev/sdb",
                "Block1ReadBytesPerSecond": (2 + i // 4) * 1024 * 1024,  # 2+ MB/s read
                "Block1WriteBytesPerSecond": (1 + i // 8) * 1024 * 1024,  # 1+ MB/s write
                # Block 2 - Low activity device (should be grouped into "Other")
                "Block2Name": "/dev/sdc",
                "Block2ReadBytesPerSecond": 100 * 1024,  # 100 KB/s (below 5MB threshold)
                "Block2WriteBytesPerSecond": 50 * 1024,  # 50 KB/s
            }
            data.append(record)

        return data

    def get_column_stats(self):
        """Mock column stats."""
        return {
            "Block0ReadBytesPerSecond": {"count": len(self.sample_data), "mean": 10 * 1024 * 1024},
            "Block1ReadBytesPerSecond": {"count": len(self.sample_data), "mean": 2 * 1024 * 1024},
        }

    def get_resource_column(self, column, start_time, end_time):
        """Mock get_resource_column method like the real ResourceAnalyzer."""
        self.call_count += 1

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start_time is None or end_time is None or (start_time <= record["timestamp"] <= end_time):
                if column in record:
                    filtered_data.append(record)

        if not filtered_data:
            return {"timestamps": [], "values": []}

        # Extract timestamps and values for the requested column
        timestamps = [record["timestamp"].isoformat() for record in filtered_data]
        values = [record.get(column, 0) for record in filtered_data]

        return {"timestamps": timestamps, "values": values}

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        self.call_count += 1

        # If no sample data, return empty DataFrame
        if not self.sample_data:
            return pd.DataFrame()

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start is None or end is None or (start <= record["timestamp"] <= end):
                filtered_data.append(record)

        if not filtered_data:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(filtered_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        return df


class TestIoThroughputPipelineMigration:
    """Test I/O throughput data pipeline migration."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_resource_analyzer = MockResourceAnalyzer()
        self.mock_cross_analyzer = Mock()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        # Create DataSources and Cache instances for pipeline testing
        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )
        self.cache = VisualizationDataCache(max_cached_results=10)

        # Test time range
        self.start_time = datetime.now() - timedelta(hours=1)
        self.end_time = datetime.now()

    @pytest.mark.asyncio
    async def test_pipeline_output_format(self):
        """Test that pipeline output matches expected format."""
        result = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Check output structure
        assert isinstance(result, dict)
        assert "timestamps" in result
        assert "read_throughput" in result
        assert "write_throughput" in result

        # Check data types
        assert isinstance(result["timestamps"], list)
        assert isinstance(result["read_throughput"], dict)
        assert isinstance(result["write_throughput"], dict)

        # Check that throughput data contains expected devices
        if result["timestamps"]:
            # Should have high-activity devices individually
            assert "/dev/sda" in result["read_throughput"]  # High activity
            assert "/dev/sdb" in result["read_throughput"]  # Medium activity
            assert "Other (Read)" in result["read_throughput"]  # Low activity grouped

            assert "/dev/sda" in result["write_throughput"]
            assert "/dev/sdb" in result["write_throughput"]
            assert "Other (Write)" in result["write_throughput"]

            # Each device should have same length as timestamps
            timestamp_count = len(result["timestamps"])
            for device, values in result["read_throughput"].items():
                assert len(values) == timestamp_count
            for device, values in result["write_throughput"].items():
                assert len(values) == timestamp_count

    @pytest.mark.asyncio
    async def test_low_activity_grouping(self):
        """Test that low-activity devices are grouped into 'Other' category."""
        # Use default 5MB/s threshold
        result = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time, low_activity_threshold_mb=5.0
        )

        if result["timestamps"]:
            # /dev/sdc should be grouped into "Other" since it's <5MB/s
            assert "/dev/sdc" not in result["read_throughput"]
            assert "/dev/sdc" not in result["write_throughput"]
            assert "Other (Read)" in result["read_throughput"]
            assert "Other (Write)" in result["write_throughput"]

            # High-activity devices should be separate
            assert "/dev/sda" in result["read_throughput"]
            assert "/dev/sdb" in result["read_throughput"]

    @pytest.mark.asyncio
    async def test_throughput_values_reasonable(self):
        """Test that throughput values are reasonable."""
        result = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Check that throughput values are reasonable (>= 0 MB/s)
            for device, values in result["read_throughput"].items():
                for throughput in values:
                    assert throughput >= 0, f"Read throughput negative for {device}: {throughput}"

            for device, values in result["write_throughput"].items():
                for throughput in values:
                    assert throughput >= 0, f"Write throughput negative for {device}: {throughput}"

    @pytest.mark.asyncio
    async def test_unit_conversion(self):
        """Test that bytes/sec are correctly converted to MB/s."""
        result = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # /dev/sda should have ~10+ MB/s read (from 10+ MB/s in bytes)
            if "/dev/sda" in result["read_throughput"]:
                sda_read = result["read_throughput"]["/dev/sda"]
                if sda_read:
                    # Should be around 10+ MB/s (converted from bytes/sec)
                    assert any(val >= 10.0 for val in sda_read), f"Expected /dev/sda read throughput >= 10 MB/s, got {sda_read[:5]}"

    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test handling of empty data."""
        # Create analyzer with no data
        empty_analyzer = MockResourceAnalyzer(sample_data=[])
        empty_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=empty_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        result = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=empty_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure
        assert result["timestamps"] == []
        assert result["read_throughput"] == {}
        assert result["write_throughput"] == {}

    @pytest.mark.asyncio
    async def test_cache_behavior(self):
        """Test that caching works correctly."""
        # First call - should cache result
        result1 = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_first = self.mock_resource_analyzer.call_count

        # Second call with same parameters - should use cache
        result2 = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_second = self.mock_resource_analyzer.call_count

        # Should not have made additional analyzer calls
        assert call_count_after_second == call_count_after_first

        # Results should be identical
        assert result1 == result2

    @pytest.mark.asyncio
    async def test_different_thresholds(self):
        """Test that different thresholds produce different groupings."""
        # Reset call count
        self.mock_resource_analyzer.call_count = 0

        # First call with 5MB/s threshold
        result1 = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time, low_activity_threshold_mb=5.0
        )

        # Second call with 1MB/s threshold (should group fewer devices)
        result2 = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time, low_activity_threshold_mb=1.0
        )

        # Should produce different results - with 1MB threshold, more devices should be separate
        if result1["timestamps"] and result2["timestamps"]:
            # With 1MB threshold, /dev/sdb (2+ MB/s) should still be separate
            assert "/dev/sdb" in result1["read_throughput"]
            assert "/dev/sdb" in result2["read_throughput"]

    def test_pipeline_construction(self):
        """Test pipeline construction and configuration."""
        pipeline = IoThroughputDataPipeline(
            cache=VisualizationDataCache(max_cached_results=10),
            method_name="get_io_throughput_data",
            start_time=self.start_time,
            end_time=self.end_time,
            low_activity_threshold_mb=5.0,
        )

        # Check pipeline properties
        assert pipeline.method_name == "get_io_throughput_data"
        assert pipeline.cache_params["start_time"] == self.start_time
        assert pipeline.cache_params["end_time"] == self.end_time
        assert pipeline.cache_params["low_activity_threshold_mb"] == 5.0

        # Test data sources injection
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        assert pipeline.data_sources == data_sources

    def test_pipeline_step_configuration(self):
        """Test pipeline step configuration."""
        pipeline = IoThroughputDataPipeline()
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        pipeline.build_io_throughput_pipeline(self.start_time, self.end_time, 5.0)

        # Check that steps were added
        assert len(pipeline.steps) == 3
        assert pipeline.steps[0].name == "discover_block_columns"
        assert pipeline.steps[1].name == "fetch_io_metrics"
        assert pipeline.steps[2].name == "process_io_throughput"

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in pipeline."""
        # Mock analyzer to raise exception on query method (used by bulk fetch)
        self.mock_resource_analyzer.query = Mock(side_effect=Exception("Test error"))

        result = await IoThroughputDataPipeline.get_io_throughput_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure on error
        assert result["timestamps"] == []
        assert result["read_throughput"] == {}
        assert result["write_throughput"] == {}


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
