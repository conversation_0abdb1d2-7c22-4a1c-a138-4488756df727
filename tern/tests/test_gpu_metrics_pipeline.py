"""
Tests for GPU metrics data pipeline migration.

This module tests the migration of get_gpu_metrics_data() from the original implementation
to the pipeline pattern, ensuring output consistency and performance characteristics.
"""

import asyncio

# Add tern module to path
import sys
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio

tern_path = Path(__file__).parent.parent / "tern"
sys.path.insert(0, str(tern_path))

# Configure pytest for async tests
pytest_plugins = ("pytest_asyncio",)

from tern.core.cache import VisualizationDataCache
from tern.core.gpu_metrics_pipeline import GpuMetricsDataPipeline
from tern.core.pipeline import DataSources


class MockResourceAnalyzer:
    """Mock resource analyzer for testing GPU metrics data."""

    def __init__(self, sample_data=None, multi_gpu=True):
        self.sample_data = sample_data if sample_data is not None else self._generate_sample_data(multi_gpu)
        self.call_count = 0
        self.multi_gpu = multi_gpu

    def _generate_sample_data(self, multi_gpu=True):
        """Generate realistic GPU metrics sample data for testing."""
        base_time = datetime.now() - timedelta(hours=2)
        data = []

        for i in range(30):  # 30 data points
            record = {
                "timestamp": base_time + timedelta(minutes=i * 2),
            }

            if multi_gpu:
                # Multi-GPU case: GPU 0 and GPU 1
                record.update(
                    {
                        "gpu0UtilizationPercent": 80 + (i % 20),  # 80-100% utilization
                        "gpu0Temperature": 70 + (i % 15),  # 70-85°C
                        "gpu0PowerDrawWatts": 200 + (i % 50),  # 200-250W
                        "gpu0MemoryTotalMB": 8192,  # 8GB
                        "gpu0MemoryFreeMB": 2048 - (i * 10),  # Decreasing free memory
                        "gpu1UtilizationPercent": 60 + (i % 30),  # 60-90% utilization
                        "gpu1Temperature": 65 + (i % 10),  # 65-75°C
                        "gpu1PowerDrawWatts": 180 + (i % 40),  # 180-220W
                        "gpu1MemoryTotalMB": 8192,  # 8GB
                        "gpu1MemoryFreeMB": 3072 - (i * 15),  # Decreasing free memory
                    }
                )
            else:
                # Single GPU case
                record.update(
                    {
                        "UtilizationPercent": 75 + (i % 25),  # 75-100% utilization
                        "Temperature": 68 + (i % 12),  # 68-80°C
                        "PowerDrawWatts": 190 + (i % 60),  # 190-250W
                        "MemoryTotalMB": 8192,  # 8GB
                        "MemoryFreeMB": 1024 - (i * 20),  # Decreasing free memory
                    }
                )

            data.append(record)

        return data

    def get_column_stats(self):
        """Mock column stats."""
        if self.multi_gpu:
            return {
                "gpu0UtilizationPercent": {"count": len(self.sample_data), "mean": 90.0},
                "gpu1UtilizationPercent": {"count": len(self.sample_data), "mean": 75.0},
            }
        else:
            return {
                "UtilizationPercent": {"count": len(self.sample_data), "mean": 87.5},
            }

    def get_resource_column(self, column, start_time, end_time):
        """Mock get_resource_column method like the real ResourceAnalyzer."""
        self.call_count += 1

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start_time is None or end_time is None or (start_time <= record["timestamp"] <= end_time):
                if column in record:
                    filtered_data.append(record)

        if not filtered_data:
            return {"timestamps": [], "values": []}

        # Extract timestamps and values for the requested column
        timestamps = [record["timestamp"].isoformat() for record in filtered_data]
        values = [record.get(column, 0) for record in filtered_data]

        return {"timestamps": timestamps, "values": values}

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        self.call_count += 1

        # If no sample data, return empty DataFrame
        if not self.sample_data:
            return pd.DataFrame()

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start is None or end is None or (start <= record["timestamp"] <= end):
                filtered_data.append(record)

        if not filtered_data:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(filtered_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        return df


class TestGpuMetricsPipelineMigration:
    """Test GPU metrics data pipeline migration."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_resource_analyzer = MockResourceAnalyzer(multi_gpu=True)
        self.mock_cross_analyzer = Mock()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        # Create DataSources and Cache instances for pipeline testing
        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )
        self.cache = VisualizationDataCache(max_cached_results=10)

        # Test time range
        self.start_time = datetime.now() - timedelta(hours=1)
        self.end_time = datetime.now()

    @pytest.mark.asyncio
    async def test_pipeline_output_format(self):
        """Test that pipeline output matches expected format."""
        result = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Check output structure
        assert isinstance(result, dict)
        assert "timestamps" in result
        assert "utilization" in result
        assert "memory_used_pct" in result
        assert "temperature" in result
        assert "power_draw" in result

        # Check data types
        assert isinstance(result["timestamps"], list)
        assert isinstance(result["utilization"], dict)
        assert isinstance(result["memory_used_pct"], dict)
        assert isinstance(result["temperature"], dict)
        assert isinstance(result["power_draw"], dict)

        # Check that GPU data contains expected GPUs
        if result["timestamps"]:
            # Should have GPU 0 and GPU 1 for multi-GPU case
            assert "GPU 0" in result["utilization"]
            assert "GPU 1" in result["utilization"]
            assert "GPU 0" in result["memory_used_pct"]
            assert "GPU 1" in result["memory_used_pct"]
            assert "GPU 0" in result["temperature"]
            assert "GPU 1" in result["temperature"]
            assert "GPU 0" in result["power_draw"]
            assert "GPU 1" in result["power_draw"]

            # Each GPU should have same length as timestamps
            timestamp_count = len(result["timestamps"])
            for gpu, values in result["utilization"].items():
                assert len(values) == timestamp_count
            for gpu, values in result["memory_used_pct"].items():
                assert len(values) == timestamp_count
            for gpu, values in result["temperature"].items():
                assert len(values) == timestamp_count
            for gpu, values in result["power_draw"].items():
                assert len(values) == timestamp_count

    @pytest.mark.asyncio
    async def test_single_gpu_case(self):
        """Test single GPU case with different column naming."""
        # Create single GPU analyzer
        single_gpu_analyzer = MockResourceAnalyzer(multi_gpu=False)
        single_gpu_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=single_gpu_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        result = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=single_gpu_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Should have single "GPU" entry
            assert "GPU" in result["utilization"]
            assert "GPU" in result["memory_used_pct"]
            assert "GPU" in result["temperature"]
            assert "GPU" in result["power_draw"]

            # Should not have numbered GPUs
            assert "GPU 0" not in result["utilization"]
            assert "GPU 1" not in result["utilization"]

    @pytest.mark.asyncio
    async def test_metric_value_ranges(self):
        """Test that metric values are in reasonable ranges."""
        result = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Check utilization percentages (0-100%)
            for gpu, values in result["utilization"].items():
                for util in values:
                    assert 0 <= util <= 100, f"Utilization out of range for {gpu}: {util}"

            # Check memory usage percentages (0-100%)
            for gpu, values in result["memory_used_pct"].items():
                for mem_pct in values:
                    assert 0 <= mem_pct <= 100, f"Memory usage out of range for {gpu}: {mem_pct}"

            # Check temperatures (reasonable range)
            for gpu, values in result["temperature"].items():
                for temp in values:
                    assert 0 <= temp <= 120, f"Temperature out of range for {gpu}: {temp}"

            # Check power draw (reasonable range)
            for gpu, values in result["power_draw"].items():
                for power in values:
                    assert 0 <= power <= 500, f"Power draw out of range for {gpu}: {power}"

    @pytest.mark.asyncio
    async def test_memory_percentage_calculation(self):
        """Test that memory usage percentage is calculated correctly."""
        result = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Memory percentage should be calculated as (total - free) / total * 100
            # For our test data: total=8192MB, decreasing free memory
            # Should result in increasing memory usage percentages
            for gpu, values in result["memory_used_pct"].items():
                if len(values) >= 2:
                    # Memory usage should generally increase over time in our test data
                    # (since free memory decreases)
                    first_half_avg = sum(values[: len(values) // 2]) / (len(values) // 2)
                    second_half_avg = sum(values[len(values) // 2 :]) / (len(values) - len(values) // 2)
                    assert second_half_avg >= first_half_avg, f"Expected increasing memory usage for {gpu}"

    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test handling of empty data."""
        # Create analyzer with no data
        empty_analyzer = MockResourceAnalyzer(sample_data=[])
        empty_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=empty_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        result = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=empty_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure
        assert result["timestamps"] == []
        assert result["utilization"] == {}
        assert result["memory_used_pct"] == {}
        assert result["temperature"] == {}
        assert result["power_draw"] == {}

    @pytest.mark.asyncio
    async def test_cache_behavior(self):
        """Test that caching works correctly."""
        # First call - should cache result
        result1 = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Second call with same parameters - should use cache
        result2 = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Results should be identical
        assert result1 == result2

        # Check that we got valid results
        if result1["timestamps"]:
            assert len(result1["timestamps"]) == len(result2["timestamps"])
            assert result1["utilization"] == result2["utilization"]

    def test_pipeline_construction(self):
        """Test pipeline construction and configuration."""
        pipeline = GpuMetricsDataPipeline(
            cache=VisualizationDataCache(max_cached_results=10),
            method_name="get_gpu_metrics_data",
            start_time=self.start_time,
            end_time=self.end_time,
        )

        # Check pipeline properties
        assert pipeline.method_name == "get_gpu_metrics_data"
        assert pipeline.cache_params["start_time"] == self.start_time
        assert pipeline.cache_params["end_time"] == self.end_time

        # Test data sources injection
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        assert pipeline.data_sources == data_sources

    def test_pipeline_step_configuration(self):
        """Test pipeline step configuration."""
        pipeline = GpuMetricsDataPipeline()
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        pipeline.build_gpu_metrics_pipeline(self.start_time, end_time=self.end_time)

        # Check that steps were added
        assert len(pipeline.steps) == 3
        assert pipeline.steps[0].name == "discover_gpu_columns"
        assert pipeline.steps[1].name == "fetch_gpu_metrics"
        assert pipeline.steps[2].name == "process_gpu_metrics"

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in pipeline."""
        # Mock analyzer to raise exception on get_resource_column method
        self.mock_resource_analyzer.get_resource_column = Mock(side_effect=Exception("Test error"))

        result = await GpuMetricsDataPipeline.get_gpu_metrics_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure on error
        assert result["timestamps"] == []
        assert result["utilization"] == {}
        assert result["memory_used_pct"] == {}
        assert result["temperature"] == {}
        assert result["power_draw"] == {}


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
