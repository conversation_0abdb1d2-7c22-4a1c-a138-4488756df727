from __future__ import annotations

import pytest
from dependency_injector import containers, providers

from log_parser.analysis.analyzer_factory import AnalyzerFactory
from log_parser.analysis.cross_analyzer import CrossAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.analysis.timeline_analyzer import TimelineAnalyzer


class MockContainer(containers.DynamicContainer):
    """Mock container for dependency injection testing."""

    config = providers.Configuration()

    # Create analyzers using factory
    resource_analyzer = providers.Singleton(lambda: AnalyzerFactory.create_analyzer("resource", []))
    minknow_analyzer = providers.Singleton(lambda: AnalyzerFactory.create_analyzer("minknow", []))
    cross_analyzer = providers.Singleton(CrossAnalyzer, resource_analyzer=resource_analyzer, minknow_analyzer=minknow_analyzer)
    timeline_analyzer = providers.Singleton(lambda: AnalyzerFactory.create_analyzer("timeline", []))


@pytest.fixture
def container():
    """Create a test container instance."""
    container = MockContainer()
    return container


def test_container_initialization(container):
    """Test that the container initializes correctly."""
    assert isinstance(container, containers.DynamicContainer)  # nosec B101
    assert isinstance(container.resource_analyzer(), ResourceAnalyzer)  # nosec B101
    assert isinstance(container.minknow_analyzer(), StructuredLogAnalyzer)  # nosec B101
    assert isinstance(container.cross_analyzer(), CrossAnalyzer)  # nosec B101
    assert isinstance(container.timeline_analyzer(), TimelineAnalyzer)  # nosec B101


def test_container_providers(container):
    """Test that all providers are properly configured."""
    assert isinstance(container.resource_analyzer, providers.Singleton)  # nosec B101
    assert isinstance(container.minknow_analyzer, providers.Singleton)  # nosec B101
    assert isinstance(container.cross_analyzer, providers.Singleton)  # nosec B101
    assert isinstance(container.timeline_analyzer, providers.Singleton)  # nosec B101


def test_container_dependency_injection():
    """Test that dependency injection works correctly."""

    # Create a test class that uses dependency injection
    class TestClass:
        def __init__(
            self,
            resource_analyzer: ResourceAnalyzer,
            minknow_analyzer: StructuredLogAnalyzer,
            cross_analyzer: CrossAnalyzer,
            timeline_analyzer: TimelineAnalyzer,
        ):
            self.resource_analyzer = resource_analyzer
            self.minknow_analyzer = minknow_analyzer
            self.cross_analyzer = cross_analyzer
            self.timeline_analyzer = timeline_analyzer

    # Create container and wire it
    container = MockContainer()

    # Create test instance with dependencies
    test_instance = TestClass(
        resource_analyzer=container.resource_analyzer(),
        minknow_analyzer=container.minknow_analyzer(),
        cross_analyzer=container.cross_analyzer(),
        timeline_analyzer=container.timeline_analyzer(),
    )

    # Verify all dependencies are injected correctly
    assert isinstance(test_instance.resource_analyzer, ResourceAnalyzer)  # nosec B101
    assert isinstance(test_instance.minknow_analyzer, StructuredLogAnalyzer)  # nosec B101
    assert isinstance(test_instance.cross_analyzer, CrossAnalyzer)  # nosec B101
    assert isinstance(test_instance.timeline_analyzer, TimelineAnalyzer)  # nosec B101
