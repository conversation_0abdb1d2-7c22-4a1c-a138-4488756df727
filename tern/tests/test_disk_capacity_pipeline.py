"""
Tests for disk capacity data pipeline migration.

This module tests the migration of get_disk_capacity_data() from the original implementation
to the pipeline pattern, ensuring output consistency and performance characteristics.
"""

import asyncio

# Add tern module to path
import sys
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio

tern_path = Path(__file__).parent.parent / "tern"
sys.path.insert(0, str(tern_path))

# Configure pytest for async tests
pytest_plugins = ("pytest_asyncio",)

from tern.core.cache import VisualizationDataCache
from tern.core.disk_capacity_pipeline import DiskCapacityDataPipeline
from tern.core.pipeline import DataSources


class MockResourceAnalyzer:
    """Mock resource analyzer for testing disk capacity data."""

    def __init__(self, sample_data=None):
        self.sample_data = sample_data if sample_data is not None else self._generate_sample_data()
        self.call_count = 0

    def _generate_sample_data(self):
        """Generate realistic disk capacity sample data for testing."""
        base_time = datetime.now() - timedelta(hours=2)
        data = []

        for i in range(50):  # 50 data points
            record = {
                "timestamp": base_time + timedelta(minutes=i * 2),
                # Disk 0 - Large disk (root partition)
                "Disk0TotalBytes": 1000 * 1024**3,  # 1TB
                "Disk0FreeBytes": (800 - i) * 1024**3,  # Gradually decreasing free space
                "Disk0Mountpoint": "/",
                # Disk 1 - Medium disk (home partition)
                "Disk1TotalBytes": 500 * 1024**3,  # 500GB
                "Disk1FreeBytes": (300 - i // 2) * 1024**3,
                "Disk1Mountpoint": "/home",
                # Disk 2 - Small disk (should be grouped into "Other")
                "Disk2TotalBytes": 512 * 1024**2,  # 512MB (below 1GB threshold)
                "Disk2FreeBytes": (400 - i) * 1024**2,
                "Disk2Mountpoint": "/tmp",
            }
            data.append(record)

        return data

    def get_column_stats(self):
        """Mock column stats."""
        return {
            "Disk0TotalBytes": {"count": len(self.sample_data), "mean": 1000 * 1024**3},
            "Disk1TotalBytes": {"count": len(self.sample_data), "mean": 500 * 1024**3},
        }

    def get_resource_column(self, column, start_time, end_time):
        """Mock get_resource_column method like the real ResourceAnalyzer."""
        self.call_count += 1

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start_time is None or end_time is None or (start_time <= record["timestamp"] <= end_time):
                if column in record:
                    filtered_data.append(record)

        if not filtered_data:
            return {"timestamps": [], "values": []}

        # Extract timestamps and values for the requested column
        timestamps = [record["timestamp"].isoformat() for record in filtered_data]
        values = [record.get(column, 0) for record in filtered_data]

        return {"timestamps": timestamps, "values": values}

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        self.call_count += 1

        # If no sample data, return empty DataFrame
        if not self.sample_data:
            return pd.DataFrame()

        # Filter data by time range
        filtered_data = []
        for record in self.sample_data:
            if start is None or end is None or (start <= record["timestamp"] <= end):
                filtered_data.append(record)

        if not filtered_data:
            return pd.DataFrame()

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(filtered_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        return df


class TestDiskCapacityPipelineMigration:
    """Test disk capacity data pipeline migration."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_resource_analyzer = MockResourceAnalyzer()
        self.mock_cross_analyzer = Mock()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        # Create DataSources and Cache instances for pipeline testing
        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )
        self.cache = VisualizationDataCache(max_cached_results=10)

        # Test time range
        self.start_time = datetime.now() - timedelta(hours=1)
        self.end_time = datetime.now()

    @pytest.mark.asyncio
    async def test_pipeline_output_format(self):
        """Test that pipeline output matches expected format."""
        result = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Check output structure
        assert isinstance(result, dict)
        assert "timestamps" in result
        assert "mount_points" in result

        # Check data types
        assert isinstance(result["timestamps"], list)
        assert isinstance(result["mount_points"], dict)

        # Check that mount_points contains expected mount points
        if result["timestamps"]:
            # Should have at least the large mounts (not small ones)
            assert "/" in result["mount_points"]  # Root partition
            assert "/home" in result["mount_points"]  # Home partition
            assert "Other" in result["mount_points"]  # Small mounts grouped

            # Each mount point should have same length as timestamps
            timestamp_count = len(result["timestamps"])
            for mount_point, values in result["mount_points"].items():
                assert len(values) == timestamp_count

    @pytest.mark.asyncio
    async def test_small_mount_grouping(self):
        """Test that small mounts are grouped into 'Other' category."""
        # Use default 1GB threshold
        result = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time, small_mount_threshold_gb=1.0
        )

        if result["timestamps"]:
            # /tmp should be grouped into "Other" since it's 512MB (< 1GB)
            assert "/tmp" not in result["mount_points"]
            assert "Other" in result["mount_points"]

            # Large mounts should be separate
            assert "/" in result["mount_points"]
            assert "/home" in result["mount_points"]

    @pytest.mark.asyncio
    async def test_usage_percentage_calculation(self):
        """Test that usage percentages are calculated correctly."""
        result = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        if result["timestamps"]:
            # Check that usage percentages are reasonable (0-100)
            for mount_point, values in result["mount_points"].items():
                for usage_percent in values:
                    assert 0 <= usage_percent <= 100, f"Usage percent out of range for {mount_point}: {usage_percent}"

    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test handling of empty data."""
        # Create analyzer with no data
        empty_analyzer = MockResourceAnalyzer(sample_data=[])
        empty_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=empty_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        result = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=empty_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure
        assert result["timestamps"] == []
        assert result["mount_points"] == {}

    @pytest.mark.asyncio
    async def test_cache_behavior(self):
        """Test that caching works correctly."""
        # First call - should cache result
        result1 = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_first = self.mock_resource_analyzer.call_count

        # Second call with same parameters - should use cache
        result2 = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )
        call_count_after_second = self.mock_resource_analyzer.call_count

        # Should not have made additional analyzer calls
        assert call_count_after_second == call_count_after_first

        # Results should be identical
        assert result1 == result2

    @pytest.mark.asyncio
    async def test_different_thresholds(self):
        """Test that different thresholds produce different groupings."""
        # Reset call count
        self.mock_resource_analyzer.call_count = 0

        # First call with 1GB threshold
        result1 = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time, small_mount_threshold_gb=1.0
        )

        # Second call with 10GB threshold (should group /home into Other too)
        result2 = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time, small_mount_threshold_gb=10.0
        )

        # Should produce different results
        if result1["timestamps"] and result2["timestamps"]:
            # With 10GB threshold, /home (500GB) should still be separate
            assert "/home" in result1["mount_points"]
            assert "/home" in result2["mount_points"]

    def test_pipeline_construction(self):
        """Test pipeline construction and configuration."""
        pipeline = DiskCapacityDataPipeline(
            cache=VisualizationDataCache(max_cached_results=10),
            method_name="get_disk_capacity_data",
            start_time=self.start_time,
            end_time=self.end_time,
            small_mount_threshold_gb=1.0,
        )

        # Check pipeline properties
        assert pipeline.method_name == "get_disk_capacity_data"
        assert pipeline.cache_params["start_time"] == self.start_time
        assert pipeline.cache_params["end_time"] == self.end_time
        assert pipeline.cache_params["small_mount_threshold_gb"] == 1.0

        # Test data sources injection
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        assert pipeline.data_sources == data_sources

    def test_pipeline_step_configuration(self):
        """Test pipeline step configuration."""
        pipeline = DiskCapacityDataPipeline()
        data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

        pipeline.inject_sources(data_sources)
        pipeline.build_disk_capacity_pipeline(self.start_time, self.end_time, 1.0)

        # Check that steps were added
        assert len(pipeline.steps) == 3
        assert pipeline.steps[0].name == "discover_disk_columns"
        assert pipeline.steps[1].name == "fetch_disk_metrics"
        assert pipeline.steps[2].name == "process_disk_capacity"

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in pipeline."""
        # Mock analyzer to raise exception on query method (used by bulk fetch)
        self.mock_resource_analyzer.query = Mock(side_effect=Exception("Test error"))

        result = await DiskCapacityDataPipeline.get_disk_capacity_data(
            cache=self.cache, sources=self.data_sources, start_time=self.start_time, end_time=self.end_time
        )

        # Should return empty structure on error
        assert result["timestamps"] == []
        assert result["mount_points"] == {}


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
