"""
Tests for the pipeline infrastructure.

This module tests the core pipeline classes, DataSources wrapper,
and transformation functions in isolation.
"""

import asyncio
from copy import deepcopy
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, Mock, patch

import pytest
import pytest_asyncio
from tern.core.pipeline import (
    CachedTransformationPipeline,
    DataSources,
    PipelineExecutionError,
    PipelineValidationError,
    TestableTransformationPipeline,
)
from tern.core.pipeline.base_pipeline import PipelineStep
from tern.core.pipeline.transformations import BaseTransformations


class MockCache:
    """Mock cache for testing."""

    def __init__(self):
        self.cache = {}

    def get(self, key):
        return self.cache.get(key)

    def put(self, key, value):
        self.cache[key] = value

    def get_cached_transformation(self, method_name, start_time, end_time):
        """Mock the VisualizationDataCache interface."""
        # Simple mock: if we have any cached data for this method, return it
        for key, value in self.cache.items():
            if value and isinstance(value, dict):
                return value
        return None

    def add_to_cache(self, query_key, result, method_name, start_time, end_time):
        """Mock the VisualizationDataCache interface."""
        self.cache[query_key] = result


class MockAnalyzer:
    """Mock analyzer for testing."""

    def __init__(self):
        self.call_count = 0

    def get_column_stats(self):
        self.call_count += 1
        return {"TestColumn": {"count": 100, "mean": 50.0}}

    def get_resource_data_for_columns(self, columns, start_time, end_time, limit=5000, offset=0):
        self.call_count += 1
        return [{"timestamp": datetime.now(), "TestColumn": 42}, {"timestamp": datetime.now() + timedelta(seconds=1), "TestColumn": 43}]

    def query(self, start=None, end=None, **filters):
        """Mock query method that returns a pandas DataFrame like the real ResourceAnalyzer."""
        import pandas as pd

        # Create sample data
        sample_data = [
            {"timestamp": datetime.now(), "TestColumn": 42},
            {"timestamp": datetime.now() + timedelta(seconds=1), "TestColumn": 43},
        ]

        # Create DataFrame and set timestamp as index (like real ResourceAnalyzer)
        df = pd.DataFrame.from_records(sample_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)

        self.call_count += 1
        return df


class TestPipelineStep:
    """Test individual pipeline steps."""

    def test_pipeline_step_construction(self):
        """Test step building without execution."""

        def dummy_func(data, multiplier=1):
            return data * multiplier

        step = PipelineStep("test_step", dummy_func, (2,), {"multiplier": 3})

        assert step.name == "test_step"
        assert step.func == dummy_func
        assert step.args == (2,)
        assert step.kwargs == {"multiplier": 3}
        assert not step.executed
        assert step.result is None

    def test_pipeline_step_execution(self):
        """Test step execution."""

        def add_func(data, value):
            return data + value

        step = PipelineStep("add_step", add_func, (10,))
        result = step.execute(5)

        assert result == 15
        assert step.executed
        assert step.result == 15

    def test_pipeline_step_execution_error(self):
        """Test step execution with error."""

        def error_func(data):
            raise ValueError("Test error")

        step = PipelineStep("error_step", error_func)

        with pytest.raises(PipelineExecutionError) as exc_info:
            step.execute(5)

        assert "error_step" in str(exc_info.value)
        assert "Test error" in str(exc_info.value)


class TestCachedTransformationPipeline:
    """Test core pipeline functionality."""

    def test_pipeline_construction(self):
        """Test pipeline construction."""
        cache = MockCache()
        pipeline = CachedTransformationPipeline(cache=cache, method_name="test_method", param1="value1")

        assert pipeline.cache == cache
        assert pipeline.method_name == "test_method"
        assert pipeline.cache_params == {"param1": "value1"}
        assert len(pipeline.steps) == 0
        assert not pipeline._executed

    def test_add_step(self):
        """Test adding steps to pipeline."""
        pipeline = CachedTransformationPipeline()

        def test_func(data):
            return data * 2

        result = pipeline.add_step("multiply", test_func)

        assert result == pipeline  # Should return self for chaining
        assert len(pipeline.steps) == 1
        assert pipeline.steps[0].name == "multiply"

    def test_add_step_after_execution_fails(self):
        """Test that adding steps after execution fails."""
        pipeline = CachedTransformationPipeline()
        pipeline._executed = True

        with pytest.raises(PipelineValidationError):
            pipeline.add_step("test", lambda x: x)

    def test_cache_key_generation(self):
        """Test cache key generation."""
        pipeline = CachedTransformationPipeline(method_name="test_method", param1="value1", param2=42)

        pipeline.add_step("step1", lambda x: x, "arg1")

        key1 = pipeline._generate_cache_key()

        # Same pipeline should generate same key
        key2 = pipeline._generate_cache_key()
        assert key1 == key2

        # Different parameters should generate different key
        pipeline2 = CachedTransformationPipeline(method_name="test_method", param1="value1", param2=43)  # Different value
        pipeline2.add_step("step1", lambda x: x, "arg1")

        key3 = pipeline2._generate_cache_key()
        assert key1 != key3

    def test_cache_integration(self):
        """Test cache checking and storing."""
        from datetime import datetime

        cache = MockCache()
        start_time = datetime.now()
        end_time = datetime.now()

        pipeline = CachedTransformationPipeline(cache=cache, method_name="test_method", start_time=start_time, end_time=end_time)

        # Should return None for cache miss
        result = pipeline._check_cache()
        assert result is None

        # Store something in cache
        test_data = {"result": "test"}
        pipeline._store_cache(test_data)

        # Should now return cached data
        cached_result = pipeline._check_cache()
        assert cached_result == test_data

    def test_pipeline_validation(self):
        """Test pipeline validation."""
        pipeline = CachedTransformationPipeline()

        # Should fail with no steps
        with pytest.raises(PipelineValidationError, match="no steps"):
            pipeline._validate_pipeline()

        # Should fail with no data sources
        pipeline.add_step("test", lambda x: x)
        with pytest.raises(PipelineValidationError, match="no data sources"):
            pipeline._validate_pipeline()

        # Should pass with steps and data sources
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)
        pipeline._validate_pipeline()  # Should not raise

    @pytest.mark.asyncio
    async def test_pipeline_execution(self):
        """Test pipeline execution."""
        pipeline = CachedTransformationPipeline()
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)

        # Add some steps
        pipeline.add_step("multiply", lambda x: (x or 1) * 2)
        pipeline.add_step("add", lambda x: x + 10)

        result = await pipeline.execute()

        assert result == 12  # (1 * 2) + 10
        assert pipeline._executed
        assert pipeline._result == 12

    @pytest.mark.asyncio
    async def test_pipeline_execution_with_cache_hit(self):
        """Test pipeline execution with cache hit."""
        cache = MockCache()
        cache.cache["test_key"] = "cached_result"

        pipeline = CachedTransformationPipeline(cache=cache, method_name="test", start_time=datetime.now(), end_time=datetime.now())
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)

        # Mock the cache check to return cached result
        pipeline._check_cache = Mock(return_value="cached_result")

        result = await pipeline.execute()

        assert result == "cached_result"
        assert pipeline._executed
        assert len(pipeline.steps) == 0  # No steps should be executed

    @pytest.mark.asyncio
    async def test_pipeline_reset(self):
        """Test pipeline reset functionality."""
        pipeline = CachedTransformationPipeline()
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)

        pipeline.add_step("test", lambda x: x or 42)
        await pipeline.execute()

        assert pipeline._executed
        assert pipeline._result == 42

        # Reset pipeline
        pipeline.reset()

        assert not pipeline._executed
        assert pipeline._result is None
        assert not pipeline.steps[0].executed

    def test_pipeline_clone(self):
        """Test pipeline cloning."""
        cache = MockCache()
        pipeline = CachedTransformationPipeline(cache=cache, method_name="test", param1="value1")

        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)
        pipeline.add_step("test", lambda x: x, "arg1")

        # Clone with new parameters
        cloned = pipeline.clone(param2="value2")

        assert cloned.cache == cache
        assert cloned.method_name == "test"
        assert cloned.cache_params == {"param1": "value1", "param2": "value2"}
        assert len(cloned.steps) == 1
        assert cloned.data_sources == mock_sources
        assert not cloned._executed  # Should not be executed


class TestTestableTransformationPipeline:
    """Test enhanced pipeline for debugging."""

    def test_debug_mode_construction(self):
        """Test construction with debug mode."""
        pipeline = TestableTransformationPipeline(debug_mode=True)

        assert pipeline.debug_mode
        assert pipeline.debug_info is not None
        assert len(pipeline._step_results) == 0

    def test_step_by_step_execution(self):
        """Test step-by-step execution for debugging."""
        pipeline = TestableTransformationPipeline(debug_mode=True)
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)

        pipeline.add_step("multiply", lambda x: (x or 1) * 2)
        pipeline.add_step("add", lambda x: x + 10)
        pipeline.add_step("divide", lambda x: x / 2)

        step_results = pipeline.execute_step_by_step()

        assert len(step_results) == 3
        assert step_results[0] == 2  # 1 * 2
        assert step_results[1] == 12  # 2 + 10
        assert step_results[2] == 6  # 12 / 2

        # Test getting specific step results
        assert pipeline.get_step_result(0) == 2
        assert pipeline.get_step_result(1) == 12
        assert pipeline.get_step_result(2) == 6

    def test_get_step_result_validation(self):
        """Test step result access validation."""
        pipeline = TestableTransformationPipeline()

        # Should fail if not executed
        with pytest.raises(PipelineExecutionError):
            pipeline.get_step_result(0)

        # Execute pipeline
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)
        pipeline.add_step("test", lambda x: x or 42)
        pipeline.execute()

        # Should fail for invalid index
        with pytest.raises(IndexError):
            pipeline.get_step_result(5)

    def test_debug_summary(self):
        """Test debug summary generation."""
        pipeline = TestableTransformationPipeline(debug_mode=True)
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)

        pipeline.add_step("test", lambda x: x or 42)
        pipeline.execute()

        summary = pipeline.get_debug_summary()

        assert "start_time" in summary
        assert "end_time" in summary
        assert "total_execution_time" in summary
        assert "step_count" in summary
        assert "step_details" in summary
        assert summary["step_count"] == 1
        assert len(summary["step_details"]) == 1

    def test_debug_reset(self):
        """Test reset with debug information."""
        pipeline = TestableTransformationPipeline(debug_mode=True)
        mock_sources = Mock()
        pipeline.inject_sources(mock_sources)

        pipeline.add_step("test", lambda x: x or 42)
        pipeline.execute()

        # Verify execution
        assert pipeline._executed
        assert len(pipeline._step_results) > 0
        assert pipeline.debug_info.start_time is not None

        # Reset
        pipeline.reset()

        # Verify reset
        assert not pipeline._executed
        assert len(pipeline._step_results) == 0
        assert pipeline.debug_info.start_time is None


class TestDataSources:
    """Test DataSources wrapper."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_cross_analyzer = Mock()
        self.mock_resource_analyzer = MockAnalyzer()
        self.mock_minknow_analyzer = Mock()
        self.mock_timeline_analyzer = Mock()

        self.data_sources = DataSources(
            cross_analyzer=self.mock_cross_analyzer,
            resource_analyzer=self.mock_resource_analyzer,
            minknow_analyzer=self.mock_minknow_analyzer,
            timeline_analyzer=self.mock_timeline_analyzer,
        )

    def test_resource_columns(self):
        """Test resource columns access."""
        # Test without stats (default)
        result = self.data_sources.resource_columns()

        assert "columns" in result
        assert "column_stats" not in result
        assert "TestColumn" in result["columns"]
        assert self.mock_resource_analyzer.call_count == 1

        # Test with stats
        result_with_stats = self.data_sources.resource_columns(include_stats=True)

        assert "columns" in result_with_stats
        assert "column_stats" in result_with_stats
        assert "TestColumn" in result_with_stats["columns"]
        assert self.mock_resource_analyzer.call_count == 2

    def test_find_columns_by_pattern(self):
        """Test column pattern matching functionality."""
        # Add more diverse columns to mock
        self.mock_resource_analyzer.get_column_stats = lambda: {
            "TestColumn": {"count": 100},
            "DiskUsedBytes": {"count": 100},
            "disk_capacity": {"count": 100},
            "MemTotalBytes": {"count": 100},
            "GpuMemoryUsed": {"count": 100},
            "gpu_temperature": {"count": 100},
        }

        # Test case insensitive matching (default)
        disk_columns = self.data_sources.find_columns_by_pattern(["disk"])
        assert len(disk_columns) == 2
        assert "DiskUsedBytes" in disk_columns
        assert "disk_capacity" in disk_columns

        # Test case sensitive matching
        gpu_columns = self.data_sources.find_columns_by_pattern(["gpu"], case_sensitive=True)
        assert len(gpu_columns) == 1
        assert "gpu_temperature" in gpu_columns

        # Test multiple patterns
        memory_columns = self.data_sources.find_columns_by_pattern(["Mem"])
        assert len(memory_columns) == 2
        assert "MemTotalBytes" in memory_columns
        assert "GpuMemoryUsed" in memory_columns

        # Test multiple patterns with case sensitivity
        gpu_columns_all = self.data_sources.find_columns_by_pattern(["gpu"])
        assert len(gpu_columns_all) == 2  # Both gpu_temperature and GpuMemoryUsed
        assert "gpu_temperature" in gpu_columns_all
        assert "GpuMemoryUsed" in gpu_columns_all

    @pytest.mark.asyncio
    async def test_resource_metrics(self):
        """Test resource metrics access."""
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=1)
        columns = ["TestColumn"]

        result = await self.data_sources.resource_metrics(columns=columns, start_time=start_time, end_time=end_time)

        assert "records" in result
        assert "total" in result
        assert len(result["records"]) == 2
        assert self.mock_resource_analyzer.call_count == 1

    def test_log_events(self):
        """Test log events access."""
        # Mock the minknow analyzer query response for log_events_raw
        import pandas as pd

        # Create mock DataFrame with log events
        mock_df = pd.DataFrame([{"folder_name": "pos1", "log_level": "INFO", "message": "Test message"}])
        mock_df.index = [datetime.now()]
        self.mock_minknow_analyzer.query.return_value = mock_df

        result = self.data_sources.log_events(start_time=datetime.now(), end_time=datetime.now() + timedelta(hours=1))

        assert "events" in result
        self.mock_minknow_analyzer.query.assert_called_once()

    def test_timeline_events(self):
        """Test timeline events access."""
        # Mock the timeline analyzer response
        self.mock_timeline_analyzer.get_timeline_events.return_value = {
            "timeline_events": [{"timestamp": "2023-01-01T00:00:00", "timeline_event": "test"}]
        }

        result = self.data_sources.timeline_events()

        assert "timeline_events" in result
        self.mock_timeline_analyzer.get_timeline_events.assert_called_once()

    def test_timeline_events_error_handling(self):
        """Test timeline events error handling."""
        # Mock an error
        self.mock_timeline_analyzer.get_timeline_events.side_effect = Exception("Test error")

        result = self.data_sources.timeline_events()

        assert result == {"timeline_events": []}

    def test_validate_connections(self):
        """Test analyzer connection validation."""
        # Mock successful calls
        self.mock_cross_analyzer.get_common_time_range.return_value = (datetime.now(), datetime.now())
        self.mock_timeline_analyzer.get_timeline_events.return_value = {}

        status = self.data_sources.validate_connections()

        assert status["resource_analyzer"] is True
        assert status["cross_analyzer"] is True
        assert status["timeline_analyzer"] is True


class TestBaseTransformations:
    """Test transformation functions."""

    def test_convert_units_bytes_to_gb(self):
        """Test unit conversion from bytes to GB."""
        test_data = {
            "records": [
                {"timestamp": "2023-01-01T00:00:00", "MemTotalBytes": 1073741824},  # 1 GB
                {"timestamp": "2023-01-01T00:01:00", "MemUsedBytes": 2147483648},  # 2 GB
            ]
        }

        result = BaseTransformations.transform_convert_units(test_data, "bytes", "gb")

        assert len(result["records"]) == 2
        assert result["records"][0]["MemTotalBytes"] == 1.0
        assert result["records"][1]["MemUsedBytes"] == 2.0

    def test_bucket_by_time(self):
        """Test enhanced time bucketing transformation."""
        test_data = [
            {"timestamp": "2023-01-01T00:00:00", "severity": "INFO", "position": "pos1"},
            {"timestamp": "2023-01-01T00:00:30", "severity": "ERROR", "position": "pos1"},
            {"timestamp": "2023-01-01T00:05:00", "severity": "INFO", "position": "pos2"},
            {"timestamp": "2023-01-01T00:05:30", "severity": "INFO", "position": "pos2"},
        ]

        # Test basic bucketing (5-minute buckets)
        result = BaseTransformations.transform_bucket_by_time(test_data, bucket_size_seconds=300)

        # Should create separate records for each position/severity combination in each bucket
        assert len(result) == 3  # pos1/INFO, pos1/ERROR in bucket1; pos2/INFO in bucket2

        # Verify record structure
        for record in result:
            assert "timestamp" in record
            assert "value" in record
            assert "position" in record
            assert "severity" in record
            assert record["value"] >= 1

        # Test with custom aggregation fields
        result_custom = BaseTransformations.transform_bucket_by_time(test_data, bucket_size_seconds=300, aggregation_fields=["position"])

        # Should aggregate by position only (not severity)
        assert len(result_custom) == 2  # pos1 and pos2 in their respective buckets

        # Test with time range (should fill empty buckets)
        from datetime import datetime

        start_time = datetime.fromisoformat("2023-01-01T00:00:00")
        end_time = datetime.fromisoformat("2023-01-01T00:10:00")

        result_with_range = BaseTransformations.transform_bucket_by_time(
            test_data, bucket_size_seconds=300, start_time=start_time, end_time=end_time
        )

        # Should include empty buckets for missing time periods
        assert len(result_with_range) >= len(result)  # At least as many as basic bucketing

    def test_format_time_series(self):
        """Test time series formatting."""
        test_data = {
            "records": [
                {"timestamp": "2023-01-01T00:00:00", "cpu_load": 0.5, "memory_gb": 2.0},
                {"timestamp": "2023-01-01T00:01:00", "cpu_load": 0.7, "memory_gb": 2.1},
            ]
        }

        result = BaseTransformations.format_time_series(test_data)

        assert "timestamps" in result
        assert "cpu_load" in result
        assert "memory_gb" in result
        assert len(result["timestamps"]) == 2
        assert len(result["cpu_load"]) == 2
        assert len(result["memory_gb"]) == 2
        assert result["cpu_load"] == [0.5, 0.7]
        assert result["memory_gb"] == [2.0, 2.1]

    def test_format_heatmap_records(self):
        """Test heatmap record formatting."""
        test_data = [
            {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "value": 5},
            {"timestamp": "2023-01-01T00:01:00", "position": "pos2", "log_level": "ERROR"},  # Missing value
        ]

        result = BaseTransformations.format_heatmap_records(test_data)

        assert len(result) == 2
        assert result[0]["value"] == 5
        assert result[1]["value"] == 1  # Default value
        assert result[1]["severity"] == "ERROR"  # Should handle log_level field

    def test_create_scatter_points(self):
        """Test scatter point creation."""
        test_data = [{"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "message": "Test message"}]

        result = BaseTransformations.create_scatter_points(test_data)

        assert len(result) == 1
        point = result[0]
        assert point["x"] == "2023-01-01T00:00:00"
        assert point["y"] == "pos1"
        assert point["severity"] == "INFO"
        assert point["message"] == "Test message"
        assert "original_event" in point

    def test_aggregate_by_position_and_severity(self):
        """Test aggregation by position and severity."""
        test_data = [
            {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO"},
            {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO"},
            {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "ERROR"},
        ]

        result = BaseTransformations.aggregate_by_position_and_severity(test_data)

        # Should create groups by position/severity/timestamp combination
        assert len(result) == 2  # Two unique combinations

        # Find the INFO group
        info_group = next(r for r in result if r["severity"] == "INFO")
        assert info_group["count"] == 2

        # Find the ERROR group
        error_group = next(r for r in result if r["severity"] == "ERROR")
        assert error_group["count"] == 1


# Integration test combining multiple components
class TestPipelineIntegration:
    """Integration tests for pipeline components."""

    @pytest.mark.asyncio
    async def test_simple_pipeline_integration(self):
        """Test simple pipeline with DataSources and transformations."""
        # Create mock analyzers
        mock_resource_analyzer = MockAnalyzer()
        mock_cross_analyzer = Mock()
        mock_minknow_analyzer = Mock()
        mock_timeline_analyzer = Mock()

        # Create data sources
        data_sources = DataSources(
            cross_analyzer=mock_cross_analyzer,
            resource_analyzer=mock_resource_analyzer,
            minknow_analyzer=mock_minknow_analyzer,
            timeline_analyzer=mock_timeline_analyzer,
        )

        # Create pipeline
        pipeline = TestableTransformationPipeline(method_name="test_integration", debug_mode=True)

        pipeline.inject_sources(data_sources)

        # Add steps that use data sources and transformations
        def fetch_data(data):
            # Create mock data instead of async call for testing
            return {
                "records": [{"timestamp": "2023-01-01T00:00:00", "TestColumn": 42}, {"timestamp": "2023-01-01T00:01:00", "TestColumn": 43}]
            }

        def format_data(data):
            return BaseTransformations.format_time_series(data)

        pipeline.add_step("fetch", fetch_data)
        pipeline.add_step("format", format_data)

        # Execute pipeline
        result = pipeline.execute()

        # Verify results
        assert "timestamps" in result
        assert len(pipeline.debug_info.step_details) == 2
        assert pipeline._executed

        # Verify debug information
        debug_summary = pipeline.get_debug_summary()
        assert debug_summary["step_count"] == 2
        assert debug_summary["step_details"][0]["name"] == "fetch"
        assert debug_summary["step_details"][1]["name"] == "format"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
