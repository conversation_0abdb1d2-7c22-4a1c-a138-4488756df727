"""Main application runner with proper imports."""

from __future__ import annotations

import argparse
import logging
import sys
import threading
import time

import webview

from .app import LogVisualizerApp
from .config.settings import settings
from .core.container import AnalyzerContainer

# Set up logging
logger = logging.getLogger(__name__)


def setup_performance_logging(*, enable_profile: bool = False) -> None:
    """Setup performance logging configuration."""
    # Create a specific logger for performance logs
    perf_logger = logging.getLogger("app.performance")

    if enable_profile:
        # Enable DEBUG level for performance logger only
        perf_logger.setLevel(logging.DEBUG)

        # Create console handler if it doesn't exist
        if not perf_logger.handlers:
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            console_handler.setFormatter(formatter)
            perf_logger.addHandler(console_handler)

        # Prevent propagation to parent logger to avoid duplicate logs
        perf_logger.propagate = False
    else:
        # Disable DEBUG level for performance logger
        perf_logger.setLevel(logging.INFO)


def setup_log_parser_performance_logging(*, enable_profile_parsing: bool = False) -> None:
    """Setup log parser performance logging configuration."""
    try:
        # Conditional import for log_parser - works both in development and when vendored
        try:
            # Try vendored import first (for built wheel)
            from .log_parser import enable_performance_logging
        except ImportError:
            # Fall back to external package (for development)
            import log_parser

            enable_performance_logging = log_parser.enable_performance_logging

        # Enable or disable log parser performance logging
        enable_performance_logging(enable=enable_profile_parsing)

    except ImportError as e:
        # If log_parser is not available, just log a warning
        logging.getLogger("app").warning("Could not configure log_parser performance logging: %s", e)


def start_dash(app_instance, port: int = 8050, *, debug: bool = False) -> None:
    """Start the Dash application in a separate thread."""
    app_instance.run(debug=debug, port=port, host="127.0.0.1")


def run_app(args: argparse.Namespace) -> None:
    """Run the main application with parsed arguments."""
    try:
        # Setup performance logging based on --profile flag
        setup_performance_logging(enable_profile=args.profile)

        # Setup log parser performance logging based on --profile-parsing flag
        setup_log_parser_performance_logging(enable_profile_parsing=args.profile_parsing)

        logger.info("🐦 Initializing Tern...")

        # Create the container with settings
        logger.info("📦 Setting up analyzer container...")
        container = AnalyzerContainer(config=settings)
        # Note: Analyzers will be initialized lazily when logs are actually loaded
        # This prevents "No log entries found" warnings on startup

        if args.no_webview:
            # Create the app without webview window
            logger.info("🚀 Creating log visualizer app...")
            app_instance = LogVisualizerApp(container=container, webview_window=None)

            # Run Dash directly in the main thread
            logger.info("🌐 Starting Dash server on port %d...", args.port)
            app_instance.run(debug=args.debug, port=args.port, host="127.0.0.1")
        else:
            # Create webview window first
            logger.info("🖥️  Creating native window...")
            window = webview.create_window(
                "Tern - Log Visualizer",
                f"http://127.0.0.1:{args.port}",
                width=args.width,
                height=args.height,
                resizable=True,
                text_select=True,
                confirm_close=True,
            )

            # Create the app with webview window reference
            logger.info("🚀 Creating log visualizer app...")
            app_instance = LogVisualizerApp(container=container, webview_window=window)

            # Start Dash in a separate thread
            logger.info("🌐 Starting Dash server on port %d...", args.port)
            dash_thread = threading.Thread(target=start_dash, args=(app_instance, args.port), kwargs={"debug": args.debug})
            dash_thread.daemon = True
            dash_thread.start()

            # Give Dash a moment to start up
            time.sleep(2)

            # Start the webview
            logger.info("🖥️  Opening native window...")
            webview.start(debug=args.debug)

    except KeyboardInterrupt:
        logger.info("\n🐦 Shutting down Tern...")
        sys.exit(0)
    except (ImportError, OSError, RuntimeError, ValueError):
        logger.exception("❌ Error starting Tern")
        sys.exit(1)


def main() -> None:
    """Legacy main function - redirects to CLI module."""
    # Import here to maintain backward compatibility
    from .cli import main as cli_main

    cli_main()


if __name__ == "__main__":
    main()
