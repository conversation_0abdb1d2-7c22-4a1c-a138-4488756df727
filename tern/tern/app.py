"""Main application module for the log visualizer."""

from __future__ import annotations

import atexit
import logging
import os
import signal
import threading
import time
from threading import Lock

import dash
import dash_mantine_components as dmc
from dash import _dash_renderer

from .config.settings import settings

# Conditional import for log_parser - works both in development and when vendored
try:
    # Try vendored import first (for built wheel)
    from . import log_parser
    from .log_parser.utils.logging import setup_logging
except ImportError:
    # Fall back to external package (for development)
    import log_parser
    from log_parser.utils.logging import setup_logging

from .core.container import AnalyzerContainer
from .core.data_context import DataContext
from .core.progress_tracker import ProgressTracker
from .core.structured_logs import StructuredLogs
from .services.service_factory import ServiceFactory

# Set React version for Dash Mantine Components
_dash_renderer._set_react_version("18.2.0")

# Configure logging using the log_parser's setup
logger = setup_logging(settings.log_level)

# Create a specific logger for performance logs
perf_logger = logging.getLogger("app.performance")

# Enable log_parser performance tracking will be controlled by --profile-parsing flag
# This is now handled in main.py via setup_log_parser_performance_logging()
logger.info("LOG_PARSER_INIT: Log parser performance tracking configured via command line flags")


# Setup performance summary on shutdown
def shutdown_performance_summary():
    """Print final performance summary on shutdown."""
    perf_logger.debug("LOG_PARSER_PERF: Final performance summary on shutdown")
    print_log_parser_performance()


atexit.register(shutdown_performance_summary)


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info("Received signal %d, printing performance summary before shutdown", signum)
    shutdown_performance_summary()
    exit(0)


signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Import layout and callbacks
from .callbacks import register_callbacks
from .layout.main_layout import create_main_layout

# Global performance tracking
_performance_counters = {}


def print_log_parser_performance():
    """Print log_parser performance summary to logs."""
    try:
        perf_logger.debug("=" * 60)
        perf_logger.debug("LOG PARSER PERFORMANCE SUMMARY")
        perf_logger.debug("=" * 60)

        stats = log_parser.get_performance_summary()

        # Print call counters
        if stats["call_counters"]:
            perf_logger.debug("FUNCTION CALL COUNTS:")
            perf_logger.debug("-" * 30)
            for func_name, count in sorted(stats["call_counters"].items(), key=lambda x: x[1], reverse=True):
                perf_logger.debug("  %s %d calls", func_name.ljust(35, "."), count)

        # Print memory tracking
        if stats["memory_tracking"]:
            perf_logger.debug("PERFORMANCE METRICS:")
            perf_logger.debug("-" * 50)
            perf_logger.debug(f"{'Function':<25} {'Calls':<8} {'Avg Time':<10} {'Avg Mem Δ':<12} {'Max Mem Δ':<12}")
            perf_logger.debug("-" * 50)

            for func_name, metrics in sorted(stats["memory_tracking"].items(), key=lambda x: x[1]["total_calls"], reverse=True):
                perf_logger.debug(
                    f"{func_name:<25} {metrics['total_calls']:<8} "
                    f"{metrics['avg_duration']:<10.3f} "
                    f"{metrics['avg_memory_delta']:+<12.1f} "
                    f"{metrics['max_memory_delta']:+<12.1f}"
                )

        perf_logger.debug("=" * 60)
        perf_logger.debug("Legend: Avg Time (seconds), Mem Δ (MB change)")
        perf_logger.debug("=" * 60)

    except (AttributeError, KeyError, TypeError, ImportError):
        logger.exception("Error printing log_parser performance summary")


def _track_performance(operation_name):
    """Simple performance tracking utility."""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()

            # Increment call counter
            if operation_name not in _performance_counters:
                _performance_counters[operation_name] = 0
            _performance_counters[operation_name] += 1

            perf_logger.debug(f"PERF_START: {operation_name} (call #{_performance_counters[operation_name]})")

            try:
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time
                perf_logger.debug("PERF_END: %s completed in %.3fs", operation_name, elapsed)
                return result
            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
                elapsed = time.time() - start_time
                perf_logger.debug("PERF_ERROR: %s failed after %.3fs: %s", operation_name, elapsed, e)
                raise

        return wrapper

    return decorator


class LogVisualizerApp:
    """Main application class for the log visualizer."""

    def __init__(self, container: AnalyzerContainer, webview_window=None):
        app_init_start = time.time()
        perf_logger.debug("PERF_START: LogVisualizerApp.__init__")

        self.container = container

        # Use lazy initialization for analyzer-dependent objects
        # These will be created when first accessed to avoid triggering
        # analyzer initialization with empty data on startup
        self._structured_logs = None
        self._data_context = None

        # Thread synchronization
        self._parsing_lock = Lock()
        self._is_parsing = False

        # Initialize services
        self.progress_tracker = ProgressTracker()
        self.service_factory = ServiceFactory(container, self.progress_tracker, webview_window)

        # Initialize Dash app
        dash_init_start = time.time()
        perf_logger.debug("PERF_START: Dash app initialization")
        self.app = dash.Dash(__name__, external_stylesheets=[dmc.styles.ALL], suppress_callback_exceptions=True)
        dash_init_elapsed = time.time() - dash_init_start
        perf_logger.debug(f"PERF_END: Dash app initialization completed in {dash_init_elapsed:.3f}s")

        self._setup_layout()
        self._setup_callbacks()

        app_init_elapsed = time.time() - app_init_start
        perf_logger.debug(f"PERF_END: LogVisualizerApp.__init__ completed in {app_init_elapsed:.3f}s")

        # Print log_parser performance summary after initialization
        perf_logger.debug("LOG_PARSER_PERF: Performance summary after app initialization")
        print_log_parser_performance()

    @_track_performance("log_parsing_check_and_parse")
    def _check_and_parse_logs(self):
        """Check for BASE_LOG_FOLDER environment variable and parse logs if set."""
        with self._parsing_lock:
            if self._is_parsing:
                logger.info("PARSING_SKIP: Parsing already in progress in application layer")
                return

            self._is_parsing = True
            try:
                log_folder = os.getenv("BASE_LOG_FOLDER")
                logger.info("Checking for logs in folder: %s", log_folder)
                if log_folder:
                    try:
                        logger.info("Starting container log parsing...")
                        parse_start = time.time()
                        # Parse logs and update status
                        self.container.parse_logs(log_folder)
                        parse_elapsed = time.time() - parse_start
                        logger.info(f"Container log parsing completed successfully in {parse_elapsed:.3f}s")

                        # Print log_parser performance summary after parsing
                        perf_logger.debug("LOG_PARSER_PERF: Performance summary after log parsing")
                        print_log_parser_performance()
                    except Exception:
                        logger.exception("Error parsing logs")
                else:
                    logger.info("BASE_LOG_FOLDER environment variable not set. No logs will be parsed.")
            finally:
                self._is_parsing = False

    @_track_performance("app_layout_setup")
    def _setup_layout(self):
        """Setup the layout of the Dash app."""
        perf_logger.debug("PERF_START: Layout setup")
        layout_start = time.time()
        self.app.layout = create_main_layout()
        layout_elapsed = time.time() - layout_start
        perf_logger.debug("PERF_END: Layout setup completed in %.3fs", layout_elapsed)

    @_track_performance("app_callbacks_setup")
    def _setup_callbacks(self):
        """Setup the callbacks for the Dash app."""
        perf_logger.debug("PERF_START: Callback registration")
        callbacks_start = time.time()
        register_callbacks(
            self.app,
            self,  # Pass the LogVisualizerApp instance
            self.service_factory,  # Pass the service factory
        )
        callbacks_elapsed = time.time() - callbacks_start
        perf_logger.debug("PERF_END: Callback registration completed in %.3fs", callbacks_elapsed)

    def run(self, debug: bool = False, **kwargs):
        """Run the Dash app."""

        # Remove automatic parsing - now controlled by frontend
        # def parse_logs_background():
        #     logger.info("Starting log parsing in background...")
        #     try:
        #         self._check_and_parse_logs()
        #         logger.info("Background log parsing completed")
        #     except Exception:
        #         logger.exception("Error during background log parsing")

        # # Create and start the background thread
        # thread_start = time.time()
        # perf_logger.debug("PERF_START: Background parsing thread creation")
        # log_parsing_thread = threading.Thread(target=parse_logs_background)
        # log_parsing_thread.daemon = True
        # log_parsing_thread.start()
        # thread_elapsed = time.time() - thread_start
        # perf_logger.debug("PERF_END: Background parsing thread started in %.3fs", thread_elapsed)

        # Start a periodic performance summary thread
        def periodic_performance_summary():
            """Print performance summary every 10 minutes."""
            while True:
                time.sleep(600)  # 10 minutes
                perf_logger.debug("LOG_PARSER_PERF: Periodic performance summary (every 10 minutes)")
                print_log_parser_performance()

        perf_thread = threading.Thread(target=periodic_performance_summary)
        perf_thread.daemon = True
        perf_thread.start()
        perf_logger.debug("PERF_START: Periodic performance summary thread started")

        # Run the app
        perf_logger.debug("PERF_START: Dash app.run()")
        app_start = time.time()
        self.app.run(debug=debug, **kwargs)

    # Lazy initialization properties
    @property
    def structured_logs(self):
        if self._structured_logs is None:
            self._structured_logs = StructuredLogs(self.container.get_analyzer("minknow"))
        return self._structured_logs

    @property
    def data_context(self):
        if self._data_context is None:
            self._data_context = DataContext(
                cross_analyzer=self.container.get_analyzer("cross"),
                resource_analyzer=self.container.get_analyzer("resource"),
                minknow_analyzer=self.container.get_analyzer("minknow"),
                timeline_analyzer=self.container.get_analyzer("timeline"),
            )
        return self._data_context


if __name__ == "__main__":
    overall_start = time.time()
    perf_logger.debug("PERF_START: Overall app startup")
    logger.info("Starting App...")

    # Create the container with settings
    container_start = time.time()
    perf_logger.debug("PERF_START: AnalyzerContainer creation")
    container = AnalyzerContainer(config=settings)
    container_create_elapsed = time.time() - container_start
    perf_logger.debug(f"PERF_END: AnalyzerContainer creation completed in {container_create_elapsed:.3f}s")

    # Note: Analyzers will be initialized lazily when first accessed
    # This prevents "No log entries found" warnings on startup

    # Create the app with the container
    app_create_start = time.time()
    perf_logger.debug("PERF_START: LogVisualizerApp creation")
    app = LogVisualizerApp(container=container)
    app_create_elapsed = time.time() - app_create_start
    perf_logger.debug("PERF_END: LogVisualizerApp creation completed in %.3fs", app_create_elapsed)

    overall_elapsed = time.time() - overall_start
    perf_logger.debug("PERF_END: Overall app startup completed in %.3fs", overall_elapsed)

    app.run(debug=True, use_reloader=False)
