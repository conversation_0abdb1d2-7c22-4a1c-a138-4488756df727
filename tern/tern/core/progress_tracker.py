"""Progress tracking implementation for the Tern application."""

import threading
import time
from typing import Any, Dict, Optional

from .interfaces import IProgressTracker, ParsingStatus, ProgressUpdate


class ProgressTracker(IProgressTracker):
    """Thread-safe progress tracker implementation."""

    def __init__(self):
        self._lock = threading.Lock()
        self._status = ParsingStatus(
            status="idle",
            stage="",
            progress=0,
            current_file="",
            files_processed=0,
            total_files=0,
            start_time=None,
            end_time=None,
            error=None,
        )

    def update_progress(self, update: ProgressUpdate) -> None:
        """Update parsing progress with thread-safe operations."""
        with self._lock:
            self._status.stage = update.stage
            self._status.progress = update.progress
            self._status.current_file = update.current_file
            self._status.files_processed = update.files_processed
            self._status.total_files = update.total_files
            self._status.elapsed_time = update.elapsed_time

    def get_status(self) -> ParsingStatus:
        """Get current parsing status."""
        with self._lock:
            return ParsingStatus(
                status=self._status.status,
                stage=self._status.stage,
                progress=self._status.progress,
                current_file=self._status.current_file,
                files_processed=self._status.files_processed,
                total_files=self._status.total_files,
                start_time=self._status.start_time,
                end_time=self._status.end_time,
                error=self._status.error,
            )

    def reset_status(self) -> None:
        """Reset to idle state."""
        with self._lock:
            self._status = ParsingStatus(
                status="idle",
                stage="",
                progress=0,
                current_file="",
                files_processed=0,
                total_files=0,
                start_time=None,
                end_time=None,
                error=None,
            )

    def set_error(self, error_msg: str, stage: str = "") -> None:
        """Set error state with details."""
        with self._lock:
            self._status.status = "error"
            self._status.stage = stage
            self._status.error = {"message": error_msg, "timestamp": time.time()}
            self._status.end_time = time.time()

    def set_parsing_start(self) -> None:
        """Set parsing start state."""
        with self._lock:
            self._status.status = "parsing"
            self._status.start_time = time.time()
            self._status.end_time = None
            self._status.error = None

    def set_parsing_complete(self) -> None:
        """Set parsing complete state."""
        with self._lock:
            self._status.status = "complete"
            self._status.end_time = time.time()
            self._status.progress = 100
