"""Visualization data caching for log visualization system."""

from __future__ import annotations

import logging
from datetime import datetime, timedelta
from typing import Any


class VisualizationDataCache:
    """Phase 2B: Hierarchical caching for visualization data transformations.

    Optimizes drill-down usage patterns:
    1. Full range queries → cached as dictionaries
    2. Sub-range queries → sliced from cached results by timestamp
    3. Return to full range → instant cache hit
    """

    def __init__(self, max_cached_results: int = 5):
        self.cached_results = {}  # {cache_key: dict}
        self.cache_metadata = {}  # {cache_key: metadata}
        self.max_cached_results = max_cached_results
        self.logger = logging.getLogger("app")  # Use same logger as rest of app

    def get_cached_transformation(
        self,
        method_name: str,
        start_time: datetime | None,
        end_time: datetime | None,
        **params,
    ) -> dict[str, Any] | None:
        """Get cached transformation result or None if not available.

        Args:
            method_name: Name of the transformation method
            start_time: Start timestamp (inclusive)
            end_time: End timestamp (inclusive)
            **params: Additional parameters for cache key

        Returns:
            Cached result dict or None if not found
        """
        try:
            # Generate cache key for this query
            query_key = self._generate_cache_key(method_name, start_time, end_time, **params)

            # 1. Check for exact cache match
            if query_key in self.cached_results:
                self.logger.info("VISUALIZATION_CACHE_HIT: Exact match for %s", method_name)
                self._update_last_used(query_key)
                return self._deep_copy_result(self.cached_results[query_key])

            # 2. Check if this query can be satisfied by slicing a cached result
            parent_result = self._find_parent_cache(method_name, start_time, end_time, **params)
            if parent_result:
                parent_data, parent_key = parent_result
                parent_metadata = self.cache_metadata[parent_key]
                self.logger.info(
                    f"VISUALIZATION_CACHE_SLICE: Slicing from cached parent for {method_name} "
                    f"(parent span: {parent_metadata['query_span']})"
                )
                try:
                    sliced_result = self._slice_cached_data(parent_data, start_time, end_time)

                    # Cache this sliced result for potential future use
                    self.add_to_cache(query_key, sliced_result, method_name, start_time, end_time, **params)
                    return self._deep_copy_result(sliced_result)
                except (ValueError, TypeError, AttributeError, KeyError, IndexError):
                    self.logger.exception(f"VISUALIZATION_CACHE_SLICE_FAILED: Error slicing for {method_name}")
                    # Return None to fall back to full computation
                    return None

            # 3. No cache hit - provide debugging info
            available_caches = []
            for cache_key, cached_data in self.cached_results.items():
                metadata = self.cache_metadata[cache_key]
                if metadata["method_name"] == method_name and metadata["params"] == params:
                    available_caches.append(f"span: {metadata['query_span']} (start: {metadata['start']}, end: {metadata['end']})")

            if available_caches:
                self.logger.info(
                    f"VISUALIZATION_CACHE_MISS: No suitable cached data for {method_name} "
                    f"(start: {start_time}, end: {end_time}). Available caches: {available_caches}"
                )
            else:
                self.logger.info(
                    f"VISUALIZATION_CACHE_MISS: No cached data available for {method_name} (start: {start_time}, end: {end_time})"
                )
            return None

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            self.logger.exception(f"VISUALIZATION_CACHE_ERROR: Error in get_cached_transformation for {method_name}")
            return None

    def add_to_cache(
        self,
        query_key: str,
        result: dict[str, Any],
        method_name: str,
        start_time: datetime | None,
        end_time: datetime | None,
        **params,
    ) -> None:
        """Add result to cache with intelligent eviction."""
        try:
            # Don't cache empty results
            if not result or not result.get("timestamps"):
                self.logger.debug(f"VISUALIZATION_CACHE_SKIP: Not caching empty result for {method_name}")
                return

            # Calculate query span for eviction prioritization
            if start_time is not None and end_time is not None:
                query_span = end_time - start_time
            else:
                # Use a large span for unbounded queries (full dataset)
                query_span = timedelta(days=365 * 10)  # 10 years as max span

            # Evict if cache is full
            if len(self.cached_results) >= self.max_cached_results:
                self._evict_least_valuable()

            # Estimate data size (rough approximation)
            estimated_size = self._estimate_dict_size(result)

            # Store in cache
            self.cached_results[query_key] = self._deep_copy_result(result)
            self.cache_metadata[query_key] = {
                "size": estimated_size,
                "last_used": datetime.now(),
                "query_span": query_span,
                "method_name": method_name,
                "start": start_time,
                "end": end_time,
                "params": params,
            }

            cache_size_mb = estimated_size / (1024 * 1024)
            self.logger.info(
                f"VISUALIZATION_CACHE_ADD: Cached {method_name} ({cache_size_mb:.1f}MB, {len(result.get('timestamps', []))} records)"
            )

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            self.logger.exception("VISUALIZATION_CACHE_ADD_ERROR: Failed to cache %s", method_name)

    def _generate_cache_key(
        self,
        method_name: str,
        start_time: datetime | None,
        end_time: datetime | None,
        **params,
    ) -> str:
        """Generate cache key from query parameters."""
        # Convert datetime to string for hashing
        start_str = start_time.isoformat() if start_time else "None"
        end_str = end_time.isoformat() if end_time else "None"

        # Sort params for consistent hashing
        params_str = "_".join(f"{k}={v}" for k, v in sorted(params.items()))

        return f"{method_name}_{start_str}_{end_str}_{params_str}"

    def _find_parent_cache(
        self,
        method_name: str,
        start_time: datetime | None,
        end_time: datetime | None,
        **params,
    ) -> tuple[dict[str, Any], str] | None:
        """Find best cached result that contains this time range and method."""
        candidates = []

        for cache_key, cached_data in self.cached_results.items():
            metadata = self.cache_metadata[cache_key]

            # Check if method matches and params match
            if metadata["method_name"] == method_name and metadata["params"] == params:
                cached_start = metadata["start"]
                cached_end = metadata["end"]

                # Check if time range is contained (handle None cases)
                start_contained = start_time is None or cached_start is None or cached_start <= start_time
                end_contained = end_time is None or cached_end is None or cached_end >= end_time

                if start_contained and end_contained:
                    # Calculate cache quality score: larger spans are better, recent usage is better
                    span_score = metadata["query_span"].total_seconds()
                    recency_score = 1.0 / (1 + (datetime.now() - metadata["last_used"]).total_seconds())
                    quality_score = span_score * (1 + recency_score)  # Prioritize span but consider recency

                    candidates.append((cached_data, cache_key, quality_score, metadata["query_span"]))

        if not candidates:
            return None

        # Sort by quality score (descending) - best cache first
        candidates.sort(key=lambda x: x[2], reverse=True)
        best_cache, best_key, best_score, best_span = candidates[0]

        # Log which cache we're using for debugging
        self.logger.info(f"VISUALIZATION_CACHE_BEST_PARENT: Using cache with span {best_span} (score: {best_score:.1f}) for {method_name}")

        return best_cache, best_key

    def _slice_cached_data(
        self,
        cached_data: dict[str, Any],
        start_time: datetime | None,
        end_time: datetime | None,
    ) -> dict[str, Any]:
        """Slice cached dictionary data by timestamp range."""
        if not cached_data.get("timestamps"):
            return cached_data

        # Convert timestamp strings to datetime objects for comparison
        timestamps = []
        for ts in cached_data["timestamps"]:
            if isinstance(ts, str):
                try:
                    # Handle ISO format with Z suffix (UTC) - convert to naive for comparison
                    if ts.endswith("Z"):
                        dt = datetime.fromisoformat(ts.replace("Z", "+00:00"))
                        dt = dt.replace(tzinfo=None)  # Make timezone-naive for comparison
                    else:
                        dt = datetime.fromisoformat(ts)
                        if dt.tzinfo is not None:
                            dt = dt.replace(tzinfo=None)  # Make timezone-naive for comparison
                    timestamps.append(dt)
                except (ValueError, TypeError):
                    # If parsing fails, skip this timestamp
                    continue
            else:
                # Already a datetime object, ensure it's naive
                dt = ts
                if hasattr(dt, "tzinfo") and dt.tzinfo is not None:
                    dt = dt.replace(tzinfo=None)
                timestamps.append(dt)

        # Ensure comparison datetimes are also naive
        compare_start = (
            start_time.replace(tzinfo=None) if start_time and hasattr(start_time, "tzinfo") and start_time.tzinfo else start_time
        )
        compare_end = end_time.replace(tzinfo=None) if end_time and hasattr(end_time, "tzinfo") and end_time.tzinfo else end_time

        # Find indices that fall within the time range
        indices = []
        for i, ts in enumerate(timestamps):
            include = True
            if compare_start is not None and ts < compare_start:
                include = False
            if compare_end is not None and ts > compare_end:
                include = False
            if include:
                indices.append(i)

        if not indices:
            # Return empty structure matching the original
            result = {}
            for key, value in cached_data.items():
                if key == "timestamps" or isinstance(value, list):
                    result[key] = []
                elif isinstance(value, dict):
                    result[key] = {sub_key: [] for sub_key in value.keys()}
                else:
                    result[key] = value
            return result

        # Slice all arrays and nested dictionaries
        result = {}
        for key, value in cached_data.items():
            if key == "timestamps":
                result[key] = [cached_data["timestamps"][i] for i in indices]
            elif isinstance(value, list) and len(value) == len(cached_data["timestamps"]):
                # This is a time-series array, slice it
                result[key] = [value[i] for i in indices]
            elif isinstance(value, dict):
                # This is a nested dictionary (e.g., mount_points), slice each sub-array
                result[key] = {}
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, list) and len(sub_value) == len(cached_data["timestamps"]):
                        result[key][sub_key] = [sub_value[i] for i in indices]
                    else:
                        result[key][sub_key] = sub_value
            else:
                # Non-time-series data, keep as-is
                result[key] = value

        return result

    def _update_last_used(self, query_key: str) -> None:
        """Update last used timestamp for cache entry."""
        if query_key in self.cache_metadata:
            self.cache_metadata[query_key]["last_used"] = datetime.now()

    def _evict_least_valuable(self) -> None:
        """Evict least valuable cache entry based on age and query span."""
        if not self.cache_metadata:
            return

        # Find entry with lowest value score (heavily prioritize large spans)
        min_score = float("inf")
        evict_key = None

        for key, metadata in self.cache_metadata.items():
            # Calculate value score (higher = more valuable)
            age_penalty = (datetime.now() - metadata["last_used"]).total_seconds()
            span_bonus = metadata["query_span"].total_seconds()

            # Heavily prioritize large spans (likely the original full dataset)
            # Use quadratic span bonus to strongly preserve large caches
            score = (span_bonus**1.5) / (1 + age_penalty)  # Larger spans get exponentially higher scores

            if score < min_score:
                min_score = score
                evict_key = key

        if evict_key:
            evicted_metadata = self.cache_metadata[evict_key]
            method_name = evicted_metadata["method_name"]
            span = evicted_metadata["query_span"]
            self.logger.info(f"VISUALIZATION_CACHE_EVICT: Evicting {method_name} cache entry (span: {span}, score: {min_score:.1f})")
            del self.cached_results[evict_key]
            del self.cache_metadata[evict_key]

    def _deep_copy_result(self, result: dict[str, Any]) -> dict[str, Any]:
        """Create a deep copy of the result dictionary."""
        import copy

        return copy.deepcopy(result)

    def _estimate_dict_size(self, data: dict[str, Any]) -> int:
        """Estimate memory usage of dictionary data structure."""
        import sys

        def get_size(obj):
            size = sys.getsizeof(obj)
            if isinstance(obj, dict):
                size += sum(get_size(k) + get_size(v) for k, v in obj.items())
            elif isinstance(obj, (list, tuple)):
                size += sum(get_size(item) for item in obj)
            return size

        return get_size(data)

    def get_cache_stats(self) -> dict[str, Any]:
        """Get cache statistics for monitoring and debugging."""
        total_size = sum(metadata["size"] for metadata in self.cache_metadata.values())
        return {
            "cached_queries": len(self.cached_results),
            "total_size_mb": total_size / (1024 * 1024),
            "cache_keys": list(self.cached_results.keys()),
            "methods_cached": list(set(metadata["method_name"] for metadata in self.cache_metadata.values())),
        }
