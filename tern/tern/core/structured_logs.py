from __future__ import annotations

from datetime import datetime
from typing import Any

# Conditional imports for log_parser - works both in development and when vendored
try:
    # Try vendored import first (for built wheel)
    from ..log_parser.analysis.base_analyzer import BaseAnalyzer
    from ..log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from ..log_parser.utils import convert_to_serializable, logger
except ImportError:
    # Fall back to external package (for development)
    from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from log_parser.utils import logger


class StructuredLogs:
    """Component for displaying structured log analysis."""

    def __init__(self, analyzer: StructuredLogAnalyzer):
        self.analyzer = analyzer

    def get_positions(self) -> list[str]:
        """Get list of available positions from logs."""
        try:
            positions = self.analyzer.get_positions()
            return positions
        except Exception:
            logger.exception("Error getting positions")
            return []

    def query_logs(self, start: datetime, end: datetime, page: int = 0, page_size: int = 50, **filters) -> dict[str, Any]:
        """Query logs using existing analyzer capabilities."""
        try:
            # Use existing query() method that actually exists
            df_filtered = self.analyzer.query(start=start, end=end, **filters)
            total_in_range = len(df_filtered)

            # Apply pagination
            start_idx = page * page_size
            end_idx = start_idx + page_size
            df_page = df_filtered.iloc[start_idx:end_idx]

            # Convert to list of dicts using existing method
            logs = self.analyzer._df_to_dict_list(df_page)

            return {
                "logs": logs,
                "total_in_range": total_in_range,
                "page": page,
                "page_size": page_size,
                "pages_in_range": (total_in_range + page_size - 1) // page_size,
            }
        except Exception as e:
            logger.exception("Error querying logs")
            return {"error": str(e), "logs": []}

    def get_timestamp_range(self) -> tuple[datetime | None, datetime | None]:
        """Get the timestamp range from logs."""
        try:
            start, end = self.analyzer.get_time_range()
            return start, end
        except Exception:
            logger.exception("Error getting timestamp range")
            return None, None
