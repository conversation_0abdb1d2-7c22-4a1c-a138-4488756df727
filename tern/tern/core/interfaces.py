"""Core interfaces for the Tern application."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, Optional


@dataclass
class ProgressUpdate:
    """Standard progress update data structure"""

    stage: str
    progress: int  # 0-100
    current_file: str = ""
    files_processed: int = 0
    total_files: int = 0
    elapsed_time: float = 0


@dataclass
class ParsingStatus:
    """Complete parsing status data structure"""

    status: str  # 'idle' | 'parsing' | 'complete' | 'error'
    stage: str = ""
    progress: int = 0
    current_file: str = ""
    files_processed: int = 0
    total_files: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[Dict[str, Any]] = None


class IProgressTracker(ABC):
    """Interface for progress tracking functionality"""

    @abstractmethod
    def update_progress(self, update: ProgressUpdate) -> None:
        """Update parsing progress with thread-safe operations"""
        pass

    @abstractmethod
    def get_status(self) -> ParsingStatus:
        """Get current parsing status"""
        pass

    @abstractmethod
    def reset_status(self) -> None:
        """Reset to idle state"""
        pass

    @abstractmethod
    def set_error(self, error_msg: str, stage: str = "") -> None:
        """Set error state with details"""
        pass


class IFileDialogService(ABC):
    """Interface for file dialog operations"""

    @abstractmethod
    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        """Open directory selection dialog"""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Check if file dialog is available in current environment"""
        pass

    @abstractmethod
    def validate_path(self, path: str) -> Dict[str, Any]:
        """Validate selected path and return validation result"""
        pass


class IParsingController(ABC):
    """Interface for parsing operations"""

    @abstractmethod
    def start_parsing(self, log_folder: str) -> bool:
        """Start parsing process, returns success status"""
        pass

    @abstractmethod
    def cancel_parsing(self) -> bool:
        """Cancel current parsing operation"""
        pass

    @abstractmethod
    def is_parsing(self) -> bool:
        """Check if parsing is currently in progress"""
        pass
