"""Container for dependency injection and service management."""

# Conditional imports for log_parser - works both in development and when vendored
from __future__ import annotations

try:
    # Try vendored import first (for built wheel)
    from ..log_parser.analysis import (
        BaseAnalyzer,
        CrossAnalyzer,
        ResourceAnalyzer,
        StructuredLogAnalyzer,
        TimelineAnalyzer,
    )
    from ..log_parser.analysis.cross_analyzer import CrossAnalyzer
    from ..log_parser.analysis.resource_analyzer import ResourceAnalyzer
    from ..log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from ..log_parser.analysis.timeline_analyzer import TimelineAnalyzer
    from ..log_parser.models import LogEntry, QuarantineEntry
    from ..log_parser.parsers.parser_factory import ParserFactory
    from ..log_parser.utils import logger
except ImportError:
    # Fall back to external package (for development)
    from log_parser.analysis import (
        <PERSON>lineAnalyzer,
        CrossAnalyzer,
        ResourceAnalyzer,
        StructuredLogAnalyzer,
    )
    from log_parser.analysis.timeline_analyzer import TimelineAnalyzer
    from log_parser.analysis.cross_analyzer import CrossAnalyzer
    from log_parser.analysis.resource_analyzer import ResourceAnalyzer
    from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from log_parser.models import LogEntry
    from log_parser.parsers.parser_factory import ParserFactory
    from log_parser.utils import logger

import hashlib
import json
import os
import pickle  # nosec B403 - Used only for local file caching, not user data
from datetime import datetime
from pathlib import Path
from typing import Any

from ..config.settings import settings
from .interfaces import IProgressTracker, ProgressUpdate
from .progress_calculator import ProgressCalculator
from .progress_tracker import ProgressTracker


class AnalyzerProcessingError(Exception):
    """Exception raised when there is an error processing analyzer data."""


class AnalyzerContainer(IProgressTracker):
    """Container for managing analyzer instances with progress tracking."""

    def __init__(self, config):
        self.config = config
        self._initialized = False
        self._analyzers = {}
        self._start_time = datetime.utcnow()
        self._parsing_status = {
            "status": "idle",
            "start_time": None,
            "end_time": None,
            "error": None,
        }
        self._parsing_lock = False  # Simple parsing lock to prevent duplicate runs
        self._parsing_completed = False  # Track if parsing has been completed successfully

        # Initialize progress tracking components
        self._progress_tracker = ProgressTracker()
        self._progress_calculator = ProgressCalculator()

    def initialize(self):
        """Initialize all analyzers with empty data."""
        if self._initialized:
            return

        try:
            # Create analyzers with empty data
            resource_analyzer = ResourceAnalyzer([])
            minknow_analyzer = StructuredLogAnalyzer([])
            self._analyzers = {
                "resource": resource_analyzer,
                "minknow": minknow_analyzer,
                "cross": CrossAnalyzer(resource_analyzer=resource_analyzer, minknow_analyzer=minknow_analyzer),
                "timeline": TimelineAnalyzer([]),
            }

            self._initialized = True
            logger.info("Analyzer container initialized successfully with empty analyzers")
        except Exception:
            logger.exception("Failed to initialize analyzer container")
            raise

    def update_analyzers(self, parsed_logs: dict[str, list[LogEntry]]) -> None:
        """Update analyzers with newly parsed logs.

        Args:
            parsed_logs: Dictionary mapping log types to log entries
        """
        if not self._initialized:
            self.initialize()

        try:
            # Update resource analyzer
            if "resource" in parsed_logs:
                self._analyzers["resource"].update_data(parsed_logs["resource"])

            # Update minknow analyzer
            if "minknow" in parsed_logs:
                self._analyzers["minknow"].update_data(parsed_logs["minknow"])

            # Update cross analyzer with both resource and minknow logs
            if "resource" in parsed_logs and "minknow" in parsed_logs:
                self._analyzers["cross"] = CrossAnalyzer(self._analyzers["resource"], self._analyzers["minknow"])

            # Update timeline analyzer with minknow logs
            if "minknow" in parsed_logs:
                self._analyzers["timeline"].update_data(parsed_logs["minknow"])

            logger.info("Analyzers updated successfully with new log data")
        except Exception as e:
            logger.exception("Error updating analyzers")
            raise AnalyzerProcessingError(f"Error updating analyzers: {e}")

    def get_health_status(self) -> dict[str, Any]:
        """Get system health status."""
        try:
            startup_status = {
                "status": "ok",
                "api_uptime": str(datetime.utcnow() - self._start_time),
                "initialized": self._initialized,
                "details": {},
            }

            # Add details about what has been initialized
            if "cross" in self._analyzers:
                startup_status["details"] = {
                    "common_time_range": self._analyzers["cross"]._common_time_range is not None,
                    "positions_loaded": (len(self._analyzers["cross"]._positions) if self._analyzers["cross"]._positions else 0),
                    "column_availability": (
                        len(self._analyzers["cross"]._column_availability) if self._analyzers["cross"]._column_availability else 0
                    ),
                    "log_summary": self._analyzers["cross"]._log_summary is not None,
                }

            if not self._initialized:
                startup_status["status"] = "starting"

            return startup_status
        except Exception as e:
            return {"status": "error", "error": str(e), "initialized": False}

    def get_parsing_status(self) -> dict[str, Any]:
        """Get current log parsing status from progress tracker."""
        status = self._progress_tracker.get_status()
        return status.__dict__ if hasattr(status, "__dict__") else status

    def get_analyzer(self, analyzer_type):
        """Get an analyzer instance by type."""
        if not self._initialized:
            self.initialize()

        if analyzer_type not in self._analyzers:
            raise ValueError(f"Unknown analyzer type: {analyzer_type}")

        return self._analyzers[analyzer_type]

    def get_factory(self):
        """Get the analyzer factory."""
        return AnalyzerFactory

    def save_intermediate_data(self, data: Any, filename: str) -> None:
        """Save intermediate data to disk if configured."""
        if not settings.save_intermediate_data:
            return

        # Create intermediate data directory if it doesn't exist
        data_dir = Path(settings.intermediate_data_dir)
        data_dir.mkdir(parents=True, exist_ok=True)

        # Save the data
        filepath = data_dir / filename
        try:
            with open(filepath, "w") as f:
                json.dump(data, f, indent=2, default=str)
            logger.info("Saved intermediate data to %s", filepath)
        except Exception:
            logger.exception("Error saving intermediate data to %s", filepath)

    # IProgressTracker interface implementation
    def update_progress(self, update: ProgressUpdate) -> None:
        """Update parsing progress with thread-safe operations."""
        self._progress_tracker.update_progress(update)

    def get_status(self):
        """Get current parsing status."""
        return self._progress_tracker.get_status()

    def reset_status(self) -> None:
        """Reset to idle state."""
        self._progress_tracker.reset_status()

    def set_error(self, error_msg: str, stage: str = "") -> None:
        """Set error state with details."""
        self._progress_tracker.set_error(error_msg, stage)

    def start_parsing_async(self, log_folder: str) -> bool:
        """Start parsing process asynchronously with progress tracking."""
        try:
            # Reset progress tracking
            self._progress_calculator.start_parsing()
            self._progress_tracker.set_parsing_start()

            # Start parsing in a separate thread
            import threading

            parsing_thread = threading.Thread(target=self._parse_logs_with_progress, args=(log_folder,), daemon=True)
            parsing_thread.start()

            return True
        except Exception as e:
            logger.exception("Failed to start async parsing")
            self._progress_tracker.set_error(str(e), "startup")
            return False

    def _parse_logs_with_progress(self, base_dir: str) -> None:
        """Parse logs with detailed progress tracking."""
        try:
            # Stage 1: Discovery
            self._progress_calculator.set_stage("🎣 Casting nets for logs")
            self._update_progress_from_calculator()

            file_counts = self._progress_calculator.discover_parseable_files(base_dir)
            logger.info(f"Discovered {file_counts['total']} parseable files")

            # Stage 2: Parser initialization
            self._progress_calculator.set_stage("🐟 Teaching analyzers to fish")
            self._update_progress_from_calculator()

            resource_parser = ParserFactory.create_parser("resource")
            structured_log_parser = ParserFactory.create_parser("structured")

            # Initialize result dictionary
            parsed_logs = {"resource": [], "minknow": []}

            # Stage 3: MinKnow parsing
            minknow_dir = os.path.join(base_dir, "minknow")
            if os.path.isdir(minknow_dir):
                self._progress_calculator.set_stage("🐠 Diving for MinKnow logs", file_counts["minknow"])
                self._update_progress_from_calculator()

                # Get list of files to process
                minknow_files = []
                for root, dirs, files in os.walk(minknow_dir):
                    for file in files:
                        if file.endswith(".log") or file.endswith(".txt"):
                            minknow_files.append(os.path.join(root, file))

                # Simulate processing files with realistic progress updates
                for i, file_path in enumerate(minknow_files):
                    try:
                        logger.info(f"Processing MinKnow file: {file_path}")

                        # Update progress with current file
                        self._progress_calculator.update_file_progress(i + 1, os.path.basename(file_path))
                        self._update_progress_from_calculator()

                    except Exception as e:
                        logger.warning(f"Error processing file {file_path}: {e}")

                # Update progress to show parsing phase
                self._progress_calculator.update_file_progress(len(minknow_files), "Parsing MinKnow logs...")
                self._update_progress_from_calculator()

                # Parse the entire directory
                minknow_logs, minknow_quarantine = structured_log_parser.parse_directory(minknow_dir)
                if minknow_logs:
                    parsed_logs["minknow"] = minknow_logs
                    logger.info("Parsed %d minknow log entries", len(minknow_logs))
                    self.save_intermediate_data([log.model_dump() for log in minknow_logs], "minknow_logs.json")

            # Stage 4: Resource parsing
            resource_dir = os.path.join(base_dir, "ont-resource-logger")
            if os.path.isdir(resource_dir):
                self._progress_calculator.set_stage("🌊 Cross-referencing events", file_counts["resource"])
                self._update_progress_from_calculator()

                # Get list of files to process
                resource_files = []
                for root, dirs, files in os.walk(resource_dir):
                    for file in files:
                        if file.endswith(".log") or file.endswith(".txt"):
                            resource_files.append(os.path.join(root, file))

                # Process files with progress updates
                for i, file_path in enumerate(resource_files):
                    try:
                        logger.info(f"Processing resource file: {file_path}")

                        # Update progress with current file
                        self._progress_calculator.update_file_progress(i + 1, os.path.basename(file_path))
                        self._update_progress_from_calculator()

                    except Exception as e:
                        logger.warning(f"Error processing file {file_path}: {e}")

                # Update progress to show parsing phase
                self._progress_calculator.update_file_progress(len(resource_files), "Parsing resource logs...")
                self._update_progress_from_calculator()

                # Parse the entire directory
                resource_logs, resource_quarantine = resource_parser.parse_directory(resource_dir)
                if resource_logs:
                    parsed_logs["resource"] = resource_logs
                    logger.info("Parsed %d resource log entries", len(resource_logs))
                    self.save_intermediate_data([log.model_dump() for log in resource_logs], "resource_logs.json")

            # Stage 5: Analysis and finalization
            self._progress_calculator.set_stage("📊 Counting the catch", 0)  # No files to process in this stage
            self._update_progress_from_calculator()

            # Cache the parsed logs
            try:
                cache_key = self._get_cache_key(base_dir)
                self._save_cached_logs(cache_key, parsed_logs)
                logger.info("PARSING_CACHE_SAVE: Saved parsed logs to cache %s", cache_key)
            except Exception as e:
                logger.warning("Failed to cache parsed logs: %s", e)

            # Update analyzers with new data
            self.update_analyzers(parsed_logs)

            # Mark parsing as complete
            self._progress_tracker.set_parsing_complete()
            self._parsing_completed = True
            self._parsing_lock = False

            logger.info("Parsing completed successfully with progress tracking")

        except Exception as e:
            logger.exception("Error during progress-tracked parsing")
            self._progress_tracker.set_error(str(e), self._progress_calculator._current_stage)
            self._parsing_lock = False
            raise AnalyzerProcessingError(f"Error parsing logs: {e}")

    def _update_progress_from_calculator(self) -> None:
        """Update progress tracker with current calculator state."""
        progress_update = self._progress_calculator.create_progress_update()
        self._progress_tracker.update_progress(progress_update)
        # Debug logging to track progress updates
        logger.info(
            f"Progress update: {progress_update.stage} - {progress_update.progress}% - Files: {progress_update.files_processed}/{progress_update.total_files} - Current: {progress_update.current_file}"
        )

    def parse_logs(self, base_dir: str | None = None) -> dict[str, list[LogEntry]]:
        """Parse log files from the base directory (legacy method)."""
        # Check if parsing is already in progress or completed
        if self._parsing_lock:
            logger.info("PARSING_SKIP: Parsing already in progress, skipping duplicate request")
            return {"resource": [], "minknow": []}

        if self._parsing_completed:
            logger.info("PARSING_SKIP: Parsing already completed successfully, skipping duplicate request")
            return {"resource": [], "minknow": []}

        # Set parsing lock
        self._parsing_lock = True

        # Update parsing status
        self._parsing_status = {
            "status": "parsing",
            "start_time": datetime.utcnow(),
            "end_time": None,
            "error": None,
        }

        if not base_dir:
            base_dir = settings.base_log_folder

        if not base_dir or not os.path.isdir(base_dir):
            logger.warning("Invalid log directory: %s", base_dir)
            self._parsing_status.update({"status": "error", "end_time": datetime.utcnow(), "error": "Invalid log directory"})
            self._parsing_lock = False
            return {"resource": [], "minknow": []}

        # Check cache first
        try:
            cache_key = self._get_cache_key(base_dir)
            cached_logs = self._load_cached_logs(cache_key)
            if cached_logs:
                logger.info("PARSING_CACHE_HIT: Loaded cached parsed logs in %s", cache_key)

                # Update parsing status to analysing
                self._parsing_status.update({"status": "analysing", "end_time": datetime.utcnow()})

                # Update analyzers with cached data
                self.update_analyzers(cached_logs)

                # Update parsing status to complete
                self._parsing_status.update({"status": "complete", "end_time": datetime.utcnow()})

                # Mark parsing as completed and release lock
                self._parsing_completed = True
                self._parsing_lock = False
                return cached_logs

        except Exception as e:
            logger.warning("Cache check failed: %s, proceeding with fresh parsing", e)

        logger.info("Parsing logs from %s", base_dir)
        logger.info("Directory contents: %s", os.listdir(base_dir))

        # Create parsers
        resource_parser = ParserFactory.create_parser("resource")
        structured_log_parser = ParserFactory.create_parser("structured")

        # Initialize result dictionary
        parsed_logs = {"resource": [], "minknow": []}

        try:
            resource_dir = os.path.join(base_dir, "ont-resource-logger")
            minknow_dir = os.path.join(base_dir, "minknow")

            logger.info("Looking for resource logs in: %s", resource_dir)
            logger.info("Looking for minknow logs in: %s", minknow_dir)

            if os.path.isdir(resource_dir):
                logger.info("Found resource directory: %s", resource_dir)
                logger.info("Resource directory contents: %s", os.listdir(resource_dir))
                resource_logs, resource_quarantine = resource_parser.parse_directory(resource_dir)
                if resource_logs:
                    parsed_logs["resource"] = resource_logs
                    logger.info("Parsed %d resource log entries", len(resource_logs))
                    self.save_intermediate_data([log.model_dump() for log in resource_logs], "resource_logs.json")
            else:
                logger.warning("Resource directory not found: %s", resource_dir)

            if os.path.isdir(minknow_dir):
                logger.info("Found minknow directory: %s", minknow_dir)
                logger.info("Minknow directory contents: %s", os.listdir(minknow_dir))
                minknow_logs, minknow_quarantine = structured_log_parser.parse_directory(minknow_dir)
                if minknow_logs:
                    parsed_logs["minknow"] = minknow_logs
                    logger.info("Parsed %d minknow log entries", len(minknow_logs))
                    self.save_intermediate_data([log.model_dump() for log in minknow_logs], "minknow_logs.json")
            else:
                logger.warning("Minknow directory not found: %s", minknow_dir)

            # Cache the parsed logs
            try:
                cache_key = self._get_cache_key(base_dir)
                self._save_cached_logs(cache_key, parsed_logs)
                logger.info("PARSING_CACHE_SAVE: Saved parsed logs to cache %s", cache_key)
            except Exception as e:
                logger.warning("Failed to cache parsed logs: %s", e)

            # Update parsing status to complete
            self._parsing_status.update({"status": "analysing", "end_time": datetime.utcnow()})

            # Update analyzers with new data
            self.update_analyzers(parsed_logs)

            # Update parsing status to complete
            self._parsing_status.update({"status": "complete", "end_time": datetime.utcnow()})

            # Mark parsing as completed and release lock
            self._parsing_completed = True
            self._parsing_lock = False
            return parsed_logs
        except Exception as e:
            logger.exception("Error parsing logs")
            self._parsing_status.update({"status": "error", "end_time": datetime.utcnow(), "error": str(e)})
            # Release lock even on error
            self._parsing_lock = False
            raise AnalyzerProcessingError(f"Error parsing logs: {e}")

    def _get_cache_key(self, base_dir: str) -> str:
        """Generate a cache key based on log directory contents."""
        try:
            # Get modification times of key directories
            resource_dir = os.path.join(base_dir, "ont-resource-logger")
            minknow_dir = os.path.join(base_dir, "minknow")

            hash_content = f"{base_dir}"

            if os.path.isdir(resource_dir):
                # Get latest modification time in resource directory
                resource_files = []
                for root, dirs, files in os.walk(resource_dir):
                    for file in files:
                        filepath = os.path.join(root, file)
                        if os.path.isfile(filepath):
                            resource_files.append(str(os.path.getmtime(filepath)))
                hash_content += "".join(sorted(resource_files))

            if os.path.isdir(minknow_dir):
                # Get latest modification time in minknow directory
                minknow_files = []
                for root, dirs, files in os.walk(minknow_dir):
                    for file in files:
                        filepath = os.path.join(root, file)
                        if os.path.isfile(filepath):
                            minknow_files.append(str(os.path.getmtime(filepath)))
                hash_content += "".join(sorted(minknow_files))

            # Create hash of the content
            return hashlib.md5(hash_content.encode(), usedforsecurity=False).hexdigest()[:12]
        except Exception as e:
            logger.warning("Failed to generate cache key: %s", e)
            return "default"

    def _get_cache_path(self, cache_key: str) -> Path:
        """Get the cache file path for a given cache key."""
        cache_dir = Path(settings.intermediate_data_dir) / "parse_cache"
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir / f"parsed_logs_{cache_key}.pkl"

    def _load_cached_logs(self, cache_key: str) -> dict[str, list[LogEntry]] | None:
        """Load cached parsed logs if available."""
        cache_path = self._get_cache_path(cache_key)
        if not cache_path.exists():
            return None

        try:
            with open(cache_path, "rb") as f:
                cached_data = pickle.load(f)  # nosec B301 - Loading from local cache file only

            # Validate cache data
            if isinstance(cached_data, dict) and "resource" in cached_data and "minknow" in cached_data:
                logger.info(f"Found cached logs: {len(cached_data['resource'])} resource, {len(cached_data['minknow'])} minknow")
                return cached_data
        except Exception as e:
            logger.warning("Failed to load cached logs: %s", e)
            # Remove corrupted cache file
            try:
                cache_path.unlink()
            except (OSError, FileNotFoundError):
                # Ignore if file already removed or permissions issue
                pass

        return None

    def _save_cached_logs(self, cache_key: str, parsed_logs: dict[str, list[LogEntry]]) -> None:
        """Save parsed logs to cache."""
        cache_path = self._get_cache_path(cache_key)

        try:
            with open(cache_path, "wb") as f:
                pickle.dump(parsed_logs, f)
        except Exception as e:
            logger.warning("Failed to save logs to cache: %s", e)


class AnalyzerFactory:
    """Factory for creating analyzer instances."""

    @staticmethod
    def create_analyzer(analyzer_type: str, **kwargs):
        """Create an analyzer instance by type."""
        if analyzer_type == "resource":
            return ResourceAnalyzer(**kwargs)
        elif analyzer_type == "minknow":
            return StructuredLogAnalyzer(**kwargs)
        elif analyzer_type == "cross":
            return CrossAnalyzer(**kwargs)
        elif analyzer_type == "timeline":
            return TimelineAnalyzer(**kwargs)
        else:
            raise ValueError(f"Unknown analyzer type: {analyzer_type}")
