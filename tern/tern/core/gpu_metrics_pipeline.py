"""
GPU metrics data pipeline for LogVisualisation.

This module contains the specialized pipeline for GPU metrics data transformation,
implementing the Phase 3 migration of get_gpu_metrics_data() to the pipeline pattern.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class GpuMetricsDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for GPU metrics data transformation."""

    def build_gpu_metrics_pipeline(self, start_time: datetime, end_time: datetime):
        """Build the GPU metrics data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Self for method chaining
        """
        return (
            self.add_step("discover_gpu_columns", self._discover_gpu_columns)
            .add_step("fetch_gpu_metrics", self._fetch_gpu_metrics, start_time, end_time)
            .add_step("process_gpu_metrics", self._process_gpu_metrics)
        )

    async def _discover_gpu_columns(self, data):
        """Discover available GPU columns from resource analyzer.

        Args:
            data: Input data (ignored for discovery step)

        Returns:
            Dictionary with discovered GPU column information
        """
        try:
            # Get a sample of data to discover columns
            import pandas as pd

            df = self.data_sources.resource_analyzer.query()
            all_columns = df.columns.tolist() if not df.empty else []

            logger.info("DEBUG: Total available columns: %d", len(all_columns))
            gpu_related_columns = [col for col in all_columns if "gpu" in col.lower() or "GPU" in col]
            logger.info("DEBUG: GPU-related columns found: %s", gpu_related_columns)

            # Find GPU columns with patterns like: gpu0UtilizationPercent, gpu0Temperature, etc.
            # Also handle cases without gpu prefix for single GPU: UtilizationPercent, Temperature, etc.
            utilization_columns = [col for col in all_columns if "UtilizationPercent" in col]
            temperature_columns = [col for col in all_columns if "Temperature" in col and "gpu" in col.lower()]
            power_columns = [col for col in all_columns if "PowerDrawWatts" in col]
            memory_total_columns = [
                col for col in all_columns if "MemoryTotalMB" in col and ("gpu" in col.lower() or col == "MemoryTotalMB")
            ]
            memory_free_columns = [col for col in all_columns if "MemoryFreeMB" in col and ("gpu" in col.lower() or col == "MemoryFreeMB")]

            # If no gpu-prefixed columns found, check for single GPU columns
            if not utilization_columns:
                utilization_columns = [col for col in all_columns if col == "UtilizationPercent"]
            if not temperature_columns:
                temperature_columns = [col for col in all_columns if col == "Temperature"]
            if not power_columns:
                power_columns = [col for col in all_columns if col == "PowerDrawWatts"]

            logger.info("DEBUG: Utilization columns: %s", utilization_columns)
            logger.info("DEBUG: Temperature columns: %s", temperature_columns)
            logger.info("DEBUG: Power columns: %s", power_columns)
            logger.info("DEBUG: Memory total columns: %s", memory_total_columns)
            logger.info("DEBUG: Memory free columns: %s", memory_free_columns)

            if not utilization_columns and not temperature_columns:
                logger.warning("No GPU utilization or temperature columns found")
                return {"gpu_columns": [], "gpu_mapping": {}}

            # Prepare query columns
            query_columns = utilization_columns + temperature_columns + power_columns + memory_total_columns + memory_free_columns

            # Determine GPU indices and names
            gpu_mapping = {}  # gpu_index -> gpu_name or "GPU" for single GPU

            # Extract GPU indices from column names
            for col in query_columns:
                if col.startswith("gpu") and col[3:].isdigit():
                    # Multi-GPU case: gpu0UtilizationPercent -> GPU 0
                    match = re.search(r"gpu(\d+)", col)
                    if match:
                        gpu_idx = int(match.group(1))
                        gpu_mapping[gpu_idx] = f"GPU {gpu_idx}"
                elif col in [
                    "UtilizationPercent",
                    "Temperature",
                    "PowerDrawWatts",
                    "MemoryTotalMB",
                    "MemoryFreeMB",
                ]:
                    # Single GPU case
                    gpu_mapping[0] = "GPU"

            logger.info("DEBUG: GPU mapping: %s", gpu_mapping)

            return {
                "gpu_columns": query_columns,
                "gpu_mapping": gpu_mapping,
                "utilization_columns": utilization_columns,
                "temperature_columns": temperature_columns,
                "power_columns": power_columns,
                "memory_total_columns": memory_total_columns,
                "memory_free_columns": memory_free_columns,
            }

        except Exception as e:
            logger.error("Error discovering GPU columns: %s", e)
            return {"gpu_columns": [], "gpu_mapping": {}}

    async def _fetch_gpu_metrics(self, data, start_time, end_time):
        """Fetch GPU metrics using discovered columns.

        Args:
            data: Dictionary with discovered GPU column information
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with "records" key containing GPU metric records
        """
        gpu_columns = data.get("gpu_columns", [])

        if not gpu_columns:
            logger.warning("No GPU columns available for fetching")
            return {"records": [], "gpu_info": data}

        # Use generic method for consistent resource column fetching
        result = await BaseTransformations.fetch_resource_columns_with_sorting(self.data_sources, gpu_columns, start_time, end_time)

        # Preserve GPU info for next step
        result["gpu_info"] = data
        return result

    async def _process_gpu_metrics(self, data):
        """Process GPU metrics data.

        Args:
            data: Dictionary with "records" and "gpu_info" keys

        Returns:
            Dictionary with timestamps and GPU metric data
        """
        if not data or "records" not in data:
            logger.warning("No GPU data records found")
            return {
                "timestamps": [],
                "utilization": {},
                "memory_used_pct": {},
                "temperature": {},
                "power_draw": {},
            }

        records = data["records"]
        gpu_info = data.get("gpu_info", {})
        gpu_mapping = gpu_info.get("gpu_mapping", {})

        if not records or not gpu_mapping:
            return {
                "timestamps": [],
                "utilization": {},
                "memory_used_pct": {},
                "temperature": {},
                "power_draw": {},
            }

        # Initialize data structures
        timestamps = []
        utilization_data = {}
        memory_used_pct_data = {}
        temperature_data = {}
        power_draw_data = {}

        # Initialize data structures for each GPU
        for gpu_idx, gpu_name in gpu_mapping.items():
            utilization_data[gpu_name] = []
            memory_used_pct_data[gpu_name] = []
            temperature_data[gpu_name] = []
            power_draw_data[gpu_name] = []

        # Process each record
        for record in records:
            timestamps.append(record["timestamp"])

            # Process each GPU
            for gpu_idx, gpu_name in gpu_mapping.items():
                # Determine column names for this GPU
                if gpu_idx == 0 and gpu_name == "GPU":
                    # Single GPU case
                    util_col = "UtilizationPercent"
                    temp_col = "Temperature"
                    power_col = "PowerDrawWatts"
                    mem_total_col = "MemoryTotalMB"
                    mem_free_col = "MemoryFreeMB"
                else:
                    # Multi-GPU case
                    util_col = f"gpu{gpu_idx}UtilizationPercent"
                    temp_col = f"gpu{gpu_idx}Temperature"
                    power_col = f"gpu{gpu_idx}PowerDrawWatts"
                    mem_total_col = f"gpu{gpu_idx}MemoryTotalMB"
                    mem_free_col = f"gpu{gpu_idx}MemoryFreeMB"

                # Get utilization
                utilization = record.get(util_col, 0) or 0
                utilization_data[gpu_name].append(utilization)

                # Calculate memory used percentage
                mem_total = record.get(mem_total_col, 0) or 0
                mem_free = record.get(mem_free_col, 0) or 0
                if mem_total > 0:
                    mem_used = mem_total - mem_free
                    mem_used_pct = (mem_used / mem_total) * 100
                else:
                    mem_used_pct = 0
                memory_used_pct_data[gpu_name].append(mem_used_pct)

                # Get temperature
                temperature = record.get(temp_col, 0) or 0
                temperature_data[gpu_name].append(temperature)

                # Get power draw
                power_draw = record.get(power_col, 0) or 0
                power_draw_data[gpu_name].append(power_draw)

        logger.info(f"Processed {len(timestamps)} GPU metrics data points for {len(gpu_mapping)} GPUs")
        logger.info("GPUs: %s", list(gpu_mapping.values()))

        return {
            "timestamps": timestamps,
            "utilization": utilization_data,
            "memory_used_pct": memory_used_pct_data,
            "temperature": temperature_data,
            "power_draw": power_draw_data,
        }

    @staticmethod
    @_track_data_performance("get_gpu_metrics_data")
    async def get_gpu_metrics_data(
        cache: VisualizationDataCache, sources: DataSources, start_time: datetime | None = None, end_time: datetime | None = None
    ) -> dict[str, Any]:
        """Get GPU metrics data for dual y-axis visualization.

        Args:
            cache: Visualization data cache instance
            sources: Data sources instance
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary containing:
            {
                "timestamps": List[str] - ISO format timestamps
                "utilization": Dict[str, List[float]] - GPU utilization percentages by GPU
                "memory_used_pct": Dict[str, List[float]] - Memory usage percentages by GPU
                "temperature": Dict[str, List[float]] - Temperature values by GPU
                "power_draw": Dict[str, List[float]] - Power draw values by GPU
            }
        """
        try:
            logger.info("GET_GPU_METRICS_CALLED: start=%s, end=%s", start_time, end_time)

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = GpuMetricsDataPipeline(cache=cache, method_name="get_gpu_metrics_data", start_time=start_time, end_time=end_time)

            pipeline.inject_sources(sources)
            result = await pipeline.build_gpu_metrics_pipeline(start_time, end_time).execute()

            logger.info("GPU_METRICS_TRANSFORM_COMPLETE: Pipeline execution successful")
            return result

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("Error getting GPU metrics data")
            import traceback

            logger.error("Traceback: %s", traceback.format_exc())
            # Return empty data structure on error
            return {
                "timestamps": [],
                "utilization": {},
                "memory_used_pct": {},
                "temperature": {},
                "power_draw": {},
            }
