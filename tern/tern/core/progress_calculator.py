"""Progress calculation engine for the Tern application."""

import time
from dataclasses import dataclass
from typing import Dict, List, Optional

from .interfaces import ProgressUpdate


@dataclass
class StageInfo:
    """Information about a parsing stage."""

    name: str
    weight: int
    description: str
    emoji: str


class ProgressCalculator:
    """Calculates weighted progress based on parsing stages and file tracking."""

    # Stage weights based on typical parsing time distribution
    STAGE_WEIGHTS = {
        "🎣 Casting nets for logs": 5,  # Directory discovery (fast)
        "🐟 Teaching analyzers to fish": 10,  # Parser initialization
        "🐠 Diving for MinKnow logs": 40,  # MinKnow parsing (heavy)
        "🌊 Cross-referencing events": 35,  # Resource parsing (heavy)
        "📊 Counting the catch": 10,  # Analysis & finalization
    }

    # Stage descriptions for user-friendly messages
    STAGE_DESCRIPTIONS = {
        "🎣 Casting nets for logs": "Scanning directories for log files...",
        "🐟 Teaching analyzers to fish": "Initializing parsers and analyzers...",
        "🐠 Diving for MinKnow logs": "Parsing MinKnow log files...",
        "🌊 Cross-referencing events": "Processing resource logs and cross-referencing...",
        "📊 Counting the catch": "Building analysis and generating metrics...",
    }

    def __init__(self):
        self._start_time = None
        self._stage_start_time = None
        self._current_stage = ""
        self._stage_progress = 0.0
        self._cumulative_files_processed = 0  # Track total files processed across all stages
        self._total_files_discovered = 0  # Total files discovered at start
        self._stage_files_processed = 0  # Files processed in current stage only
        self._stage_total_files = 0  # Total files expected in current stage
        self._current_file = ""

    def start_parsing(self) -> None:
        """Start the parsing progress tracking."""
        self._start_time = time.time()
        self._stage_start_time = time.time()
        self._current_stage = ""
        self._stage_progress = 0.0
        self._cumulative_files_processed = 0
        self._total_files_discovered = 0
        self._stage_files_processed = 0
        self._stage_total_files = 0
        self._current_file = ""

    def set_stage(self, stage: str, stage_total_files: int = 0) -> None:
        """Set the current parsing stage.

        Args:
            stage: Name of the stage
            stage_total_files: Number of files expected to be processed in this stage only
        """
        self._current_stage = stage
        self._stage_start_time = time.time()
        self._stage_progress = 0.0
        self._stage_files_processed = 0  # Reset stage counter, but keep cumulative
        self._stage_total_files = stage_total_files
        self._current_file = ""

    def update_file_progress(self, stage_files_processed: int, current_file: str = "") -> None:
        """Update file-based progress within the current stage.

        Args:
            stage_files_processed: Number of files processed in the current stage
            current_file: Name of the current file being processed
        """
        # Calculate how many new files were processed since last update
        new_files = stage_files_processed - self._stage_files_processed
        self._cumulative_files_processed += new_files
        self._stage_files_processed = stage_files_processed
        self._current_file = current_file

        # Calculate stage progress based on stage-specific file counts
        if self._stage_total_files > 0:
            self._stage_progress = (stage_files_processed / self._stage_total_files) * 100
        else:
            self._stage_progress = 0.0

    def calculate_overall_progress(self) -> int:
        """Calculate overall progress (0-100) using stage weights and file progress."""
        if not self._current_stage:
            return 0

        # Get the weight for the current stage
        stage_weight = self.STAGE_WEIGHTS.get(self._current_stage, 10)

        # Calculate progress within the current stage
        stage_progress = min(self._stage_progress, 100.0)

        # Calculate cumulative progress from previous stages
        cumulative_progress = 0
        for stage, weight in self.STAGE_WEIGHTS.items():
            if stage == self._current_stage:
                # Add partial progress for current stage
                cumulative_progress += (stage_progress / 100.0) * weight
                break
            else:
                # Add full weight for completed stages
                cumulative_progress += weight

        # Convert to percentage
        total_weight = sum(self.STAGE_WEIGHTS.values())
        overall_progress = int((cumulative_progress / total_weight) * 100)

        return min(overall_progress, 100)

    def get_elapsed_time(self) -> float:
        """Get total elapsed time since parsing started."""
        if self._start_time is None:
            return 0.0
        return time.time() - self._start_time

    def get_stage_elapsed_time(self) -> float:
        """Get elapsed time for the current stage."""
        if self._stage_start_time is None:
            return 0.0
        return time.time() - self._stage_start_time

    def create_progress_update(self) -> ProgressUpdate:
        """Create a progress update with current state."""
        return ProgressUpdate(
            stage=self._current_stage,
            progress=self.calculate_overall_progress(),
            current_file=self._current_file,
            files_processed=self._cumulative_files_processed,  # Use cumulative count across all stages
            total_files=self._total_files_discovered,  # Use total files discovered
            elapsed_time=self.get_elapsed_time(),
        )

    def get_stage_description(self, stage: str) -> str:
        """Get a user-friendly description for a stage."""
        return self.STAGE_DESCRIPTIONS.get(stage, stage)

    def discover_parseable_files(self, base_dir: str) -> Dict[str, int]:
        """Discover parseable files in the log directory structure."""
        import os

        file_counts = {"resource": 0, "minknow": 0, "total": 0}

        try:
            # Check resource logs
            resource_dir = os.path.join(base_dir, "ont-resource-logger")
            if os.path.isdir(resource_dir):
                for root, dirs, files in os.walk(resource_dir):
                    for file in files:
                        if file.endswith(".log") or file.endswith(".txt"):
                            file_counts["resource"] += 1
                            file_counts["total"] += 1

            # Check MinKnow logs
            minknow_dir = os.path.join(base_dir, "minknow")
            if os.path.isdir(minknow_dir):
                for root, dirs, files in os.walk(minknow_dir):
                    for file in files:
                        if file.endswith(".log") or file.endswith(".txt"):
                            file_counts["minknow"] += 1
                            file_counts["total"] += 1

        except (OSError, FileNotFoundError):
            # If directory discovery fails, return default counts
            pass

        # Store the total discovered count for cumulative tracking
        self._total_files_discovered = file_counts["total"]

        return file_counts

    def format_time(self, seconds: float) -> str:
        """Format elapsed time in a human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            remaining_seconds = int(seconds % 60)
            return f"{minutes}m {remaining_seconds}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"
