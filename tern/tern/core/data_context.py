"""Data context for pipeline dependencies."""

from __future__ import annotations

import logging

# Conditional import for log_parser - works both in development and when vendored
try:
    # Try vendored import first (for built wheel)
    from ..log_parser.analysis.cross_analyzer import CrossAnalyzer
    from ..log_parser.analysis.resource_analyzer import ResourceAnalyzer
    from ..log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from ..log_parser.analysis.timeline_analyzer import TimelineAnalyzer
except ImportError:
    # Fall back to external package (for development)
    from log_parser.analysis.cross_analyzer import CrossAnalyzer
    from log_parser.analysis.resource_analyzer import ResourceAnalyzer
    from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from log_parser.analysis.timeline_analyzer import TimelineAnalyzer

from .cache import VisualizationDataCache
from .pipeline.data_sources import DataSources

logger = logging.getLogger(__name__)


class DataContext:
    """Context object that holds the cache and data sources needed for pipeline operations."""

    def __init__(
        self, cross_analyzer: <PERSON><PERSON>nal<PERSON><PERSON>, resource_analyzer: <PERSON><PERSON><PERSON><PERSON><PERSON>, minknow_analyzer: Structured<PERSON>og<PERSON><PERSON><PERSON><PERSON>, timeline_analyzer
    ):
        """
        Initialize DataContext with analyzers.

        Args:
            cross_analyzer: CrossAnalyzer instance for accessing cross-cross-analyzer operations
            resource_analyzer: ResourceAnalyzer instance for accessing resource data
            minknow_analyzer: StructuredLogAnalyzer instance for accessing minknow data
            timeline_analyzer: TimelineAnalyzer instance for accessing timeline data
        """
        self.cross_analyzer = cross_analyzer
        self.resource_analyzer = resource_analyzer
        self.minknow_analyzer = minknow_analyzer
        self.timeline_analyzer = timeline_analyzer

        # Hierarchical Transformation Cache
        # Increased cache size to better support drill-up/drill-down UI patterns
        self.transformation_cache = VisualizationDataCache(max_cached_results=40)

        # Pipeline infrastructure for data sources
        self.pipeline_sources = DataSources(
            cross_analyzer=cross_analyzer,
            resource_analyzer=resource_analyzer,
            minknow_analyzer=minknow_analyzer,
            timeline_analyzer=timeline_analyzer,
        )

    def get_transformation_cache_stats(self):
        """Get transformation cache statistics for monitoring and debugging."""
        return self.transformation_cache.get_cache_stats()

    def reset_transformation_cache(self):
        """Reset the transformation cache. Useful for testing."""
        logger.info("TRANSFORMATION_CACHE_RESET: Manually clearing transformation cache")
        self.transformation_cache = VisualizationDataCache(max_cached_results=40)
