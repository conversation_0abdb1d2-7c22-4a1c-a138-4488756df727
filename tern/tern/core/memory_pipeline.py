"""
Memory data pipeline for LogVisualisation.

This module contains the specialized pipeline for memory data transformation,
implementing the Phase 2 migration of get_memory_data() to the pipeline pattern.
"""

import logging
from datetime import datetime
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class MemoryDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for memory data transformation."""

    def build_memory_pipeline(self, start_time: datetime, end_time: datetime):
        """Build the memory data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Self for method chaining
        """
        memory_columns = ["MemTotalBytes", "MemUsedBytes", "SwapTotalBytes", "SwapUsedBytes"]

        return (
            self.add_step("fetch_resource_metrics", self._fetch_resource_metrics, memory_columns, start_time, end_time)
            .add_step("convert_units", BaseTransformations.transform_convert_units, "bytes", "gb")
            .add_step("format_time_series", BaseTransformations.format_time_series)
        )

    async def _fetch_resource_metrics(self, data, columns, start_time, end_time):
        """Fetch resource metrics from data sources.

        Args:
            data: Input data (ignored for data source step)
            columns: List of column names to fetch
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with "records" key containing list of resource records
        """
        logger.info(f"Fetching resource metrics for columns: {columns}")
        logger.info(f"Time range: {start_time} to {end_time}")

        return await self.data_sources.resource_metrics(columns=columns, start_time=start_time, end_time=end_time)

    @staticmethod
    @_track_data_performance("get_memory_data")
    async def get_memory_data(
        cache: VisualizationDataCache, sources: DataSources, start_time: datetime | None = None, end_time: datetime | None = None
    ) -> dict[str, Any]:
        """Get memory usage data for visualization.

        Simplified pipeline pattern - caching handled automatically by base pipeline.

        Args:
            cache: Visualization data cache instance
            sources: Data sources instance
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary containing:
            {
                "timestamps": List[str] - ISO format timestamps
                "mem_total_gb": List[float] - Total memory in GB
                "mem_used_gb": List[float] - Used memory in GB
                "swap_total_gb": List[float] - Total swap in GB
                "swap_used_gb": List[float] - Used swap in GB
            }
        """
        try:
            logger.info("GET_MEMORY_DATA_CALLED: start=%s, end=%s", start_time, end_time)

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = MemoryDataPipeline(cache=cache, method_name="get_memory_data", start_time=start_time, end_time=end_time)

            pipeline.inject_sources(sources).build_memory_pipeline(start_time, end_time)
            result = await pipeline.execute()

            # Transform field names to match original output format for backward compatibility
            if result and "MemTotalBytes" in result:
                # Rename fields to match original output format
                result["mem_total_gb"] = result.pop("MemTotalBytes", [])
                result["mem_used_gb"] = result.pop("MemUsedBytes", [])
                result["swap_total_gb"] = result.pop("SwapTotalBytes", [])
                result["swap_used_gb"] = result.pop("SwapUsedBytes", [])
            elif result and not result.get("timestamps"):
                # Handle empty data case - ensure all expected fields are present
                result = {
                    "timestamps": [],
                    "mem_total_gb": [],
                    "mem_used_gb": [],
                    "swap_total_gb": [],
                    "swap_used_gb": [],
                }

            logger.info("GET_MEMORY_DATA_PIPELINE_COMPLETE: Pipeline execution successful")
            return result

        except Exception:
            logger.exception("Error getting memory data")
            # Return empty data structure on error
            return {
                "timestamps": [],
                "mem_total_gb": [],
                "mem_used_gb": [],
                "swap_total_gb": [],
                "swap_used_gb": [],
            }
