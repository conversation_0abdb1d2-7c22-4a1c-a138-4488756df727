"""Parsing controller for managing log parsing operations."""

import threading
import time
from typing import Optional

from .container import AnalyzerContainer
from .interfaces import IParsingController


class ParsingController(IParsingController):
    """Controller for managing parsing operations with cancellation support."""

    def __init__(self, container: AnalyzerContainer):
        self.container = container
        self._parsing_thread: Optional[threading.Thread] = None
        self._cancellation_requested = False
        self._lock = threading.Lock()

    def start_parsing(self, log_folder: str) -> bool:
        """Start parsing process, returns success status."""
        with self._lock:
            if self.is_parsing():
                return False  # Already parsing

            # Reset cancellation flag
            self._cancellation_requested = False

            # Start parsing asynchronously
            success = self.container.start_parsing_async(log_folder)

            if success:
                # Note: The parsing thread is created inside start_parsing_async
                # We don't store a direct reference since it's managed by the container
                pass

            return success

    def cancel_parsing(self) -> bool:
        """Cancel current parsing operation."""
        with self._lock:
            if not self.is_parsing():
                return False  # Not currently parsing

            # Set cancellation flag
            self._cancellation_requested = True

            # Wait for parsing to complete (with timeout)
            if self._parsing_thread and self._parsing_thread.is_alive():
                self._parsing_thread.join(timeout=5.0)  # 5 second timeout

            return True

    def is_parsing(self) -> bool:
        """Check if parsing is currently in progress."""
        status = self.container.get_status()
        return status.status == "parsing"

    def is_cancellation_requested(self) -> bool:
        """Check if cancellation has been requested."""
        return self._cancellation_requested

    def get_parsing_status(self) -> dict:
        """Get detailed parsing status."""
        return self.container.get_status().__dict__

    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """Wait for parsing to complete, returns True if successful."""
        start_time = time.time()

        while self.is_parsing():
            if timeout and (time.time() - start_time) > timeout:
                return False  # Timeout

            if self._cancellation_requested:
                return False  # Cancelled

            time.sleep(0.1)  # Small delay to avoid busy waiting

        # Check final status
        status = self.container.get_status()
        return status.status == "complete"

    def reset_parsing_state(self) -> None:
        """Reset parsing state to allow new parsing operations."""
        with self._lock:
            self._cancellation_requested = False
            self._parsing_thread = None
            self.container.reset_status()


class MockParsingController(IParsingController):
    """Mock parsing controller for testing."""

    def __init__(self):
        self._is_parsing = False
        self._mock_success = True

    def start_parsing(self, log_folder: str) -> bool:
        """Mock parsing start."""
        self._is_parsing = True
        return self._mock_success

    def cancel_parsing(self) -> bool:
        """Mock parsing cancellation."""
        if self._is_parsing:
            self._is_parsing = False
            return True
        return False

    def is_parsing(self) -> bool:
        """Mock parsing status."""
        return self._is_parsing

    def set_mock_success(self, success: bool) -> None:
        """Set mock success status for testing."""
        self._mock_success = success
