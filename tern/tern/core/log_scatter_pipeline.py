"""
Log scatter data pipeline for LogVisualisation.

This module contains the specialized pipeline for log scatter data transformation,
implementing the Phase 3 migration of get_log_scatter_data() to the pipeline pattern.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class LogScatterDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for log scatter data transformation."""

    def build_log_scatter_pipeline(self, start_time: datetime, end_time: datetime):
        """Build the log scatter data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Self for method chaining
        """
        return (
            self.add_step("calculate_bucket_frequency", self._calculate_bucket_frequency, start_time, end_time)
            .add_step("discover_positions", self._discover_positions)
            .add_step("collect_timeline_data", self._collect_timeline_data, start_time, end_time)
            .add_step("process_scatter_data", self._process_scatter_data)
        )

    async def _calculate_bucket_frequency(self, data, start_time, end_time):
        """Calculate dynamic bucket size based on time range.

        Args:
            data: Input data (ignored for calculation step)
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with bucket_freq key
        """
        if start_time and end_time:
            time_range = end_time - start_time

            if time_range <= timedelta(days=1):
                bucket_freq = "15Min"  # 15 minutes for <= 1 day
            elif time_range <= timedelta(days=7):
                bucket_freq = "1h"  # 1 hour for <= 1 week
            elif time_range <= timedelta(days=30):
                bucket_freq = "6h"  # 6 hours for <= 1 month
            else:
                bucket_freq = "6h"  # 6 hours for longer periods
        else:
            bucket_freq = "1h"  # Default bucket size

        logger.info("Using bucket frequency: %s", bucket_freq)
        return {"bucket_freq": bucket_freq}

    async def _discover_positions(self, data):
        """Discover all available positions from minknow analyzer.

        Args:
            data: Dictionary with bucket_freq information

        Returns:
            Dictionary with positions and bucket_freq
        """
        try:
            # Get all positions from minknow analyzer
            all_positions = self.data_sources.minknow_analyzer.get_positions()
            logger.info("Found positions: %s", all_positions)

            if not all_positions:
                logger.warning("No positions found")
                return {"positions": [], "bucket_freq": data.get("bucket_freq", "1h")}

            return {"positions": all_positions, "bucket_freq": data.get("bucket_freq", "1h")}

        except Exception as e:
            logger.error("Error discovering positions: %s", e)
            return {"positions": [], "bucket_freq": data.get("bucket_freq", "1h")}

    async def _collect_timeline_data(self, data, start_time, end_time):
        """Collect log level timeline data for all positions.

        Args:
            data: Dictionary with positions and bucket_freq
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with timeline data for all positions
        """
        positions = data.get("positions", [])
        bucket_freq = data.get("bucket_freq", "1h")

        if not positions:
            return {"positions": [], "timeline_data": {}, "all_timestamps": []}

        timeline_data = {}
        all_timestamps = set()

        # Process each position
        for position in positions:
            try:
                # Get log level timeline for this position
                position_timeline = self.data_sources.minknow_analyzer.get_log_level_timeline(
                    start=start_time, end=end_time, position=position, freq=bucket_freq
                )

                timeline_data[position] = position_timeline

                # Add timestamps to global set
                timestamps = position_timeline.get("timestamps", [])
                all_timestamps.update(timestamps)

            except Exception as e:
                logger.warning("Error collecting timeline data for position %s: %s", position, e)
                timeline_data[position] = {"timestamps": [], "log_levels": {}}

        # Convert timestamps set to sorted list
        all_timestamps = sorted(list(all_timestamps))

        return {"positions": positions, "timeline_data": timeline_data, "all_timestamps": all_timestamps}

    async def _process_scatter_data(self, data):
        """Process timeline data into scatter plot format.

        Args:
            data: Dictionary with positions, timeline_data, and all_timestamps

        Returns:
            Dictionary with scatter plot data
        """
        positions = data.get("positions", [])
        timeline_data = data.get("timeline_data", {})
        all_timestamps = data.get("all_timestamps", [])

        if not positions:
            return {
                "timestamps": [],
                "positions": [],
                "scatter_data": {
                    "WARNING": {"x": [], "y": [], "sizes": []},
                    "ERROR": {"x": [], "y": [], "sizes": []},
                },
            }

        # Initialize scatter data structures
        scatter_data = {
            "WARNING": {"x": [], "y": [], "sizes": []},
            "ERROR": {"x": [], "y": [], "sizes": []},
        }

        # Process each position's timeline data
        for position in positions:
            position_data = timeline_data.get(position, {})
            timestamps = position_data.get("timestamps", [])
            log_levels = position_data.get("log_levels", {})

            # Process WARNING data
            if "WARNING" in log_levels:
                warning_counts = log_levels["WARNING"]
                for i, count in enumerate(warning_counts):
                    if count > 0 and i < len(timestamps):  # Only add points where there are warnings
                        scatter_data["WARNING"]["x"].append(timestamps[i])
                        scatter_data["WARNING"]["y"].append(position)
                        scatter_data["WARNING"]["sizes"].append(count)

            # Process ERROR data
            if "ERROR" in log_levels:
                error_counts = log_levels["ERROR"]
                for i, count in enumerate(error_counts):
                    if count > 0 and i < len(timestamps):  # Only add points where there are errors
                        scatter_data["ERROR"]["x"].append(timestamps[i])
                        scatter_data["ERROR"]["y"].append(position)
                        scatter_data["ERROR"]["sizes"].append(count)

        logger.info("Processed log scatter data:")
        logger.info("  - Timestamps: %d", len(all_timestamps))
        logger.info("  - Positions: %d", len(positions))
        logger.info("  - Warning points: %d", len(scatter_data["WARNING"]["x"]))
        logger.info("  - Error points: %d", len(scatter_data["ERROR"]["x"]))

        return {
            "timestamps": all_timestamps,
            "positions": positions,
            "scatter_data": scatter_data,
        }

    @staticmethod
    @_track_data_performance("get_log_scatter_data")
    async def get_log_scatter_data(
        cache: VisualizationDataCache, sources: DataSources, start_time: datetime | None = None, end_time: datetime | None = None
    ) -> dict[str, Any]:
        """Get log scatter data for visualization showing warnings and errors by position.

        Args:
            cache: Transformation cache instance
            pipeline_sources: Data sources for pipeline
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary containing:
            {
                "timestamps": List[str] - ISO format timestamps
                "positions": List[str] - Position names
                "scatter_data": Dict[str, Dict] - Scatter data by log level containing:
                    "WARNING": {"x": List[str], "y": List[str], "sizes": List[int]}
                    "ERROR": {"x": List[str], "y": List[str], "sizes": List[int]}
            }
        """
        try:
            logger.info("GET_LOG_SCATTER_CALLED: start=%s, end=%s", start_time, end_time)

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = LogScatterDataPipeline(cache=cache, method_name="get_log_scatter_data", start_time=start_time, end_time=end_time)

            pipeline.inject_sources(sources)
            result = await pipeline.build_log_scatter_pipeline(start_time, end_time).execute()

            logger.info("LOG_SCATTER_TRANSFORM_COMPLETE: Pipeline execution successful")

            return result

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("Error getting log scatter data")
            import traceback

            logger.error("Traceback: %s", traceback.format_exc())
            # Return empty data structure on error
            return {
                "timestamps": [],
                "positions": [],
                "scatter_data": {
                    "WARNING": {"x": [], "y": [], "sizes": []},
                    "ERROR": {"x": [], "y": [], "sizes": []},
                },
            }
