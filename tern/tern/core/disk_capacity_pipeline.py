"""
Disk capacity data pipeline for LogVisualisation.

This module contains the specialized pipeline for disk capacity data transformation,
implementing the Phase 3 migration of get_disk_capacity_data() to the pipeline pattern.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class DiskCapacityDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for disk capacity data transformation."""

    def build_disk_capacity_pipeline(self, start_time: datetime, end_time: datetime, small_mount_threshold_gb: float = 1.0):
        """Build the disk capacity data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range
            small_mount_threshold_gb: Threshold for grouping small mounts into "Other"

        Returns:
            Self for method chaining
        """
        return (
            self.add_step("discover_disk_columns", self._discover_disk_columns)
            .add_step("fetch_disk_metrics", self._fetch_disk_metrics, start_time, end_time)
            .add_step("process_disk_capacity", self._process_disk_capacity, small_mount_threshold_gb)
        )

    async def _discover_disk_columns(self, data):
        """Discover available disk columns using generic method.

        Args:
            data: Input data (ignored for discovery step)

        Returns:
            Dictionary with discovered disk column information
        """
        try:
            # Define disk column patterns
            prefix_patterns = {"total": "TotalBytes", "free": "FreeBytes", "mount": "Mountpoint"}
            index_patterns = {"disk": r"Disk(\d+)"}

            # Use generic column discovery method
            result = BaseTransformations.discover_resource_columns(self.data_sources, prefix_patterns, index_patterns)

            # Convert to expected format for disk pipeline
            discovered_columns = result.get("discovered_columns", [])
            device_mapping = result.get("device_mapping", {})
            column_groups = result.get("column_groups", {})
            device_indices = result.get("device_indices", [])

            logger.info("DEBUG: Discovered %d disk columns for %d devices", len(discovered_columns), len(device_indices))

            return {
                "disk_columns": discovered_columns,
                "disk_indices": device_indices,
                "total_columns": column_groups.get("total", []),
                "free_columns": column_groups.get("free", []),
                "mount_columns": column_groups.get("mount", []),
            }

        except Exception as e:
            logger.error("Error discovering disk columns: %s", e)
            return {"disk_columns": [], "disk_indices": []}

    async def _fetch_disk_metrics(self, data, start_time, end_time):
        """Fetch disk metrics using generic method with info preservation."""
        return await BaseTransformations.fetch_resource_metrics_with_info_preservation(
            self.data_sources, data, "disk_columns", "disk_info", start_time, end_time
        )

    async def _process_disk_capacity(self, data, small_mount_threshold_gb):
        """Process disk capacity data with mount point grouping.

        Args:
            data: Dictionary with "records" and "disk_info" keys
            small_mount_threshold_gb: Threshold for grouping small mounts

        Returns:
            Dictionary with timestamps and mount_points data
        """
        if not data or "records" not in data:
            logger.warning("No disk data records found")
            return {"timestamps": [], "mount_points": {}}

        records = data["records"]
        disk_info = data.get("disk_info", {})
        disk_indices = disk_info.get("disk_indices", [])

        if not records or not disk_indices:
            return {"timestamps": [], "mount_points": {}}

        # Convert threshold to bytes for comparison
        threshold_bytes = small_mount_threshold_gb * (1024**3)

        # Determine mount point names and which are small
        mount_point_mapping = {}  # disk_index -> mount_point_name
        small_mounts = set()

        # Sample first 10 records to determine mount points
        for record in records[:10]:
            for disk_idx in disk_indices:
                mount_col = f"Disk{disk_idx}Mountpoint"
                total_col = f"Disk{disk_idx}TotalBytes"

                if mount_col in record and total_col in record:
                    mount_point = record[mount_col]
                    total_bytes = record.get(total_col, 0) or 0

                    if mount_point and mount_point not in mount_point_mapping.values():
                        mount_point_mapping[disk_idx] = mount_point

                        # Check if it's a small mount
                        if total_bytes < threshold_bytes:
                            small_mounts.add(disk_idx)

        logger.info("DEBUG: Mount point mapping: %s", mount_point_mapping)
        logger.info("DEBUG: Small mounts (disk indices): %s", small_mounts)

        # Initialize data structures for mount points
        timestamps = []
        mount_points_data = {}

        for disk_idx, mount_point in mount_point_mapping.items():
            if disk_idx not in small_mounts:
                mount_points_data[mount_point] = []

        # If we have small mounts, add "Other" category
        if small_mounts:
            mount_points_data["Other"] = []

        # Process each record
        for record in records:
            timestamps.append(record["timestamp"])

            # Collect usage for small mounts
            small_mount_usage_total = 0.0

            # Process each disk
            for disk_idx in disk_indices:
                total_col = f"Disk{disk_idx}TotalBytes"
                free_col = f"Disk{disk_idx}FreeBytes"

                total_bytes = record.get(total_col, 0) or 0
                free_bytes = record.get(free_col, 0) or 0

                # Calculate usage percentage
                if total_bytes > 0:
                    used_bytes = total_bytes - free_bytes
                    usage_percent = (used_bytes / total_bytes) * 100
                else:
                    usage_percent = 0.0

                # Add to appropriate category
                if disk_idx in small_mounts:
                    small_mount_usage_total += usage_percent
                elif disk_idx in mount_point_mapping:
                    mount_point = mount_point_mapping[disk_idx]
                    mount_points_data[mount_point].append(usage_percent)

            # Add aggregated small mounts usage to "Other" if applicable
            if small_mounts:
                mount_points_data["Other"].append(small_mount_usage_total)

        logger.info(f"Processed {len(timestamps)} disk capacity data points for {len(mount_points_data)} mount groups")
        logger.info("Mount points: %s", list(mount_points_data.keys()))
        if small_mounts:
            small_mount_names = [mount_point_mapping.get(idx, f"Disk{idx}") for idx in small_mounts]
            logger.info("Small mounts grouped into 'Other': %s", small_mount_names)

        return {"timestamps": timestamps, "mount_points": mount_points_data}

    @staticmethod
    @_track_data_performance("get_disk_capacity_data")
    async def get_disk_capacity_data(
        cache: VisualizationDataCache,
        sources: DataSources,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        small_mount_threshold_gb: float = 1.0,
    ) -> dict[str, Any]:
        """Get disk capacity usage data for visualization with stacked area chart.

        Args:
            cache: Visualization data cache instance
            sources: Data sources instance
            start_time: Start timestamp for data range
            end_time: End timestamp for data range
            small_mount_threshold_gb: Threshold in GB to group small mounts into "Other"

        Returns:
            Dictionary containing:
            {
                "timestamps": List[str] - ISO format timestamps
                "mount_points": Dict[str, List[float]] - Usage percentages by mount point
            }
        """
        try:
            logger.info(f"GET_DISK_CAPACITY_CALLED: start={start_time}, end={end_time}, threshold={small_mount_threshold_gb}")

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = DiskCapacityDataPipeline(
                cache=cache,
                method_name="get_disk_capacity_data",
                start_time=start_time,
                end_time=end_time,
                small_mount_threshold_gb=small_mount_threshold_gb,
            )

            pipeline.inject_sources(sources)
            result = await pipeline.build_disk_capacity_pipeline(start_time, end_time, small_mount_threshold_gb).execute()

            logger.info("DISK_CAPACITY_TRANSFORM_COMPLETE: Pipeline execution successful")
            return result

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("Error getting disk capacity data")
            import traceback

            logger.error("Traceback: %s", traceback.format_exc())
            # Return empty data structure on error
            return {"timestamps": [], "mount_points": {}}
