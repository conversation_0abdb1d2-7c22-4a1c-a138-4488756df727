"""Custom exceptions for the log visualizer."""

from __future__ import annotations


class AnalyzerError(Exception):
    """Base class for analyzer-related exceptions."""


class AnalyzerNotFoundError(AnalyzerError):
    """Exception raised when an analyzer is not found."""


class AnalyzerProcessingError(AnalyzerError):
    """Exception raised when there is an error processing analyzer data."""


class InvalidTimestampError(AnalyzerError):
    """Exception raised when a timestamp is invalid."""
