"""
Unified interface to all analyzer data sources.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

# Conditional import handling - same pattern as LogVisualisation
try:
    # Try vendored import first (for built wheel)
    from ...log_parser.analysis.cross_analyzer import CrossAnalyzer
    from ...log_parser.analysis.resource_analyzer import ResourceAnalyzer
    from ...log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from ...log_parser.analysis.timeline_analyzer import TimelineAnalyzer
    from ...log_parser.utils import convert_to_serializable
except ImportError:
    # Fall back to external package (for development)
    from log_parser.analysis.cross_analyzer import CrossAnalyzer
    from log_parser.analysis.resource_analyzer import ResourceAnalyzer
    from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
    from log_parser.analysis.timeline_analyzer import TimelineAnalyzer
    from log_parser.utils import convert_to_serializable

logger = logging.getLogger(__name__)


class DataSources:
    """Unified interface to all analyzer data sources."""

    def __init__(
        self,
        cross_analyzer: <PERSON><PERSON>naly<PERSON>,
        resource_analyzer: Resource<PERSON>nalyzer,
        minknow_analyzer: StructuredLogAnalyzer,
        timeline_analyzer: TimelineAnalyzer,
    ):
        """
        Initialize DataSources with analyzer instances.

        Args:
            cross_analyzer: CrossAnalyzer instance for accessing cross-cross-analyzer operations
            resource_analyzer: ResourceAnalyzer instance for accessing resource data
            minknow_analyzer: StructuredLogAnalyzer instance for accessing minknow data
            timeline_analyzer: TimelineAnalyzer instance for accessing timeline data
        """
        self.cross_analyzer = cross_analyzer
        self.resource_analyzer = resource_analyzer
        self.minknow_analyzer = minknow_analyzer
        self.timeline_analyzer = timeline_analyzer

    # Resource data access methods

    def resource_columns(self, include_stats: bool = False) -> Dict[str, Any]:
        """
        Get available resource columns, optionally with statistics.

        This method supports the existing pattern where methods like
        get_disk_capacity_data(), get_io_throughput_data(), and get_gpu_metrics_data()
        dynamically discover available columns by name patterns.

        Args:
            include_stats: Whether to include column statistics (default: False for performance)
        """
        logger.debug("Getting resource columns")

        # Get column statistics from analyzer
        column_stats = self.resource_analyzer.get_column_stats()
        columns = list(column_stats.keys())

        result = {"columns": columns}

        if include_stats:
            # Make sure all values are properly serializable
            serialized_stats = convert_to_serializable(column_stats)
            result["column_stats"] = serialized_stats

        logger.debug("Found %d resource columns", len(columns))
        return result

    def find_columns_by_pattern(self, patterns: List[str], case_sensitive: bool = False) -> List[str]:
        """
        Find resource columns matching name patterns.

        This supports the common pattern in methods like get_disk_capacity_data()
        where columns are filtered by patterns like ["disk", "Disk"].

        Args:
            patterns: List of patterns to match in column names
            case_sensitive: Whether to perform case-sensitive matching

        Returns:
            List of matching column names
        """
        all_columns = self.resource_columns()["columns"]
        matching_columns = []

        for column in all_columns:
            for pattern in patterns:
                if case_sensitive:
                    if pattern in column:
                        matching_columns.append(column)
                        break
                else:
                    if pattern.lower() in column.lower():
                        matching_columns.append(column)
                        break

        logger.debug("Found %d columns matching patterns %s", len(matching_columns), patterns)
        return matching_columns

    async def resource_metrics(
        self, columns: List[str], start_time: datetime, end_time: datetime, limit: int = 5000, offset: int = 0
    ) -> Dict[str, Any]:
        """
        Query resource metrics with time range and column filters using optimized bulk processing.

        Args:
            columns: List of column names to fetch
            start_time: Start timestamp for data range
            end_time: End timestamp for data range
            limit: Maximum number of records to return
            offset: Offset for pagination

        Returns:
            Dictionary with records and metadata
        """
        logger.debug("Querying resource logs: %s to %s, columns=%s", start_time, end_time, columns)

        # Use optimized bulk fetching method
        from .transformations import BaseTransformations

        result = await BaseTransformations.fetch_resource_columns_bulk(self, columns, start_time, end_time)
        records = result.get("records", [])

        # Apply limit and offset
        if offset:
            records = records[offset:]
        if limit and len(records) > limit:
            records = records[:limit]

        return {
            "records": records,
            "total": len(records),
            "limit": limit,
            "offset": offset,
        }

    # Log event data access methods

    def log_events_raw(self, start_time: datetime = None, end_time: datetime = None, positions: List[str] = None) -> List[Dict[str, Any]]:
        """
        Access raw, unbucketed log events for pipeline processing.

        This method returns raw event data that can then be bucketed by the pipeline,
        replacing the analyzer-based bucketing in CrossAnalyzer.get_heatmap_data().
        """
        logger.debug("Getting raw log events: %s to %s, positions=%s", start_time, end_time, positions)

        # Get raw events from MinKnow analyzer (bypassing bucketing)
        try:
            df = self.minknow_analyzer.query(start=start_time, end=end_time)
            if df.empty:
                return []

            # Convert DataFrame to list of events
            events = []
            for _, row in df.iterrows():
                event = {
                    "timestamp": row.name.isoformat() if hasattr(row.name, "isoformat") else str(row.name),
                    "position": row.get("folder_name", "unknown"),
                    "severity": row.get("log_level", "INFO"),
                    "message": row.get("message", ""),
                    "source": "minknow",
                }

                # Apply position filter if specified
                if positions is None or event["position"] in positions:
                    events.append(event)

            logger.debug(f"Retrieved {len(events)} raw log events")
            return events

        except Exception as e:
            logger.warning(f"Failed to get raw log events: {e}")
            return []

    def log_events(
        self, start_time: datetime = None, end_time: datetime = None, positions: List[str] = None, bucket_size_seconds: int = 300
    ) -> Dict[str, Any]:
        """
        Access log events using pipeline-based bucketing.

        NOTE: This method now uses log_events_raw() + transform_bucket_by_time() instead
        of the legacy CrossAnalyzer.get_heatmap_data() method.
        """
        logger.debug("Getting log events: %s to %s, positions=%s", start_time, end_time, positions)

        # Get raw events and apply bucketing transformation
        raw_events = self.log_events_raw(start_time=start_time, end_time=end_time, positions=positions)

        # Apply bucketing using pipeline transformation
        from .transformations import BaseTransformations

        bucketed_events = BaseTransformations.transform_bucket_by_time(
            raw_events, bucket_size_seconds=bucket_size_seconds, start_time=start_time, end_time=end_time
        )

        return {"events": bucketed_events}

    def log_scatter_data(self, start_time: datetime = None, end_time: datetime = None, positions: List[str] = None) -> List[Dict[str, Any]]:
        """Access scatter plot data from structured logs."""
        logger.debug("Getting log scatter data: %s to %s, positions=%s", start_time, end_time, positions)

        # Get all log events
        events = self.minknow_analyzer.get_all_log_events()

        # Apply filters similar to existing get_log_scatter_data logic
        filtered_events = []
        for event in events:
            # Apply time filter
            if start_time and event.get("timestamp") and event["timestamp"] < start_time:
                continue
            if end_time and event.get("timestamp") and event["timestamp"] > end_time:
                continue

            # Apply position filter
            if positions and event.get("position") not in positions:
                continue

            filtered_events.append(event)

        return filtered_events

    def log_level_anomalies(self, start_time: datetime = None, end_time: datetime = None) -> Dict[str, Any]:
        """Access log level anomaly detection from StructuredLogAnalyzer."""
        logger.debug("Getting log level anomalies: %s to %s", start_time, end_time)

        # Call the detect_log_level_anomalies method that was requested
        anomalies = self.minknow_analyzer.detect_log_level_anomalies(start_time=start_time, end_time=end_time)

        return anomalies

    # Timeline data access methods (future use)

    def timeline_events(self, start_time: datetime = None, end_time: datetime = None) -> Dict[str, Any]:
        """Access TimelineAnalyzer timeline data."""
        logger.debug("Getting timeline events: %s to %s", start_time, end_time)

        try:
            timeline_data = self.timeline_analyzer.get_timeline_events(start_time, end_time)
            return convert_to_serializable(timeline_data)
        except Exception as e:
            logger.warning(f"Failed to get timeline events: {e}")
            return {"timeline_events": []}

    def timeline_duration_stats(self, start_time: datetime = None, end_time: datetime = None) -> Dict[str, Any]:
        """Access TimelineAnalyzer statistics."""
        logger.debug("Getting timeline duration stats: %s to %s", start_time, end_time)

        try:
            stats = self.timeline_analyzer.get_timeline_duration_stats(start_time, end_time)
            return convert_to_serializable(stats)
        except Exception as e:
            logger.warning(f"Failed to get timeline duration stats: {e}")
            return {"stats": {}}

    # MinKnow specific access methods

    def minknow_positions(self) -> List[str]:
        """Get all available MinKnow positions."""
        try:
            return self.minknow_analyzer.get_positions()
        except Exception as e:
            logger.warning(f"Failed to get MinKnow positions: {e}")
            return []

    def minknow_log_level_timeline(
        self, position: str, start_time: datetime = None, end_time: datetime = None, bucket_size_seconds: int = 300
    ) -> Dict[str, Any]:
        """Get MinKnow log level timeline for a specific position."""
        try:
            timeline_data = self.minknow_analyzer.get_log_level_timeline(
                position=position, start_time=start_time, end_time=end_time, bucket_size_seconds=bucket_size_seconds
            )
            return timeline_data
        except Exception as e:
            logger.warning(f"Failed to get MinKnow log level timeline for {position}: {e}")
            return {"timeline": []}

    # Utility methods

    def validate_connections(self) -> Dict[str, bool]:
        """Validate that all analyzer connections are working."""
        status = {}

        try:
            # Test ResourceAnalyzer
            self.resource_analyzer.get_column_stats()
            status["resource_analyzer"] = True
        except Exception as e:
            logger.error(f"ResourceAnalyzer validation failed: {e}")
            status["resource_analyzer"] = False

        try:
            # Test CrossAnalyzer with a legitimate cross-analyzer operation
            self.cross_analyzer.get_common_time_range()
            status["cross_analyzer"] = True
        except Exception as e:
            logger.error(f"CrossAnalyzer validation failed: {e}")
            status["cross_analyzer"] = False

        try:
            # Test TimelineAnalyzer
            self.timeline_analyzer.get_timeline_events(limit=1)
            status["timeline_analyzer"] = True
        except Exception as e:
            logger.error(f"TimelineAnalyzer validation failed: {e}")
            status["timeline_analyzer"] = False

        return status
