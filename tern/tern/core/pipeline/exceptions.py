"""
Exception classes for pipeline execution.
"""


class PipelineExecutionError(Exception):
    """Enhanced error handling for pipeline execution."""

    def __init__(self, message: str, step_name: str = None, step_data: dict = None):
        self.step_name = step_name
        self.step_data = step_data or {}

        if step_name:
            message = f"Pipeline step '{step_name}' failed: {message}"

        super().__init__(message)


class PipelineValidationError(Exception):
    """Error raised when pipeline configuration is invalid."""

    def __init__(self, message: str, pipeline_name: str = None):
        self.pipeline_name = pipeline_name

        if pipeline_name:
            message = f"Pipeline '{pipeline_name}' validation failed: {message}"

        super().__init__(message)


class PipelineCacheError(Exception):
    """Error raised when pipeline caching fails."""

    pass
