"""
Base transformation methods for pipelines.

This module contains reusable transformation functions that can be
composed into pipelines for data processing.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class BaseTransformations:
    """Base transformation methods for pipelines."""

    # Unit conversion constants
    BYTES_TO_GB = 1024 * 1024 * 1024
    BYTES_TO_MB = 1024 * 1024

    @staticmethod
    def transform_convert_units(data: Dict[str, Any], from_unit: str, to_unit: str) -> Dict[str, Any]:
        """
        Transform data by converting units.

        Converts numeric fields based on naming patterns (e.g., fields containing "bytes").

        Args:
            data: Dictionary with "records" key containing list of records
            from_unit: Source unit (e.g., "bytes", "mb")
            to_unit: Target unit (e.g., "gb", "mb")

        Returns:
            Dictionary with converted records

        Example:
            Input:
                data = {
                    "records": [
                        {"timestamp": "2023-01-01T00:00:00", "MemTotalBytes": 1073741824, "CPULoad": 0.5},
                        {"timestamp": "2023-01-01T00:01:00", "MemUsedBytes": 536870912, "CPULoad": 0.7}
                    ]
                }
                from_unit = "bytes"
                to_unit = "gb"

            Output:
                {
                    "records": [
                        {"timestamp": "2023-01-01T00:00:00", "MemTotalBytes": 1.0, "CPULoad": 0.5},
                        {"timestamp": "2023-01-01T00:01:00", "MemUsedBytes": 0.5, "CPULoad": 0.7}
                    ]
                }

        Note: Only fields with "bytes" in the name are converted. Other fields remain unchanged.
        """
        if not data or "records" not in data:
            logger.warning("No data records found for unit conversion")
            return data

        conversion_factor = BaseTransformations._get_conversion_factor(from_unit, to_unit)
        if conversion_factor is None:
            logger.warning(f"Unknown conversion from {from_unit} to {to_unit}")
            return data

        converted_records = []
        for record in data["records"]:
            converted_record = record.copy()

            # Convert numeric fields based on naming patterns
            for key, value in record.items():
                if isinstance(value, (int, float)) and BaseTransformations._should_convert_field(key, from_unit):
                    converted_record[key] = value / conversion_factor

            converted_records.append(converted_record)

        result = data.copy()
        result["records"] = converted_records

        logger.debug(f"Converted {len(converted_records)} records from {from_unit} to {to_unit}")
        return result

    @staticmethod
    def _get_conversion_factor(from_unit: str, to_unit: str) -> Optional[float]:
        """Get conversion factor between units."""
        if from_unit.lower() == "bytes" and to_unit.lower() == "gb":
            return BaseTransformations.BYTES_TO_GB
        elif from_unit.lower() == "bytes" and to_unit.lower() == "mb":
            return BaseTransformations.BYTES_TO_MB
        elif from_unit == to_unit:
            return 1.0
        else:
            return None

    @staticmethod
    def _should_convert_field(field_name: str, from_unit: str) -> bool:
        """Determine if a field should be converted based on naming conventions."""
        field_lower = field_name.lower()

        if from_unit.lower() == "bytes":
            return "bytes" in field_lower

        return False

    @staticmethod
    def transform_bucket_by_time(
        data: List[Dict[str, Any]],
        bucket_size_seconds: int,
        start_time: datetime = None,
        end_time: datetime = None,
        aggregation_fields: List[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Transform data by bucketing events by time intervals.

        This is an improved, unified time bucketing implementation that replaces
        the scattered bucketing logic currently in CrossAnalyzer.get_heatmap_data()
        and StructuredLogAnalyzer.get_log_level_timeline().

        Args:
            data: List of events with timestamp fields
            bucket_size_seconds: Size of time buckets in seconds
            start_time: Optional start time to align buckets (default: first event timestamp)
            end_time: Optional end time to fill empty buckets (default: last event timestamp)
            aggregation_fields: Fields to aggregate by (default: ["position", "severity"])

        Returns:
            List of bucketed and aggregated events

        Example:
            Input:
                data = [
                    {"timestamp": "2023-01-01T00:00:30", "position": "pos1", "severity": "INFO"},
                    {"timestamp": "2023-01-01T00:01:45", "position": "pos1", "severity": "ERROR"},
                    {"timestamp": "2023-01-01T00:05:15", "position": "pos2", "severity": "INFO"},
                    {"timestamp": "2023-01-01T00:05:45", "position": "pos2", "severity": "INFO"}
                ]
                bucket_size_seconds = 300  # 5-minute buckets

            Output:
                [
                    {
                        "timestamp": "2023-01-01T00:00:00",  # Bucket start (aligned)
                        "position": "pos1",
                        "severity": "INFO",
                        "value": 1,  # 1 event in this bucket
                        "events": 1
                    },
                    {
                        "timestamp": "2023-01-01T00:00:00",
                        "position": "pos1",
                        "severity": "ERROR",
                        "value": 1,
                        "events": 1
                    },
                    {
                        "timestamp": "2023-01-01T00:05:00",  # Next 5-minute bucket
                        "position": "pos2",
                        "severity": "INFO",
                        "value": 2,  # 2 events in this bucket
                        "events": 2
                    }
                ]

        Note: Events are grouped by time bucket + aggregation fields (position, severity).
              Each output record represents the count of events in that bucket/group combination.
        """
        if not data:
            logger.debug("No data to bucket by time")
            return []

        # Default aggregation fields for backward compatibility
        if aggregation_fields is None:
            aggregation_fields = ["position", "severity"]

        # Parse timestamps and determine time range
        parsed_events = []
        min_time = None
        max_time = None

        for event in data:
            timestamp = event.get("timestamp")
            if not timestamp:
                continue

            # Parse timestamp to datetime
            dt = BaseTransformations._parse_timestamp(timestamp)
            if dt is None:
                continue

            parsed_events.append({**event, "_parsed_timestamp": dt})

            if min_time is None or dt < min_time:
                min_time = dt
            if max_time is None or dt > max_time:
                max_time = dt

        if not parsed_events:
            logger.warning("No events with valid timestamps found")
            return []

        # Use provided time range or derive from data
        bucket_start = start_time if start_time else min_time
        bucket_end = end_time if end_time else max_time

        # Align bucket_start to bucket boundary
        bucket_start = BaseTransformations._floor_to_bucket(bucket_start, bucket_size_seconds)

        # Generate all bucket timestamps (including empty ones)
        bucket_timestamps = []
        current = bucket_start
        while current <= bucket_end:
            bucket_timestamps.append(current)
            current += timedelta(seconds=bucket_size_seconds)

        # Group events by bucket and aggregation fields
        buckets = {}

        for event in parsed_events:
            dt = event["_parsed_timestamp"]

            # Find the bucket this event belongs to
            bucket_timestamp = BaseTransformations._floor_to_bucket(dt, bucket_size_seconds)

            # Skip events outside the time range
            if bucket_timestamp < bucket_start or bucket_timestamp > bucket_end:
                continue

            # Create aggregation key
            agg_values = []
            for field in aggregation_fields:
                value = event.get(field)
                if field == "severity" and value is None:
                    value = event.get("log_level", "INFO")  # Handle both field names
                agg_values.append(value or "unknown")

            bucket_key = (bucket_timestamp, tuple(agg_values))

            if bucket_key not in buckets:
                buckets[bucket_key] = []
            buckets[bucket_key].append(event)

        # Convert buckets to result format
        result = []
        for (bucket_timestamp, agg_values), events in buckets.items():
            # Create base record
            record = {
                "timestamp": bucket_timestamp.isoformat(),
                "value": len(events),  # Count of events in bucket
                "events": len(events),  # Alternative name for count
            }

            # Add aggregation field values
            for i, field in enumerate(aggregation_fields):
                record[field] = agg_values[i]

            # Add any additional data from first event (for metadata)
            if events:
                first_event = events[0]
                for key, value in first_event.items():
                    if key not in record and not key.startswith("_") and key != "timestamp":
                        record[key] = value

            result.append(record)

        # Fill in empty buckets if requested (when start_time/end_time provided)
        if start_time and end_time:
            result = BaseTransformations._fill_empty_buckets(result, bucket_timestamps, aggregation_fields)

        # Sort by timestamp, then by aggregation fields
        result.sort(key=lambda x: (x["timestamp"], tuple(x.get(field, "") for field in aggregation_fields)))

        logger.debug(
            f"Bucketed {len(parsed_events)} events into {len(result)} time buckets "
            f"({bucket_size_seconds}s buckets, {len(bucket_timestamps)} total buckets)"
        )
        return result

    @staticmethod
    def _parse_timestamp(timestamp) -> Optional[datetime]:
        """Parse various timestamp formats to datetime."""
        if isinstance(timestamp, datetime):
            return timestamp
        elif isinstance(timestamp, str):
            try:
                # Handle ISO format with Z timezone
                if timestamp.endswith("Z"):
                    return datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                else:
                    return datetime.fromisoformat(timestamp)
            except ValueError:
                try:
                    # Try other common formats
                    return datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    logger.warning(f"Could not parse timestamp: {timestamp}")
                    return None
        else:
            logger.warning(f"Unsupported timestamp type: {type(timestamp)}")
            return None

    @staticmethod
    def _floor_to_bucket(dt: datetime, bucket_size_seconds: int) -> datetime:
        """Floor datetime to bucket interval."""
        # Convert to timestamp (seconds since epoch)
        timestamp = dt.timestamp()

        # Floor to bucket boundary
        bucket_timestamp = (int(timestamp) // bucket_size_seconds) * bucket_size_seconds

        # Convert back to datetime preserving timezone
        bucket_dt = datetime.fromtimestamp(bucket_timestamp, tz=dt.tzinfo)
        return bucket_dt

    @staticmethod
    def _fill_empty_buckets(
        result: List[Dict[str, Any]], bucket_timestamps: List[datetime], aggregation_fields: List[str]
    ) -> List[Dict[str, Any]]:
        """Fill in empty time buckets with zero values."""
        # Create a set of existing bucket keys for fast lookup
        existing_keys = set()
        for record in result:
            key_parts = [record["timestamp"]]
            for field in aggregation_fields:
                key_parts.append(record.get(field, ""))
            existing_keys.add(tuple(key_parts))

        # Get unique aggregation value combinations from existing data
        agg_combinations = set()
        for record in result:
            combo = tuple(record.get(field, "") for field in aggregation_fields)
            agg_combinations.add(combo)

        # Add empty buckets for missing combinations
        for bucket_ts in bucket_timestamps:
            bucket_iso = bucket_ts.isoformat()
            for agg_combo in agg_combinations:
                key = tuple([bucket_iso] + list(agg_combo))
                if key not in existing_keys:
                    # Create empty bucket record
                    empty_record = {"timestamp": bucket_iso, "value": 0, "events": 0}
                    # Add aggregation field values
                    for i, field in enumerate(aggregation_fields):
                        empty_record[field] = agg_combo[i]

                    result.append(empty_record)

        return result

    @staticmethod
    def _aggregate_bucket_events(bucket_timestamp: str, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Aggregate events within a time bucket."""
        # Group by position and severity for heatmap-style aggregation
        groups = {}

        for event in events:
            position = event.get("position", "unknown")
            severity = event.get("severity") or event.get("log_level", "INFO")

            key = (position, severity)
            if key not in groups:
                groups[key] = 0
            groups[key] += 1

        # Convert groups to records
        result = []
        for (position, severity), count in groups.items():
            result.append({"timestamp": bucket_timestamp, "position": position, "severity": severity, "value": count})

        return result

    @staticmethod
    def format_time_series(data: Dict[str, Any], **params) -> Dict[str, Any]:
        """
        Format data as time series with separate arrays for timestamps and values.

        Transforms record-based data into time series format suitable for line charts.
        Each numeric field becomes a separate array aligned with timestamps.

        Args:
            data: Dictionary with "records" key containing list of records
            **params: Additional parameters (currently unused)

        Returns:
            Dictionary with "timestamps" array and separate arrays for each value field

        Example:
            Input:
                data = {
                    "records": [
                        {"timestamp": "2023-01-01T00:00:00", "cpu_load": 0.5, "memory_gb": 2.0},
                        {"timestamp": "2023-01-01T00:01:00", "cpu_load": 0.7, "memory_gb": 2.1},
                        {"timestamp": "2023-01-01T00:02:00", "cpu_load": 0.6, "memory_gb": 2.0}
                    ]
                }

            Output:
                {
                    "timestamps": [
                        "2023-01-01T00:00:00",
                        "2023-01-01T00:01:00",
                        "2023-01-01T00:02:00"
                    ],
                    "cpu_load": [0.5, 0.7, 0.6],
                    "memory_gb": [2.0, 2.1, 2.0]
                }

        Note: Non-numeric values are converted to 0. Timestamp field is excluded from value arrays.
        """
        if not data or "records" not in data:
            logger.warning("No data records found for time series formatting")
            return {"timestamps": [], "values": {}}

        records = data["records"]
        if not records:
            return {"timestamps": [], "values": {}}

        # Extract timestamps
        timestamps = []
        value_arrays = {}

        # Initialize value arrays based on first record
        first_record = records[0]
        for key in first_record.keys():
            if key != "timestamp":
                value_arrays[key] = []

        # Process all records
        for record in records:
            timestamps.append(record.get("timestamp"))

            for key in value_arrays.keys():
                value = record.get(key, 0)
                # Ensure numeric values
                if isinstance(value, (int, float)):
                    value_arrays[key].append(value)
                else:
                    value_arrays[key].append(0)

        result = {"timestamps": timestamps, **value_arrays}

        logger.debug(f"Formatted {len(timestamps)} time series points with {len(value_arrays)} value series")
        return result

    @staticmethod
    def format_heatmap_records(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format data as heatmap records with timestamp, position, severity, and value.

        Ensures all records have the required fields for heatmap visualization.
        Handles both "severity" and "log_level" field names.

        Args:
            data: List of event records

        Returns:
            List of standardized heatmap records

        Example:
            Input:
                data = [
                    {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "value": 5},
                    {"timestamp": "2023-01-01T00:01:00", "position": "pos2", "log_level": "ERROR"},  # Missing value
                    {"timestamp": "2023-01-01T00:02:00", "severity": "WARNING"}  # Missing position
                ]

            Output:
                [
                    {
                        "timestamp": "2023-01-01T00:00:00",
                        "position": "pos1",
                        "severity": "INFO",
                        "value": 5
                    },
                    {
                        "timestamp": "2023-01-01T00:01:00",
                        "position": "pos2",
                        "severity": "ERROR",  # Converted from log_level
                        "value": 1  # Default value
                    },
                    {
                        "timestamp": "2023-01-01T00:02:00",
                        "position": "unknown",  # Default position
                        "severity": "WARNING",
                        "value": 1
                    }
                ]

        Note: Missing fields are filled with defaults: position="unknown", severity="INFO", value=1.
        """
        if not data:
            return []

        # Ensure all records have required fields
        formatted_records = []
        for record in data:
            # Handle both severity and log_level fields
            severity = record.get("severity") or record.get("log_level", "INFO")

            formatted_record = {
                "timestamp": record.get("timestamp", ""),
                "position": record.get("position", "unknown"),
                "severity": severity,
                "value": record.get("value", 1),
            }
            formatted_records.append(formatted_record)

        logger.debug(f"Formatted {len(formatted_records)} heatmap records")
        return formatted_records

    @staticmethod
    def group_low_activity_devices(data: Dict[str, Any], threshold_mb: float = 1.0) -> Dict[str, Any]:
        """
        Group devices with low activity into an "Other" category.

        Common pattern for disk/IO data visualization to reduce visual clutter
        by consolidating devices with minimal activity.

        Args:
            data: Dictionary with "records" containing device metrics
            threshold_mb: Minimum activity in MB to keep device separate (default: 1.0)

        Returns:
            Dictionary with grouped records

        Example (Future Implementation):
            Input:
                data = {
                    "records": [
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/sda1", "io_mb": 15.2},
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/sdb1", "io_mb": 0.3},
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/sdc1", "io_mb": 0.1},
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/nvme0", "io_mb": 23.4}
                    ]
                }
                threshold_mb = 1.0

            Output:
                {
                    "records": [
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/sda1", "io_mb": 15.2},
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/nvme0", "io_mb": 23.4},
                        {"timestamp": "2023-01-01T00:00:00", "device": "Other", "io_mb": 0.4}  # sdb1 + sdc1
                    ]
                }

        Note: Currently a placeholder. Will be implemented when migrating disk capacity methods.
        """
        if not data or "records" not in data:
            return data

        # This is a placeholder for disk/IO grouping logic
        # Will be implemented when migrating disk capacity methods
        logger.debug(f"Grouping low activity devices with threshold {threshold_mb}MB")
        return data

    @staticmethod
    def calculate_usage_percentages(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate usage percentages from absolute values.

        Transforms absolute capacity values into percentage utilization,
        common pattern for disk capacity and memory usage charts.

        Args:
            data: Dictionary with "records" containing capacity metrics

        Returns:
            Dictionary with percentage calculations added

        Example (Future Implementation):
            Input:
                data = {
                    "records": [
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/sda1", "total_gb": 100.0, "used_gb": 75.5},
                        {"timestamp": "2023-01-01T00:01:00", "device": "/dev/sda1", "total_gb": 100.0, "used_gb": 76.2},
                        {"timestamp": "2023-01-01T00:00:00", "device": "/dev/sdb1", "total_gb": 250.0, "used_gb": 180.3}
                    ]
                }

            Output:
                {
                    "records": [
                        {
                            "timestamp": "2023-01-01T00:00:00",
                            "device": "/dev/sda1",
                            "total_gb": 100.0,
                            "used_gb": 75.5,
                            "usage_percent": 75.5,  # Calculated: used_gb / total_gb * 100
                            "free_gb": 24.5         # Calculated: total_gb - used_gb
                        },
                        {
                            "timestamp": "2023-01-01T00:01:00",
                            "device": "/dev/sda1",
                            "total_gb": 100.0,
                            "used_gb": 76.2,
                            "usage_percent": 76.2,
                            "free_gb": 23.8
                        },
                        {
                            "timestamp": "2023-01-01T00:00:00",
                            "device": "/dev/sdb1",
                            "total_gb": 250.0,
                            "used_gb": 180.3,
                            "usage_percent": 72.1,
                            "free_gb": 69.7
                        }
                    ]
                }

        Note: Currently a placeholder. Will be implemented when migrating capacity methods.
        """
        if not data or "records" not in data:
            return data

        # This is a placeholder for percentage calculation logic
        # Will be implemented when migrating capacity methods
        logger.debug("Calculating usage percentages")
        return data

    @staticmethod
    def aggregate_by_position_and_severity(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Aggregate events by position, severity, and timestamp.

        Groups events that share the same position, severity, and timestamp,
        creating aggregated records with counts and event lists.

        Args:
            data: List of log event records

        Returns:
            List of aggregated records with counts

        Example:
            Input:
                data = [
                    {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "message": "Msg1"},
                    {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "message": "Msg2"},
                    {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "ERROR", "message": "Msg3"},
                    {"timestamp": "2023-01-01T00:01:00", "position": "pos2", "log_level": "WARNING", "message": "Msg4"}
                ]

            Output:
                [
                    {
                        "position": "pos1",
                        "severity": "INFO",
                        "timestamp": "2023-01-01T00:00:00",
                        "count": 2,  # 2 INFO events at this time/position
                        "events": [
                            {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "message": "Msg1"},
                            {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "message": "Msg2"}
                        ]
                    },
                    {
                        "position": "pos1",
                        "severity": "ERROR",
                        "timestamp": "2023-01-01T00:00:00",
                        "count": 1,
                        "events": [...]
                    },
                    {
                        "position": "pos2",
                        "severity": "WARNING",  # Converted from log_level
                        "timestamp": "2023-01-01T00:01:00",
                        "count": 1,
                        "events": [...]
                    }
                ]

        Note: Groups by (position, severity, timestamp) tuple. Handles both "severity" and "log_level" fields.
        """
        if not data:
            return []

        # Group by position and severity
        groups = {}
        for event in data:
            position = event.get("position", "unknown")
            severity = event.get("severity") or event.get("log_level", "INFO")
            timestamp = event.get("timestamp", "")

            key = (position, severity, timestamp)
            if key not in groups:
                groups[key] = []
            groups[key].append(event)

        # Convert groups to aggregated records
        result = []
        for (position, severity, timestamp), events in groups.items():
            result.append({"position": position, "severity": severity, "timestamp": timestamp, "count": len(events), "events": events})

        logger.debug(f"Aggregated {len(data)} events into {len(result)} groups")
        return result

    @staticmethod
    def create_scatter_points(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create scatter plot points from log events.

        Transforms log events into scatter plot format with x (time), y (position),
        and additional metadata for visualization.

        Args:
            data: List of log event records

        Returns:
            List of scatter plot points

        Example:
            Input:
                data = [
                    {"timestamp": "2023-01-01T00:00:00", "position": "pos1", "severity": "INFO", "message": "System started"},
                    {"timestamp": "2023-01-01T00:01:00", "position": "pos2", "log_level": "ERROR", "message": "Connection failed"},
                    {"timestamp": "2023-01-01T00:02:00", "position": "pos1", "severity": "WARNING", "message": "Low memory"}
                ]

            Output:
                [
                    {
                        "x": "2023-01-01T00:00:00",  # Time axis
                        "y": "pos1",                 # Position axis
                        "severity": "INFO",
                        "message": "System started",
                        "original_event": {"timestamp": "2023-01-01T00:00:00", "position": "pos1", ...}
                    },
                    {
                        "x": "2023-01-01T00:01:00",
                        "y": "pos2",
                        "severity": "ERROR",         # Converted from log_level
                        "message": "Connection failed",
                        "original_event": {...}
                    },
                    {
                        "x": "2023-01-01T00:02:00",
                        "y": "pos1",
                        "severity": "WARNING",
                        "message": "Low memory",
                        "original_event": {...}
                    }
                ]

        Note: Each event becomes a point with x=timestamp, y=position. Original event preserved for details.
        """
        if not data:
            return []

        scatter_points = []
        for event in data:
            point = {
                "x": event.get("timestamp", ""),
                "y": event.get("position", "unknown"),
                "severity": event.get("severity") or event.get("log_level", "INFO"),
                "message": event.get("message", ""),
                "original_event": event,
            }
            scatter_points.append(point)

        logger.debug(f"Created {len(scatter_points)} scatter points")
        return scatter_points

    @staticmethod
    async def fetch_resource_columns_with_sorting(
        data_sources, columns: List[str], start_time: datetime, end_time: datetime
    ) -> Dict[str, Any]:
        """
        Generic method to fetch resource columns with proper sorting and serialization.

        This method replicates the exact behavior of _query_resource_logs for resource columns,
        including gap filtering, timestamp sorting, and serialization - essential for preventing
        chart visualization artifacts.

        Args:
            data_sources: Pipeline data sources (contains resource_analyzer)
            columns: List of column names to fetch from resource analyzer
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with "records" key containing sorted and serialized resource records

        Example:
            columns = ['Load1', 'Load5', 'Load15']
            result = await BaseTransformations.fetch_resource_columns_with_sorting(
                data_sources, columns, start_time, end_time
            )
            # Returns: {"records": [{"timestamp": "...", "Load1": 0.5, "Load5": 0.4, "Load15": 0.3}, ...]}

        Note:
            - Uses get_resource_column() which automatically filters NaN values
            - Applies timestamp sorting to prevent chart line artifacts
            - Applies convert_to_serializable() for JSON compatibility
        """
        logger.info(f"Fetching resource columns: {columns}")
        logger.info(f"Time range: {start_time} to {end_time}")

        try:
            records = []
            if columns:
                # Query with specific columns using get_resource_column (filters gaps)
                for column in columns:
                    data = data_sources.resource_analyzer.get_resource_column(column, start_time, end_time)
                    if data["timestamps"]:
                        for i, ts in enumerate(data["timestamps"]):
                            # Find or create a record for this timestamp
                            record = next((r for r in records if r["timestamp"] == ts), None)
                            if not record:
                                record = {"timestamp": ts}
                                records.append(record)
                            # Add column value
                            record[column] = data["values"][i]
            else:
                # Fallback to query method for all columns
                df = data_sources.resource_analyzer.query(start=start_time, end=end_time)
                if not df.empty:
                    df_with_timestamp = df.reset_index()
                    records = df_with_timestamp.to_dict("records")

        except Exception as e:
            logger.error("Error querying resource analyzer: %s", e)
            records = []

        # Sort by timestamp like the original implementation (prevents chart artifacts)
        if records:
            sorted_records = sorted(records, key=lambda x: x["timestamp"])

            # Apply serialization like the original implementation
            try:
                from ..log_parser.utils import convert_to_serializable
            except ImportError:
                from log_parser.utils import convert_to_serializable
            serialized_records = convert_to_serializable(sorted_records)
        else:
            serialized_records = []

        logger.debug(f"Fetched and sorted {len(serialized_records)} resource records")
        return {"records": serialized_records}

    @staticmethod
    def extract_time_series_arrays(data: Dict[str, Any], column_mapping: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Generic method to extract time series arrays from records.

        Converts record-based data into separate arrays for timestamps and values,
        with optional column name mapping for output consistency.

        Args:
            data: Dictionary with "records" key containing list of records
            column_mapping: Optional mapping from source column names to output names
                          e.g., {"Load1": "load1", "Load5": "load5", "Load15": "load15"}

        Returns:
            Dictionary with "timestamps" array and mapped value arrays

        Example:
            data = {"records": [
                {"timestamp": "2023-01-01T00:00:00", "Load1": 0.5, "Load5": 0.4, "Load15": 0.3},
                {"timestamp": "2023-01-01T00:01:00", "Load1": 0.7, "Load5": 0.6, "Load15": 0.5}
            ]}
            column_mapping = {"Load1": "load1", "Load5": "load5", "Load15": "load15"}

            result = BaseTransformations.extract_time_series_arrays(data, column_mapping)
            # Returns: {
            #     "timestamps": ["2023-01-01T00:00:00", "2023-01-01T00:01:00"],
            #     "load1": [0.5, 0.7],
            #     "load5": [0.4, 0.6],
            #     "load15": [0.3, 0.5]
            # }
        """
        if not data or "records" not in data:
            logger.warning("No data records found for time series extraction")
            empty_result = {"timestamps": []}
            if column_mapping:
                for output_name in column_mapping.values():
                    empty_result[output_name] = []
            return empty_result

        records = data["records"]
        if not records:
            empty_result = {"timestamps": []}
            if column_mapping:
                for output_name in column_mapping.values():
                    empty_result[output_name] = []
            return empty_result

        # Extract timestamps and values
        timestamps = []
        value_arrays = {}

        # Initialize value arrays based on column mapping or first record
        if column_mapping:
            for output_name in column_mapping.values():
                value_arrays[output_name] = []
        else:
            # Use all non-timestamp fields from first record
            first_record = records[0]
            for key in first_record.keys():
                if key != "timestamp":
                    value_arrays[key] = []

        # Process all records
        for record in records:
            timestamps.append(record["timestamp"])

            if column_mapping:
                # Use column mapping
                for source_col, output_col in column_mapping.items():
                    value = record.get(source_col, 0)
                    value_arrays[output_col].append(value)
            else:
                # Use original field names
                for key in value_arrays.keys():
                    value = record.get(key, 0)
                    value_arrays[key].append(value)

        result = {"timestamps": timestamps, **value_arrays}

        logger.debug(f"Extracted time series with {len(timestamps)} points and {len(value_arrays)} value arrays")
        return result

    @staticmethod
    def discover_resource_columns(data_sources, prefix_patterns: Dict[str, str], index_patterns: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Generic method to discover resource columns with configurable patterns.

        Dynamically discovers resource columns based on prefix patterns and extracts device indices.
        Handles both single-device and multi-device configurations.

        Args:
            data_sources: Pipeline data sources (contains resource_analyzer)
            prefix_patterns: Dictionary mapping column types to their prefix patterns
                           e.g., {"utilization": "UtilizationPercent", "temperature": "Temperature"}
            index_patterns: Optional dictionary mapping device types to their index regex patterns
                          e.g., {"gpu": r"gpu(\\d+)", "disk": r"Disk(\\d+)", "block": r"Block(\\d+)"}

        Returns:
            Dictionary with discovered column information and device mappings

        Example:
            prefix_patterns = {
                "utilization": "UtilizationPercent",
                "temperature": "Temperature",
                "power": "PowerDrawWatts"
            }
            index_patterns = {"gpu": r"gpu(\\d+)"}

            result = BaseTransformations.discover_resource_columns(
                data_sources, prefix_patterns, index_patterns
            )
            # Returns: {
            #     "discovered_columns": ["gpu0UtilizationPercent", "gpu1UtilizationPercent", ...],
            #     "device_mapping": {0: "GPU 0", 1: "GPU 1"},
            #     "column_groups": {
            #         "utilization": ["gpu0UtilizationPercent", "gpu1UtilizationPercent"],
            #         "temperature": ["gpu0Temperature", "gpu1Temperature"]
            #     }
            # }
        """
        try:
            import re

            import pandas as pd

            # Get all available columns
            df = data_sources.resource_analyzer.query()
            all_columns = df.columns.tolist() if not df.empty else []

            logger.info(f"Discovering resource columns from {len(all_columns)} total columns")

            # Group columns by type
            column_groups = {}
            discovered_columns = []

            for column_type, pattern in prefix_patterns.items():
                matching_columns = [col for col in all_columns if pattern in col]
                column_groups[column_type] = matching_columns
                discovered_columns.extend(matching_columns)
                logger.debug(f"{column_type} columns: {matching_columns}")

            # Extract device indices and create mapping
            device_mapping = {}
            device_indices = set()

            if index_patterns:
                for device_type, regex_pattern in index_patterns.items():
                    for column in discovered_columns:
                        match = re.search(regex_pattern, column)
                        if match:
                            device_idx = int(match.group(1))
                            device_indices.add(device_idx)
                            device_mapping[device_idx] = f"{device_type.upper()} {device_idx}"

                # Handle single device case (no index in column names)
                single_device_columns = [
                    col for col in discovered_columns if not any(re.search(pattern, col) for pattern in index_patterns.values())
                ]
                if single_device_columns and not device_mapping:
                    device_mapping[0] = device_type.upper() if len(index_patterns) == 1 else "Device"

            logger.info(f"Discovered {len(discovered_columns)} resource columns")
            logger.info(f"Device mapping: {device_mapping}")

            return {
                "discovered_columns": discovered_columns,
                "device_mapping": device_mapping,
                "column_groups": column_groups,
                "device_indices": sorted(device_indices) if device_indices else [0],
            }

        except Exception as e:
            logger.error("Error discovering resource columns: %s", e)
            return {"discovered_columns": [], "device_mapping": {}, "column_groups": {}, "device_indices": []}

    @staticmethod
    def group_devices_by_activity(
        data: Dict[str, Any], threshold: float, activity_metric: str = "combined", threshold_percentage: float = 0.05
    ) -> Dict[str, Any]:
        """
        Generic method to group low-activity devices into "Other" categories.

        Analyzes device activity levels and groups devices below threshold into consolidated categories.
        Uses a two-pass approach: first to analyze activity, second to group appropriately.

        Args:
            data: Dictionary with device data in format {device_name: {"metric1": [values], "metric2": [values]}}
            threshold: Threshold value for determining low activity
            activity_metric: How to calculate activity ("combined", "max", "sustained")
            threshold_percentage: Percentage of time device must be above threshold for sustained activity

        Returns:
            Dictionary with activity analysis and grouping information

        Example:
            device_data = {
                "Device A": {"read": [10, 12, 8], "write": [5, 6, 4]},
                "Device B": {"read": [1, 0, 2], "write": [0, 1, 0]}  # Low activity
            }

            result = BaseTransformations.group_devices_by_activity(
                device_data, threshold=5.0, activity_metric="combined"
            )
            # Returns: {
            #     "active_devices": {"Device A"},
            #     "low_activity_devices": {"Device B"},
            #     "max_activity": {"Device A": 18, "Device B": 2},
            #     "activity_percentages": {"Device A": 1.0, "Device B": 0.0}
            # }
        """
        if not data:
            return {"active_devices": set(), "low_activity_devices": set(), "max_activity": {}, "activity_percentages": {}}

        active_devices = set()
        low_activity_devices = set()
        max_activity = {}
        activity_percentages = {}

        for device_name, metrics in data.items():
            if not metrics or not any(metrics.values()):
                low_activity_devices.add(device_name)
                max_activity[device_name] = 0
                activity_percentages[device_name] = 0
                continue

            # Calculate activity based on specified metric
            if activity_metric == "combined":
                # Sum all metrics for each time point
                combined_values = []
                metric_lists = list(metrics.values())
                if metric_lists:
                    for i in range(len(metric_lists[0])):
                        combined = sum(metric_list[i] for metric_list in metric_lists if i < len(metric_list))
                        combined_values.append(combined)

                    max_combined = max(combined_values) if combined_values else 0
                    high_activity_count = sum(1 for val in combined_values if val > threshold)
                    activity_pct = high_activity_count / len(combined_values) if combined_values else 0

                    max_activity[device_name] = max_combined
                    activity_percentages[device_name] = activity_pct

                    # Device is active if max exceeds threshold OR sustained activity
                    if max_combined > threshold or activity_pct > threshold_percentage:
                        active_devices.add(device_name)
                    else:
                        low_activity_devices.add(device_name)

            elif activity_metric == "max":
                # Use maximum value across all metrics
                all_values = []
                for metric_list in metrics.values():
                    all_values.extend(metric_list)

                max_val = max(all_values) if all_values else 0
                max_activity[device_name] = max_val
                activity_percentages[device_name] = 1.0 if max_val > threshold else 0.0

                if max_val > threshold:
                    active_devices.add(device_name)
                else:
                    low_activity_devices.add(device_name)

            elif activity_metric == "sustained":
                # Check for sustained activity across primary metric
                primary_metric = list(metrics.values())[0] if metrics else []
                high_activity_count = sum(1 for val in primary_metric if val > threshold)
                activity_pct = high_activity_count / len(primary_metric) if primary_metric else 0

                max_activity[device_name] = max(primary_metric) if primary_metric else 0
                activity_percentages[device_name] = activity_pct

                if activity_pct > threshold_percentage:
                    active_devices.add(device_name)
                else:
                    low_activity_devices.add(device_name)

        logger.info(f"Activity analysis: {len(active_devices)} active, {len(low_activity_devices)} low-activity devices")
        logger.debug(f"Active devices: {sorted(active_devices)}")
        logger.debug(f"Low-activity devices: {sorted(low_activity_devices)}")

        return {
            "active_devices": active_devices,
            "low_activity_devices": low_activity_devices,
            "max_activity": max_activity,
            "activity_percentages": activity_percentages,
        }

    @staticmethod
    def calculate_usage_percentages(
        data: Dict[str, Any], total_field: str, free_field: str = None, used_field: str = None
    ) -> Dict[str, Any]:
        """
        Generic method to calculate usage percentages from capacity fields.

        Calculates percentage utilization from total/free or total/used field combinations.
        Handles missing or zero values gracefully.

        Args:
            data: Dictionary with "records" key containing list of records
            total_field: Field name containing total capacity values
            free_field: Optional field name containing free capacity values
            used_field: Optional field name containing used capacity values (alternative to free_field)

        Returns:
            Dictionary with original records plus calculated usage percentages

        Example:
            data = {"records": [
                {"timestamp": "2023-01-01T00:00:00", "TotalMB": 8192, "FreeMB": 2048},
                {"timestamp": "2023-01-01T00:01:00", "TotalMB": 8192, "FreeMB": 1024}
            ]}

            result = BaseTransformations.calculate_usage_percentages(
                data, total_field="TotalMB", free_field="FreeMB"
            )
            # Returns records with added "usage_percent" field:
            # [
            #     {..., "usage_percent": 75.0},  # (8192-2048)/8192 * 100
            #     {..., "usage_percent": 87.5}   # (8192-1024)/8192 * 100
            # ]
        """
        if not data or "records" not in data:
            logger.warning("No data records found for percentage calculation")
            return data

        records = data["records"]
        if not records:
            return data

        # Validate field requirements
        if not total_field:
            logger.error("total_field is required for percentage calculation")
            return data

        if not free_field and not used_field:
            logger.error("Either free_field or used_field must be provided")
            return data

        enhanced_records = []
        for record in records:
            enhanced_record = record.copy()

            total = record.get(total_field, 0) or 0

            if total > 0:
                if free_field and free_field in record:
                    # Calculate from total and free
                    free = record.get(free_field, 0) or 0
                    used = total - free
                    usage_percent = (used / total) * 100
                elif used_field and used_field in record:
                    # Calculate from total and used
                    used = record.get(used_field, 0) or 0
                    usage_percent = (used / total) * 100
                else:
                    usage_percent = 0
            else:
                usage_percent = 0

            enhanced_record["usage_percent"] = usage_percent
            enhanced_records.append(enhanced_record)

        result = data.copy()
        result["records"] = enhanced_records

        logger.debug(f"Calculated usage percentages for {len(enhanced_records)} records")
        return result

    @staticmethod
    def initialize_device_metrics(devices: Dict[int, str], metric_names: List[str]) -> Dict[str, Dict[str, List]]:
        """
        Generic method to initialize device metric data structures.

        Creates nested dictionary structure for storing time series data by device and metric.
        Ensures consistent initialization across different pipeline types.

        Args:
            devices: Dictionary mapping device indices to device names
            metric_names: List of metric names to initialize for each device

        Returns:
            Nested dictionary with device -> metric -> empty list structure

        Example:
            devices = {0: "GPU 0", 1: "GPU 1"}
            metrics = ["utilization", "temperature", "power"]

            result = BaseTransformations.initialize_device_metrics(devices, metrics)
            # Returns: {
            #     "utilization": {"GPU 0": [], "GPU 1": []},
            #     "temperature": {"GPU 0": [], "GPU 1": []},
            #     "power": {"GPU 0": [], "GPU 1": []}
            # }
        """
        metric_data = {}

        for metric_name in metric_names:
            metric_data[metric_name] = {}
            for device_idx, device_name in devices.items():
                metric_data[metric_name][device_name] = []

        logger.debug(f"Initialized metrics {metric_names} for devices {list(devices.values())}")
        return metric_data

    @staticmethod
    def convert_units(
        data: Dict[str, Any], field_patterns: List[str], from_unit: str, to_unit: str, conversion_factor: float = None
    ) -> Dict[str, Any]:
        """
        Generic method to convert units in data records.

        Converts numeric fields matching specified patterns from one unit to another.
        Supports custom conversion factors or uses predefined conversions.

        Args:
            data: Dictionary with "records" key containing list of records
            field_patterns: List of field name patterns to convert (supports partial matching)
            from_unit: Source unit identifier
            to_unit: Target unit identifier
            conversion_factor: Optional custom conversion factor (from_unit / to_unit)

        Returns:
            Dictionary with converted records

        Example:
            data = {"records": [
                {"timestamp": "2023-01-01T00:00:00", "ReadBytesPerSecond": 10485760, "WriteBytesPerSecond": 5242880},
                {"timestamp": "2023-01-01T00:01:00", "ReadBytesPerSecond": 20971520, "WriteBytesPerSecond": 10485760}
            ]}

            result = BaseTransformations.convert_units(
                data,
                field_patterns=["BytesPerSecond"],
                from_unit="bytes",
                to_unit="mb"
            )
            # Returns records with values converted from bytes/sec to MB/sec
        """
        if not data or "records" not in data:
            logger.warning("No data records found for unit conversion")
            return data

        records = data["records"]
        if not records:
            return data

        # Determine conversion factor
        if conversion_factor is None:
            conversion_factor = BaseTransformations._get_conversion_factor(from_unit, to_unit)
            if conversion_factor is None:
                logger.warning(f"Unknown conversion from {from_unit} to {to_unit}")
                return data

        converted_records = []
        for record in records:
            converted_record = record.copy()

            # Convert fields matching patterns
            for field_name, value in record.items():
                if isinstance(value, (int, float)):
                    # Check if field matches any pattern
                    for pattern in field_patterns:
                        if pattern.lower() in field_name.lower():
                            converted_record[field_name] = value / conversion_factor
                            break

            converted_records.append(converted_record)

        result = data.copy()
        result["records"] = converted_records

        logger.debug(f"Converted units for {len(converted_records)} records from {from_unit} to {to_unit}")
        return result

    @staticmethod
    async def fetch_resource_columns_bulk(data_sources, columns: List[str], start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """
        Fetch multiple resource columns in a single cached query operation.

        This method leverages the existing HierarchicalQueryCache by making
        a single query() call and extracting all needed columns from the cached result.

        Args:
            data_sources: Pipeline data sources (contains resource_analyzer)
            columns: List of column names to fetch
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with "records" key containing all requested columns
        """
        logger.info(f"BULK_FETCH: Fetching {len(columns)} columns in single query")

        try:
            # Single query operation - leverages existing cache
            df = data_sources.resource_analyzer.query(start=start_time, end=end_time)

            if df.empty:
                logger.warning("BULK_FETCH: Empty DataFrame returned")
                return {"records": []}

            # Filter to requested columns if specified
            if columns:
                # Only include columns that exist in the dataframe
                available_columns = [col for col in columns if col in df.columns]
                missing_columns = [col for col in columns if col not in df.columns]

                if missing_columns:
                    logger.warning(f"BULK_FETCH: Missing columns: {missing_columns}")

                if available_columns:
                    # Reset index to include timestamp as a column
                    df_subset = df[available_columns].reset_index()
                    records = df_subset.to_dict("records")
                else:
                    logger.warning("BULK_FETCH: No requested columns available")
                    records = []
            else:
                # Include all columns, reset index to include timestamp
                df_with_timestamp = df.reset_index()
                records = df_with_timestamp.to_dict("records")

            # Sort by timestamp like the original implementation
            if records:
                sorted_records = sorted(records, key=lambda x: x["timestamp"])

                # Apply serialization like the original implementation
                try:
                    from ..log_parser.utils import convert_to_serializable
                except ImportError:
                    from log_parser.utils import convert_to_serializable
                serialized_records = convert_to_serializable(sorted_records)
            else:
                serialized_records = []

            logger.info(
                f"BULK_FETCH: Retrieved {len(serialized_records)} records with {len(available_columns) if columns else len(df.columns)} columns"
            )
            return {"records": serialized_records}

        except Exception as e:
            logger.exception(f"BULK_FETCH_ERROR: Failed to fetch columns: {e}")
            return {"records": []}

    @staticmethod
    async def fetch_resource_metrics_with_info_preservation(
        data_sources, data: Dict[str, Any], columns_key: str, info_key: str, start_time, end_time
    ) -> Dict[str, Any]:
        """
        Generic method to fetch resource metrics while preserving discovery info.

        This pattern is used by disk and I/O pipelines to fetch metrics using discovered columns
        while preserving the discovery information for the next pipeline step.

        Args:
            data_sources: Pipeline data sources (contains resource_analyzer)
            data: Dictionary containing discovered column information
            columns_key: Key in data dict containing the list of columns to fetch
            info_key: Key to preserve the discovery info under in the result
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with "records" key containing metric records and preserved info

        Example:
            # For disk pipeline:
            result = await fetch_resource_metrics_with_info_preservation(
                data_sources, discovery_data, "disk_columns", "disk_info", start_time, end_time
            )
            # Returns: {"records": [...], "disk_info": discovery_data}
        """
        columns = data.get(columns_key, [])

        if not columns:
            logger.warning(f"No columns available for fetching (key: {columns_key})")
            return {"records": [], info_key: data}

        # Use optimized bulk fetching method
        result = await BaseTransformations.fetch_resource_columns_bulk(data_sources, columns, start_time, end_time)

        # Preserve discovery info for next step
        result[info_key] = data
        return result
