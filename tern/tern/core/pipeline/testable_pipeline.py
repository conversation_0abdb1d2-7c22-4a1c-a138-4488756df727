"""Enhanced pipeline for testing and debugging."""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from .base_pipeline import CachedTransformationPipeline, PipelineStep
from .exceptions import PipelineExecutionError

logger = logging.getLogger(__name__)


class DebugInfo:
    """Container for debug information about pipeline execution."""

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.step_details: List[Dict] = []
        self.total_execution_time = 0.0
        self.cache_hit = False

    def add_step_detail(self, step: PipelineStep, execution_time: float, data_size: int = None):
        """Add debug details for a step."""
        self.step_details.append(
            {
                "name": step.name,
                "execution_time": execution_time,
                "data_size": data_size,
                "args": step.args,
                "kwargs": step.kwargs,
                "success": step.executed,
            }
        )

    def to_dict(self) -> Dict:
        """Convert debug info to dictionary."""
        return {
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "total_execution_time": self.total_execution_time,
            "cache_hit": self.cache_hit,
            "step_count": len(self.step_details),
            "step_details": self.step_details,
        }


class TestableTransformationPipeline(CachedTransformationPipeline):
    """Enhanced pipeline for testing and debugging."""

    def __init__(self, cache=None, method_name: str = None, debug_mode: bool = False, **cache_params):
        """
        Initialize testable pipeline.

        Args:
            cache: VisualizationDataCache instance
            method_name: Name for cache key generation
            debug_mode: Enable detailed debugging information
            **cache_params: Parameters for cache key generation
        """
        super().__init__(cache, method_name, **cache_params)
        self.debug_mode = debug_mode
        self.debug_info = DebugInfo()
        self._step_results: List[Any] = []

    def execute(self) -> Any:
        """Execute pipeline with enhanced debugging."""
        if self._executed:
            return self._result

        self.debug_info.start_time = datetime.now()

        # Check cache first
        cached_result = self._check_cache()
        if cached_result is not None:
            self.debug_info.cache_hit = True
            self.debug_info.end_time = datetime.now()
            self.debug_info.total_execution_time = (self.debug_info.end_time - self.debug_info.start_time).total_seconds()

            if self.debug_mode:
                logger.debug(f"Cache hit for pipeline {self.method_name}")

            self._result = cached_result
            self._executed = True
            return self._result

        # Validate pipeline
        self._validate_pipeline()

        # Execute with debugging
        try:
            current_data = None
            self._step_results = []

            for i, step in enumerate(self.steps):
                step_start = datetime.now()

                if self.debug_mode:
                    logger.debug(f"Executing step {i+1}/{len(self.steps)}: {step.name}")

                current_data = step.execute(current_data)
                self._step_results.append(current_data)

                step_end = datetime.now()
                step_time = (step_end - step_start).total_seconds()

                # Calculate data size if possible
                data_size = None
                try:
                    if hasattr(current_data, "__len__"):
                        data_size = len(current_data)
                    elif hasattr(current_data, "shape"):  # pandas DataFrame/Series
                        data_size = current_data.shape[0]
                except (AttributeError, TypeError):
                    # Continue if data size cannot be determined
                    pass

                self.debug_info.add_step_detail(step, step_time, data_size)

                if self.debug_mode:
                    logger.debug(f"Step {step.name} completed in {step_time:.4f}s" + (f", data size: {data_size}" if data_size else ""))

            self._result = current_data
            self._executed = True

            # Store in cache
            self._store_cache(self._result)

            self.debug_info.end_time = datetime.now()
            self.debug_info.total_execution_time = (self.debug_info.end_time - self.debug_info.start_time).total_seconds()

            if self.debug_mode:
                logger.debug(f"Pipeline {self.method_name} executed in " f"{self.debug_info.total_execution_time:.4f}s")

            return self._result

        except Exception as e:
            self.debug_info.end_time = datetime.now()
            self.debug_info.total_execution_time = (self.debug_info.end_time - self.debug_info.start_time).total_seconds()

            raise PipelineExecutionError(f"Pipeline execution failed: {str(e)}")

    def execute_step_by_step(self) -> List[Any]:
        """Execute pipeline step by step and return intermediate results."""
        if self._executed:
            return self._step_results

        # Check cache first (but don't use it for step-by-step execution)
        self._validate_pipeline()

        current_data = None
        self._step_results = []

        for i, step in enumerate(self.steps):
            current_data = step.execute(current_data)
            self._step_results.append(current_data)

            if self.debug_mode:
                logger.debug(f"Step {i+1} ({step.name}) result type: {type(current_data)}")

        self._result = current_data
        self._executed = True
        return self._step_results

    def get_step_result(self, step_index: int) -> Any:
        """Get the result of a specific step by index."""
        if not self._executed:
            raise PipelineExecutionError("Pipeline must be executed before accessing step results")

        if step_index < 0 or step_index >= len(self._step_results):
            raise IndexError(f"Step index {step_index} out of range")

        return self._step_results[step_index]

    def get_debug_summary(self) -> Dict:
        """Get summary of pipeline execution for debugging."""
        return self.debug_info.to_dict()

    def print_debug_summary(self):
        """Print formatted debug summary."""
        if not self.debug_info.start_time:
            print("Pipeline not executed yet")
            return

        print(f"\n=== Pipeline Debug Summary: {self.method_name} ===")
        print(f"Total execution time: {self.debug_info.total_execution_time:.4f}s")
        print(f"Cache hit: {self.debug_info.cache_hit}")
        print(f"Steps executed: {len(self.debug_info.step_details)}")

        if self.debug_info.step_details:
            print("\nStep breakdown:")
            for i, step in enumerate(self.debug_info.step_details):
                print(
                    f"  {i+1}. {step['name']}: {step['execution_time']:.4f}s"
                    + (f" (data: {step['data_size']})" if step["data_size"] else "")
                )

        print("=" * 50)

    def reset(self):
        """Reset pipeline and debug information."""
        super().reset()
        self.debug_info = DebugInfo()
        self._step_results = []
        return self

    def clone(self, **new_cache_params):
        """Create a copy with debug mode preserved."""
        new_pipeline = TestableTransformationPipeline(
            cache=self.cache, method_name=self.method_name, debug_mode=self.debug_mode, **{**self.cache_params, **new_cache_params}
        )

        # Copy steps
        for step in self.steps:
            new_pipeline.add_step(step.name, step.func, *step.args, **step.kwargs)

        new_pipeline.inject_sources(self.data_sources)
        return new_pipeline
