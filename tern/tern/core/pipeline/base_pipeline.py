"""Core pipeline infrastructure with automatic caching."""

import hashlib
import json
import logging
from copy import deepcopy
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Union

from .exceptions import PipelineExecutionError, PipelineValidationError

logger = logging.getLogger(__name__)


def _serialize_for_cache_key(obj):
    """Serialize objects for cache key generation, handling datetime objects."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {k: _serialize_for_cache_key(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_serialize_for_cache_key(item) for item in obj]
    else:
        return obj


class PipelineStep:
    """Represents a single transformation step in the pipeline."""

    def __init__(self, name: str, func: Callable, args: tuple = None, kwargs: dict = None):
        self.name = name
        self.func = func
        self.args = args or ()
        self.kwargs = kwargs or {}
        self.result = None
        self.executed = False

    def execute(self, data: Any) -> Any:
        """Execute this step with the given data."""
        try:
            self.result = self.func(data, *self.args, **self.kwargs)
            self.executed = True
            return self.result
        except Exception as e:
            raise PipelineExecutionError(
                f"Step execution failed: {str(e)}", step_name=self.name, step_data={"args": self.args, "kwargs": self.kwargs}
            )

    async def execute_async(self, data: Any) -> Any:
        """Execute this step with the given data, handling async functions."""
        import asyncio

        try:
            result = self.func(data, *self.args, **self.kwargs)
            if asyncio.iscoroutine(result):
                self.result = await result
            else:
                self.result = result
            self.executed = True
            return self.result
        except Exception as e:
            raise PipelineExecutionError(
                f"Step execution failed: {str(e)}", step_name=self.name, step_data={"args": self.args, "kwargs": self.kwargs}
            )

    def __repr__(self):
        return f"PipelineStep(name='{self.name}', executed={self.executed})"


class CachedTransformationPipeline:
    """Core pipeline with automatic caching."""

    def __init__(self, cache=None, method_name: str = None, **cache_params):
        """
        Initialize pipeline with optional caching.

        Args:
            cache: VisualizationDataCache instance for caching results
            method_name: Name for cache key generation
            **cache_params: Parameters used for cache key generation
        """
        self.cache = cache
        self.method_name = method_name
        self.cache_params = cache_params
        self.steps: List[PipelineStep] = []
        self.data_sources = None
        self._executed = False
        self._result = None

    def inject_sources(self, data_sources):
        """Inject data sources for pipeline operations."""
        self.data_sources = data_sources
        return self

    def _generate_cache_key(self) -> str:
        """Generate cache key from method name and parameters."""
        if not self.method_name:
            return None

        # Create deterministic hash from method name and parameters
        key_data = {
            "method": self.method_name,
            "params": _serialize_for_cache_key(self.cache_params),
            "steps": [
                {"name": step.name, "args": _serialize_for_cache_key(step.args), "kwargs": _serialize_for_cache_key(step.kwargs)}
                for step in self.steps
            ],
        }

        # Sort keys for consistent hash
        key_json = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_json.encode(), usedforsecurity=False).hexdigest()

    def _check_cache(self) -> Optional[Any]:
        """Check if result exists in cache."""
        if not self.cache:
            return None

        cache_key = self._generate_cache_key()
        if not cache_key:
            return None

        try:
            # Use the VisualizationDataCache interface
            # Extract start_time and end_time, pass remaining as **params
            cache_params = self.cache_params.copy()
            start_time = cache_params.pop("start_time", None)
            end_time = cache_params.pop("end_time", None)

            return self.cache.get_cached_transformation(
                method_name=self.method_name, start_time=start_time, end_time=end_time, **cache_params
            )
        except Exception as e:
            logger.warning(f"Cache check failed for {cache_key}: {e}")
            return None

    def _store_cache(self, result: Any) -> None:
        """Store result in cache."""
        if not self.cache:
            return

        cache_key = self._generate_cache_key()
        if not cache_key:
            return

        try:
            # Use the VisualizationDataCache interface
            # Extract start_time and end_time, pass remaining as **params
            cache_params = self.cache_params.copy()
            start_time = cache_params.pop("start_time", None)
            end_time = cache_params.pop("end_time", None)

            self.cache.add_to_cache(
                query_key=cache_key, result=result, method_name=self.method_name, start_time=start_time, end_time=end_time, **cache_params
            )
        except Exception as e:
            logger.warning(f"Cache store failed for {cache_key}: {e}")

    def add_step(self, name: str, func: Callable, *args, **kwargs):
        """Add a transformation step to the pipeline."""
        if self._executed:
            raise PipelineValidationError("Cannot add steps to executed pipeline")

        step = PipelineStep(name, func, args, kwargs)
        self.steps.append(step)
        return self

    async def execute(self) -> Any:
        """Execute the pipeline with caching."""
        if self._executed:
            return self._result

        # Check cache first
        cached_result = self._check_cache()
        if cached_result is not None:
            logger.debug(f"Cache hit for pipeline {self.method_name}")
            self._result = cached_result
            self._executed = True
            return self._result

        # Validate pipeline before execution
        self._validate_pipeline()

        # Execute steps sequentially
        try:
            current_data = None

            for i, step in enumerate(self.steps):
                logger.debug(f"Executing step {i+1}/{len(self.steps)}: {step.name}")
                current_data = await step.execute_async(current_data)

            self._result = current_data
            self._executed = True

            # Store in cache
            self._store_cache(self._result)

            logger.debug(f"Pipeline {self.method_name} executed successfully")
            return self._result

        except Exception as e:
            raise PipelineExecutionError(f"Pipeline execution failed: {str(e)}")

    def _validate_pipeline(self):
        """Validate pipeline configuration before execution."""
        if not self.steps:
            raise PipelineValidationError("Pipeline has no steps to execute")

        if not self.data_sources:
            raise PipelineValidationError("Pipeline has no data sources injected")

    def reset(self):
        """Reset pipeline for re-execution with different parameters."""
        self._executed = False
        self._result = None
        for step in self.steps:
            step.executed = False
            step.result = None
        return self

    def clone(self, **new_cache_params):
        """Create a copy of this pipeline with different cache parameters."""
        new_pipeline = self.__class__(cache=self.cache, method_name=self.method_name, **{**self.cache_params, **new_cache_params})

        # Deep copy steps
        for step in self.steps:
            new_pipeline.add_step(step.name, step.func, *deepcopy(step.args), **deepcopy(step.kwargs))

        new_pipeline.inject_sources(self.data_sources)
        return new_pipeline

    def __repr__(self):
        return f"CachedTransformationPipeline(method='{self.method_name}', steps={len(self.steps)}, executed={self._executed})"
