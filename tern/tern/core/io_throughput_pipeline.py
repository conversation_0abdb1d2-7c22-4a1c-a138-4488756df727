"""
I/O throughput data pipeline for LogVisualisation.

This module contains the specialized pipeline for I/O throughput data transformation,
implementing the Phase 3 migration of get_io_throughput_data() to the pipeline pattern.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class IoThroughputDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for I/O throughput data transformation."""

    def build_io_throughput_pipeline(self, start_time: datetime, end_time: datetime, low_activity_threshold_mb: float = 5.0):
        """Build the I/O throughput data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range
            low_activity_threshold_mb: Threshold for grouping low-activity devices into "Other"

        Returns:
            Self for method chaining
        """
        return (
            self.add_step("discover_block_columns", self._discover_block_columns)
            .add_step("fetch_io_metrics", self._fetch_io_metrics, start_time, end_time)
            .add_step("process_io_throughput", self._process_io_throughput, low_activity_threshold_mb)
        )

    async def _discover_block_columns(self, data):
        """Discover available block device columns using generic method.

        Args:
            data: Input data (ignored for discovery step)

        Returns:
            Dictionary with discovered block column information
        """
        try:
            # Define block column patterns
            prefix_patterns = {"name": "Name", "read": "ReadBytesPerSecond", "write": "WriteBytesPerSecond"}
            index_patterns = {"block": r"Block(\d+)"}

            # Use generic column discovery method
            result = BaseTransformations.discover_resource_columns(self.data_sources, prefix_patterns, index_patterns)

            # Convert to expected format for I/O pipeline
            discovered_columns = result.get("discovered_columns", [])
            device_mapping = result.get("device_mapping", {})
            column_groups = result.get("column_groups", {})

            # Extract block indices from device mapping
            block_indices = sorted(device_mapping.keys())

            logger.info("DEBUG: Discovered %d block columns for %d devices", len(discovered_columns), len(block_indices))

            return {
                "block_columns": discovered_columns,
                "block_indices": block_indices,
                "name_columns": column_groups.get("name", []),
                "read_columns": column_groups.get("read", []),
                "write_columns": column_groups.get("write", []),
            }

        except Exception as e:
            logger.error("Error discovering block columns: %s", e)
            return {"block_columns": [], "block_indices": []}

    async def _fetch_io_metrics(self, data, start_time, end_time):
        """Fetch I/O metrics using generic method with info preservation."""
        return await BaseTransformations.fetch_resource_metrics_with_info_preservation(
            self.data_sources, data, "block_columns", "block_info", start_time, end_time
        )

    async def _process_io_throughput(self, data, low_activity_threshold_mb):
        """Process I/O throughput data with device grouping.

        Args:
            data: Dictionary with "records" and "block_info" keys
            low_activity_threshold_mb: Threshold for grouping low-activity devices

        Returns:
            Dictionary with timestamps, read_throughput, and write_throughput data
        """
        if not data or "records" not in data:
            logger.warning("No I/O data records found")
            return {"timestamps": [], "read_throughput": {}, "write_throughput": {}}

        records = data["records"]
        block_info = data.get("block_info", {})
        block_indices = block_info.get("block_indices", [])

        if not records or not block_indices:
            return {"timestamps": [], "read_throughput": {}, "write_throughput": {}}

        # Determine block device names
        block_name_mapping = {}  # block_index -> block_name

        # Sample first 10 records to determine block names
        for record in records[:10]:
            for block_idx in block_indices:
                name_col = f"Block{block_idx}Name"

                if record.get(name_col):
                    block_name = record[name_col]
                    if block_name and block_name not in block_name_mapping.values():
                        block_name_mapping[block_idx] = block_name

        logger.info("DEBUG: Block name mapping: %s", block_name_mapping)

        # Convert bytes per second to MB per second
        BYTES_TO_MB = 1024 * 1024

        # First pass: collect all data and determine which devices are low-activity
        all_device_data = {}  # block_name -> {"read": [values], "write": [values]}

        for block_idx, block_name in block_name_mapping.items():
            all_device_data[block_name] = {"read": [], "write": []}

        # Process each record to collect all data
        temp_timestamps = []
        for record in records:
            temp_timestamps.append(record["timestamp"])

            # Process each block device
            for block_idx, block_name in block_name_mapping.items():
                read_col = f"Block{block_idx}ReadBytesPerSecond"
                write_col = f"Block{block_idx}WriteBytesPerSecond"

                read_bytes_per_sec = record.get(read_col, 0) or 0
                write_bytes_per_sec = record.get(write_col, 0) or 0

                # Convert to MB/s
                read_mb_per_sec = read_bytes_per_sec / BYTES_TO_MB
                write_mb_per_sec = write_bytes_per_sec / BYTES_TO_MB

                all_device_data[block_name]["read"].append(read_mb_per_sec)
                all_device_data[block_name]["write"].append(write_mb_per_sec)

        # Determine which devices are low-activity
        active_devices = set()
        low_activity_devices = set()

        for block_name, data in all_device_data.items():
            # Calculate maximum combined throughput for this device
            max_combined_throughput = 0
            for i in range(len(data["read"])):
                combined = data["read"][i] + data["write"][i]
                max_combined_throughput = max(max_combined_throughput, combined)

            # Also check if device has sustained activity (more than just occasional spikes)
            high_activity_count = sum(
                1 for i in range(len(data["read"])) if (data["read"][i] + data["write"][i]) > low_activity_threshold_mb
            )
            activity_percentage = high_activity_count / len(data["read"]) if data["read"] else 0

            # Consider device active if max throughput is above threshold OR if it has sustained activity
            if max_combined_throughput > low_activity_threshold_mb or activity_percentage > 0.05:  # 5% of the time
                active_devices.add(block_name)
            else:
                low_activity_devices.add(block_name)

        logger.info("Active devices: %s", sorted(active_devices))
        logger.info("Low-activity devices: %s", sorted(low_activity_devices))

        # Initialize data structures
        timestamps = temp_timestamps
        read_throughput_data = {}
        write_throughput_data = {}

        # Initialize data structures for active devices
        for device_name in active_devices:
            read_throughput_data[device_name] = []
            write_throughput_data[device_name] = []

        # If we have low-activity devices, add "Other" categories
        if low_activity_devices:
            read_throughput_data["Other (Read)"] = []
            write_throughput_data["Other (Write)"] = []

        # Second pass: aggregate data according to grouping
        for i in range(len(timestamps)):
            # Aggregate low-activity devices
            other_read_total = 0.0
            other_write_total = 0.0

            for device_name in low_activity_devices:
                other_read_total += all_device_data[device_name]["read"][i]
                other_write_total += all_device_data[device_name]["write"][i]

            # Add aggregated low-activity data to "Other" if applicable
            if low_activity_devices:
                read_throughput_data["Other (Read)"].append(other_read_total)
                write_throughput_data["Other (Write)"].append(other_write_total)

            # Add individual active device data
            for device_name in active_devices:
                read_throughput_data[device_name].append(all_device_data[device_name]["read"][i])
                write_throughput_data[device_name].append(all_device_data[device_name]["write"][i])

        logger.info(
            f"Processed {len(timestamps)} I/O throughput data points for {len(read_throughput_data)} read and {len(write_throughput_data)} write devices"
        )
        logger.info("Read devices: %s", list(read_throughput_data.keys()))
        logger.info("Write devices: %s", list(write_throughput_data.keys()))
        if low_activity_devices:
            logger.info("Low-activity devices grouped into 'Other': %s", sorted(low_activity_devices))

        return {"timestamps": timestamps, "read_throughput": read_throughput_data, "write_throughput": write_throughput_data}

    @staticmethod
    @_track_data_performance("get_io_throughput_data")
    async def get_io_throughput_data(
        cache: VisualizationDataCache,
        sources: DataSources,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        low_activity_threshold_mb: float = 5.0,
    ) -> dict[str, Any]:
        """Get I/O throughput data for visualization with stacked area chart.

        Args:
            cache: Visualization data cache instance
            sources: Data sources instance
            start_time: Start timestamp for data range
            end_time: End timestamp for data range
            low_activity_threshold_mb: Threshold in MB/s to group low-activity devices into "Other"

        Returns:
            Dictionary containing:
            {
                "timestamps": List[str] - ISO format timestamps
                "read_throughput": Dict[str, List[float]] - Read throughput in MB/s by block device
                "write_throughput": Dict[str, List[float]] - Write throughput in MB/s by block device
            }
        """
        try:
            logger.info(f"GET_IO_THROUGHPUT_CALLED: start={start_time}, end={end_time}, threshold={low_activity_threshold_mb}")

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = IoThroughputDataPipeline(
                cache=cache,
                method_name="get_io_throughput_data",
                start_time=start_time,
                end_time=end_time,
                low_activity_threshold_mb=low_activity_threshold_mb,
            )

            pipeline.inject_sources(sources)
            result = await pipeline.build_io_throughput_pipeline(start_time, end_time, low_activity_threshold_mb).execute()

            logger.info("IO_THROUGHPUT_TRANSFORM_COMPLETE: Pipeline execution successful")
            return result

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("Error getting I/O throughput data")
            import traceback

            logger.error("Traceback: %s", traceback.format_exc())
            # Return empty data structure on error
            return {"timestamps": [], "read_throughput": {}, "write_throughput": {}}
