"""Performance tracking utilities for data transformation methods."""

import asyncio
import logging
import time
from typing import Any

# Get the logger for this module
logger = logging.getLogger("app")

# Create a specific logger for performance logs
perf_logger = logging.getLogger("app.performance")

# Global performance tracking for data methods
_data_performance_counters = {}


def _track_data_performance(operation_name):
    """Performance tracking utility for data transformation methods."""

    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            # Async wrapper for async functions
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()

                # Increment call counter
                if operation_name not in _data_performance_counters:
                    _data_performance_counters[operation_name] = 0
                _data_performance_counters[operation_name] += 1

                perf_logger.debug("DATA_PERF_START: %s (call #%d)", operation_name, _data_performance_counters[operation_name])

                try:
                    result = await func(*args, **kwargs)
                    elapsed = time.time() - start_time

                    # Log additional metrics based on result type
                    if isinstance(result, dict):
                        if "timestamps" in result:
                            record_count = len(result["timestamps"])
                            perf_logger.debug(
                                "DATA_PERF_END: %s completed in %.3fs, processed %d records", operation_name, elapsed, record_count
                            )
                        else:
                            perf_logger.debug("DATA_PERF_END: %s completed in %.3fs", operation_name, elapsed)
                    elif isinstance(result, list):
                        record_count = len(result)
                        perf_logger.debug(
                            "DATA_PERF_END: %s completed in %.3fs, processed %d records", operation_name, elapsed, record_count
                        )
                    else:
                        perf_logger.debug("DATA_PERF_END: %s completed in %.3fs", operation_name, elapsed)

                    return result
                except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
                    elapsed = time.time() - start_time
                    perf_logger.debug("DATA_PERF_ERROR: %s failed after %.3fs: %s", operation_name, elapsed, e)
                    raise

            return async_wrapper

        # Sync wrapper for sync functions
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()

            # Increment call counter
            if operation_name not in _data_performance_counters:
                _data_performance_counters[operation_name] = 0
            _data_performance_counters[operation_name] += 1

            perf_logger.debug("DATA_PERF_START: %s (call #%d)", operation_name, _data_performance_counters[operation_name])

            try:
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time

                # Log additional metrics based on result type
                if isinstance(result, dict):
                    if "timestamps" in result:
                        record_count = len(result["timestamps"])
                        perf_logger.debug(
                            "DATA_PERF_END: %s completed in %.3fs, processed %d records", operation_name, elapsed, record_count
                        )
                    else:
                        perf_logger.debug("DATA_PERF_END: %s completed in %.3fs", operation_name, elapsed)
                elif isinstance(result, list):
                    record_count = len(result)
                    perf_logger.debug(f"DATA_PERF_END: {operation_name} completed in {elapsed:.3f}s, processed {record_count} records")
                else:
                    perf_logger.debug(f"DATA_PERF_END: {operation_name} completed in {elapsed:.3f}s")

                return result
            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
                elapsed = time.time() - start_time
                perf_logger.debug(f"DATA_PERF_ERROR: {operation_name} failed after {elapsed:.3f}s: {e}")
                raise

        return sync_wrapper

    return decorator
