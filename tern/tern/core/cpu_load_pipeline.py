"""
CPU load data pipeline for LogVisualisation.

This module contains the specialized pipeline for CPU load data transformation,
implementing the Phase 3 migration of get_cpu_load_data() to the pipeline pattern.
"""

import logging
from datetime import datetime
from typing import Any, Dict

from .cache import VisualizationDataCache
from .performance_utils import _track_data_performance
from .pipeline.base_pipeline import CachedTransformationPipeline
from .pipeline.data_sources import DataSources
from .pipeline.transformations import BaseTransformations

logger = logging.getLogger(__name__)


class CpuLoadDataPipeline(CachedTransformationPipeline):
    """Specialized pipeline for CPU load data transformation."""

    def build_cpu_load_pipeline(self, start_time: datetime, end_time: datetime):
        """Build the CPU load data transformation pipeline.

        Args:
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Self for method chaining
        """
        cpu_columns = ["Load1", "Load5", "Load15"]

        return self.add_step("fetch_resource_metrics", self._fetch_cpu_metrics, cpu_columns, start_time, end_time).add_step(
            "process_cpu_data", self._process_cpu_data
        )

    async def _fetch_cpu_metrics(self, data, columns, start_time, end_time):
        """Fetch cpu metrics using generic BaseTransformations method.

        Args:
            data: Input data (ignored for data source step)
            columns: List of column names to fetch
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary with "records" key containing sorted and serialized resource records
        """
        # Use generic method for consistent resource column fetching
        return await BaseTransformations.fetch_resource_columns_with_sorting(self.data_sources, columns, start_time, end_time)

    async def _process_cpu_data(self, data):
        """Process CPU data using generic time series extraction.

        Args:
            data: Dictionary with "records" key containing list of records

        Returns:
            Dictionary with timestamps and load arrays
        """
        # Define column mapping from source names to output names
        column_mapping = {"Load1": "load1", "Load5": "load5", "Load15": "load15"}

        # Use generic method for time series extraction with column mapping
        return BaseTransformations.extract_time_series_arrays(data, column_mapping)

    @staticmethod
    @_track_data_performance("get_cpu_load_data")
    async def get_cpu_load_data(
        cache: VisualizationDataCache, sources: DataSources, start_time: datetime | None = None, end_time: datetime | None = None
    ) -> dict[str, Any]:
        """Get CPU load average data for visualization.

        Args:
            cache: Visualization data cache instance
            sources: Data sources instance
            start_time: Start timestamp for data range
            end_time: End timestamp for data range

        Returns:
            Dictionary containing:
            {
                "timestamps": List[str] - ISO format timestamps
                "load1": List[float] - 1-minute load averages
                "load5": List[float] - 5-minute load averages
                "load15": List[float] - 15-minute load averages
            }
        """
        try:
            logger.info("GET_CPU_LOAD_DATA_CALLED: start=%s, end=%s", start_time, end_time)

            # Pipeline handles caching automatically - no manual cache checks needed!
            pipeline = CpuLoadDataPipeline(cache=cache, method_name="get_cpu_load_data", start_time=start_time, end_time=end_time)

            pipeline.inject_sources(sources)
            result = await pipeline.build_cpu_load_pipeline(start_time, end_time).execute()

            logger.info("CPU_LOAD_DATA_TRANSFORM_COMPLETE: Pipeline execution successful")
            return result

        except (ValueError, TypeError, AttributeError, KeyError, RuntimeError):
            logger.exception("Error getting CPU load data")
            # Return empty data structure on error
            return {"timestamps": [], "load1": [], "load5": [], "load15": []}
