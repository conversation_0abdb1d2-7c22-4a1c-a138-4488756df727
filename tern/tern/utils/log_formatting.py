"""Utilities for formatting structured log data for table display."""

from __future__ import annotations

import logging
from datetime import datetime
from typing import Any

logger = logging.getLogger(__name__)


def generate_key_context_summary(log_entry: dict) -> str:
    """Generate a summary focusing on key ID fields for the Key Context column.

    Args:
        log_entry: Dictionary containing log entry data

    Returns:
        Formatted key context summary string focusing on ID keys
    """
    # Extract keys from both the keys dict and flattened key_ columns
    keys = {}

    # Add keys from the original keys dict if present
    if "keys" in log_entry and log_entry["keys"]:
        keys.update(log_entry["keys"])

    # Add flattened key_ columns from DataFrame
    for col, value in log_entry.items():
        if col.startswith("key_") and value is not None and not _is_nan_value(value):
            key_name = col[4:]  # Remove 'key_' prefix
            keys[key_name] = value

    if not keys:
        return ""

    # Priority ID keys - any key that contains 'id' or is commonly an identifier
    id_keys = [
        "run_id",
        "flowcell_id",
        "position_id",
        "host_serial_number",
        "sample_id",
        "device_id",
        "protocol_group_id",
        "script_id",
        "session_id",
        "channel_id",
        "read_id",
        "strand_id",
    ]

    # Find other keys that contain 'id' or 'uuid' or 'serial'
    other_id_keys = []
    for key in keys.keys():
        key_lower = key.lower()
        if ("id" in key_lower or "uuid" in key_lower or "serial" in key_lower) and key not in id_keys:
            other_id_keys.append(key)

    # Build summary with ID keys first
    summary_parts = []

    # Add priority ID keys first
    for key in id_keys:
        if key in keys and len(summary_parts) < 3:  # Max 3 keys for space
            value = _format_value_for_display(keys[key])
            if value:  # Only add non-empty values
                summary_parts.append(f"{key}: {value}")

    # Add other ID keys if space allows
    remaining_slots = 3 - len(summary_parts)
    for key in other_id_keys[:remaining_slots]:
        if key in keys:
            value = _format_value_for_display(keys[key])
            if value:  # Only add non-empty values
                summary_parts.append(f"{key}: {value}")

    # Format with separator and overall truncation
    summary = " • ".join(summary_parts)
    if len(summary) > 200:  # Fit within key context column width
        summary = summary[:197] + "..."

    return summary


def generate_context_summary(log_entry: dict) -> str:
    """Generate a summary of key-value pairs for the context column.

    Args:
        log_entry: Dictionary containing log entry data

    Returns:
        Formatted context summary string
    """
    # Extract keys from both the keys dict and flattened key_ columns
    keys = {}

    # Add keys from the original keys dict if present
    if "keys" in log_entry and log_entry["keys"]:
        keys.update(log_entry["keys"])

    # Add flattened key_ columns from DataFrame
    for col, value in log_entry.items():
        if col.startswith("key_") and value is not None and not _is_nan_value(value):
            key_name = col[4:]  # Remove 'key_' prefix
            keys[key_name] = value

    if not keys:
        # If no structured keys, try to extract meaningful info from message
        message = log_entry.get("message", "")
        if message and len(message.strip()) > 0:
            # Try to extract key-value pairs from message
            extracted = _extract_key_values_from_message(message)
            if extracted:
                return extracted
        return ""

    # Priority order for key display based on common log patterns
    priority_keys = [
        "run_id",
        "flowcell_id",
        "position_id",
        "host_serial_number",
        "sample_id",
        "device_id",
        "hostname",
        "version",
        "output_path",
        "file_path",
        "script_path",
        "protocol_group_id",
    ]

    # Build summary with priority keys first
    summary_parts = []
    for key in priority_keys:
        if key in keys:
            value = _format_value_for_display(keys[key])
            if value:  # Only add non-empty values
                summary_parts.append(f"{key}: {value}")
                if len(summary_parts) >= 2:  # Max 2 keys in summary for space
                    break

    # Add additional keys if space allows
    remaining_keys = set(keys.keys()) - set(priority_keys)
    remaining_slots = 3 - len(summary_parts)  # Allow up to 3 total keys
    for key in list(remaining_keys)[:remaining_slots]:
        value = _format_value_for_display(keys[key])
        if value:  # Only add non-empty values
            summary_parts.append(f"{key}: {value}")

    # Format with separator and overall truncation
    summary = " • ".join(summary_parts)
    if len(summary) > 250:  # Fit within context column width
        summary = summary[:247] + "..."

    return summary


def _is_nan_value(value) -> bool:
    """Check if a value is NaN, None, or empty string."""
    if value is None:
        return True
    if isinstance(value, float):
        try:
            import math

            return math.isnan(value)
        except (TypeError, ValueError):
            return False
    if isinstance(value, str):
        return value.lower() in ["nan", "none", "", "null"]
    return False


def _format_value_for_display(value) -> str:
    """Format a value for display, handling truncation and special cases."""
    if _is_nan_value(value):
        return ""

    value_str = str(value).strip()
    if not value_str:
        return ""

    # Special handling for paths
    if "/" in value_str and len(value_str) > 30:
        # Show just the filename for paths
        parts = value_str.split("/")
        return f".../{parts[-1]}"

    # Regular truncation
    if len(value_str) > 20:
        return value_str[:17] + "..."

    return value_str


def _format_value_for_expanded_display(value) -> str:
    """Format a value for expanded display with less aggressive truncation."""
    if _is_nan_value(value):
        return ""

    value_str = str(value).strip()
    if not value_str:
        return ""

    # Special handling for paths - show more of the path
    if "/" in value_str and len(value_str) > 60:
        # Show more of the path for expanded view
        parts = value_str.split("/")
        if len(parts) > 2:
            return f".../{'/'.join(parts[-2:])}"
        else:
            return f".../{parts[-1]}"

    # More generous truncation for expanded view
    if len(value_str) > 50:
        return value_str[:47] + "..."

    return value_str


def _extract_key_values_from_message(message: str) -> str:
    """Extract key-value pairs from log message text as fallback."""
    import re

    # Common patterns in log messages
    patterns = [
        r"(\w+):\s*([^\n,]+)",  # key: value
        r"(\w+)=([^\s,]+)",  # key=value
    ]

    extracted_pairs = []
    for pattern in patterns:
        matches = re.findall(pattern, message)
        for key, value in matches[:2]:  # Max 2 pairs from message
            if key.lower() not in ["timestamp", "level", "logger"] and len(value.strip()) > 0:
                formatted_value = _format_value_for_display(value)
                if formatted_value:
                    extracted_pairs.append(f"{key}: {formatted_value}")

    return " • ".join(extracted_pairs) if extracted_pairs else ""


def format_timestamp_for_display(timestamp_str: str, is_grouped: bool = False) -> str:
    """Format timestamp for table display.

    Args:
        timestamp_str: ISO timestamp string
        is_grouped: Whether this is a grouped entry with time range

    Returns:
        Formatted timestamp string
    """
    if is_grouped:
        # Already formatted as range for grouped entries
        return timestamp_str

    try:
        # Convert ISO string to datetime and format for display
        dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
        return dt.strftime("%H:%M:%S.%f")[:-3]  # Include milliseconds
    except (ValueError, AttributeError):
        # Fallback to original string if parsing fails
        return timestamp_str


def format_log_level_badge(log_level: str) -> str:
    """Format log level as a colored badge using markdown.

    Args:
        log_level: Log level string (ERROR, WARN, INFO, DEBUG, etc.)

    Returns:
        Markdown-formatted badge string
    """
    if not log_level:
        return ""

    # Color mapping for different log levels
    level_colors = {"ERROR": "🔴", "WARN": "🟡", "WARNING": "🟡", "INFO": "🔵", "DEBUG": "⚪", "TRACE": "⚫"}

    level_upper = log_level.upper()
    emoji = level_colors.get(level_upper, "⚪")

    return f"{emoji} {log_level}"


def format_logs_for_table(logs: list[dict], expanded_rows: set = None) -> list[dict]:
    """Format logs for DataTable display with support for in-table expansion.

    Args:
        logs: List of log entry dictionaries
        expanded_rows: Set of row IDs that are currently expanded

    Returns:
        List of formatted log dictionaries ready for DataTable with expansion support
    """
    if expanded_rows is None:
        expanded_rows = set()

    formatted_logs = []

    for i, log in enumerate(logs):
        # Create unique row ID
        row_id = log.get("id") or f"log_{i}_{hash(str(log))}"

        # Explicitly ensure no rows are expanded initially
        is_expanded = row_id in expanded_rows

        formatted_log = log.copy()
        formatted_log.update(
            {
                "id": row_id,
                "row_type": "main",
                "parent_id": None,
                "level": 0,
                "expanded": is_expanded,  # This should always be False on initial load
            }
        )

        # Generate key context summary focusing on ID keys
        formatted_log["key_context_summary"] = generate_key_context_summary(log)

        # Format timestamp for display
        formatted_log["timestamp_display"] = format_timestamp_for_display(log.get("timestamp", ""), log.get("is_grouped", False))

        # Keep raw log level for conditional styling
        formatted_log["log_level_raw"] = log.get("log_level", "")

        # Add expand button with better styling
        if log.get("is_grouped", False):
            count = log.get("group_count", 1)
            if formatted_log["expanded"]:
                formatted_log["expand_button"] = f"🔽 ({count})"
            else:
                formatted_log["expand_button"] = f"▶️ ({count})"
        else:
            if formatted_log["expanded"]:
                formatted_log["expand_button"] = "🔽"
            else:
                formatted_log["expand_button"] = "▶️"

        # Ensure all required columns exist
        required_columns = ["log_event", "process_name"]
        for col in required_columns:
            if col not in formatted_log:
                formatted_log[col] = ""

        # Keep library for expansion details
        if "library" not in formatted_log:
            formatted_log["library"] = ""

        formatted_logs.append(formatted_log)

        # Add detail rows if this row is expanded
        if formatted_log["expanded"]:
            detail_rows = create_detail_rows_for_table(log, row_id)
            formatted_logs.extend(detail_rows)

    return formatted_logs


def create_pagination_info_text(result: dict) -> str:
    """Create pagination info text for display.

    Args:
        result: Query result dictionary with pagination info

    Returns:
        Formatted pagination info string
    """
    if "error" in result:
        return f"Error loading logs: {result['error']}"

    total_in_range = result.get("total_in_range", 0)
    page = result.get("page", 0)
    page_size = result.get("page_size", 50)
    pages_in_range = result.get("pages_in_range", 0)

    if total_in_range == 0:
        return "No logs found in selected time range"

    start_idx = page * page_size + 1
    end_idx = min((page + 1) * page_size, total_in_range)

    return f"Showing {start_idx}-{end_idx} of {total_in_range:,} logs (Page {page + 1} of {pages_in_range})"


def create_expanded_log_details(log_entry: dict) -> dict:
    """Create detailed log information for expanded row view.

    Args:
        log_entry: Dictionary containing log entry data

    Returns:
        Dictionary with organized log details for display
    """
    details = {"basic_info": {}, "location_info": {}, "context_keys": {}, "message_content": "", "metadata": {}}

    # Basic information
    details["basic_info"] = {
        "Timestamp": format_timestamp_for_display(log_entry.get("timestamp", ""), log_entry.get("is_grouped", False)),
        "Log Level": log_entry.get("log_level", ""),
        "Event": log_entry.get("log_event", ""),
        "Process": log_entry.get("process_name", ""),
        "Library": log_entry.get("library", ""),  # Now shown in expansion
    }

    # Location information
    details["location_info"] = {
        "File": log_entry.get("file_name", ""),
        "Folder": log_entry.get("folder_name", ""),
    }

    # Extract all context keys (both from keys dict and flattened key_ columns)
    context_keys = {}

    # Add keys from the original keys dict if present
    if "keys" in log_entry and log_entry["keys"]:
        context_keys.update(log_entry["keys"])

    # Add flattened key_ columns from DataFrame
    for col, value in log_entry.items():
        if col.startswith("key_") and value is not None and not _is_nan_value(value):
            key_name = col[4:]  # Remove 'key_' prefix
            context_keys[key_name] = value

    # Remove any generated summary fields that shouldn't appear as context keys
    excluded_keys = ["context_summary", "key_context_summary", "timestamp_display", "expand_button"]
    for excluded_key in excluded_keys:
        context_keys.pop(excluded_key, None)

    # Sort context keys by priority, then alphabetically
    priority_keys = [
        "run_id",
        "flowcell_id",
        "position_id",
        "host_serial_number",
        "sample_id",
        "device_id",
        "hostname",
        "version",
        "output_path",
        "file_path",
        "script_path",
        "protocol_group_id",
    ]

    sorted_keys = {}
    # Add priority keys first
    for key in priority_keys:
        if key in context_keys:
            sorted_keys[key] = context_keys[key]

    # Add remaining keys alphabetically
    remaining_keys = {k: v for k, v in context_keys.items() if k not in priority_keys}
    for key in sorted(remaining_keys.keys()):
        sorted_keys[key] = remaining_keys[key]

    details["context_keys"] = sorted_keys

    # Message content
    details["message_content"] = log_entry.get("message", "")

    # Metadata for grouped entries
    if log_entry.get("is_grouped", False):
        details["metadata"] = {
            "Group Count": log_entry.get("group_count", 1),
            "Duration": f"{log_entry.get('group_duration_seconds', 0):.1f}s",
            "First Event": log_entry.get("first_timestamp", ""),
            "Last Event": log_entry.get("last_timestamp", ""),
        }

    return details


def format_context_keys_for_display(context_keys: dict, max_keys_visible: int = 10) -> tuple[dict, dict]:
    """Format context keys for display with show more/less functionality.

    Args:
        context_keys: Dictionary of context key-value pairs
        max_keys_visible: Maximum number of keys to show initially

    Returns:
        Tuple of (visible_keys, hidden_keys) dictionaries
    """
    if len(context_keys) <= max_keys_visible:
        return context_keys, {}

    # Split into visible and hidden
    keys_list = list(context_keys.items())
    visible_keys = dict(keys_list[:max_keys_visible])
    hidden_keys = dict(keys_list[max_keys_visible:])

    return visible_keys, hidden_keys


def create_detail_rows_for_table(log_entry: dict, parent_id: str) -> list[dict]:
    """Create a single rich detail row for in-table expansion.

    Args:
        log_entry: Main log entry dictionary
        parent_id: ID of the parent row

    Returns:
        List containing single detail row dictionary with rich layout
    """
    import dash_mantine_components as dmc
    from dash import html

    details = create_expanded_log_details(log_entry)

    # Create two-column layout
    left_column_sections = []
    right_column_sections = []

    # Left column: Context Data
    if details["context_keys"]:
        total_items = len(details["context_keys"])

        # Show all context keys in a simple list format
        context_items = []
        for key, value in sorted(details["context_keys"].items()):
            formatted_value = _format_value_for_expanded_display(value)
            context_items.append(
                dmc.Group(
                    [
                        dmc.Text(f"{key}:", fw=500, size="sm", style={"minWidth": "140px"}),
                        dmc.Text(formatted_value, size="sm", c="dimmed", style={"wordBreak": "break-word"}),
                    ],
                    justify="flex-start",
                    gap="md",
                )
            )

        left_column_sections.append(
            dmc.Stack([dmc.Text(f"🏷️ Context Data ({total_items} items):", fw=500, size="sm"), dmc.Stack(context_items, gap="xs")], gap="sm")
        )

    # Grouped entries metadata (also on left)
    if details["metadata"] and log_entry.get("is_grouped", False):
        metadata_text = " • ".join([f"{k}: {v}" for k, v in details["metadata"].items()])
        left_column_sections.append(
            dmc.Stack([dmc.Text("📊 Group Information:", fw=500, size="sm"), dmc.Text(metadata_text, size="sm", c="dimmed")], gap="sm")
        )

    # Right column: Raw Message, Source Location, Component

    # Raw Message section
    if details["message_content"]:
        message_content = details["message_content"].strip()
        if len(message_content) > 30:
            right_column_sections.append(
                dmc.Stack(
                    [
                        dmc.Text("📄 Raw Message:", fw=500, size="sm"),
                        dmc.Code(
                            message_content,
                            block=True,
                            style={"fontSize": "11px", "maxHeight": "150px", "overflow": "auto", "backgroundColor": "#f8f9fa"},
                        ),
                    ],
                    gap="sm",
                )
            )

    # Location information
    if details["location_info"] and any(details["location_info"].values()):
        location_text = []
        if details["location_info"].get("File"):
            location_text.append(f"File: {details['location_info']['File']}")
        if details["location_info"].get("Folder"):
            location_text.append(f"Folder: {details['location_info']['Folder']}")

        if location_text:
            right_column_sections.append(
                dmc.Stack(
                    [dmc.Text("📍 Source Location:", fw=500, size="sm"), dmc.Text(" • ".join(location_text), size="sm", c="dimmed")],
                    gap="sm",
                )
            )

    # Component/Library info
    if details["basic_info"].get("Library"):
        right_column_sections.append(
            dmc.Stack(
                [dmc.Text("📚 Component:", fw=500, size="sm"), dmc.Text(details["basic_info"]["Library"], size="sm", c="dimmed")], gap="sm"
            )
        )

    # Create two-column grid layout with better space utilization
    columns = []
    if left_column_sections:
        columns.append(dmc.GridCol([dmc.Stack(left_column_sections, gap="md")], span=7))  # Context data
    if right_column_sections:
        columns.append(dmc.GridCol([dmc.Stack(right_column_sections, gap="md")], span=5))  # Right column content

    # If only one column has content, let it span the full width
    if left_column_sections and not right_column_sections:
        columns = [dmc.GridCol([dmc.Stack(left_column_sections, gap="md")], span=12)]
    elif right_column_sections and not left_column_sections:
        columns = [dmc.GridCol([dmc.Stack(right_column_sections, gap="md")], span=12)]

    # Main container with border to match design (removed header)
    rich_content = dmc.Paper(
        [dmc.Grid(columns, gutter="sm") if columns else dmc.Text("No additional details available", size="sm", c="dimmed")],
        p="md",
        withBorder=True,
        radius="md",
        style={"backgroundColor": "#f8f9fa", "margin": "8px"},
    )

    # Return single merged row with rich content
    return [
        {
            "id": f"{parent_id}_details",
            "row_type": "detail",
            "parent_id": parent_id,
            "level": 1,
            "expanded": False,
            "rich_content": rich_content,  # Store the rich content
            "log_level_raw": "DETAIL",
        }
    ]


def toggle_row_expansion(table_data: list[dict], row_id: str) -> list[dict]:
    """Toggle expansion state of a row and update table data accordingly.

    Args:
        table_data: Current table data
        row_id: ID of the row to toggle

    Returns:
        Updated table data with expansion state changed
    """
    updated_data = []
    skip_detail_rows = False
    target_found = False

    for row in table_data:
        if row["id"] == row_id and row["row_type"] == "main":
            # Toggle the main row expansion state
            row["expanded"] = not row["expanded"]
            target_found = True

            # Update expand button icon
            if row.get("is_grouped", False):
                count = row.get("group_count", 1)
                if row["expanded"]:
                    row["expand_button"] = f"🔽 ({count})"
                else:
                    row["expand_button"] = f"▶️ ({count})"
            else:
                if row["expanded"]:
                    row["expand_button"] = "🔽"
                else:
                    row["expand_button"] = "▶️"

            updated_data.append(row)

            # If expanding, add detail rows
            if row["expanded"]:
                # Reconstruct original log entry for detail creation
                original_log = {k: v for k, v in row.items() if k not in ["id", "row_type", "parent_id", "level", "expanded"]}
                detail_rows = create_detail_rows_for_table(original_log, row_id)
                updated_data.extend(detail_rows)
            else:
                # If collapsing, mark to skip existing detail rows
                skip_detail_rows = True

        elif skip_detail_rows and row["row_type"] == "detail" and row["parent_id"] == row_id:
            # Skip detail rows of the collapsed parent
            continue
        elif row["row_type"] == "main":
            # Reset skip flag when we encounter another main row
            skip_detail_rows = False
            updated_data.append(row)
        else:
            # Keep all other rows (details of other parents, etc.)
            updated_data.append(row)

    return updated_data
