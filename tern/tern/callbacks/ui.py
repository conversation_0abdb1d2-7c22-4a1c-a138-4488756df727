"""UI-related callbacks for the Dash app."""

from __future__ import annotations

from dash import Input, Output, State


def register_ui_callbacks(app):
    """Register all UI-related callbacks."""

    @app.callback(
        Output("theme-provider", "theme"),
        Output("color-scheme", "data"),
        Input("theme-toggle", "n_clicks"),
        State("color-scheme", "data"),
        prevent_initial_call=True,
    )
    def toggle_theme(_, current):
        """Toggle between light and dark theme."""
        new_scheme = "dark" if current == "light" else "light"
        return {"colorScheme": new_scheme}, new_scheme

    @app.callback(
        Output("sidebar-collapsed", "data"),
        Input("sidebar-toggle", "n_clicks"),
        State("sidebar-collapsed", "data"),
        prevent_initial_call=True,
    )
    def toggle_sidebar(_, current):
        """Toggle sidebar collapse state."""
        return not current
