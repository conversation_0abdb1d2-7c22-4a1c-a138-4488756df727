"""Callbacks for setup section functionality."""

from __future__ import annotations

import os

from dash import Dash, Input, Output, State, callback, no_update


def register_setup_callbacks(app: Dash, service_factory):
    """Register setup callbacks with real service integration.

    Args:
        app: Dash application instance
        service_factory: Service factory for accessing file dialog and parsing services
    """

    @app.callback(
        Output("log-path-input", "value"),
        Input("browse-button", "n_clicks"),
        State("log-path-input", "value"),
        prevent_initial_call=True,
    )
    def handle_browse_click(n_clicks, current_path):
        """Handle browse button click to select log folder."""
        if n_clicks:
            file_dialog_service = service_factory.get_file_dialog_service()

            # Handle case where current_path might be a list, tuple, or other unexpected type
            if isinstance(current_path, (list, tuple)):
                current_path = current_path[0] if current_path else ""
            elif not isinstance(current_path, str):
                current_path = str(current_path) if current_path is not None else ""

            # Use current path as initial path if valid
            initial_path = current_path if current_path and os.path.exists(current_path) else None

            # Open file dialog
            selected_path = file_dialog_service.select_directory(initial_path)

            if selected_path:
                # Ensure we return a string, not a list or tuple
                if isinstance(selected_path, (list, tuple)):
                    selected_path = selected_path[0] if selected_path else ""
                return str(selected_path) if selected_path else no_update
            else:
                return no_update
        return no_update

    @app.callback(
        [
            Output("load-logs-button", "disabled"),
            Output("log-path-input", "error"),
        ],
        Input("log-path-input", "value"),
    )
    def validate_log_path(path_value):
        """Validate the log path and enable/disable the load button."""
        # Handle case where path_value might be a list, tuple, or other unexpected type
        if isinstance(path_value, (list, tuple)):
            path_value = path_value[0] if path_value else ""
        elif not isinstance(path_value, str):
            path_value = str(path_value) if path_value is not None else ""

        if not path_value or path_value.strip() == "":
            return True, None  # Disabled, no error message

        # Use file dialog service for validation
        file_dialog_service = service_factory.get_file_dialog_service()
        validation_result = file_dialog_service.validate_path(path_value)

        if not validation_result.get("valid", False):
            return True, validation_result.get("error", "Invalid path")

        # Path is valid
        return False, None

    @app.callback(
        [
            Output("parsing-status", "data", allow_duplicate=True),
            Output("status-interval", "interval", allow_duplicate=True),
        ],
        Input("load-logs-button", "n_clicks"),
        State("log-path-input", "value"),
        prevent_initial_call=True,
    )
    def handle_load_logs_click(n_clicks, log_path):
        """Handle load logs button click to start parsing."""
        # Handle case where log_path might be a list, tuple, or other unexpected type
        if isinstance(log_path, (list, tuple)):
            log_path = log_path[0] if log_path else ""
        elif not isinstance(log_path, str):
            log_path = str(log_path) if log_path is not None else ""

        if not n_clicks or not log_path:
            return no_update, no_update

        # Get parsing controller
        parsing_controller = service_factory.get_parsing_controller()

        # Start parsing
        parsing_started = parsing_controller.start_parsing(log_path)

        if parsing_started:
            # Trigger immediate update to parsing status and fast polling
            parsing_status = {"status": "parsing", "stage": "🎣 Casting nets for logs... hoping for a good catch!", "progress": 0}
            return parsing_status, 1000  # 1-second interval for fast polling
        else:
            # Parsing failed to start
            error_status = {"status": "error", "error": {"message": "Failed to start parsing"}, "stage": "startup"}
            return error_status, 60000  # Normal interval for error state

    @app.callback(
        Output("log-path-input", "value", allow_duplicate=True),
        Input("appshell", "id"),  # Dummy input to trigger on app load
        prevent_initial_call="initial_duplicate",
    )
    def initialize_log_path(_):
        """Initialize log path from environment variable if available."""
        base_log_folder = os.environ.get("BASE_LOG_FOLDER", "")
        print(f"[DEBUG] initialize_log_path called. BASE_LOG_FOLDER={base_log_folder!r}")
        return base_log_folder
