"""System-related callbacks for the Dash app."""

from __future__ import annotations

import logging

from dash import Input, Output, State, no_update

logger = logging.getLogger("app")


def register_system_callbacks(app, log_visualizer_app):
    """Register all system-related callbacks."""

    @app.callback(
        Output("parsing-status", "data"),
        Output("status-interval", "interval"),
        Input("status-interval", "n_intervals"),
        State("parsing-status", "data"),
    )
    def update_system_status(n, current_status):
        """Update system status based on container health and parsing state."""
        try:
            status = log_visualizer_app.container.get_parsing_status()
        except Exception as e:
            logger.exception("Error getting parsing status")
            return {"status": "error", "message": str(e)}, 1000

        # Check current frontend status to determine interval
        current_frontend_status = current_status.get("status", "idle") if current_status else "idle"
        backend_status = status.get("status", "idle")

        # Determine interval based on current and new status
        if backend_status == "parsing" or current_frontend_status == "parsing":
            # Fast polling during parsing or when transitioning to parsing
            interval = 1000  # 1 second
        elif backend_status == "complete":
            if backend_status == current_frontend_status:
                # Already complete, slow down polling
                return no_update, 60000
            else:
                # Transitioning to complete, one more fast update
                interval = 1000
        else:
            interval = 60000  # 1 minute for idle/error states

        logger.info("Parsing status updated: %s", status)
        return status, interval

    @app.callback(
        Output("main-loading-overlay", "visible"),
        Input("parsing-status", "data"),
        prevent_initial_call=True,
    )
    def update_loading_state(system_status):
        """Control the main loading overlay based on parsing status."""
        if system_status.get("status") == "complete":
            return False
        return True
