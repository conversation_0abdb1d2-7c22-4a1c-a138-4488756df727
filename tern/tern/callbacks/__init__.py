"""Callbacks package for the Dash UI."""

from __future__ import annotations

from . import (  # Just import the module, the @callback decorator handles registration
    navbar_callbacks,
    progress_callbacks,
)
from .data import register_data_callbacks, stores
from .progress_callbacks import register_error_recovery_callbacks
from .setup_callbacks import register_setup_callbacks
from .system import register_system_callbacks
from .ui import register_ui_callbacks


def register_callbacks(app, log_visualizer_app, service_factory=None):
    """Register all callbacks with the Dash app."""
    # Add store components to the app layout
    for store in stores:
        app.layout.children.append(store)

    # Register callbacks
    register_ui_callbacks(app)
    register_system_callbacks(app, log_visualizer_app)
    register_data_callbacks(app, log_visualizer_app)

    # Register setup callbacks with services if available
    if service_factory:
        register_setup_callbacks(app, service_factory)
        register_error_recovery_callbacks(app, service_factory)
