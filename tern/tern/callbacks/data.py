"""Data-related callbacks for the Dash app."""

from __future__ import annotations

import logging
from datetime import datetime

import dash_mantine_components as dmc
import plotly.graph_objects as go
from dash import ALL, MATCH, Input, Output, State, callback_context, dcc, no_update
from dash_iconify import DashIconify

from ..core.cpu_load_pipeline import CpuLoadDataPipeline
from ..core.disk_capacity_pipeline import DiskCapacityDataPipeline
from ..core.gpu_metrics_pipeline import GpuMetricsDataPipeline
from ..core.io_throughput_pipeline import IoThroughputDataPipeline

# Import pipeline classes for direct access to static methods
from ..core.memory_pipeline import MemoryDataPipeline

# Conditional import for log_parser - works both in development and when vendored
try:
    # Try vendored import first (for built wheel)
    from ..log_parser.analysis.cross_analyzer import CrossAnalyzer
except ImportError:
    # Fall back to external package (for development)
    pass

import asyncio
import time

from dash import html

# Get the logger for this module
logger = logging.getLogger("app")

# Create a specific logger for performance logs
perf_logger = logging.getLogger("app.performance")

# Global performance tracking for callbacks
_callback_performance_counters = {}

# Global storage for table data (since Dash stores have limitations)
_current_table_data = []
_click_counter = 0


def _track_callback_performance(callback_name):
    """Performance tracking utility for callback functions."""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()

            # Increment call counter
            if callback_name not in _callback_performance_counters:
                _callback_performance_counters[callback_name] = 0
            _callback_performance_counters[callback_name] += 1

            perf_logger.debug("CALLBACK_PERF_START: %s (call #%d)", callback_name, _callback_performance_counters[callback_name])

            try:
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time
                perf_logger.debug("CALLBACK_PERF_END: %s completed in %.3fs", callback_name, elapsed)
                return result
            except (ValueError, TypeError, AttributeError, KeyError, RuntimeError) as e:
                elapsed = time.time() - start_time
                perf_logger.debug("CALLBACK_PERF_ERROR: %s failed after %.3fs: %s", callback_name, elapsed, e)
                raise

        return wrapper

    return decorator


def _is_input_stable(slider_value):
    """Check if the input is stable and should trigger callback execution."""
    # Don't execute if no slider value
    if not slider_value or len(slider_value) != 2:
        return False

    # Check callback context for stability
    triggered = callback_context.triggered
    if not triggered:
        return False

    # Allow execution if this is the first stable trigger
    return True


def _should_skip_heavy_callback(system_status, additional_check=None):
    """Centralized logic to determine if heavy callbacks should be skipped."""
    # Skip if parsing is not complete
    if system_status.get("status") != "complete":
        logger.info("CALLBACK_SKIP: Parsing not complete")
        return True

    # Skip if additional check fails
    if additional_check is not None and not additional_check:
        logger.info("CALLBACK_SKIP: Additional stability check failed")
        return True

    return False


def _is_light_callback_stable():
    """Check if light callbacks should execute by examining trigger context."""
    triggered = callback_context.triggered
    if not triggered:
        return False

    # Only execute if triggered by a meaningful event
    trigger_info = triggered[0]
    if trigger_info["value"] is None:
        return False

    return True


# Store components for position colors
stores = [
    dcc.Store(
        id="position-colors",
        data={},
    ),
    dcc.Store(
        id="previous-slider-state",
        data=[0, 604800],  # Default range
    ),
    # New stores for shared data caching
    dcc.Store(
        id="resource-data-cache",
        storage_type="memory",
        data={},
    ),
    dcc.Store(
        id="log-scatter-cache",
        storage_type="memory",
        data={},
    ),
    # Store for coordinating loading states during data fetching
    dcc.Store(
        id="resource-fetch-status",
        storage_type="memory",
        data={"loading": False, "requested_range": None},
    ),
]


def register_data_callbacks(app, log_visualizer_app):
    """Register all data-related callbacks."""
    logger.info("CALLBACK_REGISTRATION: Starting registration of data callbacks")
    # Register specialized log scatter callback
    from ..components.charts import register_log_scatter_callback, register_pattern_matching_callbacks

    register_log_scatter_callback("log-scatter-graph", ["start-date-picker", "end-date-picker"], None, log_visualizer_app)

    # Register pattern-matching callbacks for unified chart handling
    register_pattern_matching_callbacks()

    @app.callback(
        Output("resource-fetch-status", "data"),
        Input("time-range-slider", "value"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("update_fetch_status")
    def update_fetch_status(slider_value):
        """Immediately signal that data fetching has started for the new range.

        This callback provides immediate feedback to charts that they should show
        loading overlays while waiting for new data to be fetched.
        """
        logger.info("Fetch status callback triggered for range: %s", slider_value)
        print(f"🔄 FETCH STATUS: Updating to loading=True for range {slider_value}")

        if not slider_value or len(slider_value) != 2:
            return {"loading": False, "requested_range": None}

        # Signal that fetching has started for this range
        return {
            "loading": True,
            "requested_range": slider_value,
            "started_at": datetime.now().isoformat(),
        }

    @app.callback(
        Output("resource-fetch-status", "data", allow_duplicate=True),
        Input("resource-data-cache", "data"),
        State("resource-fetch-status", "data"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("clear_fetch_status")
    def clear_fetch_status(cached_data, current_fetch_status):
        """Clear loading state when new data is available."""
        # Only update if we're currently in a loading state
        if not current_fetch_status or not current_fetch_status.get("loading", False):
            logger.debug("FETCH STATUS: Not currently loading, skipping clear")
            return no_update

        # Check if we have valid cached data with fetch completion signal
        if cached_data and isinstance(cached_data, dict) and "error" not in cached_data:
            # Verify this is a complete fetch (has data and completion signal)
            has_data = "data" in cached_data and cached_data["data"]
            is_completed = cached_data.get("fetch_completed", False)

            if has_data and is_completed:
                logger.info("✅ FETCH STATUS: Clearing loading state (data ready and complete)")
                print(f"✅ FETCH STATUS: Updating to loading=False (data ready and complete)")
                return {
                    "loading": False,
                    "requested_range": None,
                    "completed_at": datetime.now().isoformat(),
                }
            else:
                logger.debug("FETCH STATUS: Data present but not complete yet")
                return no_update

        elif cached_data and "error" in cached_data:
            logger.info("❌ FETCH STATUS: Clearing loading state (error occurred)")
            print(f"❌ FETCH STATUS: Updating to loading=False (error occurred)")
            return {
                "loading": False,
                "requested_range": None,
                "error": cached_data["error"],
                "error_at": datetime.now().isoformat(),
            }
        return no_update

    @app.callback(
        Output("position-filter", "data"),
        Input("position-filter", "search"),
        Input("parsing-status", "data"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("update_position_options")
    def update_position_options(search, system_status):
        """Update position filter options based on search and system status."""
        # Skip if parsing not complete or unstable trigger
        if system_status.get("status") != "complete" or not _is_light_callback_stable():
            return no_update

        try:
            positions = log_visualizer_app.structured_logs.get_positions()

            # Format positions for Dash Select component
            formatted_positions = [{"value": pos, "label": pos} for pos in positions]
            return formatted_positions
        except Exception:
            logger.exception("Error getting positions")
            return []

    @app.callback(
        Output("start-date-picker", "value"),
        Output("end-date-picker", "value"),
        Output("start-date-picker", "minDate"),
        Output("end-date-picker", "minDate"),
        Output("start-date-picker", "maxDate"),
        Output("end-date-picker", "maxDate"),
        Input("parsing-status", "data"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("update_date_pickers")
    def update_date_pickers(parsing_status):
        """Update date pickers with available time range from logs."""
        logger.info("Date picker callback triggered")

        if parsing_status.get("status") != "complete":
            return no_update, no_update, no_update, no_update, no_update, no_update

        try:
            # Get time range from structured logs
            start_time, end_time = log_visualizer_app.structured_logs.get_timestamp_range()
            if not start_time or not end_time:
                logger.warning("No valid time range available")
                return no_update, no_update, no_update, no_update, no_update, no_update

            logger.info("Retrieved time range: %s to %s", start_time, end_time)

            # Return values for date pickers
            return (
                start_time,  # start date value
                end_time,  # end date value
                start_time,  # start date min
                start_time,  # end date min
                end_time,  # start date max
                end_time,  # end date max
            )
        except Exception:
            logger.exception("Error updating date pickers")
            return no_update, no_update, no_update, no_update, no_update, no_update

    @app.callback(
        Output("resource-data-cache", "data"),
        Input("time-range-slider", "value"),
        State("parsing-status", "data"),
        State("resource-data-cache", "data"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("fetch_all_resource_data")
    def fetch_all_resource_data(slider_value, system_status, current_cache):
        """Central data coordinator: fetch ALL resource data in one coordinated operation."""
        logger.info("Data coordinator callback triggered")

        # Use centralized stability checking
        if _should_skip_heavy_callback(system_status, _is_input_stable(slider_value)):
            return no_update

        # Convert slider value to datetime objects
        start_time, end_time = _slider_value_to_datetime_range(slider_value)

        if not start_time or not end_time:
            logger.warning("Could not parse slider value to datetime range")
            return no_update

        # Check if we already have data for this exact time range
        if current_cache and isinstance(current_cache, dict):
            cached_time_range = current_cache.get("time_range")
            if cached_time_range and len(cached_time_range) == 2:
                cached_start = cached_time_range[0]
                cached_end = cached_time_range[1]
                current_start = start_time.isoformat()
                current_end = end_time.isoformat()

                if cached_start == current_start and cached_end == current_end:
                    logger.info("COORDINATOR_SKIP: Data already cached for this exact time range")
                    return no_update

        try:
            # Fetch ALL resource data in one coordinated operation
            logger.info("COORDINATOR_START: Fetching all resource data")
            coord_start = time.time()

            # Run all data fetches in parallel using asyncio
            async def fetch_all_data():
                # Create all data fetch tasks
                tasks = []

                # Memory data
                tasks.append(
                    (
                        "memory",
                        MemoryDataPipeline.get_memory_data(
                            cache=log_visualizer_app.data_context.transformation_cache,
                            sources=log_visualizer_app.data_context.pipeline_sources,
                            start_time=start_time,
                            end_time=end_time,
                        ),
                    )
                )

                # CPU data
                tasks.append(
                    (
                        "cpu",
                        CpuLoadDataPipeline.get_cpu_load_data(
                            cache=log_visualizer_app.data_context.transformation_cache,
                            sources=log_visualizer_app.data_context.pipeline_sources,
                            start_time=start_time,
                            end_time=end_time,
                        ),
                    )
                )

                # Disk data
                tasks.append(
                    (
                        "disk",
                        DiskCapacityDataPipeline.get_disk_capacity_data(
                            cache=log_visualizer_app.data_context.transformation_cache,
                            sources=log_visualizer_app.data_context.pipeline_sources,
                            start_time=start_time,
                            end_time=end_time,
                        ),
                    )
                )

                # I/O data
                tasks.append(
                    (
                        "io",
                        IoThroughputDataPipeline.get_io_throughput_data(
                            cache=log_visualizer_app.data_context.transformation_cache,
                            sources=log_visualizer_app.data_context.pipeline_sources,
                            start_time=start_time,
                            end_time=end_time,
                        ),
                    )
                )

                # GPU data
                tasks.append(
                    (
                        "gpu",
                        GpuMetricsDataPipeline.get_gpu_metrics_data(
                            cache=log_visualizer_app.data_context.transformation_cache,
                            sources=log_visualizer_app.data_context.pipeline_sources,
                            start_time=start_time,
                            end_time=end_time,
                        ),
                    )
                )

                # Execute all fetches in parallel using asyncio.gather()
                logger.info("PARALLEL_EXECUTION: Starting parallel execution of %d pipelines", len(tasks))

                try:
                    # Extract just the coroutines for parallel execution
                    coroutines = [task[1] for task in tasks]
                    data_types = [task[0] for task in tasks]

                    # Execute all pipelines in parallel
                    results_list = await asyncio.gather(*coroutines, return_exceptions=True)

                    # Process results and handle exceptions
                    results = {}
                    for data_type, result in zip(data_types, results_list):
                        if isinstance(result, Exception):
                            logger.exception("PARALLEL_EXECUTION_ERROR: Failed to fetch %s data", data_type)
                            results[data_type] = {"timestamps": [], "error": str(result)}
                        else:
                            results[data_type] = result
                            logger.info("PARALLEL_EXECUTION: %s data completed", data_type)

                    return results

                except Exception as e:
                    logger.exception("PARALLEL_EXECUTION_CRITICAL: Critical error in parallel execution")
                    # Fallback to sequential execution if parallel fails
                    logger.info("PARALLEL_EXECUTION_FALLBACK: Falling back to sequential execution")
                    return await _fallback_sequential_execution(tasks)

            # Execute the coordinated data fetch
            all_data = asyncio.run(fetch_all_data())

            coord_elapsed = time.time() - coord_start
            logger.info("COORDINATOR_END: All resource data fetched in %.3fs", coord_elapsed)

            # Add metadata
            cache_data = {
                "data": all_data,
                "time_range": [start_time.isoformat(), end_time.isoformat()],
                "cached_at": datetime.now().isoformat(),
                "data_points": {data_type: len(data.get("timestamps", [])) for data_type, data in all_data.items()},
                "fetch_completed": True,  # Signal that fetch is complete
            }

            logger.info(f"COORDINATOR_CACHE: Cached data with {sum(cache_data['data_points'].values())} total data points")

            # Data fetching complete - the fetch status callback will clear loading state
            logger.info("✅ DATA READY: Resource data cached and ready for charts")
            print(f"✅ DATA READY: Resource data cached and ready for charts")

            return cache_data

        except Exception as e:
            logger.exception("Error in data coordinator")
            return {"error": str(e)}

    @app.callback(
        Output("time-range-slider", "min"),
        Output("time-range-slider", "max"),
        Output("time-range-slider", "value"),
        Output("time-range-slider", "marks"),
        Input("start-date-picker", "value"),
        Input("end-date-picker", "value"),
        State("parsing-status", "data"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("update_time_range_slider")
    def update_time_range_slider(start_date, end_date, system_status):
        """Update the time range slider based on the scatter plot's actual time range."""
        logger.info("Time range slider callback triggered")

        # Show loading state while parsing is not complete
        if system_status.get("status") != "complete":
            return no_update, no_update, no_update, no_update

        # Convert date picker values to datetime objects
        start_time = _parse_timestamp(start_date)
        end_time = _parse_timestamp(end_date)

        if not start_time or not end_time:
            logger.warning("Could not parse date picker timestamps for range slider")
            return no_update, no_update, no_update, no_update

        try:
            # Convert to Unix timestamps (seconds since epoch)
            start_timestamp = start_time.timestamp()
            end_timestamp = end_time.timestamp()

            # Calculate the time span
            time_span_seconds = end_timestamp - start_timestamp

            # Always default to the full available range
            default_start = start_timestamp
            default_end = end_timestamp

            # Set slider properties
            slider_min = start_timestamp
            slider_max = end_timestamp
            slider_value = [default_start, default_end]
            slider_marks = []  # No marks as requested

            logger.info(f"Updated time range slider: min={start_time}, max={end_time}, full range selected ({time_span_seconds}s)")

            return slider_min, slider_max, slider_value, slider_marks

        except Exception:
            logger.exception("Error updating time range slider")
            return no_update, no_update, no_update, no_update

    @app.callback(
        Output("range-start-text", "children"),
        Output("range-end-text", "children"),
        Input("time-range-slider", "value"),
        prevent_initial_call=True,
    )
    def update_range_text_outputs(slider_value):
        """Update the range text outputs based on slider value."""
        try:
            if not slider_value or len(slider_value) != 2:
                logger.warning("Invalid slider value for range text update")
                return "Invalid", "Invalid"

            # Convert slider values to datetime objects
            start_time, end_time = _slider_value_to_datetime_range(slider_value)

            if not start_time or not end_time:
                return "Invalid", "Invalid"

            # Format as readable strings
            start_text = start_time.strftime("%Y-%m-%d %H:%M")
            end_text = end_time.strftime("%Y-%m-%d %H:%M")

            return start_text, end_text

        except Exception:
            logger.exception("Error updating range text outputs")
            return "Error", "Error"

    @app.callback(
        Output("time-range-slider", "value", allow_duplicate=True),
        Output("previous-slider-state", "data"),
        Input("time-range-slider", "value"),
        State("lock-window-size", "checked"),
        State("previous-slider-state", "data"),
        State("time-range-slider", "min"),
        State("time-range-slider", "max"),
        prevent_initial_call=True,
    )
    @_track_callback_performance("handle_slider_constraints_and_locking")
    def handle_slider_constraints_and_locking(current_range, is_locked, previous_range, slider_min, slider_max):
        """Handle slider constraints and window locking functionality."""
        try:
            if not current_range or len(current_range) != 2:
                logger.warning("Invalid current_range: %s", current_range)
                return no_update, no_update

            if not is_locked:
                # Not locked, just ensure minimum range constraint is met
                current_span = current_range[1] - current_range[0]
                one_hour_seconds = 3600

                if current_span < one_hour_seconds:
                    logger.info(f"Applying minimum range constraint: {current_span}s < {one_hour_seconds}s")

                    # Expand the range to meet minimum constraint
                    range_center = (current_range[0] + current_range[1]) / 2
                    half_min_span = one_hour_seconds / 2

                    new_start = max(slider_min, range_center - half_min_span)
                    new_end = min(slider_max, range_center + half_min_span)

                    # Adjust if we hit boundaries
                    if new_start == slider_min:
                        new_end = min(slider_max, new_start + one_hour_seconds)
                    elif new_end == slider_max:
                        new_start = max(slider_min, new_end - one_hour_seconds)

                    final_range = [new_start, new_end]
                    return final_range, final_range
                else:
                    # No constraint violation, don't update
                    logger.debug("SLIDER_CONSTRAINT: No constraint violation, skipping update")
                    return no_update, no_update

            # Locked mode - maintain window size
            if not previous_range or len(previous_range) != 2:
                # No previous state, just update the state without changing value
                logger.debug("SLIDER_LOCK: No previous range, updating state only")
                return no_update, current_range

            previous_window_size = previous_range[1] - previous_range[0]
            current_window_size = current_range[1] - current_range[0]

            # Check if this is a small change (likely from automatic updates) vs user drag
            # Small changes suggest this was triggered by another callback
            start_distance = abs(current_range[0] - previous_range[0])
            end_distance = abs(current_range[1] - previous_range[1])
            max_change = max(start_distance, end_distance)

            # If changes are very small, likely from automatic updates - don't adjust
            if max_change < 1.0:  # Less than 1 second change
                logger.debug("SLIDER_LOCK: Small change detected, likely automatic - skipping lock adjustment")
                return no_update, current_range

            # Only adjust if window size has changed significantly (user dragged handles)
            window_size_change = abs(current_window_size - previous_window_size)
            if window_size_change < 1.0:  # Window size hasn't changed significantly
                logger.debug("SLIDER_LOCK: Window size unchanged, skipping lock adjustment")
                return no_update, current_range

            # Detect which handle was moved for significant changes
            if start_distance > end_distance:
                # Start handle was moved, adjust end to maintain window size
                new_start = current_range[0]
                new_end = min(slider_max, new_start + previous_window_size)

                # If we hit the max boundary, adjust start
                if new_end == slider_max:
                    new_start = max(slider_min, new_end - previous_window_size)

                final_range = [new_start, new_end]
                logger.info(f"Locked window - start moved: {final_range} (window size: {previous_window_size})")

            else:
                # End handle was moved, adjust start to maintain window size
                new_end = current_range[1]
                new_start = max(slider_min, new_end - previous_window_size)

                # If we hit the min boundary, adjust end
                if new_start == slider_min:
                    new_end = min(slider_max, new_start + previous_window_size)

                final_range = [new_start, new_end]
                logger.info(f"Locked window - end moved: {final_range} (window size: {previous_window_size})")

            return final_range, final_range

        except Exception:
            logger.exception("Error handling locked window movement")
            return no_update, no_update

    @app.callback(
        Output("log-table-container", "children"),
        Output("log-table-loading", "visible"),
        Output("table-navigation-state", "data"),
        [Input("time-range-slider", "value"), Input("position-filter", "value")],
        [State("parsing-status", "data"), State("table-navigation-state", "data")],
        prevent_initial_call=True,
    )
    @_track_callback_performance("update_log_table")
    def update_log_table(time_range, position_filter, system_status, navigation_state):
        """Update structured log table with range-aware pagination and keyboard navigation."""
        logger.info("Log table callback triggered")

        # Get trigger context
        ctx = callback_context
        if not ctx.triggered:
            return no_update, no_update, no_update

        trigger_id = ctx.triggered[0]["prop_id"].split(".")[0]

        # Get current state - simplified for now
        updated_state = {"page": 0, "selected_row_id": None, "table_data": []}

        # Skip if parsing not complete or unstable input
        if _should_skip_heavy_callback(system_status, _is_input_stable(time_range)):
            return html.Div("Loading..."), True, updated_state  # Keep loading visible

        # Convert slider values to datetime
        start_time, end_time = _slider_value_to_datetime_range(time_range)
        if not start_time or not end_time:
            logger.warning("Could not parse time range for log table")
            return html.Div("Invalid time range"), True, updated_state  # Keep loading visible

        # Build filters dictionary
        filters = {}
        if position_filter:
            filters["folder_name"] = position_filter

        try:
            # Get structured logs with pagination using fixed method
            result = log_visualizer_app.structured_logs.query_logs(
                start=start_time, end=end_time, page=updated_state["page"], page_size=50, **filters
            )

            if "error" in result:
                logger.error("Error querying logs: %s", result["error"])
                error_msg = html.Div(f"Error: {result['error']}")
                return error_msg, False, updated_state  # Hide loading on error

            # Format data for table using new formatting utilities
            from ..utils.log_formatting import format_logs_for_table

            formatted_data = format_logs_for_table(result.get("logs", []), set())  # No expanded rows initially

            # Store data in state and global variable
            updated_state["total_pages"] = result.get("pages_in_range", 0)
            updated_state["total_logs"] = result.get("total_in_range", 0)

            # Store table data globally since Dash stores have limitations
            global _current_table_data
            _current_table_data = formatted_data

            logger.info(f"Storing {len(formatted_data)} rows globally and {len(updated_state)} items in navigation state")

            # Validate page bounds
            if updated_state["page"] >= updated_state["total_pages"] and updated_state["total_pages"] > 0:
                updated_state["page"] = updated_state["total_pages"] - 1

            # Create the professional table using DMC
            import dash_mantine_components as dmc

            from ..components.log_table import create_professional_log_table, create_table_pagination

            table_component = create_professional_log_table(formatted_data, updated_state.get("selected_row_id"))

            pagination_component = create_table_pagination(updated_state["page"], updated_state["total_pages"], updated_state["total_logs"])

            logger.info(
                f"Pagination values: page={updated_state['page']}, total_pages={updated_state['total_pages']}, total_logs={updated_state['total_logs']}"
            )

            table_container = dmc.Stack([pagination_component, table_component], gap="md")  # Pagination above the table

            logger.info("Log table updated: %d logs, %d pages", len(formatted_data), updated_state["total_pages"])

            return table_container, False, updated_state  # Hide loading when data is ready

        except Exception as e:
            logger.exception("Error updating log table")
            error_msg = html.Div(f"Exception: {str(e)}")
            return error_msg, False, updated_state  # Hide loading on error

    # Keyboard handling removed for now - focusing on pagination and expansion

    # Pattern-matching callback for expand button clicks - much cleaner approach
    @app.callback(
        Output("log-table-container", "children", allow_duplicate=True),
        Input({"type": "expand-btn", "index": ALL}, "n_clicks"),
        State("table-navigation-state", "data"),
        prevent_initial_call=True,
    )
    def handle_expand_button_clicks(all_clicks, navigation_state):
        """Handle clicks on expand buttons."""
        ctx = callback_context
        if not ctx.triggered:
            return no_update

        # Find which button was clicked
        triggered_id = ctx.triggered[0]["prop_id"]
        triggered_value = ctx.triggered[0]["value"]

        # Prevent automatic triggers when components are recreated
        # Only process clicks that have a positive click count
        if triggered_value is None or triggered_value <= 0:
            logger.info(f"Ignoring automatic trigger for {triggered_id} with value {triggered_value}")
            return no_update

        logger.info(f"Expand button clicked: {triggered_id} with value {triggered_value}")

        # Extract row ID from the triggered component
        import json

        try:
            component_id = json.loads(triggered_id.split(".")[0])
            row_id = component_id["index"]
            logger.info(f"Extracted row ID from button click: {row_id}")
        except (json.JSONDecodeError, KeyError, IndexError):
            logger.error(f"Could not parse component ID from: {triggered_id}")
            return no_update

        # Get table data from global storage
        global _current_table_data
        table_data = _current_table_data

        if not table_data:
            logger.info("No table data available in global storage")
            return no_update

        # Toggle expansion of the clicked row
        from ..utils.log_formatting import toggle_row_expansion

        updated_data = toggle_row_expansion(table_data, row_id)

        # Update global storage
        _current_table_data = updated_data

        logger.info(f"Updated data length after toggling row {row_id}: {len(updated_data)}")

        # Create the updated table
        import dash_mantine_components as dmc

        from ..components.log_table import create_professional_log_table, create_table_pagination

        table_component = create_professional_log_table(updated_data, navigation_state.get("selected_row_id") if navigation_state else None)

        # For expansion, preserve the original pagination state - don't use defaults
        if navigation_state and all(key in navigation_state for key in ["page", "total_pages", "total_logs"]):
            pagination_component = create_table_pagination(
                navigation_state["page"], navigation_state["total_pages"], navigation_state["total_logs"]
            )
            logger.info(
                f"Expansion: using preserved pagination values: page={navigation_state['page']}, total_pages={navigation_state['total_pages']}, total_logs={navigation_state['total_logs']}"
            )
        else:
            # Fallback: no pagination controls for expansion if state is incomplete
            pagination_component = dmc.Text("Pagination state lost", size="sm", c="red")
            logger.warning("Navigation state incomplete during expansion - pagination values may be lost")

        logger.info(f"Returning updated table component for row {row_id}")
        return dmc.Stack([pagination_component, table_component], gap="md")

    # Pagination callbacks
    @app.callback(
        Output("log-table-container", "children", allow_duplicate=True),
        Output("table-navigation-state", "data", allow_duplicate=True),
        [Input("log-table-prev", "n_clicks"), Input("log-table-next", "n_clicks")],
        [
            State("table-navigation-state", "data"),
            State("time-range-slider", "value"),
            State("position-filter", "value"),
            State("parsing-status", "data"),
        ],
        prevent_initial_call=True,
    )
    def handle_pagination_clicks(prev_clicks, next_clicks, navigation_state, time_range, position_filter, system_status):
        """Handle pagination button clicks."""
        logger.info(f"Pagination callback triggered - prev_clicks: {prev_clicks}, next_clicks: {next_clicks}")

        ctx = callback_context
        if not ctx.triggered:
            logger.info("Pagination callback: no trigger context")
            return no_update, no_update

        triggered_id = ctx.triggered[0]["prop_id"].split(".")[0]
        triggered_value = ctx.triggered[0]["value"]

        # Prevent automatic triggers when pagination components are recreated
        # Only process clicks that have a positive click count
        if triggered_value is None or triggered_value <= 0:
            logger.info(f"Pagination callback: ignoring automatic trigger for {triggered_id} with value {triggered_value}")
            return no_update, no_update

        logger.info(f"Pagination callback: triggered by {triggered_id} with value {triggered_value}")

        # Skip if parsing not complete
        if system_status.get("status") != "complete":
            logger.info(f"Pagination callback: parsing not complete, status is {system_status.get('status')}")
            return no_update, no_update

        # Get current navigation state
        current_page = navigation_state.get("page", 0) if navigation_state else 0
        total_pages = navigation_state.get("total_pages", 1) if navigation_state else 1

        logger.info(f"Pagination callback: current_page={current_page}, total_pages={total_pages}, navigation_state={navigation_state}")

        # Determine which button was clicked
        triggered_id = ctx.triggered[0]["prop_id"].split(".")[0]

        if triggered_id == "log-table-prev" and current_page > 0:
            new_page = current_page - 1
            logger.info(f"Pagination: going to previous page {new_page}")
        elif triggered_id == "log-table-next" and current_page < total_pages - 1:
            new_page = current_page + 1
            logger.info(f"Pagination: going to next page {new_page}")
        else:
            # No valid page change
            logger.info(
                f"Pagination: no valid page change - triggered_id={triggered_id}, current_page={current_page}, total_pages={total_pages}"
            )
            return no_update, no_update

        logger.info(f"Pagination: moving from page {current_page} to page {new_page}")

        # Convert slider values to datetime
        start_time, end_time = _slider_value_to_datetime_range(time_range)
        if not start_time or not end_time:
            logger.warning("Could not parse time range for pagination")
            return no_update, no_update

        # Build filters dictionary
        filters = {}
        if position_filter:
            filters["folder_name"] = position_filter

        try:
            # Get new page of data
            result = log_visualizer_app.structured_logs.query_logs(start=start_time, end=end_time, page=new_page, page_size=50, **filters)

            if "error" in result:
                logger.error("Error querying logs for pagination: %s", result["error"])
                return no_update, no_update

            # Format data for table
            from ..utils.log_formatting import format_logs_for_table

            formatted_data = format_logs_for_table(result.get("logs", []), set())  # No expanded rows on page change

            # Update navigation state
            updated_state = {
                "page": new_page,
                "selected_row_id": None,  # Clear selection on page change
                "total_pages": result.get("pages_in_range", 0),
                "total_logs": result.get("total_in_range", 0),
            }

            # Store data globally
            global _current_table_data
            _current_table_data = formatted_data

            logger.info(f"Pagination: loaded page {new_page} with {len(formatted_data)} rows")

            # Create the updated table
            import dash_mantine_components as dmc

            from ..components.log_table import create_professional_log_table, create_table_pagination

            table_component = create_professional_log_table(formatted_data, updated_state.get("selected_row_id"))

            pagination_component = create_table_pagination(updated_state["page"], updated_state["total_pages"], updated_state["total_logs"])

            logger.info(
                f"Pagination values: page={updated_state['page']}, total_pages={updated_state['total_pages']}, total_logs={updated_state['total_logs']}"
            )

            table_container = dmc.Stack([pagination_component, table_component], gap="md")  # Pagination above the table

            return table_container, updated_state

        except Exception as e:
            logger.exception("Error handling pagination")
            return no_update, no_update

    # Simplified callbacks - removed interactive elements for now

    logger.info("CALLBACK_REGISTRATION: Completed registration of all data callbacks including log details")


def _parse_timestamp(timestamp):
    """Parse timestamp from various formats."""
    try:
        if isinstance(timestamp, str):
            return datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
        if hasattr(timestamp, "timestamp"):
            return timestamp
        logger.warning("Unexpected timestamp type: %s", type(timestamp))
        return None
    except Exception:
        logger.warning("Could not parse timestamp string: %s", timestamp)
        return None


def _slider_value_to_datetime_range(slider_value):
    """Convert slider value to datetime range."""
    try:
        if not slider_value or len(slider_value) != 2:
            logger.warning("Invalid slider value: %s", slider_value)
            return None, None

        # Convert slider values (seconds since epoch) to datetime objects
        start_datetime = datetime.fromtimestamp(slider_value[0])
        end_datetime = datetime.fromtimestamp(slider_value[1])

        return start_datetime, end_datetime

    except Exception:
        logger.exception("Error converting slider value to datetime range")
        return None, None


async def _fallback_sequential_execution(tasks):
    """Fallback sequential execution if parallel execution fails."""
    results = {}
    for data_type, task in tasks:
        try:
            results[data_type] = await task
            logger.info("FALLBACK_SEQUENTIAL: %s data completed", data_type)
        except Exception as e:
            logger.exception("FALLBACK_SEQUENTIAL_ERROR: Failed to fetch %s data", data_type)
            results[data_type] = {"timestamps": [], "error": str(e)}
    return results
