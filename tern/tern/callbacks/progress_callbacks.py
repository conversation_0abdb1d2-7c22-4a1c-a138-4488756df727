"""Callbacks for progress display functionality."""

from __future__ import annotations

from datetime import datetime

from dash import Dash, Input, Output, State, callback, no_update


def format_elapsed_time(start_time, current_time=None):
    """Format elapsed time as HH:MM:SS."""
    if not start_time:
        return "00:00:00"

    if current_time is None:
        current_time = datetime.now().timestamp()

    # Convert start_time to float if it's a string
    try:
        if isinstance(start_time, str):
            start_time = float(start_time)
        elif not isinstance(start_time, (int, float)):
            return "00:00:00"
    except (ValueError, TypeError):
        return "00:00:00"

    elapsed_seconds = int(current_time - start_time)
    hours = elapsed_seconds // 3600
    minutes = (elapsed_seconds % 3600) // 60
    seconds = elapsed_seconds % 60

    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


@callback(
    [
        Output("progress-stage-text", "children"),
        Output("progress-bar", "value"),
        Output("progress-elapsed-text", "children"),
        Output("progress-files-text", "children"),
        Output("progress-current-file-text", "children"),
    ],
    Input("status-interval", "n_intervals"),
    Input("parsing-status", "data"),
)
def update_progress_display(n_intervals, parsing_status):
    """Update progress display based on parsing status."""
    if not parsing_status:
        return no_update, no_update, no_update, no_update, no_update

    status = parsing_status.get("status", "idle")
    stage = parsing_status.get("stage", "")
    progress = parsing_status.get("progress", 0)
    current_file = parsing_status.get("current_file", "")
    files_processed = parsing_status.get("files_processed", 0)
    total_files = parsing_status.get("total_files", 0)
    start_time = parsing_status.get("start_time")

    # Format elapsed time
    elapsed_text = f"⏱️ Elapsed: {format_elapsed_time(start_time)}"

    # Format files text
    files_text = f"📊 Files: {files_processed}/{total_files}" if total_files > 0 else "📊 Files: 0/0"

    # Format current file text
    current_file_text = f"📁 {current_file}" if current_file else ""

    return stage, progress, elapsed_text, files_text, current_file_text


@callback(
    [
        Output("progress-section", "style", allow_duplicate=True),
        Output("welcome-message", "style", allow_duplicate=True),
        Output("charts-content", "style", allow_duplicate=True),
        Output("filters-section-wrapper", "style", allow_duplicate=True),
    ],
    Input("parsing-status", "data"),
    prevent_initial_call=True,
)
def update_ui_based_on_parsing_status(parsing_status):
    """Update UI visibility based on parsing status."""
    if not parsing_status:
        return no_update, no_update, no_update, no_update

    status = parsing_status.get("status", "idle")

    if status == "idle":
        # Show welcome message, hide everything else
        progress_style = {"display": "none"}
        welcome_style = {"display": "block"}
        charts_style = {"display": "none"}
        filters_style = {"display": "none"}
    elif status == "parsing":
        # Show progress, hide welcome and charts, show filters with loading
        progress_style = {"display": "block"}
        welcome_style = {"display": "none"}
        charts_style = {"display": "none"}
        filters_style = {"display": "block"}
    elif status == "complete":
        # Hide progress and welcome, show charts and filters
        progress_style = {"display": "none"}
        welcome_style = {"display": "none"}
        charts_style = {"display": "block"}
        filters_style = {"display": "block"}
    elif status == "error":
        # Hide progress, show welcome message (with error), hide charts and filters
        progress_style = {"display": "none"}
        welcome_style = {"display": "block"}
        charts_style = {"display": "none"}
        filters_style = {"display": "none"}
    else:
        return no_update, no_update, no_update, no_update

    return progress_style, welcome_style, charts_style, filters_style


@callback(
    [
        Output("error-modal", "opened"),
        Output("error-message-text", "children"),
        Output("error-stage-text", "children"),
        Output("error-stage-text", "style"),
        Output("progress-bar", "color"),
    ],
    Input("parsing-status", "data"),
    prevent_initial_call=True,
)
def update_error_dialog(parsing_status):
    """Update error dialog based on parsing status."""
    if not parsing_status:
        return False, no_update, no_update, no_update, "blue"

    status = parsing_status.get("status", "idle")

    if status == "error":
        error_data = parsing_status.get("error", {})
        stage = parsing_status.get("stage", "")

        # Get error message
        error_message = error_data.get("message", "An unknown error occurred during parsing.")

        # Format stage text if available
        stage_text = f"Error in stage: {stage}" if stage else ""
        stage_style = {"display": "block"} if stage else {"display": "none"}

        # Set progress bar to red color to indicate error
        progress_color = "red"

        return True, error_message, stage_text, stage_style, progress_color
    else:
        # Reset progress bar color when not in error state
        progress_color = "blue"
        return False, no_update, no_update, no_update, progress_color


@callback(
    Output("filters-loading", "visible"),
    Input("parsing-status", "data"),
    prevent_initial_call=True,
)
def update_filters_loading_overlay(parsing_status):
    """Update filters loading overlay based on parsing status."""
    if not parsing_status:
        return True  # Show loading when no status

    status = parsing_status.get("status", "idle")

    # Show loading when parsing is not complete, hide when complete
    # This matches the scatter plot behavior
    return status != "complete"


def register_error_recovery_callbacks(app, service_factory):
    """Register error recovery callbacks with service integration.

    Args:
        app: Dash application instance
        service_factory: Service factory for accessing services
    """

    @app.callback(
        Output("error-modal", "opened", allow_duplicate=True),
        Input("error-cancel-btn", "n_clicks"),
        prevent_initial_call=True,
    )
    def handle_error_cancel(n_clicks):
        """Handle error dialog cancel button."""
        if n_clicks:
            return False
        return no_update

    @app.callback(
        [
            Output("error-modal", "opened", allow_duplicate=True),
            Output("parsing-status", "data", allow_duplicate=True),
        ],
        Input("error-retry-btn", "n_clicks"),
        State("log-path-input", "value"),
        prevent_initial_call=True,
    )
    def handle_error_retry(n_clicks, log_path):
        """Handle error dialog retry button."""
        if n_clicks and log_path:
            # Get parsing controller and retry
            parsing_controller = service_factory.get_parsing_controller()
            parsing_started = parsing_controller.start_parsing(log_path)

            if parsing_started:
                # Close error modal and reset parsing status
                return False, {"status": "parsing", "stage": "🎣 Casting nets for logs... hoping for a good catch!", "progress": 0}
            else:
                # Keep error modal open if retry failed
                return no_update, no_update
        return no_update, no_update

    @app.callback(
        Output("error-modal", "opened", allow_duplicate=True),
        Input("error-change-path-btn", "n_clicks"),
        prevent_initial_call=True,
    )
    def handle_error_change_path(n_clicks):
        """Handle error dialog change path button."""
        if n_clicks:
            # Close error modal and let user select new path
            # The browse button functionality will handle the path selection
            return False
        return no_update
