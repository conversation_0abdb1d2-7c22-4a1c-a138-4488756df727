"""Keyboard event handling components for table navigation."""

from __future__ import annotations

import dash_mantine_components as dmc
from dash import Input, Output, State, callback_context, dcc, html, no_update


def create_keyboard_event_listener():
    """Create a keyboard event listener component for table navigation.

    Returns:
        Dash component that listens for keyboard events
    """
    # Use a hidden div with tabindex to capture keyboard events
    return html.Div(
        [
            html.Div(
                id="keyboard-capture",
                tabIndex=0,  # Make it focusable
                style={"position": "absolute", "top": "-9999px", "left": "-9999px", "width": "1px", "height": "1px", "opacity": 0},
            ),
            dcc.Store(id="keyboard-events", data={"key": None, "timestamp": None}),
            dcc.Store(id="table-navigation-state", data={"selected_row": 0, "table_focused": False}),
        ]
    )


def register_keyboard_callbacks(app):
    """Register keyboard event handling callbacks.

    Args:
        app: Dash application instance
    """

    # JavaScript component to capture keyboard events
    app.clientside_callback(
        """
        function(n_intervals) {
            // Set up keyboard event listener on the document
            if (!window.ternKeyboardListenerSetup) {
                window.ternKeyboardListenerSetup = true;

                // Track table focus state
                window.ternTableFocused = false;

                // Focus detection for table area
                document.addEventListener('click', function(e) {
                    const tableContainer = document.querySelector('.table-container');
                    if (tableContainer && tableContainer.contains(e.target)) {
                        window.ternTableFocused = true;
                        // Update store to reflect focus - preserve existing pagination data
                        const currentState = window.dash_clientside.callback_context.states['table-navigation-state.data'] || {};
                        window.dash_clientside.set_props('table-navigation-state', {
                            data: {
                                ...currentState,  // Preserve existing state (page, total_pages, total_logs)
                                selected_row: window.ternSelectedRow || 0,
                                table_focused: true
                            }
                        });
                    } else {
                        window.ternTableFocused = false;
                        // Preserve existing pagination data when losing focus too
                        const currentState = window.dash_clientside.callback_context.states['table-navigation-state.data'] || {};
                        window.dash_clientside.set_props('table-navigation-state', {
                            data: {
                                ...currentState,  // Preserve existing state (page, total_pages, total_logs)
                                selected_row: window.ternSelectedRow || 0,
                                table_focused: false
                            }
                        });
                    }
                });

                // Keyboard event listener
                document.addEventListener('keydown', function(e) {
                    if (!window.ternTableFocused) return;

                    const targetKeys = ['ArrowUp', 'ArrowDown', 'Enter', 'Escape'];
                    if (targetKeys.includes(e.key)) {
                        e.preventDefault();  // Prevent default behavior

                        // Update the keyboard events store
                        window.dash_clientside.set_props('keyboard-events', {
                            data: {
                                key: e.key,
                                timestamp: Date.now()
                            }
                        });
                    }
                });
            }

            return window.dash_clientside.no_update;
        }
        """,
        Output("keyboard-capture", "style"),
        Input("status-interval", "n_intervals"),  # Use existing interval
    )


def handle_table_keyboard_navigation(table_data, keyboard_event, navigation_state):
    """Handle keyboard navigation for the log table.

    Args:
        table_data: Current table data
        keyboard_event: Keyboard event data
        navigation_state: Current navigation state

    Returns:
        Tuple of (updated_table_data, updated_navigation_state, selected_rows)
    """
    if not keyboard_event or not keyboard_event.get("key") or not navigation_state.get("table_focused"):
        return table_data, navigation_state, no_update

    key = keyboard_event["key"]
    current_row = navigation_state.get("selected_row", 0)

    if key == "Enter":
        # Toggle expansion of selected row
        if current_row < len(table_data):
            selected_row = table_data[current_row]
            if selected_row.get("row_type") == "main":
                # Import the toggle function
                from ..utils.log_formatting import toggle_row_expansion

                updated_table_data = toggle_row_expansion(table_data, selected_row["id"])
                return updated_table_data, navigation_state, no_update

    elif key == "Escape":
        # Collapse all expanded rows
        updated_table_data = []
        for row in table_data:
            if row.get("row_type") == "main" and row.get("expanded"):
                # Collapse this row
                row["expanded"] = False
                # Update expand button
                if row.get("is_grouped", False):
                    count = row.get("group_count", 1)
                    row["expand_button"] = f"▶️ ({count})"
                else:
                    row["expand_button"] = "▶️"
                updated_table_data.append(row)
            elif row.get("row_type") == "main":
                # Keep main rows
                updated_table_data.append(row)
            # Skip all detail rows (effectively collapsing them)

        return updated_table_data, navigation_state, no_update

    # For arrow keys, just update navigation state, don't interfere with table selection
    elif key == "ArrowDown":
        new_row = min(current_row + 1, len(table_data) - 1)
        updated_nav_state = {**navigation_state, "selected_row": new_row}
        return table_data, updated_nav_state, no_update

    elif key == "ArrowUp":
        new_row = max(current_row - 1, 0)
        updated_nav_state = {**navigation_state, "selected_row": new_row}
        return table_data, updated_nav_state, no_update

    return table_data, navigation_state, no_update
