"""Base chart factory function and layout utilities."""

from __future__ import annotations

from typing import Optional, Union

import dash_mantine_components as dmc
from dash import dcc

from .configs import DEFAULT_LAYOUT, get_chart_config, get_plotly_config


def create_chart_card_layout(
    graph_id: str, loading_id: str, title: str, height: int, description: str = "", plotly_config: Optional[dict] = None
) -> dmc.Stack:
    """Create standardized chart card layout.

    Args:
        graph_id: ID for the graph component
        loading_id: ID for the loading overlay
        title: Chart title to display
        height: Chart height in pixels
        description: Optional description text
        plotly_config: Optional Plotly configuration dictionary

    Returns:
        DMC Stack component with chart layout
    """
    children = [
        dmc.Group(
            justify="space-between",
            align="center",
            children=[dmc.Text(title, fw=500, size="lg"), dmc.Text(description, size="sm", c="dimmed") if description else None],
        ),
        dmc.Paper(
            className="graph-container",
            style={"minHeight": f"{height}px"},
            children=[
                dmc.LoadingOverlay(
                    id=loading_id,
                    visible=True,
                    loaderProps={"variant": "dots", "size": "lg"},
                    overlayProps={"opacity": 0.6, "blur": 2},
                    className="loading-overlay",
                ),
                dcc.Graph(
                    id=graph_id,
                    config=plotly_config or {},
                    style={"width": "100%", "height": f"{height}px"},
                    figure={"data": [], "layout": DEFAULT_LAYOUT},
                ),
            ],
        ),
    ]

    # Filter out None children
    filtered_children = [child for child in children if child is not None]

    return dmc.Stack(gap="md", children=filtered_children)


def chart_card(
    card_id: str,
    input_ids: Union[list[str], str],
    *,
    title: str = "",
    chart_type: str = "line",
    height: int = 400,
    config_overrides: Optional[dict] = None,
    description: str = "",
) -> dmc.Stack:
    """Factory function that creates a chart card with injected input dependencies.

    Args:
        card_id: Unique identifier for this chart instance
        input_ids: List of input component IDs that this chart should listen to
        title: Chart title to display
        chart_type: Type of chart to render (line, area, scatter, etc.)
        height: Chart height in pixels
        config_overrides: Custom Plotly configuration overrides
        description: Optional description text

    Returns:
        DMC Stack component with embedded graph and loading overlay
    """
    graph_id = f"{card_id}-graph"
    loading_id = f"{card_id}-loading"

    # Get appropriate Plotly configuration for this chart type
    plotly_config = get_plotly_config(chart_type)

    # Create the DMC card structure
    card = create_chart_card_layout(graph_id, loading_id, title, height, description, plotly_config)

    # Register callback with injected input IDs
    # Special handling for log scatter charts (they use a different callback pattern)
    if chart_type != "log_scatter":
        from .callbacks import register_chart_callback

        register_chart_callback(graph_id, input_ids, chart_type, config_overrides)
    # Note: log_scatter callbacks are registered separately via register_log_scatter_callback()
    # in the main app callback registration process

    return card


def chart_card_pattern_matching(
    card_id: str,
    chart_type: str,
    title: Optional[str] = None,
    height: int = 400,
    description: Optional[str] = None,
    config_overrides: Optional[dict] = None,
) -> dmc.Stack:
    """Create a chart card component using pattern-matching IDs.

    This function creates chart components that work with the pattern-matching
    callback system, providing better performance and scalability compared
    to individual callbacks.

    Args:
        card_id: Unique identifier for this chart instance (used in pattern ID)
        chart_type: Type of chart to create (cpu_load, disk_capacity, etc.)
        title: Optional custom title (uses chart metadata default if None)
        height: Height of the chart in pixels
        description: Optional description text
        config_overrides: Custom Plotly configuration overrides

    Returns:
        DMC Stack component with pattern-matching IDs for unified callback handling
    """
    # Create pattern-matching component IDs
    graph_id = {"type": "chart", "id": card_id, "chart_type": chart_type}
    loading_id = {"type": "chart-loading", "id": card_id}

    # Get appropriate Plotly configuration for this chart type
    plotly_config = get_plotly_config(chart_type)

    # Get chart metadata for defaults
    from .configs import get_chart_metadata

    metadata = get_chart_metadata(chart_type)
    final_title = title if title is not None else metadata["title"]
    final_description = description if description is not None else metadata["description"]
    final_height = height if height != 400 else metadata["height"]  # Use metadata default if not explicitly set

    # Create the DMC card structure with pattern-matching IDs
    card = create_chart_card_layout(graph_id, loading_id, final_title, final_height, final_description, plotly_config)

    # Note: Pattern-matching callbacks are registered separately via
    # register_pattern_matching_callbacks() in the main callback registration

    return card


def _normalize_input_list(input_ids: Union[list[str], str]) -> list[str]:
    """Normalize input IDs to a list format.

    Args:
        input_ids: Single input ID or list of input IDs

    Returns:
        List of input ID strings
    """
    if isinstance(input_ids, str):
        return [input_ids]
    elif isinstance(input_ids, list):
        return [id for id in input_ids if id is not None and id != ""]
    else:
        return []
