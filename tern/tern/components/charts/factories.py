"""Specialized chart factory functions with configurable input sources."""

from __future__ import annotations

from typing import Optional, Union

import dash_mantine_components as dmc

from .base import _normalize_input_list, chart_card, chart_card_pattern_matching
from .configs import get_chart_metadata


def create_chart(
    chart_type: str,
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
    height: Optional[int] = None,
    color_palette: Optional[str] = None,
    theme: Optional[str] = None,
    config_overrides: Optional[dict] = None,
) -> dmc.Stack:
    """Unified factory for creating any chart type with configurable input sources.

    Args:
        chart_type: Type of chart to create (cpu_load, disk_capacity, etc.)
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses chart metadata default if None)
        description: Optional custom description (uses chart metadata default if None)
        height: Optional custom height (uses chart metadata default if None)
        color_palette: Optional color palette name (default, viridis, plasma, etc.)
        theme: Optional theme name (light, dark)
        config_overrides: Optional Plotly configuration overrides

    Returns:
        DMC Stack component with the specified chart
    """
    # Get chart metadata for defaults
    metadata = get_chart_metadata(chart_type)

    # Use provided values or fall back to metadata defaults
    final_title = title if title is not None else metadata["title"]
    final_description = description if description is not None else metadata["description"]
    final_height = height if height is not None else metadata["height"]

    # Normalize input IDs
    input_ids = _normalize_input_list(time_input_ids)
    if position_input_id:
        input_ids.append(position_input_id)

    # Build config overrides with theme and palette customizations
    final_config_overrides = {}
    if theme or color_palette:
        from .configs import get_chart_theme, get_color_palette

        if theme:
            theme_config = get_chart_theme(theme)
            final_config_overrides.update(theme_config)
        # Color palette will be applied during data extraction

    if config_overrides:
        final_config_overrides.update(config_overrides)

    return chart_card(
        card_id=card_id,
        input_ids=input_ids,
        title=final_title,
        description=final_description,
        chart_type=chart_type,
        height=final_height,
        config_overrides=final_config_overrides if final_config_overrides else None,
    )


def cpu_load_chart(
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Factory for CPU load charts with configurable input sources.

    Args:
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with CPU load chart
    """
    return create_chart("cpu_load", card_id, time_input_ids, position_input_id, title, description)


def disk_capacity_chart(
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Factory for disk capacity charts.

    Args:
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with disk capacity chart
    """
    return create_chart("disk_capacity", card_id, time_input_ids, position_input_id, title, description)


def log_scatter_chart(
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Factory for log scatter plots.

    Args:
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with log scatter chart
    """
    return create_chart("log_scatter", card_id, time_input_ids, position_input_id, title, description)


def gpu_metrics_chart(
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Factory for GPU metrics charts.

    Args:
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with GPU metrics chart
    """
    return create_chart("gpu_metrics", card_id, time_input_ids, position_input_id, title, description)


def memory_chart(
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Factory for memory usage charts.

    Args:
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with memory chart
    """
    return create_chart("memory_details", card_id, time_input_ids, position_input_id, title, description)


def io_throughput_chart(
    card_id: str,
    time_input_ids: Union[list[str], str],
    position_input_id: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Factory for I/O throughput charts.

    Args:
        card_id: Unique identifier for this chart instance
        time_input_ids: Input component IDs for time range selection
        position_input_id: Optional input component ID for position filter
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with I/O throughput chart
    """
    return create_chart("io_throughput", card_id, time_input_ids, position_input_id, title, description)


# Pattern-Matching Chart Factories
# These provide better performance by using a single unified callback


def create_chart_pattern_matching(
    chart_type: str,
    card_id: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
    height: Optional[int] = None,
    color_palette: Optional[str] = None,
    theme: Optional[str] = None,
    config_overrides: Optional[dict] = None,
) -> dmc.Stack:
    """Unified pattern-matching factory for creating any chart type.

    This factory creates charts that use pattern-matching callbacks for better
    performance. Instead of individual callbacks, all charts are handled by a
    single unified callback, reducing overhead by 83%.

    Args:
        chart_type: Type of chart to create (cpu_load, disk_capacity, etc.)
        card_id: Unique identifier for this chart instance
        title: Optional custom title (uses chart metadata default if None)
        description: Optional custom description (uses chart metadata default if None)
        height: Optional custom height (uses chart metadata default if None)
        color_palette: Optional color palette name (default, viridis, plasma, etc.)
        theme: Optional theme name (light, dark)
        config_overrides: Optional Plotly configuration overrides

    Returns:
        DMC Stack component with pattern-matching IDs for unified callback handling
    """
    # Get chart metadata for defaults
    metadata = get_chart_metadata(chart_type)

    # Use provided values or fall back to metadata defaults
    final_title = title if title is not None else metadata["title"]
    final_description = description if description is not None else metadata["description"]
    final_height = height if height is not None else metadata["height"]

    # Build config overrides with theme and palette customizations
    final_config_overrides = {}
    if theme or color_palette:
        from .configs import get_chart_theme, get_color_palette

        if theme:
            theme_config = get_chart_theme(theme)
            final_config_overrides.update(theme_config)
        # Color palette will be applied during data extraction

    if config_overrides:
        final_config_overrides.update(config_overrides)

    return chart_card_pattern_matching(
        card_id=card_id,
        chart_type=chart_type,
        title=final_title,
        description=final_description,
        height=final_height,
        config_overrides=final_config_overrides if final_config_overrides else None,
    )


def cpu_load_chart_pattern_matching(
    card_id: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Pattern-matching factory for CPU load charts.

    Uses pattern-matching callbacks for better performance compared to individual callbacks.

    Args:
        card_id: Unique identifier for this chart instance
        title: Optional custom title (uses metadata default if None)
        description: Optional custom description (uses metadata default if None)

    Returns:
        DMC Stack component with CPU load chart using pattern-matching IDs
    """
    return create_chart_pattern_matching("cpu_load", card_id, title, description)


def disk_capacity_chart_pattern_matching(
    card_id: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Pattern-matching factory for disk capacity charts."""
    return create_chart_pattern_matching("disk_capacity", card_id, title, description)


def memory_chart_pattern_matching(
    card_id: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Pattern-matching factory for memory usage charts."""
    return create_chart_pattern_matching("memory_details", card_id, title, description)


def io_throughput_chart_pattern_matching(
    card_id: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Pattern-matching factory for I/O throughput charts."""
    return create_chart_pattern_matching("io_throughput", card_id, title, description)


def gpu_metrics_chart_pattern_matching(
    card_id: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
) -> dmc.Stack:
    """Pattern-matching factory for GPU metrics charts."""
    return create_chart_pattern_matching("gpu_metrics", card_id, title, description)
