"""Chart component factories and utilities."""

from .base import chart_card, chart_card_pattern_matching, create_chart_card_layout
from .callbacks import register_chart_callback, register_log_scatter_callback, register_pattern_matching_callbacks
from .configs import (
    CHART_PLOTLY_CONFIGS,
    CHART_TYPE_CONFIGS,
    DEFAULT_LAYOUT,
    DEFAULT_PLOTLY_CONFIG,
    SIMPLIFIED_PLOTLY_CONFIG,
    get_chart_config,
    get_chart_metadata,
    get_chart_mode,
    get_chart_theme,
    get_color_palette,
    get_plotly_config,
    merge_configs,
)
from .factories import (
    cpu_load_chart,
    cpu_load_chart_pattern_matching,
    create_chart,
    create_chart_pattern_matching,
    disk_capacity_chart,
    disk_capacity_chart_pattern_matching,
    gpu_metrics_chart,
    gpu_metrics_chart_pattern_matching,
    io_throughput_chart,
    io_throughput_chart_pattern_matching,
    log_scatter_chart,
    memory_chart,
    memory_chart_pattern_matching,
)

__all__ = [
    "chart_card",
    "chart_card_pattern_matching",
    "create_chart_card_layout",
    "register_chart_callback",
    "register_log_scatter_callback",
    "register_pattern_matching_callbacks",
    "DEFAULT_PLOTLY_CONFIG",
    "SIMPLIFIED_PLOTLY_CONFIG",
    "DEFAULT_LAYOUT",
    "CHART_TYPE_CONFIGS",
    "CHART_PLOTLY_CONFIGS",
    "get_plotly_config",
    "get_chart_config",
    "get_chart_metadata",
    "get_chart_mode",
    "get_chart_theme",
    "get_color_palette",
    "merge_configs",
    "create_chart",
    "create_chart_pattern_matching",
    "cpu_load_chart",
    "cpu_load_chart_pattern_matching",
    "disk_capacity_chart",
    "disk_capacity_chart_pattern_matching",
    "gpu_metrics_chart",
    "gpu_metrics_chart_pattern_matching",
    "io_throughput_chart",
    "io_throughput_chart_pattern_matching",
    "log_scatter_chart",
    "memory_chart",
    "memory_chart_pattern_matching",
]
