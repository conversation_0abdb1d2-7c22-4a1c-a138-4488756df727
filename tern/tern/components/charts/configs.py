"""Chart configuration management and default settings."""

from __future__ import annotations

# Default Plotly configuration that will be applied to all charts
DEFAULT_PLOTLY_CONFIG = {
    "displaylogo": False,
    "modeBarButtonsToRemove": [
        "pan2d",
        "lasso2d",
        "select2d",
        "zoom2d",
        "zoomIn2d",
        "zoomOut2d",
        "autoScale2d",
        "resetScale2d",
        "hoverClosestCartesian",
        "hoverCompareCartesian",
        "toggleSpikelines",
    ],
    "scrollZoom": False,
    "doubleClick": False,
    "editable": False,
    "staticPlot": False,
}

# Simplified configuration with hidden toolbar (for log scatter)
SIMPLIFIED_PLOTLY_CONFIG = {
    "displayModeBar": False,  # Completely hides toolbar
    "displaylogo": False,
    "scrollZoom": False,
    "doubleClick": False,
    "editable": False,
    "staticPlot": False,
}

# Common styling patterns for consistency
COMMON_GRID_STYLE = {
    "showgrid": True,
    "gridcolor": "rgba(0,0,0,0.1)",
}

COMMON_AXIS_STYLE = {
    **COMMON_GRID_STYLE,
    "zeroline": True,
    "zerolinecolor": "rgba(0,0,0,0.2)",
}

COMMON_XAXIS_STYLE = {
    "title": None,
    **COMMON_GRID_STYLE,
}

# Default layout settings for all charts
DEFAULT_LAYOUT = {
    "title": None,
    "showlegend": True,
    "legend": {"orientation": "h", "yanchor": "bottom", "y": 1.02, "xanchor": "right", "x": 1},
    "hovermode": "x unified",
    "margin": {"t": 10, "r": 10, "b": 40, "l": 60},
    "xaxis": COMMON_XAXIS_STYLE,
    "plot_bgcolor": "rgba(0,0,0,0)",
    "paper_bgcolor": "rgba(0,0,0,0)",
}

# Chart-specific Plotly configuration overrides
CHART_PLOTLY_CONFIGS = {
    "log_scatter": SIMPLIFIED_PLOTLY_CONFIG,  # Use simplified config for log scatter
    # All other charts use DEFAULT_PLOTLY_CONFIG (default behavior)
}

# Chart-specific layout configuration overrides
CHART_TYPE_CONFIGS = {
    "cpu_load": {
        "yaxis": {"title": "Load Average", **COMMON_AXIS_STYLE},
        "traces": ["Load (1 min)", "Load (5 min)", "Load (15 min)"],
        "chart_mode": "lines",
    },
    "disk_capacity": {
        "yaxis": {"title": "Usage (%)", "range": [0, 100], **COMMON_AXIS_STYLE},
        "chart_mode": "stacked_area",
    },
    "gpu_metrics": {
        "yaxis": {"title": "Percent (%)", "range": [0, 100], **COMMON_AXIS_STYLE},
        "yaxis2": {
            "title": "Temp °C / Power W",
            "overlaying": "y",
            "side": "right",
            "showgrid": False,
            "autorange": True,
        },
        "chart_mode": "dual_axis",
    },
    "log_scatter": {
        "yaxis": {"title": "Position", "type": "category", **COMMON_GRID_STYLE},
        "hovermode": "closest",
        "margin": {"t": 10, "r": 10, "b": 40, "l": 100},
        "chart_mode": "scatter",
    },
    "memory_details": {
        "yaxis": {"title": "GiB", **COMMON_AXIS_STYLE},
        "chart_mode": "stacked_area",
    },
    "io_throughput": {
        "yaxis": {"title": "Throughput (MB/s)", **COMMON_AXIS_STYLE},
        "chart_mode": "stacked_area",
    },
    "log_scatter": {
        "hovermode": "closest",
        "margin": {"t": 10, "r": 10, "b": 40, "l": 100},  # Wider left margin for position labels
        "xaxis": {
            "title": None,
            **COMMON_GRID_STYLE,
            "type": "date",
            "autorange": False,
            "fixedrange": False,
            "rangeslider": {"visible": False},
            "constrain": "domain",
            "constraintoward": "center",
        },
        "yaxis": {
            "title": "Position",
            **COMMON_GRID_STYLE,
            "type": "category",
            "categoryorder": "array",
        },
        "chart_mode": "scatter",
    },
}

# Chart metadata for factory functions
CHART_METADATA = {
    "cpu_load": {
        "title": "CPU Load Average",
        "description": "Shows 1, 5, and 15-minute load averages",
        "height": 400,
    },
    "disk_capacity": {
        "title": "Disk Capacity Usage",
        "description": "Shows disk usage percentage for all mount points",
        "height": 400,
    },
    "gpu_metrics": {
        "title": "GPU Metrics",
        "description": "Shows GPU utilization, memory usage, temperature, and power draw",
        "height": 400,
    },
    "log_scatter": {
        "title": "Log Events by Position",
        "description": "Shows warnings and errors across positions over time",
        "height": 250,
    },
    "memory_details": {
        "title": "Memory & Swap Pressure",
        "description": "Shows RAM and swap usage over time",
        "height": 400,
    },
    "io_throughput": {
        "title": "I/O Throughput",
        "description": "Shows read and write throughput for all block devices",
        "height": 400,
    },
}

# Color palettes for chart customization
COLOR_PALETTES = {
    "default": ["royalblue", "seagreen", "firebrick", "darkorange", "purple", "brown"],
    "viridis": ["#440154", "#31688e", "#35b779", "#fde725"],
    "plasma": ["#0d0887", "#7e03a8", "#cc4778", "#f89441", "#f0f921"],
    "cool": ["#3182bd", "#6baed6", "#9ecae1", "#c6dbef"],
    "warm": ["#de2d26", "#fc9272", "#fee0d2", "#fff5f0"],
}

# Theme configurations
CHART_THEMES = {
    "light": {
        "plot_bgcolor": "rgba(0,0,0,0)",
        "paper_bgcolor": "rgba(0,0,0,0)",
        "font_color": "#000000",
        "grid_color": "rgba(0,0,0,0.1)",
        "axis_color": "rgba(0,0,0,0.2)",
    },
    "dark": {
        "plot_bgcolor": "#1e1e1e",
        "paper_bgcolor": "#1e1e1e",
        "font_color": "#ffffff",
        "grid_color": "rgba(255,255,255,0.1)",
        "axis_color": "rgba(255,255,255,0.2)",
    },
}


def get_plotly_config(chart_type: str) -> dict:
    """Get Plotly configuration for a specific chart type.

    Args:
        chart_type: Type of chart to get config for

    Returns:
        Dictionary with Plotly configuration
    """
    return CHART_PLOTLY_CONFIGS.get(chart_type, DEFAULT_PLOTLY_CONFIG).copy()


def get_chart_config(chart_type: str) -> dict:
    """Get layout configuration for a specific chart type.

    Args:
        chart_type: Type of chart to get config for

    Returns:
        Dictionary with chart layout configuration
    """
    base_config = DEFAULT_LAYOUT.copy()
    if chart_type in CHART_TYPE_CONFIGS:
        base_config.update(CHART_TYPE_CONFIGS[chart_type])
    return base_config


def get_chart_mode(chart_type: str) -> str:
    """Get the chart visualization mode for a specific chart type.

    Args:
        chart_type: Type of chart to get mode for

    Returns:
        String indicating chart mode (lines, stacked_area, dual_axis, scatter)
    """
    return CHART_TYPE_CONFIGS.get(chart_type, {}).get("chart_mode", "lines")


def get_chart_metadata(chart_type: str) -> dict:
    """Get metadata for a specific chart type.

    Args:
        chart_type: Type of chart to get metadata for

    Returns:
        Dictionary with chart metadata (title, description, height)
    """
    return CHART_METADATA.get(
        chart_type,
        {
            "title": f"{chart_type.replace('_', ' ').title()} Chart",
            "description": f"Chart showing {chart_type.replace('_', ' ')} data",
            "height": 400,
        },
    )


def get_color_palette(palette_name: str = "default") -> list[str]:
    """Get color palette for chart styling.

    Args:
        palette_name: Name of the color palette to use

    Returns:
        List of color hex codes
    """
    return COLOR_PALETTES.get(palette_name, COLOR_PALETTES["default"])


def get_chart_theme(theme_name: str = "light") -> dict:
    """Get theme configuration for chart styling.

    Args:
        theme_name: Name of the theme to use

    Returns:
        Dictionary with theme configuration
    """
    return CHART_THEMES.get(theme_name, CHART_THEMES["light"])


def merge_configs(base_config: dict, overrides: dict | None) -> dict:
    """Merge base configuration with user overrides.

    Args:
        base_config: Base configuration dictionary
        overrides: Override configuration dictionary

    Returns:
        Merged configuration dictionary
    """
    if not overrides:
        return base_config.copy()

    merged = base_config.copy()
    merged.update(overrides)
    return merged
