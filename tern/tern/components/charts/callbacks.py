"""Chart callback management and registration."""

from __future__ import annotations

import asyncio
from datetime import datetime
from typing import Optional

import plotly.graph_objects as go
from dash import ALL, MATCH, Input, Output, State, callback, ctx, no_update

# Import pipeline classes for direct access to static methods
from ...core.log_scatter_pipeline import LogScatterDataPipeline
from .configs import get_chart_config, get_chart_mode


def _validate_chart_data(cached_data: dict, data_key: str) -> tuple[dict, bool]:
    """Common validation for chart data extraction.

    Args:
        cached_data: Resource data cache from resource-data-cache Store
        data_key: Key to extract from cached_data["data"] (e.g., "cpu", "memory")

    Returns:
        Tuple of (extracted_data, is_valid)
    """
    if not cached_data or "data" not in cached_data:
        return {}, False

    chart_data = cached_data.get("data", {}).get(data_key, {})
    if not chart_data or not chart_data.get("timestamps"):
        return {}, False

    return chart_data, True


def _create_base_trace(x_data: list, y_data: list, name: str, **trace_options) -> dict:
    """Create a base Plotly trace with common settings.

    Args:
        x_data: X-axis data (usually timestamps)
        y_data: Y-axis data
        name: Trace name
        **trace_options: Additional trace options to override defaults

    Returns:
        Plotly trace dictionary
    """
    base_trace = {
        "x": x_data,
        "y": y_data,
        "name": name,
        "type": "scatter",
        "mode": "lines",
        "line": {"width": 2},
        "hovertemplate": "%{y:.2f}<extra></extra>",
    }
    base_trace.update(trace_options)
    return base_trace


def register_chart_callback(graph_id: str, input_ids: list[str], chart_type: str, config_overrides: Optional[dict] = None):
    """Register callback for chart with dynamic input sources.

    Integrates with the existing 2-phase data architecture:
    1. Listens to time/position inputs
    2. Uses resource-data-cache Store for data
    3. Renders chart figure with appropriate configuration

    Args:
        graph_id: ID of the graph component to update
        input_ids: List of input component IDs that this chart should listen to
        chart_type: Type of chart to render
        config_overrides: Custom Plotly configuration overrides
    """
    # Build Input list - prioritize fetch status for immediate loading response
    # Then listen to resource-data-cache for data updates
    inputs = [
        Input("resource-fetch-status", "data"),  # Primary: immediate loading feedback
        Input("resource-data-cache", "data"),  # Secondary: data updates
    ]

    # Additional State inputs
    states = []

    # Add additional inputs (like position filters) as State if provided
    for input_id in input_ids:
        if input_id and input_id != "time-range-slider":  # Skip time-range-slider as it's handled by resource-data-cache
            states.append(State(input_id, "value"))

    # Get loading component ID (follows existing pattern)
    loading_id = graph_id.replace("-graph", "-loading")

    @callback([Output(graph_id, "figure"), Output(loading_id, "visible")], inputs + states, prevent_initial_call=False)
    def update_chart(*input_values):
        """Dynamic callback that handles any number of inputs and renders chart data."""
        try:
            # Check which input triggered this callback
            triggered_input = ctx.triggered[0] if ctx.triggered else None

            # Validate input values
            if not input_values or len(input_values) < 2:
                print(f"Warning: Insufficient input values provided to chart {graph_id}")
                return _get_empty_chart_figure(chart_type, config_overrides), False

            # Extract fetch status (first Input) and cached data (second Input)
            fetch_status = input_values[0]
            cached_data = input_values[1] if len(input_values) > 1 else None

            # Handle different trigger sources
            if triggered_input and "resource-fetch-status" in triggered_input["prop_id"]:
                # Fetch status changed - check if loading started
                if fetch_status and fetch_status.get("loading", False):
                    # Return existing figure but show loading overlay
                    if cached_data and isinstance(cached_data, dict) and "error" not in cached_data:
                        figure = _generate_chart_figure(chart_type, cached_data, [], config_overrides)
                    else:
                        figure = _get_empty_chart_figure(chart_type, config_overrides, cached_data)
                    return figure, True  # Show loading overlay
                else:
                    # Loading finished, check if we have data to process
                    if cached_data and isinstance(cached_data, dict) and "error" not in cached_data:
                        # Extract filter values
                        filter_values = input_values[2:] if len(input_values) > 2 else []
                        figure = _generate_chart_figure(chart_type, cached_data, filter_values, config_overrides)
                        return figure, False
                    else:
                        return no_update, no_update

            elif triggered_input and "resource-data-cache" in triggered_input["prop_id"]:
                # Data cache updated - process new data and hide loading

                # Validate cached data structure
                if not cached_data or not isinstance(cached_data, dict):
                    print(f"Warning: No cached data or invalid type for chart {graph_id}: {type(cached_data)}")
                    return _get_empty_chart_figure(chart_type, config_overrides, cached_data), False

                # Check for error in cached data
                if "error" in cached_data:
                    print(f"Error in cached data for chart {graph_id}: {cached_data['error']}")
                    return _get_empty_chart_figure(chart_type, config_overrides, cached_data), False

                # Extract filter values (remaining arguments after fetch_status and cached_data are States)
                filter_values = input_values[2:] if len(input_values) > 2 else []

                # Generate chart figure using cached data and filters
                figure = _generate_chart_figure(chart_type, cached_data, filter_values, config_overrides)

                # Validate generated figure
                if not isinstance(figure, dict) or "data" not in figure or "layout" not in figure:
                    print(f"Warning: Invalid figure generated for chart {graph_id}")
                    return _get_empty_chart_figure(chart_type, config_overrides, cached_data), False

                # Return figure and hide loading
                return figure, False

            else:
                # Initial load or other trigger - show current state

                # Check if currently loading
                if fetch_status and fetch_status.get("loading", False):
                    return _get_empty_chart_figure(chart_type, config_overrides, cached_data), True

                # Show data if available
                if cached_data and isinstance(cached_data, dict) and "error" not in cached_data:
                    # Extract filter values
                    filter_values = input_values[2:] if len(input_values) > 2 else []
                    figure = _generate_chart_figure(chart_type, cached_data, filter_values, config_overrides)
                    return figure, False
                else:
                    return _get_empty_chart_figure(chart_type, config_overrides, cached_data), False

        except Exception as e:
            # Log detailed error and return empty figure with loading hidden
            print(f"Error in chart callback for {graph_id} (type: {chart_type}): {e}")
            import traceback

            print(f"Traceback: {traceback.format_exc()}")
            empty_figure = _get_empty_chart_figure(chart_type, config_overrides, cached_data)
            return empty_figure, False


def register_log_scatter_callback(graph_id: str, input_ids: list[str], config_overrides: Optional[dict] = None, log_visualizer_app=None):
    """Register specialized callback for log scatter chart.

    Log scatter charts have a different data flow than resource charts:
    - They listen to date pickers instead of resource-data-cache
    - They fetch data directly from log_visualizer_app
    - They have specialized scatter plot rendering

    Args:
        graph_id: ID of the graph component to update
        input_ids: List of input component IDs (should include date pickers)
        config_overrides: Custom Plotly configuration overrides
        log_visualizer_app: The log visualizer app instance for data fetching
    """
    # Get loading component ID
    loading_id = graph_id.replace("-graph", "-loading")

    @callback(
        [Output(graph_id, "figure"), Output(loading_id, "visible")],
        [Input("start-date-picker", "value"), Input("end-date-picker", "value")],
        [State("parsing-status", "data")],
        prevent_initial_call=True,
    )
    def update_log_scatter_chart(start_date, end_date, system_status):
        """Update log scatter chart with direct data fetching."""
        try:
            # Show loading state while parsing is not complete
            if system_status.get("status") != "complete":
                return _get_empty_chart_figure("log_scatter", config_overrides), True

            # Parse timestamps
            start_time = _parse_timestamp(start_date)
            end_time = _parse_timestamp(end_date)

            if not start_time or not end_time:
                print(f"Warning: Could not parse timestamps for log scatter {graph_id}")
                return _get_empty_chart_figure("log_scatter", config_overrides), False

            if not log_visualizer_app:
                print(f"Warning: No log_visualizer_app provided for log scatter {graph_id}")
                return _get_empty_chart_figure("log_scatter", config_overrides), False

            # Fetch log scatter data directly
            scatter_data = asyncio.run(
                LogScatterDataPipeline.get_log_scatter_data(
                    cache=log_visualizer_app.data_context.transformation_cache,
                    sources=log_visualizer_app.data_context.pipeline_sources,
                    start_time=start_time,
                    end_time=end_time,
                )
            )

            if not scatter_data["scatter_data"]["WARNING"]["x"] and not scatter_data["scatter_data"]["ERROR"]["x"]:
                return _get_empty_chart_figure("log_scatter", config_overrides), False

            # Generate figure using extracted data
            figure = _generate_log_scatter_figure(scatter_data, start_time, end_time, config_overrides)

            return figure, False

        except Exception as e:
            print(f"Error in log scatter callback for {graph_id}: {e}")
            import traceback

            print(f"Traceback: {traceback.format_exc()}")
            return _get_empty_chart_figure("log_scatter", config_overrides), False


def _parse_timestamp(timestamp):
    """Parse timestamp from various formats."""
    try:
        if isinstance(timestamp, str):
            return datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
        if hasattr(timestamp, "timestamp"):
            return timestamp
        print(f"Warning: Unexpected timestamp type: {type(timestamp)}")
        return None
    except Exception:
        print(f"Warning: Could not parse timestamp string: {timestamp}")
        return None


def _generate_log_scatter_figure(
    scatter_data: dict, start_time: datetime, end_time: datetime, config_overrides: Optional[dict] = None
) -> dict:
    """Generate log scatter figure from scatter data."""
    # Get chart configuration
    layout = get_chart_config("log_scatter")
    if config_overrides:
        layout.update(config_overrides)

    traces = []

    # Add WARNING scatter trace (plotted first, so errors appear on top)
    warning_data = scatter_data["scatter_data"]["WARNING"]
    if warning_data["x"]:
        # Normalize sizes for better visualization (scale between 5 and 50)
        sizes = warning_data.get("sizes", [])
        if sizes:
            min_size, max_size = min(sizes), max(sizes)
            if max_size > min_size:
                normalized_sizes = [5 + 45 * (size - min_size) / (max_size - min_size) for size in sizes]
            else:
                normalized_sizes = [20] * len(sizes)
        else:
            normalized_sizes = []

        traces.append(
            go.Scatter(
                x=warning_data["x"],
                y=warning_data["y"],
                mode="markers",
                marker=dict(
                    size=normalized_sizes,
                    color="gold",
                    opacity=0.7,
                    line=dict(width=1, color="orange"),
                ),
                name="WARNING",
                customdata=sizes,
                hovertemplate="<b>%{y}</b><br>Time: %{x}<br>Warnings: %{customdata}<extra></extra>",
            )
        )

    # Add ERROR scatter trace (plotted second, so appears on top)
    error_data = scatter_data["scatter_data"]["ERROR"]
    if error_data["x"]:
        # Normalize sizes for better visualization
        sizes = error_data.get("sizes", [])
        if sizes:
            min_size, max_size = min(sizes), max(sizes)
            if max_size > min_size:
                normalized_sizes = [5 + 45 * (size - min_size) / (max_size - min_size) for size in sizes]
            else:
                normalized_sizes = [20] * len(sizes)
        else:
            normalized_sizes = []

        traces.append(
            go.Scatter(
                x=error_data["x"],
                y=error_data["y"],
                mode="markers",
                marker=dict(
                    size=normalized_sizes,
                    color="red",
                    opacity=0.8,
                    line=dict(width=1, color="darkred"),
                ),
                name="ERROR",
                customdata=sizes,
                hovertemplate="<b>%{y}</b><br>Time: %{x}<br>Errors: %{customdata}<extra></extra>",
            )
        )

    # Update layout with time range and position categories
    layout.update(
        {
            "xaxis": {
                **layout.get("xaxis", {}),
                "range": [start_time, end_time],
            },
            "yaxis": {
                **layout.get("yaxis", {}),
                "categoryarray": sorted(scatter_data["positions"]),
            },
        }
    )

    return {"data": traces, "layout": layout}


def register_pattern_matching_callbacks():
    """Register unified pattern-matching callback for all chart types.

    This replaces individual chart callbacks with a single callback that handles
    all chart updates using Dash pattern-matching. Provides significant performance
    benefits by reducing callback overhead from 6 callbacks to 1 callback.

    Component ID Structure:
        Charts: {"type": "chart", "id": "chart-name", "chart_type": "chart_type"}
        Loading: {"type": "chart-loading", "id": "chart-name"}

    Benefits:
        - 83% reduction in callback executions (6 → 1)
        - Eliminates redundant data validation and processing
        - Single coordinated update instead of multiple competing updates
        - Better performance and scalability
    """

    @callback(
        [
            Output({"type": "chart", "id": ALL, "chart_type": ALL}, "figure"),
            Output({"type": "chart-loading", "id": ALL}, "visible"),
        ],
        [
            Input("resource-data-cache", "data"),
            Input("resource-fetch-status", "data"),
        ],
        [State({"type": "chart", "id": ALL, "chart_type": ALL}, "id")],
        prevent_initial_call=True,
    )
    def update_all_charts_pattern_matching(cached_data, fetch_status, chart_component_ids):
        """Unified callback that updates all charts using pattern-matching.

        Args:
            cached_data: Resource data from resource-data-cache Store
            fetch_status: Loading status from resource-fetch-status Store
            chart_component_ids: List of chart component IDs with embedded metadata

        Returns:
            Tuple of (figures_list, loading_states_list)
        """
        try:
            # Validate input
            if not chart_component_ids:
                print("Warning: No chart components found for pattern-matching callback")
                return [], []

            # Check if data is being fetched - show loading for all charts if so
            is_loading = fetch_status and fetch_status.get("loading", False)

            figures = []
            loading_states = []

            # Process each chart component
            for component_id in chart_component_ids:
                try:
                    # Extract chart metadata from component ID
                    chart_type = component_id["chart_type"]
                    chart_id = component_id["id"]

                    # Generate figure for this specific chart type
                    if cached_data:
                        figure = _generate_chart_figure(chart_type, cached_data, [], None)
                    else:
                        figure = _get_empty_chart_figure(chart_type, None, cached_data)

                    figures.append(figure)
                    loading_states.append(is_loading)  # Show loading if fetch in progress

                except Exception as e:
                    print(f"Error processing chart {component_id}: {e}")
                    # Add empty figure for failed charts to maintain list alignment
                    empty_figure = _get_empty_chart_figure(component_id.get("chart_type", "unknown"), None, cached_data)
                    figures.append(empty_figure)
                    loading_states.append(is_loading)  # Maintain consistent loading state

            return figures, loading_states

        except Exception as e:
            print(f"Error in pattern-matching callback: {e}")
            import traceback

            print(f"Traceback: {traceback.format_exc()}")

            # Return empty results that match the expected output structure
            num_charts = len(chart_component_ids) if chart_component_ids else 0
            is_loading = fetch_status and fetch_status.get("loading", False)
            return [_get_empty_chart_figure("unknown", None)] * num_charts, [is_loading] * num_charts


def _generate_chart_figure(
    chart_type: str, cached_data: Optional[dict], filter_values: list, config_overrides: Optional[dict] = None
) -> dict:
    """Generate chart figure from cached data and filters.

    Args:
        chart_type: Type of chart to generate
        cached_data: Data from resource-data-cache Store
        filter_values: Values from input components (time range, position, etc.)
        config_overrides: Custom Plotly configuration overrides

    Returns:
        Plotly figure dictionary
    """
    # If no cached data, return empty figure
    if not cached_data:
        return _get_empty_chart_figure(chart_type, config_overrides, cached_data)

    # Get chart configuration
    layout = get_chart_config(chart_type)
    if config_overrides:
        layout.update(config_overrides)

    # Extract data based on chart type
    chart_data = []

    try:
        if chart_type == "cpu_load":
            chart_data = _extract_cpu_load_data(cached_data, filter_values)
        elif chart_type == "disk_capacity":
            chart_data = _extract_disk_capacity_data(cached_data, filter_values)
        elif chart_type == "memory_details":
            chart_data = _extract_memory_data(cached_data, filter_values)
        elif chart_type == "io_throughput":
            chart_data = _extract_io_throughput_data(cached_data, filter_values)
        elif chart_type == "gpu_metrics":
            chart_data = _extract_gpu_metrics_data(cached_data, filter_values)
        elif chart_type == "log_scatter":
            chart_data = _extract_log_scatter_data(cached_data, filter_values)
        else:
            # Unknown chart type, return empty
            chart_data = []

    except Exception as e:
        print(f"Error extracting data for {chart_type}: {e}")
        chart_data = []

    # RESOURCE CHART X-AXIS SYNCHRONIZATION FIX:
    # For resource charts (not log_scatter), synchronize X-axis range with time-range-slider values
    # This ensures charts show the full slider range A→Z even if data only spans E→Z
    if chart_type != "log_scatter" and cached_data and "time_range" in cached_data:
        try:
            # Get the time range from the cached data (this comes from the time-range-slider)
            time_range = cached_data["time_range"]
            if time_range and len(time_range) == 2:
                # Parse the time range strings to datetime objects for Plotly
                start_time_str, end_time_str = time_range

                # Convert ISO format strings to datetime objects
                from datetime import datetime

                start_time = datetime.fromisoformat(start_time_str.replace("Z", "+00:00"))
                end_time = datetime.fromisoformat(end_time_str.replace("Z", "+00:00"))

                # Update the layout to enforce the X-axis range to match slider values
                if "xaxis" not in layout:
                    layout["xaxis"] = {}

                layout["xaxis"]["range"] = [start_time, end_time]

        except Exception as e:
            print(f"Warning: Could not synchronize X-axis range for {chart_type}: {e}")
            # Continue without range synchronization rather than failing

    return {"data": chart_data, "layout": layout}


def _get_empty_chart_figure(chart_type: str, config_overrides: Optional[dict] = None, cached_data: Optional[dict] = None) -> dict:
    """Get empty chart figure with proper layout.

    Args:
        chart_type: Type of chart
        config_overrides: Custom Plotly configuration overrides
        cached_data: Optional cached data for X-axis range synchronization

    Returns:
        Empty Plotly figure dictionary
    """
    layout = get_chart_config(chart_type)
    if config_overrides:
        layout.update(config_overrides)

    # RESOURCE CHART X-AXIS SYNCHRONIZATION FIX:
    # Apply the same X-axis range synchronization for empty charts
    if chart_type != "log_scatter" and cached_data and "time_range" in cached_data:
        try:
            # Get the time range from the cached data (this comes from the time-range-slider)
            time_range = cached_data["time_range"]
            if time_range and len(time_range) == 2:
                # Parse the time range strings to datetime objects for Plotly
                start_time_str, end_time_str = time_range

                # Convert ISO format strings to datetime objects
                from datetime import datetime

                start_time = datetime.fromisoformat(start_time_str.replace("Z", "+00:00"))
                end_time = datetime.fromisoformat(end_time_str.replace("Z", "+00:00"))

                # Update the layout to enforce the X-axis range to match slider values
                if "xaxis" not in layout:
                    layout["xaxis"] = {}

                layout["xaxis"]["range"] = [start_time, end_time]

        except Exception as e:
            print(f"Warning: Could not synchronize X-axis range for empty {chart_type}: {e}")
            # Continue without range synchronization rather than failing

    return {"data": [], "layout": layout}


def _extract_cpu_load_data(cached_data: dict, filter_values: list) -> list:
    """Extract CPU load data from cache.

    Args:
        cached_data: Resource data cache from resource-data-cache Store
        filter_values: Filter values from inputs

    Returns:
        List of Plotly trace dictionaries
    """
    cpu_data, is_valid = _validate_chart_data(cached_data, "cpu")
    if not is_valid:
        return []

    traces = []

    # CPU load has load1, load5, load15 averages (matching existing data.py structure)
    for load_type, name in [
        ("load1", "Load (1 min)"),
        ("load5", "Load (5 min)"),
        ("load15", "Load (15 min)"),
    ]:
        if load_type in cpu_data:
            traces.append(_create_base_trace(cpu_data["timestamps"], cpu_data[load_type], name))

    return traces


def _extract_disk_capacity_data(cached_data: dict, filter_values: list) -> list:
    """Extract disk capacity data from cache."""
    # Extract disk data from the data structure used by existing callbacks
    disk_data = cached_data.get("data", {}).get("disk", {})
    if not disk_data or not disk_data.get("timestamps"):
        return []

    traces = []

    # Disk capacity has timestamps and mount_points structure (matching existing data.py)
    mount_points = disk_data.get("mount_points", {})
    for mount_point, usage_data in mount_points.items():
        traces.append(
            {
                "x": disk_data["timestamps"],
                "y": usage_data,
                "name": mount_point,
                "type": "scatter",
                "mode": "lines",
                "line": {"width": 0},
                "fill": "tonexty" if traces else "tozeroy",  # Stack area
                "stackgroup": "disk_usage",
                "hovertemplate": f"{mount_point}: %{{y:.1f}}%<extra></extra>",
            }
        )

    return traces


def _extract_memory_data(cached_data: dict, filter_values: list) -> list:
    """Extract memory data from cache."""
    # Extract memory data from the data structure used by existing callbacks
    memory_data = cached_data.get("data", {}).get("memory", {})
    if not memory_data or not memory_data.get("timestamps"):
        return []

    traces = []

    # Add RAM used trace (matching existing data.py structure)
    traces.append(
        {
            "x": memory_data["timestamps"],
            "y": memory_data["mem_used_gb"],
            "name": "RAM used",
            "type": "scatter",
            "mode": "lines",
            "line": {"width": 0},
            "fill": "tonexty",
            "stackgroup": "pressure",
            "hovertemplate": "%{y:.2f} GB<extra></extra>",
        }
    )

    # Add Swap used trace
    traces.append(
        {
            "x": memory_data["timestamps"],
            "y": memory_data["swap_used_gb"],
            "name": "Swap used",
            "type": "scatter",
            "mode": "lines",
            "line": {"width": 0},
            "fill": "tonexty",
            "stackgroup": "pressure",
            "hovertemplate": "%{y:.2f} GB<extra></extra>",
        }
    )

    # Add total memory line
    traces.append(
        {
            "x": memory_data["timestamps"],
            "y": memory_data["mem_total_gb"],
            "name": "Total memory",
            "type": "scatter",
            "mode": "lines",
            "line": {"dash": "dash", "width": 1},
            "hovertemplate": "%{y:.2f} GB<extra></extra>",
        }
    )

    return traces


def _extract_io_throughput_data(cached_data: dict, filter_values: list) -> list:
    """Extract I/O throughput data from cache."""
    # Extract I/O data from the data structure used by existing callbacks
    io_data = cached_data.get("data", {}).get("io", {})
    if not io_data or not io_data.get("timestamps"):
        return []

    traces = []

    # I/O data has timestamps, read_throughput, and write_throughput (matching existing data.py)
    # First add all read traces
    read_throughput = io_data.get("read_throughput", {})
    for block_name, read_data in read_throughput.items():
        traces.append(
            {
                "x": io_data["timestamps"],
                "y": read_data,
                "name": f"{block_name} (Read)",
                "type": "scatter",
                "mode": "lines",
                "line": {"width": 0},
                "fill": "tonexty" if traces else "tozeroy",
                "stackgroup": "read",
                "hovertemplate": f"{block_name} Read: %{{y:.1f}} MB/s<extra></extra>",
            }
        )

    # Then add all write traces
    write_throughput = io_data.get("write_throughput", {})
    for block_name, write_data in write_throughput.items():
        traces.append(
            {
                "x": io_data["timestamps"],
                "y": write_data,
                "name": f"{block_name} (Write)",
                "type": "scatter",
                "mode": "lines",
                "line": {"width": 0},
                "fill": "tonexty" if traces else "tozeroy",
                "stackgroup": "write",
                "hovertemplate": f"{block_name} Write: %{{y:.1f}} MB/s<extra></extra>",
            }
        )

    return traces


def _extract_gpu_metrics_data(cached_data: dict, filter_values: list) -> list:
    """Extract GPU metrics data from cache."""
    # Extract GPU data from the data structure used by existing callbacks
    gpu_data = cached_data.get("data", {}).get("gpu", {})
    if not gpu_data or not gpu_data.get("timestamps"):
        return []

    traces = []

    # Define colors for different GPUs and metrics (matching existing data.py)
    colors = ["royalblue", "seagreen", "firebrick", "darkorange", "purple", "brown"]

    # Add left-axis traces (percentages) for each GPU
    gpu_count = 0
    utilization_data = gpu_data.get("utilization", {})
    for gpu_name in utilization_data.keys():
        base_color_idx = gpu_count % len(colors)

        # GPU Utilization (left axis)
        traces.append(
            {
                "x": gpu_data["timestamps"],
                "y": gpu_data["utilization"][gpu_name],
                "name": f"{gpu_name} Util %",
                "type": "scatter",
                "mode": "lines",
                "line": {"color": colors[base_color_idx]},
                "hovertemplate": f"{gpu_name} Utilization: %{{y:.1f}}%<extra></extra>",
            }
        )

        # GPU Memory Usage (left axis)
        if gpu_name in gpu_data.get("memory_used_pct", {}):
            traces.append(
                {
                    "x": gpu_data["timestamps"],
                    "y": gpu_data["memory_used_pct"][gpu_name],
                    "name": f"{gpu_name} Mem %",
                    "type": "scatter",
                    "mode": "lines",
                    "line": {"color": colors[base_color_idx], "dash": "dot"},
                    "hovertemplate": f"{gpu_name} Memory: %{{y:.1f}}%<extra></extra>",
                }
            )

        gpu_count += 1

    # Add right-axis traces (absolute values) for each GPU
    gpu_count = 0
    temperature_data = gpu_data.get("temperature", {})
    for gpu_name in temperature_data.keys():
        base_color_idx = gpu_count % len(colors)

        # GPU Temperature (right axis)
        traces.append(
            {
                "x": gpu_data["timestamps"],
                "y": gpu_data["temperature"][gpu_name],
                "name": f"{gpu_name} Temp °C",
                "type": "scatter",
                "mode": "lines",
                "line": {"color": colors[(base_color_idx + 2) % len(colors)]},
                "yaxis": "y2",
                "hovertemplate": f"{gpu_name} Temperature: %{{y:.1f}}°C<extra></extra>",
            }
        )

        # GPU Power Draw (right axis)
        if gpu_name in gpu_data.get("power_draw", {}):
            traces.append(
                {
                    "x": gpu_data["timestamps"],
                    "y": gpu_data["power_draw"][gpu_name],
                    "name": f"{gpu_name} Power W",
                    "type": "scatter",
                    "mode": "lines",
                    "line": {"color": colors[(base_color_idx + 2) % len(colors)], "dash": "dash"},
                    "yaxis": "y2",
                    "hovertemplate": f"{gpu_name} Power: %{{y:.1f}}W<extra></extra>",
                }
            )

        gpu_count += 1

    return traces


def _extract_log_scatter_data(cached_data: dict, filter_values: list) -> list:
    """Extract log scatter data from cache.

    Note: Log scatter uses a different data structure and fetching pattern
    than other resource charts. The actual implementation should handle
    this through a specialized callback that fetches data directly.
    """
    # Log scatter data comes from a different source (date pickers, not resource cache)
    # and requires direct data fetching. This function serves as a placeholder
    # for the factory system but the actual data fetching happens in a custom callback.
    log_data = cached_data.get("log_scatter", {})
    if not log_data:
        return []

    traces = []

    # Handle the actual log scatter data structure from get_log_scatter_data()
    scatter_data = log_data.get("scatter_data", {})

    # Add WARNING scatter trace
    warning_data = scatter_data.get("WARNING", {})
    if warning_data.get("x"):
        # Normalize sizes for better visualization (scale between 5 and 50)
        sizes = warning_data.get("sizes", [])
        if sizes:
            min_size, max_size = min(sizes), max(sizes)
            if max_size > min_size:
                normalized_sizes = [5 + 45 * (size - min_size) / (max_size - min_size) for size in sizes]
            else:
                normalized_sizes = [20] * len(sizes)
        else:
            normalized_sizes = []

        traces.append(
            {
                "x": warning_data["x"],
                "y": warning_data["y"],
                "mode": "markers",
                "marker": {
                    "size": normalized_sizes,
                    "color": "gold",
                    "opacity": 0.7,
                    "line": {"width": 1, "color": "orange"},
                },
                "name": "WARNING",
                "customdata": sizes,
                "hovertemplate": "<b>%{y}</b><br>Time: %{x}<br>Warnings: %{customdata}<extra></extra>",
            }
        )

    # Add ERROR scatter trace
    error_data = scatter_data.get("ERROR", {})
    if error_data.get("x"):
        # Normalize sizes for better visualization
        sizes = error_data.get("sizes", [])
        if sizes:
            min_size, max_size = min(sizes), max(sizes)
            if max_size > min_size:
                normalized_sizes = [5 + 45 * (size - min_size) / (max_size - min_size) for size in sizes]
            else:
                normalized_sizes = [20] * len(sizes)
        else:
            normalized_sizes = []

        traces.append(
            {
                "x": error_data["x"],
                "y": error_data["y"],
                "mode": "markers",
                "marker": {
                    "size": normalized_sizes,
                    "color": "red",
                    "opacity": 0.8,
                    "line": {"width": 1, "color": "darkred"},
                },
                "name": "ERROR",
                "customdata": sizes,
                "hovertemplate": "<b>%{y}</b><br>Time: %{x}<br>Errors: %{customdata}<extra></extra>",
            }
        )

    return traces
