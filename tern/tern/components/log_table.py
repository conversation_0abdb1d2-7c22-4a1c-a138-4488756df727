"""Professional log table component using DMC for better styling."""

from __future__ import annotations

import dash_mantine_components as dmc
from dash import callback_context, html
from dash_iconify import DashIconify


def create_log_table_header():
    """Create the table header using DMC components."""
    return dmc.TableThead(
        [
            dmc.TableTr(
                [
                    dmc.TableTh("", style={"width": "24px", "textAlign": "center"}),  # Expand button column
                    dmc.TableTh("Time", style={"width": "140px", "fontWeight": 600}),
                    dmc.TableTh("Event", style={"width": "180px", "fontWeight": 600}),
                    dmc.TableTh("Position", style={"width": "120px", "fontWeight": 600}),
                    dmc.TableTh("Process", style={"width": "150px", "fontWeight": 600}),
                    dmc.TableTh("Key Context", style={"fontWeight": 600}),
                ]
            )
        ]
    )


def create_log_row(log_entry: dict, is_selected: bool = False) -> dmc.TableTr:
    """Create a single log table row using DMC components.

    Args:
        log_entry: Log entry data
        is_selected: Whether this row is currently selected

    Returns:
        DMC TableTr component
    """
    is_detail = log_entry.get("row_type") == "detail"
    is_expanded = log_entry.get("expanded", False)
    log_level = log_entry.get("log_level_raw", "")

    # Component creation is working correctly

    # Determine row styling based on type and state
    if is_detail:
        # Check if this is a rich content row
        if "rich_content" in log_entry:
            # For rich content rows, we'll create a special merged row
            return dmc.TableTr(
                [
                    dmc.TableTd(
                        log_entry["rich_content"],
                        style={"padding": "0", "backgroundColor": "#f8f9fa"},
                        tableProps={"colSpan": 6},  # Updated to span all 6 columns
                    )
                ],
                style={"backgroundColor": "#f8f9fa"},
            )
        else:
            # Regular detail row styling
            row_style = {"backgroundColor": "#f8f9fa", "borderLeft": "3px solid #4285f4", "paddingLeft": "16px"}
            text_style = {"fontSize": "12px", "color": "#6c757d", "fontStyle": "italic"}
    else:
        # Main row styling
        row_style = {"borderBottom": "1px solid #e9ecef", "cursor": "pointer"}
        text_style = {"fontSize": "14px", "color": "#212529"}

        # Add log level styling
        if log_level == "ERROR":
            row_style.update({"backgroundColor": "#fff5f5", "borderLeft": "4px solid #f56565"})
        elif log_level in ["WARN", "WARNING"]:
            row_style.update({"backgroundColor": "#fffaf0", "borderLeft": "4px solid #ed8936"})
        elif log_level == "DEBUG":
            row_style.update({"backgroundColor": "#f7fafc", "borderLeft": "4px solid #a0aec0"})

        # Highlight if expanded
        if is_expanded:
            row_style.update({"backgroundColor": "#e6f3ff", "fontWeight": "500"})

        # Highlight if selected
        if is_selected:
            row_style.update({"backgroundColor": "#e3f2fd", "outline": "2px solid #2196f3"})

    # Create expand button
    if is_detail:
        expand_button = ""
    else:
        expand_icon = "🔽" if is_expanded else "▶️"
        if log_entry.get("is_grouped", False):
            count = log_entry.get("group_count", 1)
            expand_button = dmc.Button(
                f"{expand_icon} ({count})",
                id={"type": "expand-btn", "index": log_entry.get("id", "")},
                variant="subtle",
                size="xs",
                style={"padding": "2px 6px", "fontSize": "12px", "minHeight": "20px"},
            )
        else:
            expand_button = dmc.Button(
                expand_icon,
                id={"type": "expand-btn", "index": log_entry.get("id", "")},
                variant="subtle",
                size="xs",
                style={"padding": "2px 6px", "fontSize": "12px", "minHeight": "20px"},
            )

    # Row styling only - expansion is handled by buttons
    row_props = {"style": row_style}

    return dmc.TableTr(
        [
            dmc.TableTd(expand_button, style={**text_style, "textAlign": "center", "cursor": "pointer"}),
            dmc.TableTd(log_entry.get("timestamp_display", ""), style={**text_style, "fontFamily": "monospace"}),
            dmc.TableTd(log_entry.get("log_event", ""), style=text_style),
            dmc.TableTd(log_entry.get("folder_name", ""), style=text_style),
            dmc.TableTd(log_entry.get("process_name", ""), style=text_style),
            dmc.TableTd(log_entry.get("key_context_summary", ""), style={**text_style, "wordBreak": "break-word"}),
        ],
        **row_props,
        **{"data-row-id": log_entry.get("id", ""), "data-row-type": log_entry.get("row_type", "main")},
    )


def create_professional_log_table(table_data: list[dict], selected_row_id: str = None) -> dmc.Table:
    """Create a professional-looking log table using DMC components.

    Args:
        table_data: List of log entries to display
        selected_row_id: ID of currently selected row

    Returns:
        DMC Table component
    """
    if not table_data:
        return dmc.Table(
            [
                create_log_table_header(),
                dmc.TableTbody(
                    [
                        dmc.TableTr(
                            [
                                dmc.TableTd(
                                    dmc.Center(
                                        [
                                            dmc.Stack(
                                                [
                                                    DashIconify(icon="mdi:information-outline", width=24, color="gray"),
                                                    dmc.Text("No log entries found", size="sm", c="dimmed"),
                                                ],
                                                align="center",
                                                gap="xs",
                                            )
                                        ],
                                        style={"height": "200px"},
                                    ),
                                    tableProps={"colSpan": 6},
                                )
                            ]
                        )
                    ]
                ),
            ],
            highlightOnHover=True,
            withTableBorder=True,
            withColumnBorders=True,
        )

    # Create table rows
    table_rows = []
    for log_entry in table_data:
        is_selected = log_entry.get("id") == selected_row_id
        row = create_log_row(log_entry, is_selected)
        table_rows.append(row)

    return dmc.Table(
        [create_log_table_header(), dmc.TableTbody(table_rows)],
        highlightOnHover=True,
        withTableBorder=True,
        withColumnBorders=True,
        style={"borderRadius": "8px", "overflow": "hidden", "boxShadow": "0 1px 3px rgba(0,0,0,0.1)"},
    )


def create_table_pagination(current_page: int, total_pages: int, total_logs: int) -> dmc.Group:
    """Create pagination controls for the table.

    Args:
        current_page: Current page number (0-based)
        total_pages: Total number of pages
        total_logs: Total number of log entries

    Returns:
        DMC Group with pagination controls
    """
    # Always show pagination controls and log count
    pagination_controls = dmc.Group(
        [
            dmc.ActionIcon(
                DashIconify(icon="mdi:chevron-left"), id="log-table-prev", variant="light", disabled=current_page == 0 or total_pages <= 1
            ),
            dmc.Text(f"Page {current_page + 1} of {max(total_pages, 1)}", size="sm"),
            dmc.ActionIcon(
                DashIconify(icon="mdi:chevron-right"),
                id="log-table-next",
                variant="light",
                disabled=current_page >= total_pages - 1 or total_pages <= 1,
            ),
        ],
        gap="xs",
    )

    log_count_text = dmc.Text(f"{total_logs:,} logs total", size="sm", c="dimmed")

    return dmc.Group([pagination_controls, log_count_text], justify="space-between")
