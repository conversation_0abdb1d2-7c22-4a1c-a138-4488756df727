/* Time window slider styles */
.time-window-slider {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Ensure slider marks are visible */
.time-window-slider .rc-slider-mark {
    font-size: 12px;
    color: var(--mantine-color-gray-6);
    font-family: var(--mantine-font-family-monospace);
}

/* Style for position time range overlays */
.time-window-slider .rc-slider-track {
    background-color: var(--mantine-color-blue-1);
    height: 8px;
}

/* Ensure slider handles are visible and clickable */
.time-window-slider .rc-slider-handle {
    border: 2px solid var(--mantine-color-blue-6);
    background-color: var(--mantine-color-white);
    width: 16px;
    height: 16px;
    margin-top: -6px;
    box-shadow: var(--mantine-shadow-sm);
}

/* Hover state for slider handles */
.time-window-slider .rc-slider-handle:hover {
    border-color: var(--mantine-color-blue-7);
    box-shadow: var(--mantine-shadow-md);
}

/* Active state for slider handles */
.time-window-slider .rc-slider-handle:active {
    border-color: var(--mantine-color-blue-8);
    box-shadow: var(--mantine-shadow-lg);
}

/* Graph container styles */
.graph-container {
    position: relative;
    width: 100%;
    min-height: 400px;
    background-color: var(--mantine-color-white);
    border-radius: var(--mantine-radius-md);
    box-shadow: var(--mantine-shadow-sm);
}

/* Loading overlay styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

/* Tooltip styles */
.time-window-slider .rc-slider-tooltip {
    font-family: var(--mantine-font-family-monospace);
    font-size: var(--mantine-font-size-sm);
    background-color: var(--mantine-color-dark-7);
    color: var(--mantine-color-white);
    border-radius: var(--mantine-radius-sm);
    padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
    box-shadow: var(--mantine-shadow-md);
}
