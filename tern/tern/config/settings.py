"""Configuration settings for the dash_ui application."""

from __future__ import annotations

from enum import Enum
from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict


class EnvironmentType(str, Enum):
    DEV = "dev"
    TEST = "test"
    PROD = "prod"


class Settings(BaseSettings):
    """Application settings container."""

    env: EnvironmentType = EnvironmentType.DEV
    debug: bool = False
    log_level: str = "INFO"

    # Log paths
    base_log_folder: str = "logs"
    resource_log_file: str = "logs/resource.log"
    event_log_file: str = "logs/minknow.log"

    # Log parsing settings
    date_format: str = "%Y-%m-%d %H:%M:%S"
    max_lines: int = 1000

    # Analysis settings
    anomaly_threshold: float = 0.8
    time_window: int = 3600  # 1 hour in seconds

    # Container settings
    singleton_providers: bool = True
    auto_wire: bool = True

    # Debug output settings
    save_intermediate_data: bool = True
    intermediate_data_dir: str = "intermediate_data"

    # Different configuration based on environment
    @property
    def is_development(self):
        return self.env == EnvironmentType.DEV

    @property
    def is_testing(self):
        return self.env == EnvironmentType.TEST

    @property
    def is_production(self):
        return self.env == EnvironmentType.PROD

    # In Pydantic v2, Config is replaced with model_config
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="allow",  # Allow extra fields from .env file
        case_sensitive=False,  # Allow case-insensitive environment variables
        env_prefix="",  # No prefix for environment variables
    )


@lru_cache
def get_settings():
    """Get settings with caching."""
    return Settings()


settings = get_settings()
