"""File dialog service for native file selection."""

import os
import platform
from pathlib import Path
from typing import Any, Dict, Optional

from ..core.interfaces import IFileDialogService


class FileDialogService(IFileDialogService):
    """Native file dialog service with PyWebView integration."""

    def __init__(self, webview_window=None):
        self.webview_window = webview_window
        self.has_webview = webview_window is not None
        self._system = platform.system().lower()

    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        """Open directory selection dialog."""
        if self.has_webview:
            return self._select_directory_webview(initial_path)
        else:
            return self._select_directory_fallback(initial_path)

    def is_available(self) -> bool:
        """Check if file dialog is available in current environment."""
        return self.has_webview or self._system in ["windows", "darwin", "linux"]

    def validate_path(self, path: str) -> Dict[str, Any]:
        """Validate selected path and return validation result."""
        result = {"valid": False, "exists": False, "is_directory": False, "readable": False, "errors": []}

        if not path:
            result["errors"].append("Path is empty")
            return result

        # Check if path exists
        if not os.path.exists(path):
            result["errors"].append("Path does not exist")
            return result

        result["exists"] = True

        # Check if it's a directory
        if not os.path.isdir(path):
            result["errors"].append("Path is not a directory")
            return result

        result["is_directory"] = True

        # Check if it's readable
        if not os.access(path, os.R_OK):
            result["errors"].append("Directory is not readable")
            return result

        result["readable"] = True

        # Check for log directories
        resource_dir = os.path.join(path, "ont-resource-logger")
        minknow_dir = os.path.join(path, "minknow")

        has_logs = False
        if os.path.isdir(resource_dir):
            has_logs = True
        if os.path.isdir(minknow_dir):
            has_logs = True

        if not has_logs:
            result["errors"].append("No log directories found (expected 'ont-resource-logger' or 'minknow')")

        result["valid"] = len(result["errors"]) == 0
        result["has_logs"] = has_logs

        return result

    def _select_directory_webview(self, initial_path: Optional[str] = None) -> Optional[str]:
        """Select directory using PyWebView native dialog."""
        try:
            import webview

            # Set initial directory
            if initial_path and os.path.isdir(initial_path):
                directory = initial_path
            else:
                directory = os.path.expanduser("~")

            # Create folder dialog
            selected_path = self.webview_window.create_file_dialog(webview.FOLDER_DIALOG, directory=directory)

            if selected_path and len(selected_path) > 0:
                return selected_path[0] if isinstance(selected_path, list) else selected_path

            return None

        except Exception as e:
            print(f"WebView file dialog error: {e}")
            return self._select_directory_fallback(initial_path)

    def _select_directory_fallback(self, initial_path: Optional[str] = None) -> str:
        """Fallback directory selection for web mode."""
        # For web mode, we'll return a default path or prompt user to enter manually
        # This will be handled by the frontend with a text input
        if initial_path and os.path.isdir(initial_path):
            return initial_path

        # Return user's home directory as default
        return os.path.expanduser("~")

    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for debugging."""
        return {
            "system": self._system,
            "has_webview": self.has_webview,
            "webview_window": str(type(self.webview_window)) if self.webview_window else None,
            "home_directory": os.path.expanduser("~"),
            "current_directory": os.getcwd(),
        }

    def suggest_log_directories(self) -> list[str]:
        """Suggest common log directory locations."""
        suggestions = []

        # Common log directory patterns
        common_paths = [
            os.path.expanduser("~/logs"),
            os.path.expanduser("~/Documents/logs"),
            os.path.expanduser("~/Desktop/logs"),
            "/var/log",
            "/opt/logs",
            "C:\\logs",
            "C:\\Users\\<USER>\\logs",
        ]

        for path in common_paths:
            if os.path.isdir(path):
                suggestions.append(path)

        return suggestions


class MockFileDialogService(IFileDialogService):
    """Mock file dialog service for testing."""

    def __init__(self, mock_path: str = "/mock/selected/path"):
        self.mock_path = mock_path

    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        """Return mock directory path."""
        return self.mock_path

    def is_available(self) -> bool:
        """Always available for testing."""
        return True

    def validate_path(self, path: str) -> Dict[str, Any]:
        """Mock validation that always succeeds."""
        return {"valid": True, "exists": True, "is_directory": True, "readable": True, "has_logs": True, "errors": []}
