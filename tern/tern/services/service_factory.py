"""Service factory for creating and managing application services."""

import logging
from typing import Optional

from ..core.interfaces import IFileDialogService, IParsingController, IProgressTracker
from ..core.parsing_controller import ParsingController
from .file_dialog import FileDialogService

logger = logging.getLogger(__name__)


class ServiceFactory:
    """Factory for creating and managing application services."""

    def __init__(self, container, progress_tracker: IProgressTracker, webview_window=None):
        """Initialize the service factory.

        Args:
            container: AnalyzerContainer instance
            progress_tracker: Progress tracker instance
            webview_window: PyWebView window instance (optional)
        """
        self.container = container
        self.progress_tracker = progress_tracker
        self.webview_window = webview_window

        # Service instances
        self._file_dialog_service: Optional[IFileDialogService] = None
        self._parsing_controller: Optional[IParsingController] = None

    def get_file_dialog_service(self) -> IFileDialogService:
        """Get or create the file dialog service.

        Returns:
            File dialog service instance
        """
        if self._file_dialog_service is None:
            self._file_dialog_service = FileDialogService(self.webview_window)
            logger.info("Created file dialog service")

        return self._file_dialog_service

    def get_parsing_controller(self) -> IParsingController:
        """Get or create the parsing controller.

        Returns:
            Parsing controller instance
        """
        if self._parsing_controller is None:
            # The container now implements IProgressTracker directly
            self._parsing_controller = ParsingController(self.container)
            logger.info("Created parsing controller")

        return self._parsing_controller

    def get_progress_tracker(self) -> IProgressTracker:
        """Get the progress tracker.

        Returns:
            Progress tracker instance (from container)
        """
        return self.container  # Container implements IProgressTracker

    def reset_services(self):
        """Reset all services to their initial state."""
        if self._parsing_controller:
            self._parsing_controller.cancel_parsing()

        if self.container:
            self.container.reset_status()

        logger.info("Reset all services")

    def get_service_status(self) -> dict:
        """Get status of all services.

        Returns:
            Dictionary with service status information
        """
        status = {
            "file_dialog_available": False,
            "parsing_in_progress": False,
            "progress_status": "idle",
            "progress_stage": "",
            "progress_percentage": 0,
        }

        # File dialog status
        if self._file_dialog_service:
            status["file_dialog_available"] = self._file_dialog_service.is_available()

        # Parsing status
        if self._parsing_controller:
            status["parsing_in_progress"] = self._parsing_controller.is_parsing()

        # Progress status (from container)
        if self.container:
            progress_status = self.container.get_status()
            status["progress_status"] = progress_status.status
            status["progress_stage"] = progress_status.stage
            status["progress_percentage"] = progress_status.progress
            status["current_file"] = progress_status.current_file
            status["files_processed"] = progress_status.files_processed
            status["total_files"] = progress_status.total_files

        return status

    def validate_log_path(self, path: str) -> dict:
        """Validate a log path using the file dialog service.

        Args:
            path: Path to validate

        Returns:
            Validation result dictionary
        """
        file_dialog = self.get_file_dialog_service()
        return file_dialog.validate_path(path)

    def start_parsing(self, log_folder: str) -> bool:
        """Start parsing using the parsing controller.

        Args:
            log_folder: Path to log folder

        Returns:
            True if parsing started successfully
        """
        controller = self.get_parsing_controller()
        return controller.start_parsing(log_folder)

    def cancel_parsing(self) -> bool:
        """Cancel current parsing operation.

        Returns:
            True if cancellation was successful
        """
        controller = self.get_parsing_controller()
        return controller.cancel_parsing()

    def get_parsing_progress(self) -> dict:
        """Get current parsing progress.

        Returns:
            Progress information dictionary
        """
        if self.container:
            status = self.container.get_status()
            return {
                "status": status.status,
                "stage": status.stage,
                "progress": status.progress,
                "current_file": status.current_file,
                "files_processed": status.files_processed,
                "total_files": status.total_files,
                "start_time": status.start_time,
                "end_time": status.end_time,
                "error": status.error,
            }
        return {}
