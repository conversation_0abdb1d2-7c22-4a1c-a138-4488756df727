"""Mock backend services for testing and development."""

import random
import time
from typing import Any, Dict, Optional

from ..core.interfaces import IFileDialogService, IParsingController, IProgressTracker, ParsingStatus, ProgressUpdate
from ..types.store_types import DEFAULT_PARSING_STATE, DEFAULT_SETUP_STATE


class MockProgressTracker(IProgressTracker):
    """Mock progress tracker for testing."""

    def __init__(self):
        self._status = ParsingStatus(
            status="idle",
            stage="",
            progress=0,
            current_file="",
            files_processed=0,
            total_files=0,
            start_time=None,
            end_time=None,
            error=None,
        )
        self._simulation_active = False
        self._simulation_thread = None

    def update_progress(self, update: ProgressUpdate) -> None:
        """Update parsing progress."""
        self._status.stage = update.stage
        self._status.progress = update.progress
        self._status.current_file = update.current_file
        self._status.files_processed = update.files_processed
        self._status.total_files = update.total_files

    def get_status(self) -> ParsingStatus:
        """Get current parsing status."""
        return self._status

    def reset_status(self) -> None:
        """Reset to idle state."""
        self._status = ParsingStatus(
            status="idle",
            stage="",
            progress=0,
            current_file="",
            files_processed=0,
            total_files=0,
            start_time=None,
            end_time=None,
            error=None,
        )

    def set_error(self, error_msg: str, stage: str = "") -> None:
        """Set error state with details."""
        self._status.status = "error"
        self._status.stage = stage
        self._status.error = {"message": error_msg, "timestamp": time.time()}
        self._status.end_time = time.time()

    def start_simulation(self) -> None:
        """Start a mock parsing simulation."""
        if self._simulation_active:
            return

        self._simulation_active = True
        self._status.status = "parsing"
        self._status.start_time = time.time()
        self._status.end_time = None
        self._status.error = None

        # Start simulation in background
        import threading

        self._simulation_thread = threading.Thread(target=self._run_simulation, daemon=True)
        self._simulation_thread.start()

    def _run_simulation(self) -> None:
        """Run the mock parsing simulation."""
        stages = [
            ("🎣 Casting nets for logs", 5, 2),
            ("🐟 Teaching analyzers to fish", 10, 1),
            ("🐠 Diving for MinKnow logs", 40, 8),
            ("🌊 Cross-referencing events", 35, 6),
            ("📊 Counting the catch", 10, 2),
        ]

        total_files = 20
        files_processed = 0

        for stage, weight, stage_files in stages:
            if not self._simulation_active:
                break

            self._status.stage = stage
            self._status.total_files = stage_files

            for i in range(stage_files):
                if not self._simulation_active:
                    break

                files_processed += 1
                self._status.files_processed = i + 1
                self._status.current_file = f"file_{files_processed:03d}.log"

                # Calculate progress based on stage weights
                stage_progress = ((i + 1) / stage_files) * 100
                cumulative_progress = self._calculate_cumulative_progress(stages, stage, stage_progress)
                self._status.progress = int(cumulative_progress)

                time.sleep(0.5)  # Simulate processing time

        if self._simulation_active:
            self._status.status = "complete"
            self._status.progress = 100
            self._status.end_time = time.time()
            self._status.current_file = ""
            self._status.files_processed = total_files
            self._status.total_files = total_files

        self._simulation_active = False

    def _calculate_cumulative_progress(self, stages, current_stage, stage_progress):
        """Calculate cumulative progress across all stages."""
        total_weight = sum(weight for _, weight, _ in stages)
        cumulative_progress = 0

        for stage, weight, _ in stages:
            if stage == current_stage:
                cumulative_progress += (stage_progress / 100.0) * weight
                break
            else:
                cumulative_progress += weight

        return (cumulative_progress / total_weight) * 100

    def stop_simulation(self) -> None:
        """Stop the mock parsing simulation."""
        self._simulation_active = False
        if self._simulation_thread:
            self._simulation_thread.join(timeout=1.0)


class MockFileDialogService(IFileDialogService):
    """Mock file dialog service for testing."""

    def __init__(self, mock_path: str = "/mock/selected/path"):
        self.mock_path = mock_path
        self._call_count = 0

    def select_directory(self, initial_path: Optional[str] = None) -> Optional[str]:
        """Return mock directory path."""
        self._call_count += 1
        return self.mock_path

    def is_available(self) -> bool:
        """Always available for testing."""
        return True

    def validate_path(self, path: str) -> Dict[str, Any]:
        """Mock validation that always succeeds."""
        return {"valid": True, "exists": True, "is_directory": True, "readable": True, "has_logs": True, "errors": []}

    def get_call_count(self) -> int:
        """Get number of times select_directory was called."""
        return self._call_count


class MockParsingController(IParsingController):
    """Mock parsing controller for testing."""

    def __init__(self):
        self._is_parsing = False
        self._mock_success = True
        self._progress_tracker = MockProgressTracker()

    def start_parsing(self, log_folder: str) -> bool:
        """Mock parsing start."""
        if not self._mock_success:
            return False

        self._is_parsing = True
        self._progress_tracker.start_simulation()
        return True

    def cancel_parsing(self) -> bool:
        """Mock parsing cancellation."""
        if self._is_parsing:
            self._is_parsing = False
            self._progress_tracker.stop_simulation()
            return True
        return False

    def is_parsing(self) -> bool:
        """Mock parsing status."""
        return self._is_parsing

    def set_mock_success(self, success: bool) -> None:
        """Set mock success status for testing."""
        self._mock_success = success

    def get_progress_tracker(self) -> MockProgressTracker:
        """Get the progress tracker for testing."""
        return self._progress_tracker


class MockServiceFactory:
    """Mock service factory for testing."""

    def __init__(self):
        self.file_dialog = MockFileDialogService()
        self.parsing_controller = MockParsingController()
        self.progress_tracker = MockProgressTracker()

    def get_file_dialog_service(self):
        """Get mock file dialog service."""
        return self.file_dialog

    def get_parsing_controller(self):
        """Get mock parsing controller."""
        return self.parsing_controller

    def get_progress_tracker(self):
        """Get mock progress tracker."""
        return self.progress_tracker


# Utility functions for testing
def create_mock_parsing_state(status: str = "idle", **kwargs) -> Dict[str, Any]:
    """Create a mock parsing state for testing."""
    state = DEFAULT_PARSING_STATE.copy()
    state.update(kwargs)
    state["status"] = status
    return state


def create_mock_setup_state(**kwargs) -> Dict[str, Any]:
    """Create a mock setup state for testing."""
    state = DEFAULT_SETUP_STATE.copy()
    state.update(kwargs)
    return state


def simulate_parsing_progress(callback, duration: float = 10.0):
    """Simulate parsing progress for testing UI components."""
    import threading
    import time

    def progress_simulation():
        stages = [
            ("🎣 Casting nets for logs", 5),
            ("🐟 Teaching analyzers to fish", 10),
            ("🐠 Diving for MinKnow logs", 40),
            ("🌊 Cross-referencing events", 35),
            ("📊 Counting the catch", 10),
        ]

        start_time = time.time()
        stage_duration = duration / len(stages)

        for stage, target_progress in stages:
            stage_start = time.time()

            while time.time() - stage_start < stage_duration:
                elapsed = time.time() - start_time
                progress = min(target_progress, int((time.time() - stage_start) / stage_duration * target_progress))

                callback(
                    {
                        "status": "parsing",
                        "stage": stage,
                        "progress": progress,
                        "current_file": f"file_{int(time.time() * 1000) % 1000:03d}.log",
                        "files_processed": int(progress / 10),
                        "total_files": 20,
                        "elapsed_time": elapsed,
                    }
                )

                time.sleep(0.1)

        # Complete
        callback(
            {
                "status": "complete",
                "stage": "✅ All done!",
                "progress": 100,
                "current_file": "",
                "files_processed": 20,
                "total_files": 20,
                "elapsed_time": duration,
            }
        )

    thread = threading.Thread(target=progress_simulation, daemon=True)
    thread.start()
    return thread
