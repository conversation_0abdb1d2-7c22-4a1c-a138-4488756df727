"""Main layout components for the Dash UI."""

from __future__ import annotations

from datetime import datetime, timedelta

import dash_mantine_components as dmc
from dash import dcc, html
from dash_iconify import DashIconify

# Import chart factories for modular components
from ..components.charts import chart_card_pattern_matching
from ..components.charts.factories import log_scatter_chart


def create_main_layout():
    """Create the main layout structure for the app."""
    return dmc.MantineProvider(
        id="theme-provider",
        theme={"colorScheme": "light"},
        children=[
            dcc.Store(id="color-scheme", data="light"),
            dcc.Store(id="parsing-status", data={"status": "idle"}),
            dcc.Store(id="expansion-trigger", data={}),
            dcc.Store(id="table-navigation-state", data={"page": 0, "selected_row_id": None}),
            dcc.Interval(id="status-interval", interval=1000, n_intervals=0),
            dmc.AppShell(
                id="appshell",
                padding="md",
                header={"height": 45},
                navbar={
                    "width": 350,
                    "breakpoint": "sm",
                    "collapsed": {"mobile": True, "desktop": False},
                },
                children=[
                    create_header(),
                    create_navbar(),
                    create_main_content(),
                ],
            ),
            create_error_dialog(),
        ],
    )


def create_header():
    """Create the app header with theme toggle and sidebar controls."""
    return dmc.AppShellHeader(
        p="xs",
        children=dmc.Group(
            justify="space-between",
            align="center",
            children=[
                dmc.Group(
                    children=[
                        dmc.Burger(
                            id="mobile-burger",
                            size="sm",
                            hiddenFrom="sm",
                            opened=False,
                        ),
                        dmc.Burger(
                            id="desktop-burger",
                            size="sm",
                            visibleFrom="sm",
                            opened=False,
                        ),
                        dmc.Group(
                            gap="sm",
                            align="center",
                            children=[
                                dmc.Title("Tern", order=3),
                                dmc.Image(
                                    src="/assets/tern_transparent.png",
                                    alt="Tern Logo",
                                    h=30,
                                    w="auto",
                                ),
                            ],
                        ),
                    ],
                ),
                dmc.Group(
                    children=[
                        dmc.ActionIcon(
                            id="theme-toggle",
                            variant="subtle",
                            size="lg",
                        ),
                    ],
                ),
            ],
        ),
    )


def create_navbar():
    """Create the app navbar with setup section and filters."""
    return dmc.AppShellNavbar(
        p="md",
        children=[
            dmc.Stack(
                gap="md",
                children=[
                    create_setup_section(),
                    create_progress_section(),
                    create_filters_section_wrapper(),
                ],
            ),
        ],
    )


def create_filters_section():
    """Create the filters section with position and date filters."""
    return dmc.Paper(
        withBorder=True,
        p="md",
        style={"position": "relative"},
        children=[
            dmc.LoadingOverlay(
                id="filters-loading",
                visible=True,
                loaderProps={"variant": "dots", "size": "lg"},
                overlayProps={"opacity": 0.6, "blur": 2},
                style={"position": "absolute", "top": 0, "left": 0, "right": 0, "bottom": 0, "zIndex": 1000},
            ),
            dmc.Stack(
                gap="md",
                children=[
                    dmc.MultiSelect(
                        id="position-filter",
                        label="Position",
                        placeholder="Select positions",
                        data=[],
                        clearable=True,
                        searchable=True,
                        style={"width": "100%"},
                    ),
                    dmc.DateTimePicker(
                        id="start-date-picker",
                        label="Start Date",
                        value=datetime.now() - timedelta(days=7),
                        maxDate=datetime.now(),
                        clearable=True,
                    ),
                    dmc.DateTimePicker(
                        id="end-date-picker",
                        label="End Date",
                        value=datetime.now(),
                        maxDate=datetime.now(),
                        clearable=True,
                    ),
                ],
            ),
        ],
    )


def create_time_range_selector():
    """Create the time range selector components (slider and controls)."""
    return dmc.Stack(
        gap="sm",
        children=[
            # RangeSlider aligned with graph area
            dmc.Box(
                style={
                    "marginLeft": "113px",  # Align with graph's actual left margin
                    "marginRight": "10px",  # Align with graph right margin
                },
                children=[
                    dmc.RangeSlider(
                        id="time-range-slider",
                        min=0,
                        max=604800,  # 1 week in seconds
                        step=3600,  # 1 hour steps
                        value=[0, 604800],  # Default to full week
                        marks=[],  # No marks
                        label=None,  # No labels
                        size="md",
                    ),
                ],
            ),
            # Time range display
            dmc.Group(
                justify="space-between",
                children=[
                    dmc.Text(
                        id="range-start-text",
                        children="Start: ",
                        size="sm",
                        c="dimmed",
                    ),
                    dmc.Checkbox(
                        id="lock-window-size",
                        label="Lock window size",
                        checked=False,
                        size="sm",
                    ),
                    dmc.Text(
                        id="range-end-text",
                        children="End: ",
                        size="sm",
                        c="dimmed",
                    ),
                ],
            ),
        ],
    )


def create_main_content():
    """Create the main content area with graphs and tables."""
    return dmc.AppShellMain(
        children=dmc.Container(
            fluid=True,
            p="md",
            children=dmc.Stack(
                gap="md",
                children=[
                    # Welcome message (shown when no logs loaded)
                    dmc.Box(
                        id="welcome-message",
                        children=dmc.Center(
                            style={"minHeight": "60vh"},
                            children=dmc.Stack(
                                align="center",
                                gap="lg",
                                children=[
                                    dmc.Title("Welcome to Tern!", order=2, ta="center"),
                                    dmc.Text(
                                        '👈 Please configure your log folder and click "Load Logs" to begin',
                                        size="lg",
                                        ta="center",
                                        c="dimmed",
                                    ),
                                    dmc.Image(
                                        src="/assets/tern_transparent.png",
                                        alt="Tern Logo",
                                        h=100,
                                        w="auto",
                                    ),
                                ],
                            ),
                        ),
                    ),
                    # Charts and data (hidden initially)
                    dmc.Box(
                        id="charts-content",
                        style={"display": "none"},
                        children=[
                            dmc.Stack(
                                gap="md",
                                children=[
                                    dmc.Stack(
                                        gap="md",
                                        children=[
                                            log_scatter_chart(
                                                card_id="log-scatter", time_input_ids=["start-date-picker", "end-date-picker"]
                                            ),
                                            create_time_range_selector(),
                                        ],
                                    ),
                                    chart_card_pattern_matching(card_id="details", chart_type="memory_details"),
                                    chart_card_pattern_matching(
                                        card_id="cpu-load",
                                        chart_type="cpu_load",
                                        title="CPU Load Average",
                                        description="Shows 1, 5, and 15-minute load averages",
                                    ),
                                    chart_card_pattern_matching(card_id="disk-capacity", chart_type="disk_capacity"),
                                    chart_card_pattern_matching(card_id="io-throughput", chart_type="io_throughput"),
                                    chart_card_pattern_matching(card_id="gpu-metrics", chart_type="gpu_metrics"),
                                    create_log_table_section(),
                                ],
                            ),
                        ],
                    ),
                ],
            ),
        ),
    )


def create_log_table_section():
    """Create the log table section."""
    from dash import dash_table

    return dmc.Stack(
        gap="md",
        children=[
            dmc.Text("Log Entries", fw=500, size="lg"),
            dmc.Paper(
                className="table-container",
                style={"minHeight": "400px", "position": "relative"},
                children=[
                    dmc.LoadingOverlay(
                        id="log-table-loading",
                        visible=True,
                        loaderProps={"variant": "dots", "size": "lg"},
                        overlayProps={"opacity": 0.6, "blur": 2},
                        style={"position": "absolute", "top": 0, "left": 0, "right": 0, "bottom": 0, "zIndex": 100},
                    ),
                    html.Div(id="log-table-container", children=[], style={"width": "100%"}),
                ],
            ),
        ],
    )


def create_setup_section():
    """Create the setup section with log path configuration."""
    return dmc.Paper(
        withBorder=True,
        p="md",
        children=[
            dmc.Stack(
                gap="md",
                children=[
                    dmc.Group(
                        justify="space-between",
                        align="center",
                        children=[
                            dmc.Text("🔧 Setup", fw=500, size="lg"),
                        ],
                    ),
                    dmc.TextInput(
                        id="log-path-input",
                        label="Log Folder Path",
                        placeholder="Select or enter log folder path",
                        style={"width": "100%"},
                        value="",
                    ),
                    dmc.Group(
                        gap="sm",
                        children=[
                            dmc.Button(
                                "Browse",
                                id="browse-button",
                                variant="outline",
                                leftSection=DashIconify(icon="mdi:folder-open"),
                                size="sm",
                            ),
                            dmc.Button(
                                "Load Logs",
                                id="load-logs-button",
                                leftSection=DashIconify(icon="mdi:rocket-launch"),
                                size="sm",
                                disabled=True,
                            ),
                        ],
                    ),
                ],
            ),
        ],
    )


def create_progress_section():
    """Create the progress section (hidden initially)."""
    return dmc.Paper(
        id="progress-section",
        withBorder=True,
        p="md",
        style={"display": "none"},
        children=[
            dmc.Stack(
                gap="md",
                children=[
                    dmc.Text(
                        id="progress-stage-text",
                        children="🎣 Casting nets for logs... hoping for a good catch!",
                        size="sm",
                        fw=500,
                    ),
                    dmc.Progress(
                        id="progress-bar",
                        value=0,
                        size="lg",
                        striped=True,
                        animated=True,
                        color="blue",
                    ),
                    dmc.Group(
                        justify="space-between",
                        children=[
                            dmc.Text(
                                id="progress-elapsed-text",
                                children="⏱️ Elapsed: 00:00:00",
                                size="xs",
                                c="dimmed",
                            ),
                            dmc.Text(
                                id="progress-files-text",
                                children="📊 Files: 0/0",
                                size="xs",
                                c="dimmed",
                            ),
                        ],
                    ),
                    dmc.Text(
                        id="progress-current-file-text",
                        children="",
                        size="xs",
                        c="dimmed",
                        truncate=True,
                    ),
                ],
            ),
        ],
    )


def create_error_dialog():
    """Create error dialog modal for displaying parsing errors."""
    return dmc.Modal(
        id="error-modal",
        title=dmc.Group(
            [
                DashIconify(icon="mdi:alert-circle", color="red", width=24),
                dmc.Text("Parsing Error", fw=500, size="lg"),
            ],
            gap="sm",
        ),
        opened=False,
        centered=True,
        size="lg",
        children=[
            dmc.Stack(
                [
                    dmc.Text(
                        id="error-message-text",
                        children="An error occurred during parsing.",
                        size="sm",
                    ),
                    dmc.Text(
                        id="error-stage-text",
                        children="",
                        size="xs",
                        c="dimmed",
                        style={"display": "none"},
                    ),
                    dmc.Group(
                        [
                            dmc.Button(
                                "Retry",
                                id="error-retry-btn",
                                variant="outline",
                                color="blue",
                                leftSection=DashIconify(icon="mdi:refresh"),
                            ),
                            dmc.Button(
                                "Change Path",
                                id="error-change-path-btn",
                                variant="outline",
                                color="gray",
                                leftSection=DashIconify(icon="mdi:folder-open"),
                            ),
                            dmc.Button(
                                "Cancel",
                                id="error-cancel-btn",
                                variant="filled",
                                color="red",
                                leftSection=DashIconify(icon="mdi:close"),
                            ),
                        ],
                        justify="end",
                        mt="md",
                    ),
                ],
                gap="md",
            ),
        ],
    )


def create_filters_section_wrapper():
    """Create the filters section wrapper with conditional visibility."""
    return dmc.Box(
        id="filters-section-wrapper",
        children=[
            dmc.Text("📊 Global Filters", fw=500, size="lg"),
            create_filters_section(),
        ],
    )
