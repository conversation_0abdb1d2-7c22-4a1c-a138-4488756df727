"""CLI entry point module that handles argument parsing without heavy imports."""

from __future__ import annotations

import argparse
import sys


def create_parser() -> argparse.ArgumentParser:
    """Create and return the argument parser."""
    parser = argparse.ArgumentParser(
        description="Tern - A precision log analysis tool that hovers over data chaos to find exactly what you need",
        epilog="""
Examples:
  tern                    # Launch with native window
  tern --no-webview       # Launch in browser mode
  tern --debug            # Enable debug mode with hot-reload
  tern --port 8080        # Use custom port
  tern --profile          # Enable performance profiling

Environment Variables:
  BASE_LOG_FOLDER         # Pre-fill log folder path in the UI
  LOG_LEVEL              # Set logging level (DEBUG, INFO, WARNING, ERROR)

⚠️  IMPORTANT: Required Directory Structure
Your log folder must contain these subdirectories for parsing to work:
  your-log-folder/
  ├── minknow/
  └── ont-resource-logger/

Use the Browse button in the app to select the parent folder containing these subdirectories.

For more information: https://github.com/unknown/tern
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    parser.add_argument("--port", type=int, default=8050, help="Port to run the Dash server on (default: 8050)")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode with hot-reload")
    parser.add_argument("--profile", action="store_true", help="Enable performance profiling logs")
    parser.add_argument(
        "--profile-parsing",
        action="store_true",
        help="Enable log parser performance profiling logs",
    )
    parser.add_argument(
        "--no-webview",
        action="store_true",
        help="Launch in browser instead of native window (useful for development)",
    )
    parser.add_argument("--width", type=int, default=1200, help="Native window width (default: 1200)")
    parser.add_argument("--height", type=int, default=800, help="Native window height (default: 800)")
    parser.add_argument("--version", action="version", version="tern 0.1.0")
    return parser


def main() -> None:
    """Fast CLI entry point that handles help/version immediately."""
    parser = create_parser()

    # Handle help/version immediately without importing heavy modules
    if len(sys.argv) == 1 or any(arg in sys.argv for arg in ["-h", "--help", "--version"]):
        parser.parse_args()
        return

    # Parse arguments first
    args = parser.parse_args()

    # Only import and run the full app when needed (after arg parsing)
    # ruff: noqa: PLC0415 - Intentional delayed import for performance
    from .main import run_app

    run_app(args)
