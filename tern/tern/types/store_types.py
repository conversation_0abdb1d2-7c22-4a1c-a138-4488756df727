"""Store type definitions for the Tern application."""

from typing import Literal, Optional, TypedDict


class ParsingState(TypedDict):
    """Parsing state data structure."""

    status: Literal["idle", "parsing", "complete", "error"]
    stage: str
    progress: int  # 0-100
    current_file: str
    files_processed: int
    total_files: int
    start_time: Optional[str]
    end_time: Optional[str]
    error: Optional[dict]


class SetupState(TypedDict):
    """Setup state data structure."""

    log_path: str
    is_valid: bool
    environment_path: str
    dialog_available: bool


class UIState(TypedDict):
    """UI state data structure."""

    navbar_open: bool
    filters_visible: bool
    setup_minimized: bool


class AppStore(TypedDict):
    """Complete application store structure."""

    parsing: ParsingState
    setup: SetupState
    ui: UIState


# Default state values for initialization
DEFAULT_PARSING_STATE: ParsingState = {
    "status": "idle",
    "stage": "",
    "progress": 0,
    "current_file": "",
    "files_processed": 0,
    "total_files": 0,
    "start_time": None,
    "end_time": None,
    "error": None,
}

DEFAULT_SETUP_STATE: SetupState = {"log_path": "", "is_valid": False, "environment_path": "", "dialog_available": True}

DEFAULT_UI_STATE: UIState = {
    "navbar_open": True,  # Open by default for setup
    "filters_visible": False,  # Hidden until parsing complete
    "setup_minimized": False,
}

DEFAULT_APP_STORE: AppStore = {"parsing": DEFAULT_PARSING_STATE, "setup": DEFAULT_SETUP_STATE, "ui": DEFAULT_UI_STATE}
