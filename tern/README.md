# Tern — Dash‑based Log Visualizer

Tern is the **primary GUI** in the monorepo. It wraps the `log_parser` engine in an interactive Dash + Plotly app and can run either:

1. **Native window** (embedded via PyWebView) – the default end‑user experience
2. **Browser mode** (`--no-webview`) – handy for development / headless servers

> **Why "Tern"?** Like the agile seabird, it hovers over oceans of noisy logs and dives only where the insight is. 🐦

---

## 🚀 Quick Start (End Users)

```bash
# Install pre‑built wheel (contains everything, even log_parser)
pip install dist/tern-0.1.0-py3-none-any.whl

# Launch the app in a native window
tern
```

**🎉 New User Experience:**
1. App opens with welcome screen and setup section visible
2. Click **Browse** to select your log folder using native file dialog
3. Click **Load Logs** to start parsing with rich progress feedback
4. Watch detailed progress updates and real-time file tracking
5. Explore your data when parsing completes!

---

## 🧑‍💻 Quick Start (Developers)

```bash
# Assume you are inside the monorepo root with an active venv
pip install -e ".[full]" -e ./log_parser

# Optional: Set default log folder path for convenience
export BASE_LOG_FOLDER=/path/to/your/log/files

# Launch Tern with hot‑reload in browser mode
hatch run dev --debug --no-webview
```

* **Python ≥ 3.10**
* Dash server auto‑reloads on code changes when `--debug` is present.
* Browser mode avoids PyWebView's threading constraints, making debugging easier.

---

## 🖥 Running Modes

| Mode               | Command                              | Hot Reload | PyWebView | Typical Use         |
| ------------------ | ------------------------------------ | ---------- | --------- | ------------------- |
| **Dev (browser)**  | `hatch run dev --debug --no-webview` | ✅          | ❌         | Active development  |
| **Test (browser)** | `hatch run dev --no-webview`         | ❌          | ❌         | Pre‑release testing |
| **Prod (native)**  | `hatch run dev` or `tern`            | ❌          | ✅         | End‑user experience |

### Common Flags

| Flag              | Default | Description                               |
| ----------------- | ------- | ----------------------------------------- |
| `--port`          | `8050`  | Dash server port                          |
| `--width`         | `1200`  | Window width (PyWebView only)             |
| `--height`        | `800`   | Window height (PyWebView only)            |
| `--debug`         | off     | Enable Dash hot‑reload & verbose logs     |
| `--profile`       | off     | Enable performance profiling logs        |
| `--profile-parsing` | off   | Enable log parser performance logs       |
| `--no-webview`    | off     | Disable native window; open in browser   |

---

## ✨ Features

### 🎣 User Flow Redesign (NEW)
* **Manual Log Loading**: Browse and select log folders with native file dialogs
* **Rich Progress Feedback**: Detailed progress tracking with real-time file counting
* **Error Recovery**: Modal dialogs with retry, change path, and cancel actions
* **Cumulative File Tracking**: Accurate progress across all parsing stages (90% accuracy)
* **Enhanced UI Layout**: Improved navbar width and synchronized loading overlays

### 📊 Analysis & Visualization
* 📊 Live time‑series of CPU, memory & custom metrics
* 🕵️‍♀️ Event overlay with log‑level color coding
* 🌀 Drag‑to‑zoom and reset viewport
* 🔍 Position & timeframe filters
* ⚡ Anomaly detection overlays (z‑score, IQR, control‑chart)

---

## 🛠 Development Workflow Cheatsheet

```bash
# Install dev extras (if not done already)
pip install -e ".[dev]"

# Lint & format
hatch run lint && hatch run fmt

# Run Tern unit tests
hatch run test-tern

# Debug with performance profiling (shows detailed timing logs)
hatch run dev --debug --profile --no-webview

# Debug with log parser performance profiling (shows log parsing timing)
hatch run dev --debug --profile-parsing --no-webview

# Debug with both performance profiling modes
hatch run dev --debug --profile --profile-parsing --no-webview
```

### 🔍 Performance Profiling

Use the `--profile` flag to enable detailed performance logging for debugging and optimization:

```bash
# Enable performance logs with timestamps
tern --profile

# Enable log parser performance logs
tern --profile-parsing

# Enable both types of performance logs
tern --profile --profile-parsing

# Example output when --profile is enabled:
# 2025-06-17 21:14:21,814 - app.performance - DEBUG - PERF_START: LogVisualizerApp.__init__ (call #1)
# 2025-06-17 21:14:21,815 - app.performance - DEBUG - CALLBACK_PERF_START: fetch_all_resource_data (call #1)
# 2025-06-17 21:14:21,816 - app.performance - DEBUG - DATA_PERF_START: get_memory_data (call #1)
# 2025-06-17 21:14:21,820 - app.performance - DEBUG - DATA_PERF_END: get_memory_data completed in 0.004s, processed 1250 records

# Example output when --profile-parsing is enabled:
# 2025-06-17 22:22:13,269 - log_parser.performance - DEBUG - PARSER_PERF_START: parse_directory (call #1) - Memory: 45.2MB
# 2025-06-17 22:22:13,285 - log_parser.performance - DEBUG - PARSER_PERF_END: parse_directory completed in 0.016s - Memory: 47.1MB (Δ+1.9MB)
# 2025-06-17 22:22:13,286 - log_parser.performance - DEBUG - PARSER_PERF_START: get_log_summary (call #1) - Memory: 47.1MB
# 2025-06-17 22:22:13,290 - log_parser.performance - DEBUG - PARSER_PERF_END: get_log_summary completed in 0.004s - Memory: 47.3MB (Δ+0.2MB)
```

**App Performance log types tracked by `--profile`:**
- `PERF_*` - General application performance (app startup, layout, callbacks)
- `CALLBACK_PERF_*` - Dash callback execution timing
- `DATA_PERF_*` - Data transformation and processing timing
- `LOG_PARSER_PERF` - Log parser engine performance summaries

**Log Parser Performance log types tracked by `--profile-parsing`:**
- `PARSER_PERF_START` - Log parser function start with memory tracking
- `PARSER_PERF_END` - Log parser function completion with timing/memory
- `PARSER_PERF_BATCH` - Batch reporting for frequently called functions
- `PARSER_PERF_MEMORY` - Significant memory change warnings (>10MB)
- `PARSER_PERF_ERROR` - Log parser function failures with timing
- `PARSER_PERF_RESET` - Performance statistics reset events
- `PARSER_PERF_CONFIG` - Performance logging configuration changes

> **Note:** Both performance logging modes are completely independent and hidden by default. They don't interfere with other DEBUG logs or each other.

---

## 🐞 Troubleshooting

| Symptom                        | Fix                                                                                         |
| ------------------------------ | ------------------------------------------------------------------------------------------- |
| Blank window / 404             | Try browser mode: `--no-webview`. Confirm server prints `Running on http://127.0.0.1:8050`. |
| Hot reload not triggering      | Ensure `--debug` is present **and** you saved files inside `tern/tern` package.             |
| Import errors for `log_parser` | Verify `pip install -e ./log_parser` or use the wheel which vendors it.                     |

---

## 🌍 Environment Variables

| Variable          | Required | Default | Description                                          |
| ----------------- | -------- | ------- | ---------------------------------------------------- |
| `BASE_LOG_FOLDER` | No       | None    | Default path pre-filled in the Browse dialog        |
| `LOG_LEVEL`       | No       | `INFO`  | Logging verbosity (`DEBUG`, `INFO`, `WARNING`, `ERROR`) |

> **🎉 New Workflow:** `BASE_LOG_FOLDER` is now optional and only pre-fills the path input. Use the **Browse** button in the app to select your log folder, then click **Load Logs** to start parsing.

*Example usage:*

```bash
# Launch app and use Browse button (recommended)
tern

# Or pre-fill log path for convenience
export BASE_LOG_FOLDER=/path/to/your/logs
tern

# Development with pre-filled path
BASE_LOG_FOLDER=/path/to/your/logs tern --debug --no-webview
```

---

## 📜 License

MIT. See root `LICENSE`.

---

*Built with Dash 2, Plotly 5, PyWebView 5, and lots of ☕️*
