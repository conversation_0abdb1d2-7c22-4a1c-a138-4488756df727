# Development Guide

This guide covers the development setup and best practices for contributing to the log visualizer project.

## Project Structure

This is a monorepo containing multiple Python components:

- **`tern/`** - Main visualization application using Dash
- **`log_parser/`** - Core log parsing and analysis library
- **`backend/`** - FastAPI backend service (optional)
- **`console/`** - CLI tools for log analysis

## Development Setup

### Prerequisites

- Python 3.10 or higher
- pip or similar package manager

### Environment Setup

1. **Clone and enter the repository:**
   ```bash
   git clone <repository-url>
   cd log_visualiser
   ```

2. **Create and activate virtual environment:**
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install development dependencies:**
   ```bash
   # Install main package with dev dependencies
   pip install -e ".[dev]"

   # Install log_parser package in development mode
   pip install -e "./log_parser[dev]"
   ```

4. **Install pre-commit hooks:**
   ```bash
   pre-commit install
   ```

## Development Workflow

### Code Quality Standards

This project follows strict Python best practices:

- **Python 3.10+** minimum version
- **PEP 8** compliance via Black formatting
- **Type hints** required for all functions
- **Google-style docstrings** for all public APIs
- **Security scanning** with Bandit
- **Comprehensive linting** with Ruff (run manually)
- **140 character line length** maximum

### Available Commands

#### Formatting and Linting

```bash
# Format code with Black
black tern/ log_parser/ --line-length=140

# Lint with Ruff (auto-fix where possible)
ruff check --fix tern/ log_parser/

# Format with Ruff
ruff format tern/ log_parser/

# Type checking with MyPy
mypy tern/ log_parser/

# Security scanning with Bandit
bandit -c pyproject.toml -r tern/ log_parser/

# Check docstrings with pydocstyle
pydocstyle tern/ log_parser/ --convention=google
```

#### Testing

```bash
# Run all tests with coverage
pytest

# Run tests for specific component
pytest tern/tests/
pytest log_parser/tests/

# Run with coverage report
pytest --cov=tern --cov=log_parser --cov-report=html
```

#### Using Hatch (Recommended)

This project is configured with Hatch for streamlined development:

```bash
# Run tests
hatch run test

# Run tests with coverage
hatch run test-cov

# Lint code
hatch run lint

# Format code
hatch run fmt

# Run the application in development mode
hatch run dev
```

### Pre-commit Hooks

Pre-commit hooks automatically run on every commit to ensure code quality:

- **Trailing whitespace removal**
- **End-of-file fixer**
- **YAML/TOML validation**
- **Black formatting** (140 character line length)
- **Import sorting (isort)**
- **Bandit security scanning**
- **Docstring validation**

**Note:** Ruff linting and MyPy type checking are available but disabled in pre-commit hooks to allow faster commits. Run them manually when needed:

```bash
# Run Ruff linting manually
ruff check --fix .

# Run MyPy type checking manually
mypy tern/ log_parser/
```

To run pre-commit hooks manually:
```bash
pre-commit run --all-files
```

### Code Style Guidelines

#### Import Organization
```python
# Standard library imports
from __future__ import annotations

import os
import sys
from datetime import datetime
from typing import Any

# Third-party imports
import pandas as pd
import plotly.express as px
from pydantic import BaseModel

# Local imports
from .config import settings
from .core.analyzer import LogAnalyzer
```

#### Function Signatures
```python
def analyze_logs(
    file_path: str | Path,
    *,
    start_time: datetime | None = None,
    end_time: datetime | None = None,
    filters: dict[str, Any] | None = None,
) -> LogAnalysisResult:
    """Analyze log file with optional time range and filters.

    Args:
        file_path: Path to the log file to analyze.
        start_time: Optional start time for filtering logs.
        end_time: Optional end time for filtering logs.
        filters: Optional additional filters to apply.

    Returns:
        LogAnalysisResult containing analysis results.

    Raises:
        FileNotFoundError: If the log file doesn't exist.
        ValueError: If time range is invalid.
    """
```

#### Class Definitions
```python
class LogAnalyzer:
    """Advanced log file analyzer with statistical and anomaly detection.

    This analyzer provides comprehensive log analysis including pattern
    recognition, anomaly detection, and statistical summaries.

    Attributes:
        config: Configuration settings for the analyzer.
        cache: Optional cache for storing intermediate results.
    """

    def __init__(
        self,
        config: AnalyzerConfig,
        *,
        cache: Cache | None = None,
    ) -> None:
        """Initialize the log analyzer.

        Args:
            config: Analyzer configuration settings.
            cache: Optional cache for performance optimization.
        """
        self.config = config
        self.cache = cache
```

### Error Handling

Use specific exception types and provide meaningful error messages:

```python
# Good
try:
    result = parse_log_file(file_path)
except FileNotFoundError:
    logger.error("Log file not found: %s", file_path)
    raise
except PermissionError:
    logger.error("Permission denied reading file: %s", file_path)
    raise
except LogParseError as e:
    logger.error("Failed to parse log file %s: %s", file_path, e)
    raise

# Avoid broad exception catching
try:
    risky_operation()
except Exception:  # Too broad
    pass
```

### Logging

Use structured logging with appropriate levels:

```python
import logging

logger = logging.getLogger(__name__)

def process_logs() -> None:
    """Process log files with proper logging."""
    logger.info("Starting log processing")

    try:
        results = expensive_operation()
        logger.debug("Operation completed with %d results", len(results))
    except ProcessingError as e:
        logger.error("Processing failed: %s", e, exc_info=True)
        raise

    logger.info("Log processing completed successfully")
```

## Testing Guidelines

### Test Structure

```python
import pytest
from unittest.mock import Mock, patch

from log_parser.analyzers import LogAnalyzer
from log_parser.exceptions import LogParseError


class TestLogAnalyzer:
    """Test suite for LogAnalyzer class."""

    def test_analyze_valid_file(self, sample_log_file: Path) -> None:
        """Test successful analysis of valid log file."""
        analyzer = LogAnalyzer()

        result = analyzer.analyze(sample_log_file)

        assert result.total_entries > 0
        assert result.error_count >= 0
        assert result.analysis_duration > 0

    def test_analyze_missing_file(self) -> None:
        """Test analyzer behavior with missing file."""
        analyzer = LogAnalyzer()

        with pytest.raises(FileNotFoundError):
            analyzer.analyze(Path("nonexistent.log"))

    @patch("log_parser.analyzers.expensive_operation")
    def test_analyze_with_mock(self, mock_operation: Mock) -> None:
        """Test analyzer with mocked dependencies."""
        mock_operation.return_value = {"status": "success"}
        analyzer = LogAnalyzer()

        result = analyzer.analyze(Path("test.log"))

        mock_operation.assert_called_once()
        assert result.status == "success"
```

### Test Fixtures

```python
@pytest.fixture
def sample_log_file(tmp_path: Path) -> Path:
    """Create a sample log file for testing."""
    log_content = [
        "2024-01-01 10:00:00 INFO Application started",
        "2024-01-01 10:01:00 ERROR Connection failed",
        "2024-01-01 10:02:00 INFO Connection restored",
    ]

    log_file = tmp_path / "sample.log"
    log_file.write_text("\n".join(log_content))
    return log_file
```

## Performance Considerations

### Memory Management

- Use generators for large file processing
- Implement proper caching strategies
- Monitor memory usage in tests
- Use `pathlib.Path` instead of string paths

### Profiling

Enable performance profiling during development:

```bash
# Run with performance profiling
python -m tern.main --profile --profile-parsing
```

## Contributing

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes following the style guidelines**

3. **Run the test suite:**
   ```bash
   hatch run test-all
   ```

4. **Run linting and formatting:**
   ```bash
   hatch run lint
   hatch run fmt
   ```

5. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

   Pre-commit hooks will automatically run and validate your changes.

6. **Push and create a pull request:**
   ```bash
   git push origin feature/your-feature-name
   ```

## IDE Configuration

### VS Code

Recommended `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=140"],
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.mypyEnabled": true,
    "python.linting.banditEnabled": true,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

### PyCharm

1. Configure Python interpreter to use `.venv/bin/python`
2. Enable Black as the formatter with line length 140
3. Configure Ruff as external tool for linting
4. Enable MyPy for type checking

## Troubleshooting

### Common Issues

1. **Pre-commit hooks failing:**
   ```bash
   # Update hooks to latest versions
   pre-commit autoupdate

   # Clear cache and reinstall
   pre-commit clean
   pre-commit install
   ```

2. **Import errors in development:**
   ```bash
   # Reinstall packages in editable mode
   pip install -e ".[dev]"
   pip install -e "./log_parser[dev]"
   ```

3. **MyPy type checking errors:**
   ```bash
   # Clear MyPy cache
   mypy --clear-cache
   ```

4. **Test failures:**
   ```bash
   # Run tests with verbose output
   pytest -v --tb=long

   # Run specific failing test
   pytest -xvs path/to/test_file.py::test_function
   ```

For additional help, check the project's issue tracker or reach out to the maintainers.
