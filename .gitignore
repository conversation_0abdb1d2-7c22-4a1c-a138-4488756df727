# General
.DS_Store
Thumbs.db

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.swp
*.swo
*.log
*.sqlite3
*.db
instance/
.env
.venv/
venv/
pip-log.txt
pip-delete-this-directory.txt
migrations/
*.egg-info/
dist/
build/
*.bak

# Virtual environments
env/
venv/
.venv/

# Jupyter Notebook
.ipynb_checkpoints/

# pytest cache
.pytest_cache/

# coverage reports
.coverage
htmlcov/
*.cover
.coverage.*
.cache
nosetests.xml
coverage.xml
*.mo
*.pot

# React/Node
node_modules/
npm-debug.log
yarn-error.log
.yarn/
.yarnrc.yml
.pnp*
.pnp.js
.pnpm-debug.log
.vscode/
.idea/

# Next.js (if applicable)
.next/
out/

# Build artifacts
build/
dist/
coverage/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor/IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS-generated files
*.swp
*.swo
*.bak
*.tmp
*.~lock

# Intermediate test files
intermediate_data/
*/intermediate_data
