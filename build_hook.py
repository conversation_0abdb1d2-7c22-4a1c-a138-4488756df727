"""Build hook to vendor log_parser into the tern package temporarily during build."""

from __future__ import annotations

import atexit
import logging
import re
import shutil
from pathlib import Path

from hatchling.builders.hooks.plugin.interface import BuildHookInterface

# Set up logging for the build hook
logger = logging.getLogger(__name__)


class CustomBuildHook(BuildHookInterface):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._vendored_path = None
        self._cleanup_registered = False

    def initialize(self, version, build_data):
        # Paths
        log_parser_src = Path(self.root) / "log_parser" / "log_parser"
        tern_pkg_dir = Path(self.root) / "tern" / "tern"
        log_parser_dest = tern_pkg_dir / "log_parser"

        # Store the vendored path for cleanup
        self._vendored_path = log_parser_dest

        # Remove old vendored code if it exists
        if log_parser_dest.exists():
            shutil.rmtree(log_parser_dest)

        # Copy the log_parser code
        if log_parser_src.exists():
            shutil.copytree(log_parser_src, log_parser_dest)
            logger.info("Vendored log_parser into %s (temporary)", log_parser_dest)

            # Fix internal imports in the vendored log_parser code
            self._fix_internal_imports(log_parser_dest)

            # Register cleanup function to run after build
            if not self._cleanup_registered:
                atexit.register(self._cleanup_vendored_code)
                self._cleanup_registered = True
        else:
            logger.warning("log_parser source not found at %s", log_parser_src)

    def finalize(self, version, build_data, artifact_path):
        """Clean up vendored code after build is complete."""
        self._cleanup_vendored_code()

    def _cleanup_vendored_code(self):
        """Clean up the vendored log_parser code after build."""
        if self._vendored_path and self._vendored_path.exists():
            try:
                shutil.rmtree(self._vendored_path)
                logger.info("Cleaned up temporary vendored code at %s", self._vendored_path)
            except Exception as e:
                logger.warning("Could not clean up vendored code at %s: %s", self._vendored_path, e)

    def _fix_internal_imports(self, log_parser_dir: Path):
        """Fix internal imports within the vendored log_parser code."""
        logger.info("Fixing internal imports in vendored log_parser...")

        # Find all Python files in the vendored log_parser
        python_files = list(log_parser_dir.rglob("*.py"))

        for py_file in python_files:
            try:
                with open(py_file, encoding="utf-8") as f:
                    content = f.read()

                original_content = content

                # Calculate relative path from current file to log_parser root
                relative_path = py_file.relative_to(log_parser_dir)
                depth = len(relative_path.parts) - 1  # -1 because we don't count the file itself

                # Create the correct relative import prefix
                if depth == 0:
                    # File is at root level (e.g., models.py, __init__.py)
                    prefix = "."
                else:
                    # File is in subdirectory (e.g., analysis/resource_analyzer.py)
                    prefix = "." + "." * depth

                # Replace absolute imports with relative imports
                # Pattern 1: from log_parser.something import ...
                def replace_from_import(match):
                    import_path = match.group(1)
                    return f"from {prefix}{import_path} import"

                content = re.sub(
                    r"^from log_parser\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*) import",
                    replace_from_import,
                    content,
                    flags=re.MULTILINE,
                )

                # Pattern 2: import log_parser.something
                def replace_direct_import(match):
                    import_path = match.group(1)
                    # For direct imports, we need to import the module and then reference it
                    # This is tricky with relative imports, so we'll convert to from...import
                    parts = import_path.split(".")
                    module_path = ".".join(parts[:-1]) if len(parts) > 1 else ""
                    module_name = parts[-1]
                    if module_path:
                        return f"from {prefix}{module_path} import {module_name}"
                    return f"from {prefix} import {module_name}"

                content = re.sub(
                    r"^import log_parser\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)",
                    replace_direct_import,
                    content,
                    flags=re.MULTILINE,
                )

                # Pattern 3: from log_parser import something
                content = re.sub(r"^from log_parser import", f"from {prefix} import", content, flags=re.MULTILINE)

                # Only write if content changed
                if content != original_content:
                    with open(py_file, "w", encoding="utf-8") as f:
                        f.write(content)
                    logger.info("Fixed imports in %s (depth: %d, prefix: %s)", py_file.relative_to(log_parser_dir), depth, prefix)

            except Exception as e:
                logger.warning("Could not fix imports in %s: %s", py_file, e)

        logger.info("Finished fixing internal imports in vendored log_parser")
