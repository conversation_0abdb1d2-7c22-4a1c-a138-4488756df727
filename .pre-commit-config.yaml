repos:
  # Pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-toml
      - id: debug-statements
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: requirements-txt-fixer

  # Code formatting with Black
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        args: [--line-length=140]

  # Import sorting with isort
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: [--profile=black, --line-length=140, --skip-gitignore]


  # Type checking with MyPy (simplified - can be run manually via hatch run lint)
  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.16.1
  #   hooks:
  #     - id: mypy
  #       exclude: ^(tests/|examples/|build_hook\.py)

  # Security scanning with Bandit
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.5
    hooks:
      - id: bandit
        args: [-c, pyproject.toml]
        additional_dependencies: ["bandit[toml]"]
        exclude: ^(tests/|.*/tests/)

  # Docstring checking with pydocstyle (disabled - can be run manually)
  # - repo: https://github.com/PyCQA/pydocstyle
  #   rev: 6.3.0
  #   hooks:
  #     - id: pydocstyle
  #       args: [--convention=google]
  #       exclude: ^(tests/|examples/|build_hook\.py)

  # Check dependencies for known security vulnerabilities (optional - can be run manually)
  # - repo: https://github.com/pyupio/safety
  #   rev: 3.5.2
  #   hooks:
  #     - id: safety

  # Additional Python checks (optional - can be run manually via hatch run lint)
  # - repo: https://github.com/pycqa/pylint
  #   rev: v3.3.7
  #   hooks:
  #     - id: pylint
  #       args: [--rcfile=.pylintrc]

default_language_version:
  python: python3

ci:
  autofix_prs: true
  autoupdate_commit_msg: "chore: update pre-commit hooks"
  autoupdate_schedule: monthly
